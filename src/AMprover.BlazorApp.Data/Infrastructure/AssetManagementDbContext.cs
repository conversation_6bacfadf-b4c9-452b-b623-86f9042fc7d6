using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.Data.Entities.AM;
using AMprover.Data.Entities.AM.SP;
using AMprover.Data.Entities.AM.SP.Reports;
using AMprover.Data.Enums;
using AMprover.Data.Models;
using Microsoft.EntityFrameworkCore;
using Object = AMprover.Data.Entities.AM.Object;

namespace AMprover.Data.Infrastructure;

public class AssetManagementDbContext : DbContext
{
    private readonly IAssetManagementPortfolioResolver _assetManagementPortfolioResolver;

    public AssetManagementDbContext(DbContextOptions<AssetManagementDbContext> options) : base(options)
    {
    }

    public AssetManagementDbContext(DbContextOptions<AssetManagementDbContext> options,
        IAssetManagementPortfolioResolver assetManagementPortfolioResolver) : base(options)
    {
        _assetManagementPortfolioResolver = assetManagementPortfolioResolver;
    }

    //Deprecated, should be removed??
    public DbSet<Amprover3EditLog> Amprover3EditLog { get; set; }
    public DbSet<Amprover3MrbId> Amprover3MrbId { get; set; }
    public DbSet<Amprover3PickClusterSi> Amprover3PickClusterSi { get; set; }
    public DbSet<Amprover3PickSi> Amprover3PickSi { get; set; }
    public DbSet<Amprover3SiLinkFilters> Amprover3SiLinkFilters { get; set; }

    //new
    public DbSet<Attachment> Attachment { get; set; }
    public DbSet<AttachmentCategory> AttachmentCategory { get; set; }
    public DbSet<BudgetCost> BudgetCost { get; set; }
    public DbSet<ClusterReport> ClusterReport { get; set; }
    public DbSet<ClusterSiTaskCollection> ClusterSiTaskCollection { get; set; }
    public DbSet<CmnCostCalculation> CmnCostCalculation { get; set; }
    public DbSet<CriticalityRanking> CriticalityRanking { get; set; }
    public DbSet<DbScriptsExecuted> DbScriptsExecuted { get; set; }
    public DbSet<DsRamsReportRams> DsRamsReportRams { get; set; }
    public DbSet<DsRamsReportRamsDetails> DsRamsReportRamsDetails { get; set; }
    public DbSet<DsRamsReportRamsTable> DsRamsReportRamsTable { get; set; }
    public DbSet<ExportSimco> ExportSimco { get; set; }
    public DbSet<ExportTasks> ExportTasks { get; set; }
    public DbSet<LccDetailGrid> LccDetailGrid { get; set; }
    public DbSet<LccFmecaOfRiskObject> LccFmecaOfRiskObject { get; set; }
    public DbSet<LccGrid> LccGrid { get; set; }
    public DbSet<LccMrbCalc> LccMrbCalc { get; set; }
    public DbSet<LccOpexCalc> LccOpexCalc { get; set; }
    public DbSet<LccOutdated> LccOutdated { get; set; }
    public DbSet<LccOutdatedOnRisk> LccOutdatedOnRisk { get; set; }
    public DbSet<LccRamsCalc> LccRamsCalc { get; set; }
    public DbSet<LccSiCalc> LccSiCalc { get; set; }
    public DbSet<Lookup> Lookup { get; set; }
    public DbSet<LookupExecutor> LookupExecutor { get; set; }
    public DbSet<LookupAdditionalData> LookupAdditionalData { get; set; }
    public DbSet<LookupFailCat> LookupFailCat { get; set; }
    public DbSet<LookupFailMode> LookupFailMode { get; set; }
    public DbSet<LookupGridColumn> LookupGridColumn { get; set; }
    public DbSet<LookupInflationGroup> LookupInflationGroup { get; set; }
    public DbSet<LookupInitiator> LookupInitiator { get; set; }
    public DbSet<LookupIntervalUnit> LookupIntervalUnit { get; set; }
    public DbSet<LookupMxPolicy> LookupMxPolicy { get; set; }
    public DbSet<LookupSettings> LookupSettings { get; set; }
    public DbSet<LookupUserDefined> LookupUserDefined { get; set; }
    public DbSet<MrbTaskCount> MrbTaskCount { get; set; }
    public DbSet<MrbTree> MrbTree { get; set; }
    public DbSet<NumberOfSiItems> NumberOfSiItems { get; set; }
    public DbSet<OpexLccDetails> OpexLccDetails { get; set; }
    public DbSet<PageNavigation> PageNavigations { get; set; }
    public DbSet<PickSiFromCommonTask> PickSiFromCommonTask { get; set; }
    public DbSet<PrioTaskCollection> PrioTaskCollection { get; set; }
    public DbSet<PriorityGridData> PriorityGridData { get; set; }
    public DbSet<PriorityTaskCost> PriorityTaskCost { get; set; }
    public DbSet<PriorityTaskGridData> PriorityTaskGridData { get; set; }
    public DbSet<SiRiskCalc> SiRiskCalc { get; set; }
    public DbSet<SiStatisticsRiskQualification> SiStatisticsRiskQualification { get; set; }
    public DbSet<SyncRamsMrb> SyncRamsMrb { get; set; }
    public DbSet<TaskCostOnClusterModified> TaskCostOnClusterModified { get; set; }
    public DbSet<TaskCostOnMrbModified> TaskCostOnMrbModified { get; set; }
    public DbSet<TaskEdit> TaskEdit { get; set; }
    public DbSet<UserSetting> UserSettings { get; set; }
    public DbSet<TaskLookupGrid> TaskLookupGrid { get; set; }
    public DbSet<BvAspectSets> BvAspectSets { get; set; }
    public DbSet<BvRelevanceSets> BvRelevanceSets { get; set; }
    public DbSet<BvSiItems> BvSiItems { get; set; }
    public DbSet<BvWeightingModels> BvWeightingModels { get; set; }
    public DbSet<Cluster> Cluster { get; set; }
    public DbSet<ClusterCost> ClusterCost { get; set; }
    public DbSet<ClusterTaskPlan> ClusterTaskPlan { get; set; }
    public DbSet<CommonCost> CommonCost { get; set; }
    public DbSet<CommonTask> CommonTask { get; set; }
    public DbSet<CommonTaskCost> CommonTaskCost { get; set; }

    [System.Obsolete("Not found in AMprover4", true)]
    public DbSet<Company> Company { get; set; }

    public DbSet<Department> Department { get; set; }
    public DbSet<DerModified> DerModified { get; set; }
    public DbSet<Descriptions> Descriptions { get; set; }
    public DbSet<EditLog> EditLog { get; set; }
    public DbSet<Filters> Filters { get; set; }
    public DbSet<FiltersSelectionList> FiltersSelectionList { get; set; }

    public DbSet<Fmeca> Fmeca { get; set; }

    [System.Obsolete("Nog niet voorbij zien komen")]
    public DbSet<FmecaSelect> FmecaSelect { get; set; }

    public DbSet<Lcc> Lcc { get; set; }
    public DbSet<Lccdetail> Lccdetail { get; set; }
    public DbSet<LcceffectDetail> LcceffectDetail { get; set; }

    [System.Obsolete("Not found in AMprover4 code", true)]
    public DbSet<Login> Login { get; set; }

    public DbSet<Mca> Mca { get; set; }
    public DbSet<Mrb> Mrb { get; set; }
    public DbSet<MrbImage> MrbImage { get; set; }
    public DbSet<Object> Object { get; set; }
    public DbSet<OpexData> OpexData { get; set; }
    public DbSet<OpexFactor> OpexFactor { get; set; }
    public DbSet<OpexToLcc> OpexToLcc { get; set; }
    public DbSet<OpexToLccDetail> OpexToLccDetail { get; set; }
    public DbSet<PickSi> PickSi { get; set; }
    public DbSet<Priority> Priority { get; set; }
    public DbSet<PriorityBudget> PriorityBudget { get; set; }
    public DbSet<PriorityCost> PriorityCost { get; set; }
    public DbSet<PriorityTask> PriorityTask { get; set; }
    public DbSet<PriorityVersion> PriorityVersion { get; set; }
    public DbSet<Rams> Rams { get; set; }
    public DbSet<RamsDiagram> RamsDiagram { get; set; }
    public DbSet<RiskObject> RiskObject { get; set; }
    public DbSet<SapaCollection> SapaCollection { get; set; }
    public DbSet<Sapa> Sapa { get; set; }
    public DbSet<SapaYear> SapaYear { get; set; }
    public DbSet<SapaDetail> SapaDetail { get; set; }
    public DbSet<SapaWorkpackage> SapaWorkpackage { get; set; }
    public DbSet<Scenario> Scenario { get; set; }
    public DbSet<Si> Si { get; set; }
    public DbSet<SiLinkFilters> SiLinkFilters { get; set; }
    public DbSet<SiRisks> SiRisks { get; set; }
    public DbSet<SiStatistics> SiStatistics { get; set; }
    public DbSet<Spare> Spare { get; set; }
    public DbSet<Task> Task { get; set; }
    public DbSet<Vdmxl> Vdmxl { get; set; }
    public DbSet<Workpackage> Workpackage { get; set; }
    public DbSet<VwSiFilter> VwSiFilter { get; set; }
    public DbSet<ReportFilter> ReportFilter { get; set; }
    public DbSet<UserDepartment> UserDepartment { get; set; }

    //Stored Procedures
    public virtual DbSet<RiskWithObjects> RisksWithObjects { get; set; }
    public virtual DbSet<TaskWithObjects> TasksWithObjects { get; set; }

    public virtual DbSet<ClusterTaskWithObjects> ClusterTasksWithObjects { get; set; }
    public virtual DbSet<ClusterTaskPlanWithObjects> ClusterTaskPlansWithObjects { get; set; }
    public virtual DbSet<ClusterTree> ClusterTree { get; set; }
    public virtual DbSet<ClusterWorkPackageTree> ClusterWorkPackageTree { get; set; }
    public virtual DbSet<ClusterStatus> ClusterStatus { get; set; }
    public virtual DbSet<RiskOnAbsStoredProcedureResult> RiskOnAbsStoredProcedure { get; set; }

    //Stored Procedures: reports
    public virtual DbSet<FunctionalTreeReportItem> FunctionalTreeReportItems { get; set; }
    public virtual DbSet<CommonActionReportItem> CommonActionReportItems { get; set; }
    public virtual DbSet<SignificantItemReportItem> SignificantItemReportItems { get; set; }
    public virtual DbSet<RiskAnalysisReportItem> RiskAnalysisReportItems { get; set; }
    public virtual DbSet<TaskPlanReportItem> TaskPlanReportItems { get; set; }
    public virtual DbSet<ClusterReportItem> ClusterReportItems { get; set; }
    public virtual DbSet<RiskAndPreviousActionsReportItem> RiskAndPreviousActionsReportItems { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            string connectionstring = _assetManagementPortfolioResolver.GetCurrentPortfolioConnectionString();

            if (string.IsNullOrWhiteSpace(connectionstring))
            {
                throw new System.IndexOutOfRangeException(
                    $"Missig connectionstring for: {nameof(AssetManagementDbContext)} via {nameof(IAssetManagementPortfolioResolver)}.");
            }

            // Re-Enable the line below for EF-Core issue debugging
            // optionsBuilder.EnableSensitiveDataLogging();

            optionsBuilder.UseSqlServer(
                connectionstring,
                builder =>
                {
                    builder.EnableRetryOnFailure(5, System.TimeSpan.FromSeconds(10), null);
                    builder.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                });

            optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
        }

        //Usage package for batch processing
        optionsBuilder.UseBatchEF_MSSQL();
        base.OnConfiguring(optionsBuilder);
    }

    public void SaveAuditLog(string userName)
    {
        ChangeTracker.DetectChanges();
        var auditEntries = new List<AuditLog>();

        var entities = ChangeTracker.Entries()
            .Where(x => x.State != EntityState.Added
                        && x.State != EntityState.Unchanged
                        && x.State != EntityState.Detached)
            .ToList();

        foreach (var entry in entities)
        {
            var auditEntry = new AuditLog(entry)
            {
                TableName = entry.Entity.GetType().Name,
                Username = userName
            };

            auditEntries.Add(auditEntry);

            foreach (var property in entry.Properties)
            {
                var propertyName = property.Metadata.Name;
                switch (entry.State)
                {
                    case EntityState.Deleted:
                        auditEntry.AuditType = AuditType.Delete;
                        auditEntry.OldValues[propertyName] = property.OriginalValue!;
                        break;
                    case EntityState.Modified:
                        if (property.IsModified)
                        {
                            auditEntry.ChangedColumns.Add(propertyName);
                            auditEntry.AuditType = AuditType.Update;
                            auditEntry.OldValues[propertyName] = property.OriginalValue!;
                            auditEntry.NewValues[propertyName] = property.CurrentValue!;
                        }
                        break;
                    case EntityState.Detached:
                        break;
                    case EntityState.Unchanged:
                        break;
                    case EntityState.Added:
                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
        }

        foreach (var auditEntry in auditEntries)
        {
            EditLog.Add(auditEntry.ToAudit());
        }
    }
    
    //TODO: Most can be declared on entities (except relationships) --> WIP
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<UserDepartment>(entity =>
        {
            entity.HasKey(ud => new { ud.UserId, ud.DepartmentId });
        
            entity.HasOne(ud => ud.Department)
                .WithMany()
                .HasForeignKey(ud => ud.DepartmentId);
        });
        
        modelBuilder.Entity<Amprover3EditLog>(entity =>
        {
            entity.HasNoKey();

            entity.ToTable("Amprover3_EditLog");

            entity.Property(e => e.LogDate).HasColumnType("datetime");

            entity.Property(e => e.LogId)
                .HasColumnName("LogID")
                .ValueGeneratedOnAdd();

            entity.Property(e => e.LogModificationType)
                .HasMaxLength(10)
                .IsUnicode(false);

            entity.Property(e => e.LogModifications).IsUnicode(false);

            entity.Property(e => e.LogObjectId).HasColumnName("LogObjectID");

            entity.Property(e => e.LogObjectType)
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.LogObjectVarId)
                .HasColumnName("LogObjectVarID")
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.LogUserId)
                .HasColumnName("LogUserID")
                .IsUnicode(false);
        });

        modelBuilder.Entity<Amprover3MrbId>(entity =>
        {
            entity.HasNoKey();

            entity.ToTable("Amprover3_Mrb_ID");

            entity.Property(e => e.Id)
                .HasColumnName("ID")
                .ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<Amprover3PickClusterSi>(entity =>
        {
            entity.HasNoKey();

            entity.ToTable("Amprover3_PickClusterSI");

            entity.Property(e => e.PckClClusterId).HasColumnName("PckClClusterID");

            entity.Property(e => e.PckClId)
                .HasColumnName("PckClID")
                .ValueGeneratedOnAdd();

            entity.Property(e => e.PckClSiId).HasColumnName("PckClSiID");

            entity.Property(e => e.PckClSiType)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PckClTaskId).HasColumnName("PckClTaskID");
        });

        modelBuilder.Entity<Amprover3PickSi>(entity =>
        {
            entity.HasNoKey();

            entity.ToTable("Amprover3_PickSI");

            entity.Property(e => e.PckSiDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.PckSiDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.PckSiDescription).IsUnicode(false);

            entity.Property(e => e.PckSiExecutionYear).HasColumnType("datetime");

            entity.Property(e => e.PckSiId)
                .HasColumnName("PckSiID")
                .ValueGeneratedOnAdd();

            entity.Property(e => e.PckSiInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.PckSiLinkFilterId).HasColumnName("PckSiLinkFilterID");

            entity.Property(e => e.PckSiModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.PckSiMrbId).HasColumnName("PckSiMrbID");

            entity.Property(e => e.PckSiReferenceId)
                .HasColumnName("PckSiReferenceID")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PckSiSiId).HasColumnName("PckSiSiID");

            entity.Property(e => e.PckSiSiType)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Amprover3SiLinkFilters>(entity =>
        {
            entity.HasNoKey();

            entity.ToTable("Amprover3_SiLinkFilters");

            entity.Property(e => e.SifCompactCatId).HasColumnName("SifCompactCatID");

            entity.Property(e => e.SifDescription).IsUnicode(false);

            entity.Property(e => e.SifFilter).IsUnicode(false);

            entity.Property(e => e.SifGroup)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.SifId)
                .HasColumnName("SifID")
                .ValueGeneratedOnAdd();

            entity.Property(e => e.SifName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.SifObjectId).HasColumnName("SifObjectID");

            entity.Property(e => e.SifParentId).HasColumnName("SifParentID");

            entity.Property(e => e.SifRiskId).HasColumnName("SifRiskID");

            entity.Property(e => e.SifSubGroup)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.SifTaskId).HasColumnName("SifTaskID");
        });

        modelBuilder.Entity<Attachment>(entity =>
        {
            modelBuilder.Entity<Attachment>()
                .HasOne(a => a.Sapa)
                .WithMany(s => s.Attachments)
                .HasForeignKey(a => a.AtchSapaId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Attachment>()
                .HasOne(a => a.Risk)
                .WithMany(s => s.Attachments)
                .HasForeignKey(a => a.AtchMrbId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Attachment>()
                .HasOne(a => a.Task)
                .WithMany(s => s.Attachments)
                .HasForeignKey(a => a.AtchTskId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Attachment>()
                .HasOne(a => a.RiskObject)
                .WithMany(s => s.Attachments)
                .HasForeignKey(a => a.AtchRiskObjectId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            modelBuilder.Entity<Attachment>()
                .HasOne(a => a.AttachmentCategory)
                .WithMany(s => s.Attachments)
                .HasForeignKey(a => a.AtchCatgoryId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        //TODO: moved to class, remove once all is done
        modelBuilder.Entity<BudgetCost>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("BudgetCost");

            entity.Property(e => e.Accepted).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Budget)
                .HasColumnName("budget")
                .HasColumnType("decimal(38, 2)");

            entity.Property(e => e.BudgetSum)
                .HasColumnName("budgetSum")
                .HasColumnType("decimal(38, 2)");

            entity.Property(e => e.CostsYear1).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.CostsYear2).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.CostsYear3).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.CostsYear4).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.CostsYear5).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Postponed).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Proposed).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Rejected).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Risk).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.RiskDelta).HasColumnType("decimal(38, 2)");
        });

        modelBuilder.Entity<ClusterReport>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("ClusterReport");

            entity.Property(e => e.ClcId).HasColumnName("ClcID");

            entity.Property(e => e.ClustCostCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustDisciplineCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustDownTime).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustDuration).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustEnergyCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustEstTaskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustExecutor)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClustId).HasColumnName("ClustID");

            entity.Property(e => e.ClustInitiator)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClustInterval).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.ClustIntervalUnit)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.ClustMaterialCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClustName0)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClustName1)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClustName2)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClustName3)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClustRemark).IsUnicode(false);

            entity.Property(e => e.ClustResponsible).IsUnicode(false);

            entity.Property(e => e.ClustSecondValues).IsUnicode(false);

            entity.Property(e => e.ClustSharedCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustShortKey)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClustTaskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustToolCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.ClustTotalCmnCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.CmnCostType)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.MrbFailureCause).IsUnicode(false);

            entity.Property(e => e.MrbFailureConsequences).IsUnicode(false);

            entity.Property(e => e.MrbId).HasColumnName("MrbID");

            entity.Property(e => e.MrbObject0)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.MrbObject2)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.MrbObject3)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.MrbObject4)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ParentClusterName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RiskObjName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RiskObject)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RiskObjectDesc).IsUnicode(false);

            entity.Property(e => e.Scenario)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.TskCostCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskCostDescription)
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.TskCostPrice).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskCostQuantity).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskCostTskId).HasColumnName("TskCostTskID");

            entity.Property(e => e.TskCostUnits).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskDescription).IsUnicode(false);

            entity.Property(e => e.TskDownTime).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskDuration).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskExecutor)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.TskGeneralDescription).IsUnicode(false);

            entity.Property(e => e.TskId).HasColumnName("TskID");

            entity.Property(e => e.TskInitiator)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.TskInterval).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskIntervalUnit)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.TskMrbId).HasColumnName("TskMrbID");

            entity.Property(e => e.TskName)
                .HasMaxLength(60)
                .IsUnicode(false);

            entity.Property(e => e.TskPermit).IsUnicode(false);

            entity.Property(e => e.TskPolicy)
                .HasMaxLength(5)
                .IsUnicode(false);

            entity.Property(e => e.TskRemark).IsUnicode(false);

            entity.Property(e => e.TskType)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.TskWorkPackage)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ClusterSiTaskCollection>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("ClusterSiTaskCollection");

            entity.Property(e => e.FailRateId).HasColumnName("FailRateID");

            entity.Property(e => e.IntUnitCalculationKey).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.MrbCustomAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbCustomBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbCustomEffectAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbDirectCostAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbDirectCostBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbEffectAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbEffectBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbMtbfAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbMtbfBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbRiskAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbRiskBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbcustomeffectbefore)
                .HasColumnName("mrbcustomeffectbefore")
                .HasColumnType("decimal(18, 3)");

            entity.Property(e => e.PckSiId).HasColumnName("PckSiID");

            entity.Property(e => e.PckSiSiId).HasColumnName("PckSiSiID");

            entity.Property(e => e.SiName)
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.SiUnitsTypeValues)
                .HasMaxLength(150)
                .IsUnicode(false);

            entity.Property(e => e.SiYear).HasColumnType("datetime");

            entity.Property(e => e.TskClusterCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskCommonActionId).HasColumnName("TskCommonActionID");

            entity.Property(e => e.TskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskDownTime).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskDuration).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskEstCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskExecutionDate).HasColumnType("smalldatetime");

            entity.Property(e => e.TskFinishDate).HasColumnType("smalldatetime");

            entity.Property(e => e.TskFmecaEffectPct).HasColumnType("numeric(18, 2)");

            entity.Property(e => e.TskId).HasColumnName("TskID");

            entity.Property(e => e.TskInterval).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskMrbId).HasColumnName("TskMrbID");

            entity.Property(e => e.TskName)
                .IsRequired()
                .HasMaxLength(60)
                .IsUnicode(false);

            entity.Property(e => e.TskOptimalCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskPermit).IsUnicode(false);

            entity.Property(e => e.TskType)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.TskUnits).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskWorkInspCost).HasColumnType("decimal(18, 2)");
        });

        modelBuilder.Entity<CmnCostCalculation>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("CmnCostCalculation");

            entity.Property(e => e.CmnTaskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.CtcCommonTaskId).HasColumnName("CtcCommonTaskID");

            entity.Property(e => e.CtcCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.InflPercentage).HasColumnType("decimal(18, 2)");
        });

        modelBuilder.Entity<DbScriptsExecuted>(entity =>
        {
            entity.HasKey(e => e.ScriptName);

            entity.Property(e => e.ScriptName).HasMaxLength(255);

            entity.Property(e => e.DateExecuted)
                .HasColumnType("datetime")
                .HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<DsRamsReportRams>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("DsRamsReport_Rams");

            entity.Property(e => e.RamsBitmap).HasColumnType("image");

            entity.Property(e => e.RamsCircuitDepCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsClassDc)
                .HasColumnName("RamsClassDC")
                .HasMaxLength(6)
                .IsUnicode(false);

            entity.Property(e => e.RamsDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDcd).HasColumnName("RamsDCd");

            entity.Property(e => e.RamsDescr).IsUnicode(false);

            entity.Property(e => e.RamsDgDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDgDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDgDescr).IsUnicode(false);

            entity.Property(e => e.RamsDgId).HasColumnName("RamsDgID");

            entity.Property(e => e.RamsDgInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgReferenceId)
                .HasColumnName("RamsDgReferenceID")
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgRemark).IsUnicode(false);

            entity.Property(e => e.RamsDgRiskObject)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgScenario)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgSiId).HasColumnName("RamsDgSiID");

            entity.Property(e => e.RamsDgSiRefId)
                .HasColumnName("RamsDgSiRefID")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgWantLcc).HasColumnName("RamsDgWantLCC");

            entity.Property(e => e.RamsDiagramRefId).HasColumnName("RamsDiagramRefID");

            entity.Property(e => e.RamsFailCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsFunctionalDemand).IsUnicode(false);

            entity.Property(e => e.RamsId).HasColumnName("RamsID");

            entity.Property(e => e.RamsInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsMtbffunct).HasColumnName("RamsMTBFFunct");

            entity.Property(e => e.RamsMtbftechn).HasColumnName("RamsMTBFTechn");

            entity.Property(e => e.RamsMttr).HasColumnName("RamsMTTR");

            entity.Property(e => e.RamsName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsObjectId).HasColumnName("RamsObjectID");

            entity.Property(e => e.RamsPfd).HasColumnName("RamsPFD");

            entity.Property(e => e.RamsPreventiveCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsRemark).IsUnicode(false);

            entity.Property(e => e.RamsRiskId).HasColumnName("RamsRiskID");

            entity.Property(e => e.RamsRiskObjectId).HasColumnName("RamsRiskObjectID");

            entity.Property(e => e.RamsSiId).HasColumnName("RamsSiID");

            entity.Property(e => e.RamsTechnCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsTotalCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsWantLcc).HasColumnName("RamsWantLCC");

            entity.Property(e => e.RamsXposition).HasColumnName("RamsXPosition");

            entity.Property(e => e.RamsYposition).HasColumnName("RamsYPosition");
        });

        modelBuilder.Entity<DsRamsReportRamsDetails>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("DsRamsReport_RamsDetails");

            entity.Property(e => e.RamsBitmap).HasColumnType("image");

            entity.Property(e => e.RamsCircuitDepCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsClassDc)
                .HasColumnName("RamsClassDC")
                .HasMaxLength(6)
                .IsUnicode(false);

            entity.Property(e => e.RamsDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDcd).HasColumnName("RamsDCd");

            entity.Property(e => e.RamsDescr).IsUnicode(false);

            entity.Property(e => e.RamsDgDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDgDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDgDescr).IsUnicode(false);

            entity.Property(e => e.RamsDgId).HasColumnName("RamsDgID");

            entity.Property(e => e.RamsDgInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgReferenceId)
                .HasColumnName("RamsDgReferenceID")
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgRemark).IsUnicode(false);

            entity.Property(e => e.RamsDgRiskObject)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgScenario)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgSiId).HasColumnName("RamsDgSiID");

            entity.Property(e => e.RamsDgSiRefId)
                .HasColumnName("RamsDgSiRefID")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgWantLcc).HasColumnName("RamsDgWantLCC");

            entity.Property(e => e.RamsDiagramRefId).HasColumnName("RamsDiagramRefID");

            entity.Property(e => e.RamsFailCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsFunctionalDemand).IsUnicode(false);

            entity.Property(e => e.RamsId).HasColumnName("RamsID");

            entity.Property(e => e.RamsInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsMtbffunct).HasColumnName("RamsMTBFFunct");

            entity.Property(e => e.RamsMtbftechn).HasColumnName("RamsMTBFTechn");

            entity.Property(e => e.RamsMttr).HasColumnName("RamsMTTR");

            entity.Property(e => e.RamsName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsObjectId).HasColumnName("RamsObjectID");

            entity.Property(e => e.RamsPfd).HasColumnName("RamsPFD");

            entity.Property(e => e.RamsPreventiveCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsRemark).IsUnicode(false);

            entity.Property(e => e.RamsRiskId).HasColumnName("RamsRiskID");

            entity.Property(e => e.RamsRiskObjectId).HasColumnName("RamsRiskObjectID");

            entity.Property(e => e.RamsSiId).HasColumnName("RamsSiID");

            entity.Property(e => e.RamsTechnCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsTotalCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsWantLcc).HasColumnName("RamsWantLCC");

            entity.Property(e => e.RamsXposition).HasColumnName("RamsXPosition");

            entity.Property(e => e.RamsYposition).HasColumnName("RamsYPosition");
        });

        modelBuilder.Entity<DsRamsReportRamsTable>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("DsRamsReport_RamsTable");

            entity.Property(e => e.RamsBitmap).HasColumnType("image");

            entity.Property(e => e.RamsCircuitDepCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsClassDc)
                .HasColumnName("RamsClassDC")
                .HasMaxLength(6)
                .IsUnicode(false);

            entity.Property(e => e.RamsDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDcd).HasColumnName("RamsDCd");

            entity.Property(e => e.RamsDescr).IsUnicode(false);

            entity.Property(e => e.RamsDgDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDgDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDgDescr).IsUnicode(false);

            entity.Property(e => e.RamsDgId).HasColumnName("RamsDgID");

            entity.Property(e => e.RamsDgInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgReferenceId)
                .HasColumnName("RamsDgReferenceID")
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgRemark).IsUnicode(false);

            entity.Property(e => e.RamsDgRiskObject)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgScenario)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgSiId).HasColumnName("RamsDgSiID");

            entity.Property(e => e.RamsDgSiRefId)
                .HasColumnName("RamsDgSiRefID")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDgWantLcc).HasColumnName("RamsDgWantLCC");

            entity.Property(e => e.RamsDiagramRefId).HasColumnName("RamsDiagramRefID");

            entity.Property(e => e.RamsFailCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsFunctionalDemand).IsUnicode(false);

            entity.Property(e => e.RamsId).HasColumnName("RamsID");

            entity.Property(e => e.RamsInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsMtbffunct).HasColumnName("RamsMTBFFunct");

            entity.Property(e => e.RamsMtbftechn).HasColumnName("RamsMTBFTechn");

            entity.Property(e => e.RamsMttr).HasColumnName("RamsMTTR");

            entity.Property(e => e.RamsName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsObjectId).HasColumnName("RamsObjectID");

            entity.Property(e => e.RamsPfd).HasColumnName("RamsPFD");

            entity.Property(e => e.RamsPreventiveCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsRemark).IsUnicode(false);

            entity.Property(e => e.RamsRiskId).HasColumnName("RamsRiskID");

            entity.Property(e => e.RamsRiskObjectId).HasColumnName("RamsRiskObjectID");

            entity.Property(e => e.RamsSiId).HasColumnName("RamsSiID");

            entity.Property(e => e.RamsTechnCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsTotalCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsWantLcc).HasColumnName("RamsWantLCC");

            entity.Property(e => e.RamsXposition).HasColumnName("RamsXPosition");

            entity.Property(e => e.RamsYposition).HasColumnName("RamsYPosition");

            entity.Property(e => e.SiDescription)
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.Property(e => e.SiMiscTextField1)
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.SiName)
                .HasMaxLength(40)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ExportSimco>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("ExportSimco");

            entity.Property(e => e.CmnTaskReferenceId)
                .HasColumnName("CmnTaskReferenceID")
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskClusterCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskCommonTaskId).HasColumnName("PrioTskCommonTaskID");

            entity.Property(e => e.PrioTskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskDateDue).HasColumnType("smalldatetime");

            entity.Property(e => e.PrioTskDateExecuted).HasColumnType("smalldatetime");

            entity.Property(e => e.PrioTskDescription)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskFromReference)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskId).HasColumnName("PrioTskID");

            entity.Property(e => e.PrioTskObjectId).HasColumnName("PrioTskObjectID");

            entity.Property(e => e.PrioTskOriginalId).HasColumnName("PrioTskOriginalID");

            entity.Property(e => e.PrioTskPriorityCode).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskPriorityId).HasColumnName("PrioTskPriorityID");

            entity.Property(e => e.PrioTskReferenceId)
                .HasColumnName("PrioTskReferenceID")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskRiskBudget).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskRiskId).HasColumnName("PrioTskRiskID");

            entity.Property(e => e.PrioTskSiDescription)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskSiId).HasColumnName("PrioTskSiID");

            entity.Property(e => e.PrioTskSiUnits).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskTaskId).HasColumnName("PrioTskTaskID");

            entity.Property(e => e.SiContractEnd).HasColumnType("datetime");

            entity.Property(e => e.SiMiscTextField1)
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.SiVendor)
                .HasMaxLength(30)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ExportTasks>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("ExportTasks");

            entity.Property(e => e.PrioTskClusterCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskCommonTaskId).HasColumnName("PrioTskCommonTaskID");

            entity.Property(e => e.PrioTskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskDateDue).HasColumnType("smalldatetime");

            entity.Property(e => e.PrioTskDateExecuted).HasColumnType("smalldatetime");

            entity.Property(e => e.PrioTskDescription)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskFromReference)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskObjectId).HasColumnName("PrioTskObjectID");

            entity.Property(e => e.PrioTskPriorityCode).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskPriorityId).HasColumnName("PrioTskPriorityID");

            entity.Property(e => e.PrioTskReferenceId)
                .HasColumnName("PrioTskReferenceID")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskRiskBudget).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskRiskId).HasColumnName("PrioTskRiskID");

            entity.Property(e => e.PrioTskSiDescription)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskSiId).HasColumnName("PrioTskSiID");

            entity.Property(e => e.PrioTskSiUnits).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskTaskId).HasColumnName("PrioTskTaskID");
        });

        modelBuilder.Entity<LccDetailGrid>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("LccDetailGrid");

            entity.Property(e => e.LccDetActionCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetAec)
                .HasColumnName("LccDetAEC")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetAvailabilityTechnical).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccDetAverageAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetAverageBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetAverageOptimalCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetAverageRealCorrectiveCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetAverageRealPreventiveCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetCorrectiveCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetDepreciation).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.LccDetDirectCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetEcoTechnical).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccDetFailureRate).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccDetFmecaAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetFmecaBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetFmecaCustomAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetFmecaCustomBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetFmecaCustomRiskAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetFmecaCustomRiskBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetId).HasColumnName("LccDetID");

            entity.Property(e => e.LccDetLccId).HasColumnName("LccDetLccID");

            entity.Property(e => e.LccDetMaintenanceCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetModificationCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetOpexCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetOptimalCorrectiveCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetOptimalPreventiveCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetPreventiveCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetProcedureCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetProductivityTechnical).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccDetRealCorrectiveCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetRealOpexCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetRealPreventiveCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetRealTotalCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetReliability).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.LccDetRiskAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetRiskBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetSpareCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetTaskCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetTotalCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetTotalNpv)
                .HasColumnName("LccDetTotalNPV")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetUtilizationTechnichal).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.LccProductionCost).HasColumnType("decimal(18, 2)");
        });

        modelBuilder.Entity<LccFmecaOfRiskObject>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("LccFmecaOfRiskObject");

            entity.Property(e => e.FmecaMatrix).IsUnicode(false);

            entity.Property(e => e.RiskObjFmecaId).HasColumnName("RiskObjFmecaID");

            entity.Property(e => e.RiskObjId).HasColumnName("RiskObjID");

            entity.Property(e => e.RiskObjName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<LccGrid>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("LccGrid");

            entity.Property(e => e.ChildObject2)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ChildObject3)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ChildObject4)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.FmecaMatrix).IsUnicode(false);

            entity.Property(e => e.LccAec)
                .HasColumnName("LccAEC")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccAverageOptimalCost).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.LccDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.LccDiscountRate).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.LccEcoFunctional).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccEcoTechnical).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccId).HasColumnName("LccID");

            entity.Property(e => e.LccInputAvailabilityTechnical).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccInputReliabilityFunctional).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccMcRav).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.LccMtbffunctional)
                .HasColumnName("LccMTBFFunctional")
                .HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccMtbftechnical)
                .HasColumnName("LccMTBFTechnical")
                .HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.LccNpv)
                .HasColumnName("LccNPV")
                .HasColumnType("decimal(18, 4)");

            entity.Property(e => e.LccNpvyear).HasColumnName("LccNPVyear");

            entity.Property(e => e.LccOptimalAverageCorrectiveCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.LccOptimalAveragePreventiveCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.LccOptimalImage).HasColumnType("image");

            entity.Property(e => e.LccOutputAvailabilityTechnical).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccOutputReliabilityTechnical).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccOverallProductionCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.LccPotential).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccProductionCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccProductivityFunctional).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccProductivityTechnical).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccRamsDiagramId).HasColumnName("LccRamsDiagramID");

            entity.Property(e => e.LccRamsId).HasColumnName("LccRamsID");

            entity.Property(e => e.LccRamsImage).HasColumnType("image");

            entity.Property(e => e.LccRealCostImage).HasColumnType("image");

            entity.Property(e => e.LccRemark).IsUnicode(false);

            entity.Property(e => e.LccReplacementValue).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.LccScenarioId).HasColumnName("LccScenarioID");

            entity.Property(e => e.LccSiId).HasColumnName("LccSiID");

            entity.Property(e => e.LccTotalAverageCost).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.LccUtilizationFunctional).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccUtilizationTechnical).HasColumnType("decimal(18, 6)");

            entity.Property(e => e.LccdateCalculated)
                .HasColumnName("LCCDateCalculated")
                .HasColumnType("smalldatetime");

            entity.Property(e => e.Obj2NewValue).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.Obj3NewValue).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.Obj4NewValue).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.ObjId).HasColumnName("ObjID");

            entity.Property(e => e.ObjName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ObjNewValue).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.PartOfName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RiskObjName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RiskObjProdCostHour).HasColumnType("decimal(18, 2)");
        });

        modelBuilder.Entity<LccMrbCalc>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("LccMrbCalc");

            entity.Property(e => e.Failmode)
                .IsRequired()
                .HasColumnName("failmode")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Failrateid).HasColumnName("failrateid");

            entity.Property(e => e.Fmecamatrix)
                .HasColumnName("fmecamatrix")
                .IsUnicode(false);

            entity.Property(e => e.Fmecaversion).HasColumnName("fmecaversion");

            entity.Property(e => e.Mrbactioncosts)
                .HasColumnName("mrbactioncosts")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbcapcosts)
                .HasColumnName("mrbcapcosts")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbchildobject).HasColumnName("mrbchildobject");

            entity.Property(e => e.Mrbchildobject1).HasColumnName("mrbchildobject1");

            entity.Property(e => e.Mrbchildobject2).HasColumnName("mrbchildobject2");

            entity.Property(e => e.Mrbchildobject3).HasColumnName("mrbchildobject3");

            entity.Property(e => e.Mrbchildobject4).HasColumnName("mrbchildobject4");

            entity.Property(e => e.Mrbcustomafter)
                .HasColumnName("mrbcustomafter")
                .HasColumnType("decimal(18, 3)");

            entity.Property(e => e.Mrbcustombefore)
                .HasColumnName("mrbcustombefore")
                .HasColumnType("decimal(18, 3)");

            entity.Property(e => e.Mrbdirectcostafter)
                .HasColumnName("mrbdirectcostafter")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbdirectcostbefore)
                .HasColumnName("mrbdirectcostbefore")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbdowntimeafter)
                .HasColumnName("mrbdowntimeafter")
                .HasColumnType("decimal(18, 4)");

            entity.Property(e => e.Mrbdowntimebefore)
                .HasColumnName("mrbdowntimebefore")
                .HasColumnType("decimal(18, 4)");

            entity.Property(e => e.Mrbeffectafter)
                .HasColumnName("mrbeffectafter")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbeffectbefore)
                .HasColumnName("mrbeffectbefore")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbfailurecategorie1)
                .HasColumnName("mrbfailurecategorie1")
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.Mrbfailurecategorie2)
                .HasColumnName("mrbfailurecategorie2")
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.Mrbfailuremode).HasColumnName("mrbfailuremode");

            entity.Property(e => e.Mrbfmecaselect)
                .HasColumnName("mrbfmecaselect")
                .IsUnicode(false);

            entity.Property(e => e.Mrbfmecaversion).HasColumnName("mrbfmecaversion");

            entity.Property(e => e.Mrbid).HasColumnName("mrbid");

            entity.Property(e => e.Mrbmtbfafter)
                .HasColumnName("mrbmtbfafter")
                .HasColumnType("decimal(18, 3)");

            entity.Property(e => e.Mrbmtbfbefore)
                .HasColumnName("mrbmtbfbefore")
                .HasColumnType("decimal(18, 3)");

            entity.Property(e => e.Mrbname)
                .IsRequired()
                .HasColumnName("mrbname")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.Mrboptimalcosts)
                .HasColumnName("mrboptimalcosts")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbriskafter)
                .HasColumnName("mrbriskafter")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbriskbefore)
                .HasColumnName("mrbriskbefore")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbriskobject).HasColumnName("mrbriskobject");

            entity.Property(e => e.Mrbsparecosts)
                .HasColumnName("mrbsparecosts")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Mrbsparemanagecost)
                .HasColumnName("mrbsparemanagecost")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.NewValue).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.NewValue1).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.NewValue2).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.NewValue3).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.NewValue4).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.Riskobjnoofinstallation).HasColumnName("riskobjnoofinstallation");
        });

        modelBuilder.Entity<LccOpexCalc>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("lccOpexCalc");

            entity.Property(e => e.OpexDataInflationGroupId).HasColumnName("OpexDataInflationGroupID");

            entity.Property(e => e.OpexDataInflationGroupId2).HasColumnName("OpexDataInflationGroupID2");

            entity.Property(e => e.OpexDataInflationGroupId3).HasColumnName("OpexDataInflationGroupID3");

            entity.Property(e => e.OpexDataName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.OpexDataName2)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.OpexDataName3)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.OpexDataPercentage).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.OpexDataPercentage2).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.OpexDataPercentage3).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.OpexLccColorName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.OpexLccId).HasColumnName("OpexLccID");

            entity.Property(e => e.OpexLccLccId).HasColumnName("OpexLccLccID");

            entity.Property(e => e.OpexLccName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.OpexLccOpexDataId1).HasColumnName("OpexLccOpexDataID1");

            entity.Property(e => e.OpexLccOpexDataId2).HasColumnName("OpexLccOpexDataID2");

            entity.Property(e => e.OpexLccOpexDataId3).HasColumnName("OpexLccOpexDataID3");

            entity.Property(e => e.OpexLccPrice).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.OpexLccQuantity).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.OpexLccUnits).HasColumnType("decimal(18, 2)");
        });

        modelBuilder.Entity<LccOutdated>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("LccOutdated");

            entity.Property(e => e.LccDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.LccId).HasColumnName("LccID");

            entity.Property(e => e.LccRamsDiagramId).HasColumnName("LccRamsDiagramID");

            entity.Property(e => e.LccRamsId).HasColumnName("LccRamsID");

            entity.Property(e => e.LccdateCalculated)
                .HasColumnName("LCCDateCalculated")
                .HasColumnType("smalldatetime");

            entity.Property(e => e.MrbLastModified).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDiagramId).HasColumnName("RamsDiagramID");

            entity.Property(e => e.RamsLastModified).HasColumnType("smalldatetime");

            entity.Property(e => e.TaskDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.TskMrbId).HasColumnName("TskMrbID");
        });

        modelBuilder.Entity<LccOutdatedOnRisk>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("LccOutdatedOnRisk");

            entity.Property(e => e.LccId).HasColumnName("LccID");

            entity.Property(e => e.LccdateCalculated)
                .HasColumnName("LCCDateCalculated")
                .HasColumnType("smalldatetime");

            entity.Property(e => e.MrbLastModified).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<LccRamsCalc>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("LccRamsCalc");

            entity.Property(e => e.FmecaMatrix).IsUnicode(false);

            entity.Property(e => e.RamsBitmap).HasColumnType("image");

            entity.Property(e => e.RamsCircuitDepCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsClassDc)
                .HasColumnName("RamsClassDC")
                .HasMaxLength(6)
                .IsUnicode(false);

            entity.Property(e => e.RamsDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.RamsDcd).HasColumnName("RamsDCd");

            entity.Property(e => e.RamsDescr).IsUnicode(false);

            entity.Property(e => e.RamsDiagramId).HasColumnName("RamsDiagramID");

            entity.Property(e => e.RamsDiagramRefId).HasColumnName("RamsDiagramRefID");

            entity.Property(e => e.RamsFailCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsFunctionalDemand).IsUnicode(false);

            entity.Property(e => e.RamsId).HasColumnName("RamsID");

            entity.Property(e => e.RamsInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsLccusePfd).HasColumnName("RamsLCCUsePFD");

            entity.Property(e => e.RamsModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.RamsMtbffunct).HasColumnName("RamsMTBFFunct");

            entity.Property(e => e.RamsMtbftechn).HasColumnName("RamsMTBFTechn");

            entity.Property(e => e.RamsMttr).HasColumnName("RamsMTTR");

            entity.Property(e => e.RamsName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsObjectId).HasColumnName("RamsObjectID");

            entity.Property(e => e.RamsPfd).HasColumnName("RamsPFD");

            entity.Property(e => e.RamsPreventiveCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsRemark).IsUnicode(false);

            entity.Property(e => e.RamsRiskId).HasColumnName("RamsRiskID");

            entity.Property(e => e.RamsRiskObjectId).HasColumnName("RamsRiskObjectID");

            entity.Property(e => e.RamsSiId).HasColumnName("RamsSiID");

            entity.Property(e => e.RamsTechnCorrCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsTotalCost).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.RamsWantLcc).HasColumnName("RamsWantLCC");

            entity.Property(e => e.RamsXposition).HasColumnName("RamsXPosition");

            entity.Property(e => e.RamsYposition).HasColumnName("RamsYPosition");

            entity.Property(e => e.ValueObject).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.ValueRiskObject).HasColumnType("decimal(18, 4)");
        });

        modelBuilder.Entity<LccSiCalc>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("LccSiCalc");

            entity.Property(e => e.FailMode)
                .IsRequired()
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.FailRateId).HasColumnName("FailRateID");

            entity.Property(e => e.FmecaMatrix).IsUnicode(false);

            entity.Property(e => e.MrbActionCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbCapCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbCustomAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbCustomBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbDirectCostAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbDirectCostBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbDownTimeAfter).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.MrbDownTimeBefore).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.MrbEffectAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbEffectBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbFailureCategorie1)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.MrbFailureCategorie2)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.MrbFmecaSelect).IsUnicode(false);

            entity.Property(e => e.MrbId).HasColumnName("MrbID");

            entity.Property(e => e.MrbMtbfAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbMtbfBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.MrbOptimalCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbRiskAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbRiskBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbSpareCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbSpareManageCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PckSiSiId).HasColumnName("PckSiSiID");
        });

        modelBuilder.Entity<Lookup>(entity =>
        {
            entity.HasComment(
                "Master data table that stores domains, that are used throughout the program. Domains are grouped by have the same name for field LookupFilter. A set of values can be created for each domain. Not editable by user. ");

            entity.HasIndex(e => new {e.LookupFilter, e.LookupValue})
                .HasName("IX_Lookup")
                .IsUnique();

            entity.Property(e => e.LookupId)
                .HasColumnName("LookupID")
                .HasComment("Unique ID (PK for Lookup)");

            entity.Property(e => e.LookupFilter)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Domain name, each unique name defines a different lookup category");

            entity.Property(e => e.LookupLongDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Long description");

            entity.Property(e => e.LookupShortDescription)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Short description");

            entity.Property(e => e.LookupValue)
                .HasComment(
                    "Value that distinguishes lookup domain values, must be unique within the same category");
        });

        modelBuilder.Entity<LookupExecutor>(entity =>
        {
            entity.HasKey(e => e.ExecutorId)
                .HasName("PK_LookupTaskExecutor");

            entity.HasComment(
                "Master data table that contains all currently defined task executors. A task executor is the person or department that handles execution of a task. ");

            entity.Property(e => e.ExecutorId)
                .HasColumnName("ExecutorID")
                .HasComment("Unique ID (PK for LookupExecutor)");

            entity.Property(e => e.ExecutorDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Description of the task executor (Which clarifies the name where needed)");

            entity.Property(e => e.ExecutorName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the task executor");
        });

        modelBuilder.Entity<LookupFailCat>(entity =>
        {
            entity.HasKey(e => e.FailCatId)
                .HasName("PK_LookupFailCat");

            entity.HasComment(
                "Master data table that contains all defined failure categories. Each failure category defines a different failure behavior. User is allowed to change failure category names and descriptions.");

            entity.HasIndex(e => e.FailCatName)
                .HasName("IX_LookupFailCat")
                .IsUnique();

            entity.Property(e => e.FailCatId)
                .HasColumnName("FailCatID")
                .HasComment("Unique ID (PK for LookupFailCat)");

            entity.Property(e => e.FailCatDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Failure category description");

            entity.Property(e => e.FailCatGroup)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("The group the failure category belongs to (Group domain is defined in table Lookup)");

            entity.Property(e => e.FailCatName)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Failure category name");
        });

        modelBuilder.Entity<LookupFailMode>(entity =>
        {
            entity.HasKey(e => e.FailId);

            entity.HasComment(
                "Master data table that defines failure modes. A failure mode describes a possible cause for the failure, if the failure shows itself, and what the frequency of the failure is. ");

            entity.Property(e => e.FailId)
                .HasColumnName("FailID")
                .HasComment("Unique ID (PK for LookupFailMode)");

            entity.Property(e => e.FailDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.FailDescription)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasComment("Description of the failure mode");

            entity.Property(e => e.FailDistributionId)
                .HasColumnName("FailDistributionID")
                .HasComment(
                    "Distribution type determines what method of calculation will be used to calculate the costs associated with each failure mode. Distribution type values are defined in the Lookup table.");

            entity.Property(e => e.FailIntervalUnit)
                .HasComment("Defines the position in the Weibull curve (when combined with Weibull location)");

            entity.Property(e => e.FailMode)
                .IsRequired()
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasComment("Name of the failure mode");

            entity.Property(e => e.FailModifiedBy)
                .IsUnicode(false)
                .HasComment("User that made the last modification to this record");

            entity.Property(e => e.FailRateId)
                .HasColumnName("FailRateID")
                .HasDefaultValueSql("((3))")
                .HasComment(
                    "Failure rate. This is the frequency at which a system or component fails. Possible values are defined in Lookup. Defaults to constant.");

            entity.Property(e => e.FailRiskTypeId)
                .HasColumnName("FailRiskTypeID")
                .HasComment(
                    "Describes if the failure shows itself, or is invisible. Possible values are defined in Lookup.");

            entity.Property(e => e.FailShape)
                .HasColumnType("decimal(10, 2)")
                .HasComment("Fail shape defines the slope of the current location on the Weibull curve");

            entity.Property(e => e.FailWeibullLocation)
                .HasColumnType("decimal(10, 2)")
                .HasComment("Defines the location on the Weibull curve (starting point)");
        });

        modelBuilder.Entity<LookupGridColumn>(entity =>
        {
            entity.HasKey(e => new {e.ControlName, e.FieldName, e.ColumnName})
                .HasName("PK_LookupGridColumn_ID");

            entity.Property(e => e.ControlName).HasMaxLength(200);

            entity.Property(e => e.FieldName).HasMaxLength(200);

            entity.Property(e => e.ColumnName).HasMaxLength(200);

            entity.Property(e => e.ColumnHeader).HasMaxLength(200);
        });

        modelBuilder.Entity<LookupInflationGroup>(entity =>
        {
            entity.HasKey(e => e.InflId)
                .HasName("PK_InflationGroups");

            entity.HasComment(
                "Master data table that contains inflation groups. The inflation groups allow us to connect a specific inflation percentage to a variety of things. ");

            entity.Property(e => e.InflId)
                .HasColumnName("InflID")
                .HasComment("Unique ID (PK of LookupInflationGroup)");

            entity.Property(e => e.InflDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.InflModifiedBy)
                .IsUnicode(false)
                .HasComment("Last modification of this record was made by this user");

            entity.Property(e => e.InflName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("Inflation group name. Describes what the inflation percentage belongs to.");

            entity.Property(e => e.InflPercentage)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Inflation percentage for this specific item.");
        });

        modelBuilder.Entity<LookupInitiator>(entity =>
        {
            entity.HasKey(e => e.InitiatorId)
                .HasName("PK_LookupTaskInitiator");

            entity.HasComment(
                "Master data table that defines who (or what) will initiate a certain action. Is used for clusters and preventive actions. Editable by user.");

            entity.Property(e => e.InitiatorId)
                .HasColumnName("InitiatorID")
                .HasComment("Unique ID (PK of LookupInitiator)");

            entity.Property(e => e.InitiatorDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Description of the initiator");

            entity.Property(e => e.InitiatorName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the initiator");
        });

        modelBuilder.Entity<LookupIntervalUnit>(entity =>
        {
            entity.HasKey(e => e.IntUnitId)
                .HasName("PK_LookupTaskIntervalUnit");

            entity.HasComment(
                "Master data table that holds values that are used in interval calculations. Editable by user.");

            entity.Property(e => e.IntUnitId)
                .HasColumnName("IntUnitID")
                .HasComment("Unique ID (PK of LookupIntervalUnit)");

            entity.Property(e => e.IntUnitCalculationKey)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Number of times an interval unit occurs in a year");

            entity.Property(e => e.IntUnitDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.IntUnitDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Description of the interval unit");

            entity.Property(e => e.IntUnitModifiedBy)
                .IsUnicode(false)
                .HasComment("User that made the last modification to this record");

            entity.Property(e => e.IntUnitName)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Name of the interval unit");

            entity.Property(e => e.IntUnitShortKey)
                .IsRequired()
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasComment("Short code for the interval unit (usually consists of the first letter of the name)");
        });

        modelBuilder.Entity<LookupMxPolicy>(entity =>
        {
            entity.HasKey(e => e.PolId);

            entity.HasComment(
                "Master data table that defines different maintenance policies. A maintenance policy can be described as the reason why maintenance should occur. (?)");

            entity.Property(e => e.PolId)
                .HasColumnName("PolID")
                .HasComment(" (PK of LookupMxPolicy)");

            entity.Property(e => e.PolDescription)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("Provides a more detailed description of the maintenance policy");

            entity.Property(e => e.PolName)
                .HasMaxLength(5)
                .IsUnicode(false)
                .HasComment("Name that describes the maintenance policy");
        });

        modelBuilder.Entity<LookupSettings>(entity =>
        {
            entity.HasKey(e => e.AmsProperty);

            entity.HasComment(
                "Master data table that defines settings for a variety of things within the Amprover software. (like grid column settings and program constants) Not editable by user. ");

            entity.Property(e => e.AmsProperty)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Unique ID and the name of the setting. (PK of LookupSettings)");

            entity.Property(e => e.AmsDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.AmsDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.AmsDecimalValue)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Decimal value of the setting");

            entity.Property(e => e.AmsInitiatedBy)
                .IsUnicode(false)
                .HasComment("Created by user");

            entity.Property(e => e.AmsIntValue).HasComment("Integer value of the setting");

            entity.Property(e => e.AmsModifiable)
                .HasComment("Boolean value that states if the setting is modifiable");

            entity.Property(e => e.AmsModifiedBy)
                .IsUnicode(false)
                .HasComment("User that made the last modification to this record");

            entity.Property(e => e.AmsTextValue)
                .IsUnicode(false)
                .HasComment(
                    "The text value containing the values of the LookupSetting. Can be either null, a pipe ('|') delimited string, or XML. ");
        });

        modelBuilder.Entity<LookupUserDefined>(entity =>
        {
            entity.HasKey(e => e.UserDefinedId);

            entity.HasComment("Master data table that stores user defined domains. ");

            entity.HasIndex(e => new {e.UserDefinedFilter, e.UserDefinedValue})
                .HasName("IX_LookupUserDefined")
                .IsUnique();

            entity.Property(e => e.UserDefinedId)
                .HasColumnName("UserDefinedID")
                .HasComment("Unique ID (PK of LookupUserDefined)");

            entity.Property(e => e.UserDefinedDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.UserDefinedFilter)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment(
                    "Filter that distinguishes user defined lookup categories (items of the same domain have the same name in this column)");

            entity.Property(e => e.UserDefinedLongDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Long description");

            entity.Property(e => e.UserDefinedModifiedBy)
                .IsUnicode(false)
                .HasComment("User that made the last modification to this record");

            entity.Property(e => e.UserDefinedShortDescription)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Short description");

            entity.Property(e => e.UserDefinedValue).HasComment("Value for the user defined domain item");
        });

        modelBuilder.Entity<MrbTaskCount>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("MrbTaskCount");

            entity.Property(e => e.TskMrbId).HasColumnName("TskMrbID");
        });

        modelBuilder.Entity<MrbTree>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("MrbTree");

            entity.Property(e => e.ChildObject1Id).HasColumnName("ChildObject1ID");

            entity.Property(e => e.ChildObject1Name)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ChildObject2Id).HasColumnName("ChildObject2ID");

            entity.Property(e => e.ChildObject2Name)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ChildObject3Id).HasColumnName("ChildObject3ID");

            entity.Property(e => e.ChildObject3Name)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ChildObject4Id).HasColumnName("ChildObject4ID");

            entity.Property(e => e.ChildObject4Name)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ChildObjectId).HasColumnName("ChildObjectID");

            entity.Property(e => e.ChildObjectName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.MrbId).HasColumnName("MrbID");

            entity.Property(e => e.MrbName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RiskObjId).HasColumnName("RiskObjID");

            entity.Property(e => e.RiskObjName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ScenarioId).HasColumnName("ScenarioID");

            entity.Property(e => e.ScenarioName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<NumberOfSiItems>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("NumberOfSiItems");

            entity.Property(e => e.UserDefinedShortDescription)
                .HasMaxLength(20)
                .IsUnicode(false);
        });

        modelBuilder.Entity<OpexLccDetails>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("OpexLccDetails");

            entity.Property(e => e.OpexLccDetOpexLccId).HasColumnName("OpexLccDetOpexLccID");

            entity.Property(e => e.OpexLccId).HasColumnName("OpexLccID");
        });

        modelBuilder.Entity<PickSiFromCommonTask>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("PickSiFromCommonTask");

            entity.Property(e => e.MrbName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.SiId).HasColumnName("SiID");

            entity.Property(e => e.SiName)
                .IsRequired()
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.SiType)
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.TskId).HasColumnName("TskID");

            entity.Property(e => e.TskMrbId).HasColumnName("TskMrbID");
        });

        modelBuilder.Entity<PrioTaskCollection>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("PrioTaskCollection");

            entity.Property(e => e.CltpClusterCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.CltpClusterCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.CltpClusterId).HasColumnName("CltpClusterID");

            entity.Property(e => e.CltpDateExecuted).HasColumnType("datetime");

            entity.Property(e => e.CltpDateGenerated).HasColumnType("datetime");

            entity.Property(e => e.CltpDownTime).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.CltpDuration).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.CltpExecutionDate).HasColumnType("datetime");

            entity.Property(e => e.CltpId).HasColumnName("CltpID");

            entity.Property(e => e.CltpObjectId).HasColumnName("CltpObjectID");

            entity.Property(e => e.CltpReferenceId)
                .HasColumnName("CltpReferenceID")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.CltpRemarks).IsUnicode(false);

            entity.Property(e => e.CltpRiskId).HasColumnName("CltpRiskID");

            entity.Property(e => e.CltpSiId).HasColumnName("CltpSiID");

            entity.Property(e => e.CltpSiUnits).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.CltpTaskId).HasColumnName("CltpTaskID");

            entity.Property(e => e.CltpToolCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.IntUnitCalculationKey).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.MrbCustomAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbCustomBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbEffectAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbEffectBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbMtbfAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbMtbfBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbRiskAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbRiskBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.SiName)
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.TskCommonActionId).HasColumnName("TskCommonActionID");

            entity.Property(e => e.TskInterval).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskName)
                .HasMaxLength(60)
                .IsUnicode(false);

            entity.Property(e => e.TskType)
                .HasMaxLength(20)
                .IsUnicode(false);
        });

        modelBuilder.Entity<PriorityGridData>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("PriorityGridData");

            entity.Property(e => e.PrioBudAccepted).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudBudget).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudBudgetCostYear1).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudBudgetCostYear2).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudBudgetCostYear3).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudBudgetCostYear4).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudBudgetCostYear5).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudBudgetSumOfParts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudCostSelected).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.PrioBudDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.PrioBudId).HasColumnName("PrioBudID");

            entity.Property(e => e.PrioBudInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.PrioBudModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.PrioBudOriginalId).HasColumnName("PrioBudOriginalID");

            entity.Property(e => e.PrioBudPostponed).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudPriorityId).HasColumnName("PrioBudPriorityID");

            entity.Property(e => e.PrioBudProposed).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudRejected).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioBudRemark).IsUnicode(false);

            entity.Property(e => e.PrioBudStatus)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.PrioId).HasColumnName("PrioID");

            entity.Property(e => e.PrioName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioSiId).HasColumnName("PrioSiID");

            entity.Property(e => e.PrioStatus)
                .HasMaxLength(20)
                .IsUnicode(false);
        });

        modelBuilder.Entity<PriorityTaskCost>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("PriorityTaskCost");

            entity.Property(e => e.PrioTskPriorityId).HasColumnName("PrioTskPriorityID");

            entity.Property(e => e.RiskBudget).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.RiskCost).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.RiskDelta).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.TaskCost).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Year1).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Year2).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Year3).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Year4).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.Year5).HasColumnType("decimal(38, 2)");
        });

        modelBuilder.Entity<PriorityTaskGridData>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("PriorityTaskGridData");

            entity.Property(e => e.FailRateId).HasColumnName("FailRateID");

            entity.Property(e => e.MrbCustomAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbCustomBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbCustomEffectAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbCustomEffectBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbEffectAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbEffectBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbMtbfAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbMtbfBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.MrbRiskAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbRiskBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioPath)
                .HasMaxLength(250)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskBudgetCostYear1).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskBudgetCostYear2).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskBudgetCostYear3).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskBudgetCostYear4).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskBudgetCostYear5).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskClusterCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskClusterPlanId).HasColumnName("PrioTskClusterPlanID");

            entity.Property(e => e.PrioTskCommonTaskId).HasColumnName("PrioTskCommonTaskID");

            entity.Property(e => e.PrioTskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskDateDue).HasColumnType("smalldatetime");

            entity.Property(e => e.PrioTskDateExecuted).HasColumnType("smalldatetime");

            entity.Property(e => e.PrioTskDateGenerated).HasColumnType("smalldatetime");

            entity.Property(e => e.PrioTskDescription)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskDirectCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskDownTime).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskDuration).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskFromReference)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskId).HasColumnName("PrioTskID");

            entity.Property(e => e.PrioTskIntervalPerYear).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.PrioTskNumberOfTimes).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.PrioTskObjectId).HasColumnName("PrioTskObjectID");

            entity.Property(e => e.PrioTskOriginalId).HasColumnName("PrioTskOriginalID");

            entity.Property(e => e.PrioTskPostponePct).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskPriorityCode).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskPriorityId).HasColumnName("PrioTskPriorityID");

            entity.Property(e => e.PrioTskReferenceId)
                .HasColumnName("PrioTskReferenceID")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskRemarks).IsUnicode(false);

            entity.Property(e => e.PrioTskRiskBudget).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskRiskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskRiskDelta).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskRiskFactor).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.PrioTskRiskId).HasColumnName("PrioTskRiskID");

            entity.Property(e => e.PrioTskSiDescription)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PrioTskSiId).HasColumnName("PrioTskSiID");

            entity.Property(e => e.PrioTskSiUnits).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.PrioTskTaskId).HasColumnName("PrioTskTaskID");

            entity.Property(e => e.SiContractEnd).HasColumnType("datetime");

            entity.Property(e => e.SiMtbf).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.SiRiskAfterCustom).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.SiRiskAfterValue).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.SiRiskBeforeCustom).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.SiRiskBeforeValue).HasColumnType("decimal(18, 0)");

            entity.Property(e => e.SiRiskBvId).HasColumnName("SiRiskBvID");

            entity.Property(e => e.SiRiskMtbfAfter).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.SiRiskMtbfBefore).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.SiYear).HasColumnType("datetime");

            entity.Property(e => e.TskType)
                .HasMaxLength(20)
                .IsUnicode(false);
        });

        modelBuilder.Entity<SiRiskCalc>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("SiRiskCalc");

            entity.Property(e => e.BvDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.BvId).HasColumnName("BvID");

            entity.Property(e => e.BvPerEffectSet).IsUnicode(false);

            entity.Property(e => e.BvWeightingModelId).HasColumnName("BvWeightingModelID");

            entity.Property(e => e.MrbDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.MrbFmecaSelect).IsUnicode(false);

            entity.Property(e => e.PckSiId).HasColumnName("PckSiID");

            entity.Property(e => e.PckSiLinkFilterId).HasColumnName("PckSiLinkFilterID");

            entity.Property(e => e.PckSiMrbId).HasColumnName("PckSiMrbID");

            entity.Property(e => e.PckSiSiId).HasColumnName("PckSiSiID");

            entity.Property(e => e.SiMtbf).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.SifTaskId).HasColumnName("SifTaskID");
        });

        modelBuilder.Entity<SiStatisticsRiskQualification>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("SiStatisticsRiskQualification");

            entity.Property(e => e.RiskIndexAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.RiskIndexBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.RiskNumberAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.RiskNumberBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.SiDescription)
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.Property(e => e.SiId).HasColumnName("SiID");

            entity.Property(e => e.SiName)
                .IsRequired()
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.SiStatId).HasColumnName("SiStatID");

            entity.Property(e => e.SiStatScenarioId).HasColumnName("SiStatScenarioID");
        });

        modelBuilder.Entity<SyncRamsMrb>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("SyncRamsMrb");

            entity.Property(e => e.MrbId).HasColumnName("MrbID");

            entity.Property(e => e.MrbMtbfAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbMtbfBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.MrbName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RamsDiagramId).HasColumnName("RamsDiagramID");

            entity.Property(e => e.RamsId).HasColumnName("RamsID");

            entity.Property(e => e.RamsMtbffunct).HasColumnName("RamsMTBFFunct");

            entity.Property(e => e.RamsMtbftechn).HasColumnName("RamsMTBFTechn");
        });

        modelBuilder.Entity<TaskCostOnClusterModified>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("TaskCostOnClusterModified");

            entity.Property(e => e.ClusterTaskCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskCosts).HasColumnType("decimal(38, 2)");
        });

        modelBuilder.Entity<TaskCostOnMrbModified>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("TaskCostOnMrbModified");

            entity.Property(e => e.MrbCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.MrbDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.TskCosts).HasColumnType("decimal(38, 2)");

            entity.Property(e => e.TskDate).HasColumnType("smalldatetime");

            entity.Property(e => e.TskMrbId).HasColumnName("TskMrbID");
        });

        modelBuilder.Entity<TaskEdit>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("TaskEdit");

            entity.Property(e => e.ObjLevel0Name)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ObjLevel2Name)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ObjLevel3Name)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ObjLevel4Name)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RiskObjId).HasColumnName("RiskObjID");

            entity.Property(e => e.RiskObjName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ScenId).HasColumnName("ScenID");

            entity.Property(e => e.ScenName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.TskClusterCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskClusterCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskCommonActionId).HasColumnName("TskCommonActionID");

            entity.Property(e => e.TskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.TskDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.TskDescription).IsUnicode(false);

            entity.Property(e => e.TskDownTime).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskDuration).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskEstCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskEstCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskExecutionDate).HasColumnType("smalldatetime");

            entity.Property(e => e.TskFinishDate).HasColumnType("smalldatetime");

            entity.Property(e => e.TskFmecaEffect).IsUnicode(false);

            entity.Property(e => e.TskFmecaEffectPct).HasColumnType("numeric(18, 2)");

            entity.Property(e => e.TskGeneralDescription).IsUnicode(false);

            entity.Property(e => e.TskId).HasColumnName("TskID");

            entity.Property(e => e.TskInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.TskInterval).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskLcceffect)
                .HasColumnName("TskLCCEffect")
                .IsUnicode(false);

            entity.Property(e => e.TskModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.TskMrbId).HasColumnName("TskMrbID");

            entity.Property(e => e.TskName)
                .IsRequired()
                .HasMaxLength(60)
                .IsUnicode(false);

            entity.Property(e => e.TskNorm).IsUnicode(false);

            entity.Property(e => e.TskOptimalCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskPermit).IsUnicode(false);

            entity.Property(e => e.TskReferenceId).HasColumnName("TskReferenceID");

            entity.Property(e => e.TskRemark).IsUnicode(false);

            entity.Property(e => e.TskResponsible)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.TskType)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.TskUnits).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskWorkInspCost).HasColumnType("decimal(18, 2)");
        });

        modelBuilder.Entity<TaskLookupGrid>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("TaskLookupGrid");

            entity.Property(e => e.ClustName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ExecutorName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.InitiatorName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.IntUnitName)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.MrbName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ObjectName2)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ObjectName3)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ObjectName4)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.PolName)
                .HasMaxLength(5)
                .IsUnicode(false);

            entity.Property(e => e.RiskObjId).HasColumnName("RiskObjID");

            entity.Property(e => e.RiskObjName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.RiskObjScenarioId).HasColumnName("RiskObjScenarioID");

            entity.Property(e => e.TskClusterCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskClusterCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskCommonActionId).HasColumnName("TskCommonActionID");

            entity.Property(e => e.TskCosts).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskDateInitiated).HasColumnType("smalldatetime");

            entity.Property(e => e.TskDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.TskDescription).IsUnicode(false);

            entity.Property(e => e.TskDownTime).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskDuration).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskEstCostPerUnit).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskExecutionDate).HasColumnType("smalldatetime");

            entity.Property(e => e.TskFinishDate).HasColumnType("smalldatetime");

            entity.Property(e => e.TskFmecaEffect).IsUnicode(false);

            entity.Property(e => e.TskFmecaEffectPct).HasColumnType("numeric(18, 2)");

            entity.Property(e => e.TskGeneralDescription).IsUnicode(false);

            entity.Property(e => e.TskId).HasColumnName("TskID");

            entity.Property(e => e.TskInitiatedBy)
                .IsUnicode(false);

            entity.Property(e => e.TskInterval).HasColumnType("decimal(18, 4)");

            entity.Property(e => e.TskLcceffect)
                .HasColumnName("TskLCCEffect")
                .IsUnicode(false);

            entity.Property(e => e.TskModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.TskMrbId).HasColumnName("TskMrbID");

            entity.Property(e => e.TskName)
                .IsRequired()
                .HasMaxLength(60)
                .IsUnicode(false);

            entity.Property(e => e.TskOptimalCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskPermit).IsUnicode(false);

            entity.Property(e => e.TskRemark).IsUnicode(false);

            entity.Property(e => e.TskResponsible)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.TskType)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.TskUnits).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.TskWorkInspCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.WpName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<BvAspectSets>(entity =>
        {
            entity.HasKey(e => e.BvAspSetId)
                .HasName("BvAspectID");

            entity.Property(e => e.BvAspSetId)
                .HasColumnName("BvAspSetID")
                .HasComment("Unique ID");

            entity.Property(e => e.BvAspAspects)
                .IsUnicode(false)
                .HasComment(
                    "Unique aspects stored in XML. Stores the name, weight factor and the bound relevance set of the aspect.");

            entity.Property(e => e.BvAspDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.BvAspModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.BvAspSetName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the aspect set");
        });

        modelBuilder.Entity<BvRelevanceSets>(entity =>
        {
            entity.HasKey(e => e.BvRelSetId);

            entity.Property(e => e.BvRelSetId)
                .HasColumnName("BvRelSetID")
                .HasComment("Unique ID");

            entity.Property(e => e.BvRelDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.BvRelModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.BvRelRelevances)
                .IsUnicode(false)
                .HasComment(
                    "Unique relevances stored in XML. Stores the name and the appreciation of the relevance.");

            entity.Property(e => e.BvRelSetName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the relevance set");
        });

        modelBuilder.Entity<BvSiItems>(entity =>
        {
            entity.HasKey(e => e.BvId);

            entity.HasIndex(e => e.BvSiId)
                .HasName("IX_BvSiItems")
                .IsUnique();

            entity.Property(e => e.BvId).HasColumnName("BvID");

            entity.Property(e => e.BvDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.BvModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.BvPerEffectSet).IsUnicode(false);

            entity.Property(e => e.BvRelevanceSelectSet).IsUnicode(false);

            entity.Property(e => e.BvSiId).HasColumnName("BvSiID");

            entity.Property(e => e.BvWeightingModelId).HasColumnName("BvWeightingModelID");

            entity.HasOne(d => d.BvSi)
                .WithOne(p => p.BvSiItems)
                .HasForeignKey<BvSiItems>(d => d.BvSiId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BvSiItems_Si");

            entity.HasOne(d => d.BvWeightingModel)
                .WithMany(p => p.BvSiItems)
                .HasForeignKey(d => d.BvWeightingModelId)
                .HasConstraintName("FK_BvSiItems_BvWeightingModels");
        });

        modelBuilder.Entity<BvWeightingModels>(entity =>
        {
            entity.HasKey(e => e.BvModelId)
                .HasName("PK_BvWeightingModels");

            entity.Property(e => e.BvModelId)
                .HasColumnName("BvModelID")
                .HasComment("Unique ID");

            entity.Property(e => e.BvModelAspectSetId)
                .HasColumnName("BvModelAspectSetID")
                .HasComment("ID of the aspect set used for this weighting model.");

            entity.Property(e => e.BvModelDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.BvModelEffectWeightSet)
                .IsUnicode(false)
                .HasComment(
                    "Effect weight set stored in XML. Stores the aspect, effect (FMECA) column and the given value of the effect weight set.");

            entity.Property(e => e.BvModelFmecaId)
                .HasColumnName("BvModelFmecaID")
                .HasComment("ID of the FMECA matrix used for this weighting model.");

            entity.Property(e => e.BvModelModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.BvModelName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the weighting model");

            entity.HasOne(d => d.BvModelAspectSet)
                .WithMany(p => p.BvWeightingModels)
                .HasForeignKey(d => d.BvModelAspectSetId)
                .HasConstraintName("FK_BvWeightingModels_BvAspectSets");

            entity.HasOne(d => d.BvModelFmeca)
                .WithMany(p => p.BvWeightingModels)
                .HasForeignKey(d => d.BvModelFmecaId)
                .HasConstraintName("FK_BvWeightingModels_Fmeca");
        });

        modelBuilder.Entity<Cluster>(entity =>
        {
            entity.HasKey(e => e.ClustId);

            entity.HasMany(e => e.Task)
                .WithOne(t => t.TskClusterNavigation)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasMany(e => e.ClusterCost)
                .WithOne(c => c.ClcCluster)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.ClusterTaskPlan)
                .WithOne(t => t.CltpCluster)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.ClusterChildren)
                .WithOne(t => t.ClustPartOfCluster)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasComment(
                "A cluster is a group of related preventive tasks, that can be grouped for entry into a maintenance information system. Most of the values stored in this table can be set from the ClusterControl. Some values get recalculated within SyncClusterData.");

            entity.HasIndex(e => e.ClustPartOf)
                .HasName("IX_ClusterPartOf");

            entity.HasOne(x => x.ClustPartOfCluster)
                .WithMany(x => x.ClusterChildren)
                .HasForeignKey(x => x.ClustPartOf);
            
            entity.HasOne(x => x.RiskObject)
                .WithMany(x => x.Clusters)
                .HasForeignKey(x => x.ClustRiskObjectId);

            entity.HasOne(x => x.Scenario)
                .WithMany(x => x.Clusters)
                .HasForeignKey(x => x.ClustScenarioId);

            entity.Property(e => e.ClustId)
                .HasColumnName("ClustID")
                .HasComment("Unique ID (PK of Cluster)");

            entity.Property(e => e.ClustDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.ClustDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.ClustDescription)
                .IsUnicode(false)
                .HasComment("Description of the cluster");

            entity.Property(e => e.ClustDisciplineCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Costs for disciplines that are essential for execution of the cluster (? what are disciplines in this context?)");

            entity.Property(e => e.ClustDivideDownTime).HasComment(
                "A bit used for the way the downtime is devided. false :The input in the cluster is devided over the tasks. True duration is the sum of the downtime in the tasks");

            entity.Property(e => e.ClustDivideDuration).HasComment(
                "A bit used for the way the duration is devided. false :The input in the cluster is devided over the tasks. True duration is the sum of the duration in the tasks");

            entity.Property(e => e.ClustDownTime)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Downtime needed to complete cluster of tasks (in hours)");

            entity.Property(e => e.ClustDuration)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Total time spent on cluster (in hours)");

            entity.Property(e => e.ClustEnergyCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Estimated energy costs made during execution of the cluster");

            entity.Property(e => e.ClustEstTaskCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Estimated task costs (?)");

            entity.Property(e => e.ClustExecutor).HasComment("ID of the executor (FK to LookupExecutor)");

            entity.Property(e => e.ClustInitiatedBy)
                .IsUnicode(false)
                .HasComment("Username of person that created this cluster");

            entity.Property(e => e.ClustInitiator).HasComment("ID of the initiator (FK to LookupInitiator)");

            entity.Property(e => e.ClustInterruptable).HasComment("Allow the cluster to be paused.");

            entity.Property(e => e.ClustInterval)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Cluster interval, interval at which the cluster will be executed");

            entity.Property(e => e.ClustIntervalUnit).HasComment("Interval unit ID (FK to LookupIntervalUnit)");

            entity.Property(e => e.ClustLevel)
                .HasComment("Describes at what level of the tree the cluster is situated.");

            entity.Property(e => e.ClustLocation)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Unique (technical) identifier of the related location");

            entity.Property(e => e.ClustMaterialCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Costs of materials that are essential for execution of the cluster");

            entity.Property(e => e.ClustModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.ClustName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of cluster");

            entity.Property(e => e.ClustOrgId)
                .HasColumnName("ClustOrgID")
                .HasMaxLength(12)
                .IsUnicode(false)
                .HasComment("Organisation to which the cluster is bound");

            entity.Property(e => e.ClustPartOf)
                .HasComment("Cluster ID of the parent cluster (FK to self, Cluster)");

            entity.Property(e => e.ClustPriority).HasComment("Priority of the cluster");

            entity.Property(e => e.ClustReferenceId)
                .HasColumnName("ClustReferenceID")
                .IsUnicode(false);

            entity.Property(e => e.ClustRemark)
                .IsUnicode(false)
                .HasComment("Remarks allow for some extra information regarding this cluster");

            entity.Property(e => e.ClustResponsible)
                .IsUnicode(false)
                .HasComment("People/departments who are responsible for execution of the cluster");

            entity.Property(e => e.ClustRiskObjectId)
                .HasColumnName("ClustRiskObjectID")
                .HasComment("Risk Object ID the cluster is referring to (FK to RiskObject)");

            entity.Property(e => e.ClustScenarioId)
                .HasColumnName("ClustScenarioID")
                .HasComment("Scenario ID the cluster is a part of (FK to Scenario)");

            entity.Property(e => e.ClustSecondValues)
                .IsUnicode(false)
                .HasComment("?");

            entity.Property(e => e.ClustSequence).HasComment("Sequence of the relationship between clusters.");

            entity.Property(e => e.ClustSharedCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("? Costs that are shared by items that belong to this cluster");

            entity.Property(e => e.ClustShiftEndDate)
                .HasComment("Amount of days that the end date of the cluster is shifted");

            entity.Property(e => e.ClustShiftStartDate)
                .HasComment("Amount of days that the start date of the cluster is shifted");

            entity.Property(e => e.ClustShortKey)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Short key of the cluster (needs to be unique within Cluster)");

            entity.Property(e => e.ClustSiteId)
                .HasColumnName("ClustSiteID")
                .HasMaxLength(12)
                .IsUnicode(false)
                .HasComment("SiteID of the asset");

            entity.Property(e => e.ClustStatus)
                .HasComment(
                    "Cluster status, user can enter any string for status.  (? status domain --> There is no status domain, should there be one ?) ");

            entity.Property(e => e.ClustTaskCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Actual task costs for this cluster");

            entity.Property(e => e.ClustTemplateType)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("The template type of the cluster");

            entity.Property(e => e.ClustToolCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Costs for tools needed for completion of the cluster");

            entity.Property(e => e.ClustTotalCmnCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("? Total costs common to this cluster (?)");

            entity.Property(e => e.ClustWorkpackageId)
                .HasColumnName("ClustWorkpackageID")
                .HasComment("Workpackage ID the cluster is a part of (FK to Workpackage)");

            entity.HasOne(e => e.ClustStatusNavigation)
                .WithMany(l => l.Clusters)
                .HasForeignKey(l => l.ClustStatus);
        });

        modelBuilder.Entity<ClusterCost>(entity =>
        {
            entity.HasKey(e => e.ClcId)
                .HasName("PK_PickClusterCost");

            entity.HasComment("Clustercosts are costs specific to a cluster. They can be defined ");

            entity.Property(e => e.ClcId)
                .HasColumnName("ClcID")
                .HasComment("Unique ID");

            entity.Property(e => e.ClcCalculationType)
                .IsRequired()
                .HasMaxLength(8)
                .IsUnicode(false)
                .HasDefaultValueSql("('PxN')")
                .HasComment("The way the common cost are calculated");

            entity.Property(e => e.ClcClusterId)
                .HasColumnName("ClcClusterID")
                .HasComment("ID of the cluster to which the cluster cost is bound (Cluster)");

            entity.Property(e => e.ClcCommonCostId)
                .HasColumnName("ClcCommonCostID")
                .HasComment("ID of the common cost to which the cluster cost is bound (CommonCost)");

            entity.Property(e => e.ClcCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cost of the common cost");

            entity.Property(e => e.ClcIsCommonTaskCost)
                .HasDefaultValueSql("((0))")
                .HasComment("Boolean to see if the cluster cost are referenced to a task");

            entity.Property(e => e.ClcPrice)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Price of the common cost");

            entity.Property(e => e.ClcPriceIndexYear).HasComment("The year in which the cluster cost are indexed");

            entity.Property(e => e.ClcQuantity)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Quantity of the common cost");

            entity.Property(e => e.ClcRemarks)
                .IsUnicode(false)
                .HasComment("Remarks of the common cost");

            entity.Property(e => e.ClcTaskId)
                .HasColumnName("ClcTaskID")
                .HasComment("ID of the tasks to which the cluster cost is bound (Task)");

            entity.Property(e => e.ClcType)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("The Type of cluster cost");

            entity.Property(e => e.ClcUnits)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Number of units of the common cost");

            entity.HasOne(d => d.ClcCluster)
                .WithMany(p => p.ClusterCost)
                .HasForeignKey(d => d.ClcClusterId)
                .HasConstraintName("FK_ClusterCost_Cluster");

            entity.HasOne(d => d.ClcCommonCost)
                .WithMany(p => p.ClusterCost)
                .HasForeignKey(d => d.ClcCommonCostId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ClusterCost_CommonCost");

            entity.HasOne(d => d.ClcTask)
                .WithMany(p => p.ClusterCost)
                .HasForeignKey(d => d.ClcTaskId)
                .HasConstraintName("FK_ClusterCost_Task")
                .OnDelete(DeleteBehavior.SetNull);
        });

        modelBuilder.Entity<ClusterTaskPlan>(entity =>
        {
            entity.HasKey(e => e.CltpId);

            entity.HasComment(
                @"Cluster task plans contain groups of preventive measures, that can be imported into a maintenance information system. 

The cluster task plans are generated from the clusters. Data in this table is generated using data from tables Cluster, ClusterCost, Task, PriorityTask, Mrb, and RiskObject.");

            entity.Property(e => e.CltpId)
                .HasColumnName("CltpID")
                .HasComment("Unique ID");

            entity.Property(e => e.CltpClusterCostPerUnit)
                .HasColumnType("decimal(18, 2)")
                .HasComment("?");

            entity.Property(e => e.CltpClusterCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cluster cost for the cluster task plan");

            entity.Property(e => e.CltpClusterId)
                .HasColumnName("CltpClusterID")
                .HasComment("Cluster ID to which the cluster task plan is referenced (Cluster)");

            entity.Property(e => e.CltpCommonTaskId)
                .HasColumnName("CltpCommonTaskID")
                .HasComment("Common task ID to which the cluster task plan is referenced (CommonTask)");

            entity.Property(e => e.CltpDateExecuted)
                .HasColumnType("datetime")
                .HasComment("Date when the cluster task plan is executed");

            entity.Property(e => e.CltpDateGenerated)
                .HasColumnType("datetime")
                .HasComment("Date when the cluster task plan is generated");

            entity.Property(e => e.CltpDownTime)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Downtime needed in hours");

            entity.Property(e => e.CltpDuration)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Time spend in hours");

            entity.Property(e => e.CltpExecuteStatus).HasComment("Execute status of the cluster task plan");

            entity.Property(e => e.CltpExecutionDate)
                .HasColumnType("datetime")
                .HasComment("Date when the cluster task plan must be executed");

            entity.Property(e => e.CltpInterruptable).HasComment("Allow the task plan to be paused.");

            entity.Property(e => e.CltpObjectId)
                .HasColumnName("CltpObjectID")
                .HasComment("Object ID to which the cluster task plan is referenced (Object)");

            entity.Property(e => e.CltpPriority).HasComment("Priority of the cluster task plan");

            entity.Property(e => e.CltpQualityScore).HasComment("?");

            entity.Property(e => e.CltpReferenceId)
                .HasColumnName("CltpReferenceID")
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("?");

            entity.Property(e => e.CltpRemarks)
                .IsUnicode(false)
                .HasComment("Remarks of the cluster task plan");

            entity.Property(e => e.CltpRiskId)
                .HasColumnName("CltpRiskID")
                .HasComment("Risk ID to which the cluster task plan is referenced (MRB)");

            entity.Property(e => e.CltpSequence)
                .HasComment("Sequence of the relationship between cluster task plans.");

            entity.Property(e => e.CltpShiftEndDate)
                .HasComment("Amount of days that the end date of the cluster task plan is shifted");

            entity.Property(e => e.CltpShiftStartDate)
                .HasComment("Amount of days that the start date of the cluster task plan is shifted");

            entity.Property(e => e.CltpSiId)
                .HasColumnName("CltpSiID")
                .HasComment("Significant item ID to which the cluster task plan is referenced (Cluster)");

            entity.Property(e => e.CltpSiUnits)
                .HasColumnType("decimal(18, 2)")
                .HasComment("?");

            entity.Property(e => e.CltpSlack).HasComment("Interval of the slack for the cluster task plan");

            entity.Property(e => e.CltpSlackIntervalType)
                .HasComment("Interval unit ID of the slack for the cluster task plan (LookupIntervalUnit)");

            entity.Property(e => e.CltpTaskId)
                .HasColumnName("CltpTaskID")
                .HasComment("Task ID to which the cluster task plan is referenced (Task)");

            entity.Property(e => e.CltpToolCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Tool cost for the cluster task plan");

            entity.Property(e => e.CltpUseLastDateExecuted)
                .HasComment("Use the last date executed as executing date for the cluster task plan");

            entity.HasOne(d => d.CltpCluster)
                .WithMany(p => p.ClusterTaskPlan)
                .HasForeignKey(d => d.CltpClusterId)
                .HasConstraintName("FK_ClusterTaskPlan_Cluster");

            entity.HasOne(d => d.CltpRisk)
                .WithMany(p => p.ClusterTaskPlan)
                .HasForeignKey(d => d.CltpRiskId)
                .HasConstraintName("FK_ClusterTaskPlan_MRB");

            entity.HasOne(d => d.CltpSi)
                .WithMany(p => p.ClusterTaskPlan)
                .HasForeignKey(d => d.CltpSiId)
                .HasConstraintName("FK_ClusterTaskPlan_Si");

            entity.HasOne(d => d.CltpTask)
                .WithMany(p => p.ClusterTaskPlan)
                .HasForeignKey(d => d.CltpTaskId)
                .HasConstraintName("FK_ClusterTaskPlan_Task")
                .OnDelete(DeleteBehavior.SetNull);
        });

        modelBuilder.Entity<CommonCost>(entity =>
        {
            entity.HasKey(e => e.CmnCostId)
                .HasName("PK_ClusterCosts");

            entity.HasComment(
                "Master data table. Common costs define recurring costs, that are used for cost calculations. They define different costs for different types of items. (like materials, tools or use of specific disciplines.) Common costs can be added to common actions and clusters.");

            entity.Property(e => e.CmnCostId)
                .HasColumnName("CmnCostID")
                .HasComment("Unique ID (PK of CommonCost)");

            entity.Property(e => e.CmnCostCalculationType)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasComment("The way the common cost must be used in calculations (P, PxN, PxNxU)");

            entity.Property(e => e.CmnCostDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.CmnCostDescription)
                .IsRequired()
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasComment(
                    "Descriptive name of the common cost. This value is used for selecting the common costs in the screens that use them.");

            entity.Property(e => e.CmnCostExtraCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Extra cost for the common cost (currently not in use)");

            entity.Property(e => e.CmnCostModifiedBy)
                .IsUnicode(false)
                .HasComment("User that made the last modification to this record");

            entity.Property(e => e.CmnCostNumber)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Quantity number, used in calculation of costs. (when calculation type is not set to price-only)");

            entity.Property(e => e.CmnCostOrgId)
                .HasColumnName("CmnCostOrgID")
                .HasMaxLength(12)
                .IsUnicode(false)
                .HasComment("Organisation to which the common cost is bound");

            entity.Property(e => e.CmnCostPrice)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Price of one item. Is always used for price calculation.");

            entity.Property(e => e.CmnCostPriceGroup)
                .HasComment("Inflation group ID to which the common cost are bound. (FK to LookupUserDefined)");

            entity.Property(e => e.CmnCostPriceIndexYear)
                .HasComment("The year in which the common cost were indexed");

            entity.Property(e => e.CmnCostReferenceCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Unique reference code or ID (customer)");

            entity.Property(e => e.CmnCostRemarks)
                .IsUnicode(false)
                .HasComment("Remarks field for common costs. ");

            entity.Property(e => e.CmnCostRotating).HasComment("Boolean to set the common cost as a rotating item");

            entity.Property(e => e.CmnCostShortKey)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment(
                    "Short key of the common cost. Usually takes the first character of the CmnCostType. (?)");

            entity.Property(e => e.CmnCostSpare).HasComment("Boolean to set the common cost as a spare part");

            entity.Property(e => e.CmnCostStatus).HasComment("Status of the common cost");

            entity.Property(e => e.CmnCostStatusDate)
                .HasColumnType("datetime")
                .HasComment("Date the status was last modified.");

            entity.Property(e => e.CmnCostSubSubType)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Extra added sub sub type as a search field");

            entity.Property(e => e.CmnCostSubType)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Extra added sub type as a search field");

            entity.Property(e => e.CmnCostType)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasDefaultValueSql("('PxN')")
                .HasComment(
                    "Common cost type domain. Specifies what caused the costs. (tools, disciplines, energy, etc.)");

            entity.Property(e => e.CmnCostUnitType)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasComment("The unit type of the common cost. Not currently used by AMprover software. (?)");

            entity.Property(e => e.CmnCostUnits)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Number of units, will be used in cost calculation (when calculation type is PxNxU)");

            entity.Property(e => e.CmnCostVendorCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Unique reference code or ID of the vendor");

            entity.HasOne(d => d.CmnCostPriceGroupNavigation)
                .WithMany(p => p.CommonCost)
                .HasForeignKey(d => d.CmnCostPriceGroup)
                .HasConstraintName("FK_CommonCost_LookupInflationGroup");
        });

        modelBuilder.Entity<CommonTask>(entity =>
        {
            entity.HasKey(e => e.CmnTaskId);

            entity.HasComment(
                "Master data table. A common task is a task that has to be executed after a defined interval, each time the interval passes. Also named common action.");

            entity.Property(e => e.CmnTaskId)
                .HasColumnName("CmnTaskID")
                .HasComment("Unique ID (PK of CommonTask)");

            entity.Property(e => e.CmnTaskCostModifiable)
                .HasDefaultValueSql("((0))")
                .HasComment("Boolean that enables modification of the common task costs during risk analysis");

            entity.Property(e => e.CmnTaskCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Cost of common task execution. (Cost per unit?) Is named action costs in the AMprover software.");

            entity.Property(e => e.CmnTaskDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.CmnTaskDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.CmnTaskDescription)
                .IsUnicode(false)
                .HasComment("Description for the common task");

            entity.Property(e => e.CmnTaskDownTime)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Downtime needed to complete the task (in hours)");

            entity.Property(e => e.CmnTaskDuration)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Time spent on task. Does not seem to be set from the AMprover software. (?)");

            entity.Property(e => e.CmnTaskExecutor)
                .HasComment("Executor ID of the common task (FK to LookupExecutor)");

            entity.Property(e => e.CmnTaskExecutorModifiable)
                .HasDefaultValueSql("((0))")
                .HasComment("Boolean that enables modification of the common task executor during risk analysis");

            entity.Property(e => e.CmnTaskFieldRights)
                .IsUnicode(false)
                .HasComment("? Is not set by the AMprover software");

            entity.Property(e => e.CmnTaskGeneralDescription)
                .IsUnicode(false)
                .HasComment(
                    "General description for the common task. Does not seem to be filled by the AMprover software. (?)");

            entity.Property(e => e.CmnTaskInitiatedBy)
                .IsUnicode(false)
                .HasComment("Username of person that created this cluster");

            entity.Property(e => e.CmnTaskInitiator)
                .HasComment("Initiator ID of the common task (FK to LookupInitiator)");

            entity.Property(e => e.CmnTaskInitiatorModifiable)
                .HasDefaultValueSql("((0))")
                .HasComment("Boolean that enables modification of the common task initiator during risk analysis");

            entity.Property(e => e.CmnTaskInterval)
                .HasColumnType("decimal(18, 4)")
                .HasDefaultValueSql("((1))")
                .HasComment(
                    "Interval of the common task. The common task needs to be executed each time this interval passes. ");

            entity.Property(e => e.CmnTaskIntervalModifiable)
                .HasDefaultValueSql("((0))")
                .HasComment("Boolean that enables modification of the interval during risk analysis");

            entity.Property(e => e.CmnTaskIntervalUnit)
                .HasComment("ID of the the interval unit of the common task (FK to LookupIntervalUnit)");

            entity.Property(e => e.CmnTaskMasterId)
                .HasColumnName("CmnTaskMasterID")
                .HasComment(
                    "ID of the master common task (FK to CommonTask) Does not seem to be set by the AMprover software (?)");

            entity.Property(e => e.CmnTaskModifiedBy)
                .IsUnicode(false)
                .HasComment("User that made the last modification to this record");

            entity.Property(e => e.CmnTaskMxPolicy)
                .HasComment("Maintenance policy ID of the common task (FK to LookupMxPolicy)");

            entity.Property(e => e.CmnTaskName)
                .IsRequired()
                .HasMaxLength(60)
                .IsUnicode(false)
                .HasComment("The name of the common task");

            entity.Property(e => e.CmnTaskPermit)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength()
                .HasComment("Permit that is needed for the common task. Never set in the AMprover software.");

            entity.Property(e => e.CmnTaskPriorityCode)
                .HasComment("Priority code of common task. Is not set by the AMprover software (?)");

            entity.Property(e => e.CmnTaskReferenceId)
                .HasColumnName("CmnTaskReferenceID")
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment(
                    "Reference ID of the common task. Not bound to anything, user can enter any string. (?)");

            entity.Property(e => e.CmnTaskRemark)
                .IsUnicode(false)
                .HasComment("Remarks of the common task. Does not seem to be filled by the AMprover software. (?)");

            entity.Property(e => e.CmnTaskResponsible)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment(
                    "Person or department that is responsible for executing the common task (never set in the AMprover software)");

            entity.Property(e => e.CmnTaskSiCategory).HasComment(
                "The si category value to which the common task is bound (FK to LookupUserDefined, UserdefinedFilter is SICategory)");

            entity.Property(e => e.CmnTaskSortOrder).HasComment("Custom sequence to order the common tasks");

            entity.Property(e => e.CmnTaskType)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment(
                    "Type of common task (task, procedure etc.) Type domain is defined in Lookup table, Lookupfilter value is MeasureType. This is called Action type in the AMprover software.");

            entity.Property(e => e.CmnTaskUnitType).HasComment(
                "The unit type value to which the cost of the common task are bound (FK to LookupUserDefined, FilterType UnitTypes)");

            entity.Property(e => e.CmnTaskValidFromYear).HasComment(
                "The starting year of the common, when it becomes valid. Does not seem to be set from the AMprover software. (?)");

            entity.Property(e => e.CmnTaskValidUntilYear).HasComment(
                "The year when the common task is no longer valid, and will no longer affect cost calculations. Does not seem to be set from the AMprover software. (?)");

            entity.Property(e => e.CmnTaskWorkInspCost)
                .HasColumnType("decimal(18, 0)")
                .HasComment("Inspection cost of the common task. Is not set by the AMprover software. (?)");

            entity.Property(e => e.CmnTaskWorkPackage)
                .HasComment("ID of the work package of the common task (FK to Workpackage)");

            entity.Property(e => e.CmnTaskWorkPackageModifiable)
                .HasDefaultValueSql("((0))")
                .HasComment("Boolean that enables modification of the work package during risk analysis");

            entity.HasOne(d => d.CmnTaskExecutorNavigation)
                .WithMany(p => p.CommonTask)
                .HasForeignKey(d => d.CmnTaskExecutor)
                .HasConstraintName("FK_CommonTask_LookupExecutor");

            entity.HasOne(d => d.CmnTaskInitiatorNavigation)
                .WithMany(p => p.CommonTask)
                .HasForeignKey(d => d.CmnTaskInitiator)
                .HasConstraintName("FK_CommonTask_LookupInitiator");

            entity.HasOne(d => d.CmnTaskIntervalUnitNavigation)
                .WithMany(p => p.CommonTask)
                .HasForeignKey(d => d.CmnTaskIntervalUnit)
                .HasConstraintName("FK_CommonTask_LookupIntervalUnit");

            entity.HasOne(d => d.CmnTaskMxPolicyNavigation)
                .WithMany(p => p.CommonTask)
                .HasForeignKey(d => d.CmnTaskMxPolicy)
                .HasConstraintName("FK_CommonTask_LookupMxPolicy");

            entity.HasOne(d => d.CmnTaskWorkPackageNavigation)
                .WithMany(p => p.CommonTask)
                .HasForeignKey(d => d.CmnTaskWorkPackage)
                .HasConstraintName("FK_CommonTask_Workpackage");
        });

        modelBuilder.Entity<CommonTaskCost>(entity =>
        {
            entity.HasKey(e => e.CtcId);

            entity.HasComment(
                "Master data table. Contains costs related to common tasks. Defined within master data, as part of a common action. (?)");

            entity.Property(e => e.CtcId)
                .HasColumnName("CtcID")
                .HasComment("Unique ID (PK of CommonTaskCost)");

            entity.Property(e => e.CtcCalculationType)
                .IsRequired()
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasDefaultValueSql("('PxN')")
                .HasComment("The way the common task cost must be calculated (P, PxN, PxNxU)");

            entity.Property(e => e.CtcCommonCostId)
                .HasColumnName("CtcCommonCostID")
                .HasComment("ID of the common cost the common task cost is bound to (FK to CommonCost)");

            entity.Property(e => e.CtcCommonTaskId)
                .HasColumnName("CtcCommonTaskID")
                .HasComment(
                    "Binds costs to a specific task. Currently selected task is added automatically when user adds a new common cost to an action. (FK to CommonTask) ");

            entity.Property(e => e.CtcCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Calculated cost of the common task cost (determined by calculation type, and stored values for Price, Quantity and Units)");

            entity.Property(e => e.CtcPrice)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Price of the common task cost (price of a single unit)");

            entity.Property(e => e.CtcPriceIndexYear)
                .HasComment("The year in which the common task cost are indexed");

            entity.Property(e => e.CtcQuantity)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Quantity of common cost items needed for this common task cost");

            entity.Property(e => e.CtcUnits)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Number of units needed for this common task cost");

            entity.HasOne(d => d.CtcCommonCost)
                .WithMany(p => p.CommonTaskCost)
                .HasForeignKey(d => d.CtcCommonCostId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CommonTaskCost_CommonCost");

            entity.HasOne(d => d.CtcCommonTask)
                .WithMany(p => p.CommonTaskCost)
                .HasForeignKey(d => d.CtcCommonTaskId)
                .HasConstraintName("FK_CommonTaskCost_CommonTask");
        });

        modelBuilder.Entity<Company>(entity =>
        {
            entity.HasKey(e => e.CompanyId);

            entity.HasComment("Would contain full contact info for companies. Not used by AMprover software. (!)");

            entity.Property(e => e.CompanyId)
                .HasColumnName("CompanyID")
                .HasComment("Unique ID");

            entity.Property(e => e.CompanyAdres)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Address of the company");

            entity.Property(e => e.CompanyContact1)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Contact field of the company");

            entity.Property(e => e.CompanyContact2)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Contact field of the company");

            entity.Property(e => e.CompanyCountry)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("Country of the company");

            entity.Property(e => e.CompanyDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Description of the company");

            entity.Property(e => e.CompanyEmail)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Email address of the company");

            entity.Property(e => e.CompanyFax)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Fax number of the company");

            entity.Property(e => e.CompanyName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("The name of the company");

            entity.Property(e => e.CompanyPhone)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasComment("Phone number of the company");

            entity.Property(e => e.CompanyPlace)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("City of the company");

            entity.Property(e => e.CompanyPostAdres)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Post address of the company");

            entity.Property(e => e.CompanyZipCode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasComment("Zip code of the company");
        });

        modelBuilder.Entity<Department>(entity =>
        {
            entity.HasKey(e => e.DepId);

            entity.HasComment("Defines different departments within a company. Defined from master data");

            entity.Property(e => e.DepId)
                .HasColumnName("DepID")
                .HasComment("Unique ID (PK of Department)");

            entity.Property(e => e.DepDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Full description of the department");

            entity.Property(e => e.DepShortKey)
                .IsRequired()
                .HasMaxLength(5)
                .IsUnicode(false)
                .HasComment("Short description of the department");
        });

        modelBuilder.Entity<DerModified>(entity =>
        {
            entity.HasKey(e => e.DerModId);

            entity.Property(e => e.DerModId).HasColumnName("DerModID");

            entity.Property(e => e.DerModCopiedFrom)
                .HasComment("For auditing reasons it is easier to know what the original object is");

            entity.Property(e => e.DerModDeleted)
                .HasComment(
                    "if this bit is set then the object is will be shown a if it has been deleted but the parent properties will be still be shown");

            entity.Property(e => e.DerModModifications)
                .IsUnicode(false)
                .HasComment("xml string with the modifications");

            entity.Property(e => e.DerModObjectId)
                .HasColumnName("DerModObjectID")
                .HasComment("The ID of the object whereto modifications have been applied");

            entity.Property(e => e.DerModObjectKey)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("not used can be applied for objects that have no unique int key");

            entity.Property(e => e.DerModObjectType)
                .HasComment("0=scenario 1=riskobject 2=risk 3 = task 4=siFilter 5=pickSi ");
        });

        modelBuilder.Entity<Descriptions>(entity =>
        {
            entity.HasKey(e => e.DescId);

            entity.Property(e => e.DescId).HasColumnName("DescID");

            entity.Property(e => e.DescDescription).IsUnicode(false);

            entity.Property(e => e.DescExtraId).HasColumnName("DescExtraID");

            entity.Property(e => e.DescExtraType)
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.DescRiskId).HasColumnName("DescRiskID");

            entity.Property(e => e.DescRiskObjectId).HasColumnName("DescRiskObjectID");

            entity.Property(e => e.DescTaskId).HasColumnName("DescTaskID");
        });

        modelBuilder.Entity<EditLog>(entity =>
        {
            entity.HasKey(e => e.LogId);

            entity.HasComment(
                "Contains the edit logs that are generated when a user makes changes to certain tables. The editlogs contain the columns changed, the old value and the new value. ");

            entity.Property(e => e.LogId)
                .HasColumnName("LogID")
                .HasComment("Unique ID (PK of EditLog)");

            entity.Property(e => e.LogDate)
                .HasColumnType("datetime")
                .HasComment("Date the modification occurred");

            entity.Property(e => e.LogModificationType)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasComment("Modification type (edit, delete etc.)");

            entity.Property(e => e.LogModifications)
                .IsUnicode(false)
                .HasComment("Describes the actual modification (column names, old value and new value)");

            entity.Property(e => e.LogObjectId)
                .HasColumnName("LogObjectID")
                .HasComment("Object ID which was modified (?)");

            entity.Property(e => e.LogObjectType)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("Describes the table that was modified (table name)");

            entity.Property(e => e.LogObjectVarId)
                .HasColumnName("LogObjectVarID")
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("?");

            entity.Property(e => e.LogUserId)
                .HasColumnName("LogUserID")
                .IsUnicode(false)
                .HasComment("Name of the user who performed the modification (not an ID!)");
        });

        modelBuilder.Entity<Filters>(entity =>
        {
            entity.HasKey(e => e.SqlSelId);

            entity.HasComment(
                "Defines filters used throughout the program. (reports, priority box, clusters, life cycle costs). The filters are used to limit the amount of data the user has to go through to find the item they want. The filter can be set up on each of the columns of the related table. Filters are identified by the combination of SqlSelGroup and SqlSubSelGroup. ");

            entity.Property(e => e.SqlSelId)
                .HasColumnName("SqlSelID")
                .HasComment("Unique ID (PK of Filters)");

            entity.Property(e => e.SqlSelGroup)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment(
                    "Main filter group. Describes the AMprover module the filter applies to. User cannot set this.");

            entity.Property(e => e.SqlSelName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Descriptive name of the filter, user can enter any name. ");

            entity.Property(e => e.SqlSelShortKey)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Shortkey of the filter, needs to be unique within the entire Filters. ");

            entity.Property(e => e.SqlSelSubGroup)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment(
                    "Filter sub group. Is set by the code, using hardcoded values which are different for each subgroup of filters. Needs to be unique within the same main group. ");
        });

        modelBuilder.Entity<FiltersSelectionList>(entity =>
        {
            entity.HasKey(e => new {e.SqlSelId, e.Sequence});

            entity.Property(e => e.SqlSelId).HasColumnName("SqlSelID");

            entity.Property(e => e.AndOr)
                .HasMaxLength(3)
                .IsUnicode(false);

            entity.Property(e => e.Criterium)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.FieldName)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.FieldType)
                .IsRequired()
                .HasMaxLength(6)
                .IsUnicode(false);

            entity.Property(e => e.FriendlyName).HasMaxLength(100);

            entity.Property(e => e.Selection).HasMaxLength(200);

            entity.Property(e => e.SortType)
                .HasMaxLength(4)
                .IsUnicode(false);

            entity.HasOne(d => d.SqlSel)
                .WithMany(p => p.FiltersSelectionList)
                .HasForeignKey(d => d.SqlSelId)
                .HasConstraintName("FK_FiltersSelectionList_Filters");
        });

        modelBuilder.Entity<Fmeca>(entity =>
        {
            entity.HasKey(e => e.FmecaId)
                .HasName("PK_tblFMECA_1");

            entity.HasComment(
                "This table defines the rows, columns and cells of risk matrices. The cell contents, colors and values are stored in XML. The created risk matrices can be selected during risk analysis.");

            entity.Property(e => e.FmecaId)
                .HasColumnName("FmecaID")
                .HasComment(" (PK of Fmeca)");

            entity.Property(e => e.FmecaDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Description of the FMECA matrix");

            entity.Property(e => e.FmecaMatrix)
                .IsUnicode(false)
                .HasComment(
                    "Layout of the FMECA matrix, stored in XML. Stores the colors, and effects for each cell of the matrix.");

            entity.Property(e => e.FmecaName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("Name for the FMECA matrix");

            entity.Property(e => e.FmecaShortName)
                .IsRequired()
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasDefaultValueSql("('-')")
                .HasComment("Short description of the FMECA matrix");

            entity.Property(e => e.FmecaVersion).HasComment("FMECA matrix version (default is 1 for AMprover 3.0)");
        });

        modelBuilder.Entity<FmecaSelect>(entity =>
        {
            entity.HasKey(e => e.MfsId);

            entity.HasComment(
                "Contains the monetary values (of each risk type) before and after the preventive actions defined during risk analysis.");

            entity.Property(e => e.MfsId)
                .HasColumnName("MfsID")
                .HasComment("Unique ID (PK of FmecaSelect)");

            entity.Property(e => e.MfsColIndex).HasComment("Fmeca matrix column number (99 is mtbf)");

            entity.Property(e => e.MfsCustomAfter).HasComment("Custom value after performing the FMECA");

            entity.Property(e => e.MfsCustomBefore).HasComment("Custom value before performing the FMECA");

            entity.Property(e => e.MfsDescription)
                .IsUnicode(false)
                .HasComment("Description for the FMECA column.");

            entity.Property(e => e.MfsLccId)
                .HasColumnName("MfsLccID")
                .HasComment("Reference to Lcc ID (FK to LCC)");

            entity.Property(e => e.MfsReferenceId)
                .HasColumnName("MfsReferenceID")
                .HasComment("Reference to risk ID (FK to Mrb)");

            entity.Property(e => e.MfsRefrenceType)
                .HasComment("Used for mrb lcc and columncost per task. Is set to 999 by SyncMrb. ");

            entity.Property(e => e.MfsSelectAfter).HasComment("Selected value after performing the FMECA");

            entity.Property(e => e.MfsSelectBefore).HasComment("Select value before performing the FMECA");

            entity.Property(e => e.MfsTaskId)
                .HasColumnName("MfsTaskID")
                .HasComment("Reference to task ID (FK to Task)");

            entity.Property(e => e.MfsValueAfter).HasComment("Value after performing the FMECA");

            entity.Property(e => e.MfsValueBefore).HasComment("Value before performing the FMECA");
        });

        modelBuilder.Entity<Lcc>(entity =>
        {
            entity.HasKey(e => e.LccId);

            entity.HasComment(
                "Contains the life cycle costs for each defined risk object. Calculations can be based on risk and/or RAMS analysis. ");

            entity.HasOne(x => x.VdmxlItem)
                .WithOne(x => x.Lcc)
                .HasForeignKey<Vdmxl>(x => x.VdmLccId);

            entity.HasOne(x => x.LccPartOfLcc)
                .WithMany(x => x.LccChildren)
                .HasForeignKey(x => x.LccPartOf);

            entity.HasOne(x => x.ChildObject)
                .WithMany(x => x.ChildObjects)
                .HasForeignKey(x => x.LccChildObject)
                .HasConstraintName("FK_Lcc_Object");

            entity.HasOne(x => x.ChildObject1)
                .WithMany(x => x.ChildObjects1)
                .HasForeignKey(x => x.LccChildObject1)
                .HasConstraintName("FK_Lcc_Object1");

            entity.HasOne(x => x.ChildObject2)
                .WithMany(x => x.ChildObjects2)
                .HasForeignKey(x => x.LccChildObject2)
                .HasConstraintName("FK_Lcc_Object2");

            entity.HasOne(x => x.ChildObject3)
                .WithMany(x => x.ChildObjects3)
                .HasForeignKey(x => x.LccChildObject3)
                .HasConstraintName("FK_Lcc_Object3");

            entity.HasOne(x => x.ChildObject4)
                .WithMany(x => x.ChildObjects4)
                .HasForeignKey(x => x.LccChildObject4)
                .HasConstraintName("FK_Lcc_Object4");

            entity.HasOne(x => x.RiskObject)
                .WithMany(x => x.LccItems)
                .HasForeignKey(x => x.LccRiskObject);

            entity.HasIndex(e => e.LccPartOf)
                .HasName("IX_LCCPartOf");

            entity.Property(e => e.LccId)
                .HasColumnName("LccID")
                .HasComment("Unique ID (PK of LCC)");

            entity.Property(e => e.LccAec)
                .HasColumnName("LccAEC")
                .HasColumnType("decimal(18, 2)")
                .HasComment("Annual equivalent cost of the LCC");

            entity.Property(e => e.LccAverageOptimalCost)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Average optimal cost of the LCC");

            entity.Property(e => e.LccChildObject).HasComment("Object ID of the LCC (FK to Object)");

            entity.Property(e => e.LccChildObject1).HasComment("Object ID of the LCC (FK to Object)");

            entity.Property(e => e.LccChildObject2).HasComment("Object ID of the LCC (FK to Object)");

            entity.Property(e => e.LccChildObject3).HasComment("Object ID of the LCC (FK to Object)");

            entity.Property(e => e.LccChildObject4).HasComment("Object ID of the LCC (FK to Object)");

            entity.Property(e => e.LccDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.LccDiscountRate)
                .HasColumnType("decimal(18, 4)")
                .HasComment(
                    "Discount rate for the LCC is a factror reflecting the time value of money that is used to convert cash flows occurring at different times, to a common time. ");

            entity.Property(e => e.LccEcoFunctional)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Functional eco score of the LCC");

            entity.Property(e => e.LccEcoTechnical)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Technical eco score of the LCC");

            entity.Property(e => e.LccExclude)
                .HasComment("Boolean that excludes the LCC from calculation when true");

            entity.Property(e => e.LccInputAvailabilityTechnical)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Availability of technical input for the LCC");

            entity.Property(e => e.LccInputReliabilityFunctional)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Reliability of functional input for the LCC");

            entity.Property(e => e.LccMaxYears).HasComment("Number of years the LCC should be calculated for");

            entity.Property(e => e.LccMcRav)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Maintenance cost divided by the replacement value");

            entity.Property(e => e.LccModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.LccMtbffunctional)
                .HasColumnName("LccMTBFFunctional")
                .HasColumnType("decimal(18, 6)")
                .HasComment("Output of the total functional MTBF (mean time between failure) of the LCC");

            entity.Property(e => e.LccMtbftechnical)
                .HasColumnName("LccMTBFTechnical")
                .HasColumnType("decimal(18, 6)")
                .HasComment("Output of the total technical MTBF (mean time between failure) of the LCC");

            entity.Property(e => e.LccName)
                .IsRequired()
                .HasMaxLength(50)
                .HasComment("Name of the LCC");

            entity.Property(e => e.LccNpv)
                .HasColumnName("LccNPV")
                .HasColumnType("decimal(18, 4)")
                .HasComment(
                    "Net present value of the LCC. NPV is the sum of discounted future cash flows, inlcuding both costs and benefits/revenues.");

            entity.Property(e => e.LccNpvyear)
                .HasColumnName("LccNPVyear")
                .HasComment("Optimal year of the LCC (year where the AEC is on his lowest point)");

            entity.Property(e => e.LccOptimalAverageCorrectiveCost)
                .HasColumnType("decimal(18, 0)")
                .HasComment("Average optimal cost for corrective maintenance of the LCC");

            entity.Property(e => e.LccOptimalAveragePreventiveCost)
                .HasColumnType("decimal(18, 0)")
                .HasComment("Average optimal cost for preventive maintenance of the LCC");

            entity.Property(e => e.LccOptimalImage)
                .HasColumnType("image")
                .HasComment("Image containing the optimal cost graph of the LCC");

            entity.Property(e => e.LccOutputAvailabilityTechnical)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Availability of technical output for the LCC");

            entity.Property(e => e.LccOutputReliabilityTechnical)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Reliability of functional input for the LCC");

            entity.Property(e => e.LccOverallProductionCost)
                .HasColumnType("decimal(18, 0)")
                .HasComment("Total production cost of the LCC");

            entity.Property(e => e.LccPartOf).HasComment("LCC ID of the parent LCC (FK to LCC)");

            entity.Property(e => e.LccPotential)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Saving potential of the LCC");

            entity.Property(e => e.LccProductionCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Production cost of the LCC");

            entity.Property(e => e.LccProductivityFunctional)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Functional productivity of the LCC");

            entity.Property(e => e.LccProductivityTechnical)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Technical productivity of the LCC");

            entity.Property(e => e.LccRamsDiagramId)
                .HasColumnName("LccRamsDiagramID")
                .HasComment("RAMS diagram ID of the LCC (FK to RamsDiagram)");

            entity.Property(e => e.LccRamsId)
                .HasColumnName("LccRamsID")
                .HasComment("RAMS ID of the LCC (FK to Rams)");

            entity.Property(e => e.LccRamsImage)
                .HasColumnType("image")
                .HasComment("Image containing the RAMS graph of the LCC");

            entity.Property(e => e.LccRealCostImage)
                .HasColumnType("image")
                .HasComment("Image containing the real cost graph of the LCC");

            entity.Property(e => e.LccRemark)
                .IsUnicode(false)
                .HasComment("Remarks field allowing for more detailed information about the LCC item");

            entity.Property(e => e.LccReplacementValue)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Replacement value of the LCC object");

            entity.Property(e => e.LccRiskObject).HasComment("Risk object ID of the LCC (FK to RiskObject)");

            entity.Property(e => e.LccScenarioId)
                .HasColumnName("LccScenarioID")
                .HasComment("Scenario ID of the LCC (FK to Scenario)");

            entity.Property(e => e.LccSiId).HasColumnName("LccSiID");

            entity.Property(e => e.LccTotalAverageCost)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Total average cost of the LCC");

            entity.Property(e => e.LccUtilizationFunctional)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Functional utilization (time) of the LCC");

            entity.Property(e => e.LccUtilizationTechnical)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Technical utilization (time) of the LCC");

            entity.Property(e => e.LccdateCalculated)
                .HasColumnName("LCCDateCalculated")
                .HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<Lccdetail>(entity =>
        {
            entity.HasKey(e => e.LccDetId)
                .HasName("PK_CalcLCCtotal");

            entity.HasOne(x => x.Lcc)
                .WithMany(x => x.Details)
                .HasForeignKey(x => x.LccDetLccId)
                .HasConstraintName("FK_LccDetail_Lcc")
                .OnDelete(DeleteBehavior.ClientCascade);

            entity.HasComment(
                @"Contains the life cycle cost details for each risk object, for each year the LCC needs to be calculated. Can be calculated based on:
- risk per fmeca effect column
- risk per year
- task per year
(one or all of the above can be chosen from the LCC command center control, which generates the LCC details based on the users' preferences)

All of the LCCDetails combined will provide the data for the actual Life Cycle Costing calculation for each riskobject.  
");

            entity.Property(e => e.LccDetId)
                .HasColumnName("LccDetID")
                .HasComment("Unique ID (PK of LCCDetail)");

            entity.Property(e => e.LccDetActionCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cost for actions of the LCC detail (? is this different than LCCDetTaskCost?) ");

            entity.Property(e => e.LccDetAec)
                .HasColumnName("LccDetAEC")
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Annual equivalent costs. Is used to compare investment options where the natural replacement cycle cannot easily be related to the period of analysis. ");

            entity.Property(e => e.LccDetAvailabilityTechnical)
                .HasColumnType("decimal(18, 6)")
                .HasComment(
                    "Technical availability (time) of the LCC detail. The amount of time the riskobject is available for use. ");

            entity.Property(e => e.LccDetAverageAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Average cost after executing preventive actions ");

            entity.Property(e => e.LccDetAverageBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Average costs before executing preventive actions ");

            entity.Property(e => e.LccDetAverageOptimalCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Average optimal costs for the current year only");

            entity.Property(e => e.LccDetAverageRealCorrectiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Average costs of corrective measures for the current year only");

            entity.Property(e => e.LccDetAverageRealPreventiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Average costs of preventive measures for the current year only");

            entity.Property(e => e.LccDetCapexCost).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.LccDetCorrectiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Costs for corrective measures taken");

            entity.Property(e => e.LccDetDepreciation)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Depreciation cost of the LCC detail");

            entity.Property(e => e.LccDetDirectCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Direct cost of the LCC detail");

            entity.Property(e => e.LccDetEcoTechnical)
                .HasColumnType("decimal(18, 6)")
                .HasComment("Technical eco score of the LCC detail.");

            entity.Property(e => e.LccDetExcluded)
                .HasComment("Boolean that excludes the LCC detail from calculation when true.");

            entity.Property(e => e.LccDetFailureRate)
                .HasColumnType("decimal(18, 6)")
                .HasComment(
                    "Failure rate of the LCC detail. Failure rate is the frequency with which an engineered system or component fails. Usually expressed in failures per hour. ");

            entity.Property(e => e.LccDetFmecaAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("FMECA cost after executing preventive actions ");

            entity.Property(e => e.LccDetFmecaBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("FMECA cost before executing preventive actions ");

            entity.Property(e => e.LccDetFmecaCustomAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Custom cost after executing preventive actions");

            entity.Property(e => e.LccDetFmecaCustomBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Custom FMECA costs before executing preventive actions");

            entity.Property(e => e.LccDetFmecaCustomRiskAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Custom risk cost after executing preventive actions");

            entity.Property(e => e.LccDetFmecaCustomRiskBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Custom risk before executing preventive actions");

            entity.Property(e => e.LccDetLccId)
                .HasColumnName("LccDetLccID")
                .HasComment("LCC ID to which the LCC detail belongs (FK to LCC)");

            entity.Property(e => e.LccDetMaintenanceCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Maintenance cost of the LCC detail");

            entity.Property(e => e.LccDetModificationCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cost for modifications of the LCC detail");

            entity.Property(e => e.LccDetOpexCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Opex cost of the LCC detail (operating expenditures)");

            entity.Property(e => e.LccDetOptimalCorrectiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Optimal costs of corrective measures for the current year only");

            entity.Property(e => e.LccDetOptimalPreventiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Optimal costs of preventive measures for the current year only");

            entity.Property(e => e.LccDetPreventiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Costs of preventive measures taken");

            entity.Property(e => e.LccDetProcedureCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cost for procedures of the LCC detail");

            entity.Property(e => e.LccDetProductivityTechnical)
                .HasColumnType("decimal(18, 6)")
                .HasComment(
                    "Technical productivity time of the LCC detail. Productivity is an average measure of the efficiency of production. It is usually expressed as a ratio of production output to what is required to produce it. ");

            entity.Property(e => e.LccDetRealCorrectiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Costs of the corrective measures for the current year only");

            entity.Property(e => e.LccDetRealOpexCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Opex cost of the current year only (operating expenditures)");

            entity.Property(e => e.LccDetRealPreventiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Costs of the preventive measures for the current year only");

            entity.Property(e => e.LccDetRealTotalCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Real total costs for the current year only");

            entity.Property(e => e.LccDetReliability)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Reliability of the LCC detail");

            entity.Property(e => e.LccDetRiskAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Risk cost after executing preventive actions");

            entity.Property(e => e.LccDetRiskBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Risk cost before executing preventive actions");

            entity.Property(e => e.LccDetSpareCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cost for spare parts needed for the LCC detail");

            entity.Property(e => e.LccDetTaskCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cost for tasks of the LCC detail (? is this different than LCCDetActionCost?) ");

            entity.Property(e => e.LccDetTotalCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("The costs of this year and all previous years");

            entity.Property(e => e.LccDetTotalNpv)
                .HasColumnName("LccDetTotalNPV")
                .HasColumnType("decimal(18, 2)")
                .HasComment("Total net present value of the LCC detail");

            entity.Property(e => e.LccDetUtilizationTechnichal)
                .HasColumnType("decimal(18, 6)")
                .HasComment(
                    "Technical utilization time of the LCC detail. The time the riskobject is actually being used in production. In basic terms, utilization is a measure of the actual revenue earned by the assets against the potential revenue they could have earned.");

            entity.Property(e => e.LccDetYear).HasComment("LCC detail year");
        });

        modelBuilder.Entity<LcceffectDetail>(entity =>
        {
            entity.HasKey(e => e.LccEfctId);

            entity.HasOne(e => e.LccEfctLccDetail)
                .WithMany(e => e.EffectDetails)
                .HasForeignKey(e => e.LccEfctLccDetailId)
                .HasConstraintName("FK_TblLccEffectDetail_TblLCCDetail_LccEfctLccDetailID")
                .OnDelete(DeleteBehavior.ClientCascade);

            entity.HasComment(
                @"Contains the life cycle cost effect details. These details are generated from data gathered from LCC, LCCDetail, Task, Mrb and RiskObject. (?)

LCC effect details are be generated from the LCC command centre control, based on the users' preferences.");

            entity.Property(e => e.LccEfctId)
                .HasColumnName("LccEfctID")
                .HasComment("Unique ID (PK of LCCEffectDetail)");

            entity.Property(e => e.LccEfctActionCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("The costs of a task not specified (Task, action, modification or inspection)");

            entity.Property(e => e.LccEfctCorrectiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Total costs of corrective actions for this LCC effect detail");

            entity.Property(e => e.LccEfctCustomAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Custom cost of LCC effect detail, after executing preventive action");

            entity.Property(e => e.LccEfctCustomBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Custom cost of LCC effect detail, before executing preventive action");

            entity.Property(e => e.LccEfctCustomRiskAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Custom risk cost of the LCC effect detail, after executing preventive actions");

            entity.Property(e => e.LccEfctCustomRiskBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Custom risk cost of the LCC effect detail, before executing preventive actions");

            entity.Property(e => e.LccEfctDirectCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Direct cost of the LCC effect detail");

            entity.Property(e => e.LccEfctEffectColumn)
                .HasComment("Concerning FMECA column of the LCC effect detail (?)");

            entity.Property(e => e.LccEfctEffectName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name for the FMECA column of the LCC effect detail");

            entity.Property(e => e.LccEfctFmecaAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("FMECA cost for this LCC effect detail, after executing preventive actions ");

            entity.Property(e => e.LccEfctFmecaBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("FMECA cost for this LCC effect detail, before executing preventive actions ");

            entity.Property(e => e.LccEfctLccDetailId)
                .HasColumnName("LccEfctLccDetailID")
                .HasComment("LCC detail ID the LCC effect detail belongs to (FK to LCCDetail)");

            entity.Property(e => e.LccEfctLccId)
                .HasColumnName("LccEfctLccID")
                .HasComment("LCC ID the LCC effect detail belongs to (FK to LCC)");

            entity.Property(e => e.LccEfctOptimalCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Optimal cost of the LCC effect detail");

            entity.Property(e => e.LccEfctPreventiveCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Total costs of preventive actions for this LCC effect detail");

            entity.Property(e => e.LccEfctRamsDiagramId)
                .HasColumnName("LccEfctRamsDiagramID")
                .HasComment("RAMS diagram ID the LCC effect detail belongs to (FK to RamsDiagram)");

            entity.Property(e => e.LccEfctRamsId)
                .HasColumnName("LccEfctRamsID")
                .HasComment("RAMS ID the LCC effect detail belongs to (FK to Rams)");

            entity.Property(e => e.LccEfctRiskAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Risk cost of LCC effect detail, after executing preventive action ");

            entity.Property(e => e.LccEfctRiskBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Risk cost of LCC effect detail, before executing preventive action ");

            entity.Property(e => e.LccEfctRiskId)
                .HasColumnName("LccEfctRiskID")
                .HasComment("Risk ID the LCC effect detail belongs to (FK to MRB)");

            entity.Property(e => e.LccEfctSparePartCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "The maintenance costs (real expenses), these are the costs for storage and purchase of spare parts for this LCC effect detail");

            entity.Property(e => e.LccEfctTaskFmeca)
                .HasColumnType("decimal(18, 2)")
                .HasComment("?");

            entity.Property(e => e.LccEfctTaskFmecaCustom)
                .HasColumnType("decimal(18, 2)")
                .HasComment("?");

            entity.Property(e => e.LccEfctTaskId)
                .HasColumnName("LccEfctTaskID")
                .HasComment("Task ID the LCC effect detail belongs to (FK to Task)");

            entity.Property(e => e.LccEfctType)
                .HasComment(
                    "Type of LCC effect detail, describes what caused the LCC effect detail (Type domain contains EffectDetail, RiskDetail, TaskDetail or RamsDetail)");

            entity.Property(e => e.LccEfctYear).HasComment("Year the LCC effect detail contains costs for");
        });

        modelBuilder.Entity<Login>(entity =>
        {
            entity.HasNoKey();

            entity.HasComment(
                "Contains data that shows which user logged in from where. This table is not used in the AMprover software (!)");

            entity.Property(e => e.LogInUserMachineName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Computer name used by logged in user");

            entity.Property(e => e.LoginId)
                .HasColumnName("LoginID")
                .ValueGeneratedOnAdd();

            entity.Property(e => e.LoginLoginDate)
                .HasColumnType("smalldatetime")
                .HasComment("Date and time the login occurred");

            entity.Property(e => e.LoginLogoutDate).HasColumnType("smalldatetime");

            entity.Property(e => e.LoginUserDomain)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Domain of the logged in user");

            entity.Property(e => e.LoginUserId)
                .HasColumnName("LoginUserID")
                .HasComment("User name of the logged in user");
        });

        modelBuilder.Entity<Mca>(entity =>
        {
            entity.HasKey(e => e.McaId);

            entity.HasComment(
                "Contains data for multiple criteria analysis. (MCA) MCA is a scientific procedure that allows for a rational choice based on more than one distinguishing criterium.");

            entity.HasIndex(e => e.McaSiId)
                .HasName("IX_Mca")
                .IsUnique();

            entity.Property(e => e.McaId)
                .HasColumnName("McaID")
                .HasComment("Unique ID (PK of Mca)");

            entity.Property(e => e.McaCommercial).HasComment("Commercial score for the multi criteria analyse");

            entity.Property(e => e.McaIntensity).HasComment("Intensity score for the multi criteria analyse");

            entity.Property(e => e.McaQualityLevel)
                .HasComment("Level of the multi criteria analyse depending of the business value (0,1,2) ");

            entity.Property(e => e.McaQualityScore)
                .HasComment("Total score (business value) of the multi criteria analyse");

            entity.Property(e => e.McaRepresentative)
                .HasComment("Representative score for the multi criteria analyse");

            entity.Property(e => e.McaSiId)
                .HasColumnName("McaSiID")
                .HasComment("ID of the significant item the multi criteria analyse belongs to (FK to Si)");

            entity.Property(e => e.McaSocial).HasComment("Social score for the multi criteria analyse");

            entity.Property(e => e.McaUsage).HasComment("Usage score for the multi criteria analyse");

            entity.Property(e => e.McaUtilization).HasComment("Utilization score for the multi criteria analyse");
        });

        modelBuilder.Entity<Mrb>(entity =>
        {
            entity.HasKey(e => e.Mrbid);

            entity.HasOne(x => x.RiskObject)
                .WithMany(x => x.Risks)
                .HasForeignKey(x => x.MrbRiskObject)
                .HasConstraintName("FK_TblMRB_TblRiskObject_MrbRiskObject");

            entity.HasMany(x => x.Spares)
                .WithOne(x => x.SpareMrb)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(x => x.Tasks)
                .WithOne(x => x.TskMrb)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(x => x.ChildObject)
                .WithMany(x => x.MrbChildObjects)
                .HasForeignKey(x => x.MrbChildObject);
            entity.HasOne(x => x.Installation)
                .WithMany(x => x.MrbChildObjects1)
                .HasForeignKey(x => x.MrbChildObject1);
            entity.HasOne(x => x.System)
                .WithMany(x => x.MrbChildObjects2)
                .HasForeignKey(x => x.MrbChildObject2);
            entity.HasOne(x => x.Component)
                .WithMany(x => x.MrbChildObjects3)
                .HasForeignKey(x => x.MrbChildObject3);
            entity.HasOne(x => x.Assembly)
                .WithMany(x => x.MrbChildObjects4)
                .HasForeignKey(x => x.MrbChildObject4);

            entity.HasComment(
                @"Contains the data that defines risks, and their relations to other risks. It also contains everything needed to build the risk matrix and the monetary before/after values. (?)Risks can be defined within the Risk Analysis module. ");

            entity.HasIndex(e => e.MrbRiskObject)
                .HasName("RiskObject");

            entity.HasIndex(e => new
                    {e.MrbChildObject, e.MrbChildObject1, e.MrbChildObject2, e.MrbChildObject3, e.MrbChildObject4})
                .HasName("IX_MRB_ChildObject");

            entity.Property(e => e.Mrbid).HasColumnName("MRBId");

            entity.Property(e => e.MrbActionCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Total cost per year for all tasks that are bound to the risk");

            entity.Property(e => e.MrbCapCosts)
                .HasColumnType("decimal(18, 2)")
                .HasDefaultValueSql("((0))")
                .HasComment("(?) not used in amprover software");

            entity.Property(e => e.MrbChildObject)
                .HasComment("ID of the object the risk belongs to (FK to Object)");

            entity.Property(e => e.MrbChildObject1)
                .HasComment("ID of the object (level 1) the risk belongs to (FK to Object)");

            entity.Property(e => e.MrbChildObject2)
                .HasComment("ID of the object (level 2) the risk belongs to (FK to Object)");

            entity.Property(e => e.MrbChildObject3)
                .HasComment("ID of the object (level 3) the risk belongs to (FK to Object)");

            entity.Property(e => e.MrbChildObject4)
                .HasComment("ID of the object (level 4) the risk belongs to (FK to Object)");

            entity.Property(e => e.MrbChildType)
                .HasComment("The way the risk is derived of another risk (eg child, master)");

            entity.Property(e => e.MrbCustomAfter)
                .HasColumnType("decimal(18, 3)")
                .HasComment("FMECA custom value after executing preventive actions for the risk");

            entity.Property(e => e.MrbCustomBefore)
                .HasColumnType("decimal(18, 3)")
                .HasComment("FMECA custom value before preventive actions for the risk are executed");

            entity.Property(e => e.MrbCustomEffectAfter)
                .HasColumnType("decimal(18, 3)")
                .HasComment("FMECA custom effect after executing preventive actions for the risk");

            entity.Property(e => e.MrbCustomEffectBefore)
                .HasColumnType("decimal(18, 3)")
                .HasComment("FMECA custom effect before executing preventive actions for the risk");

            entity.Property(e => e.MrbDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.MrbDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.MrbDescription)
                .IsUnicode(false)
                .HasComment("Description of the risk, contains the possible effects of the risk (?)");

            entity.Property(e => e.MrbDirectCostAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Direct cost of the risk after executing preventive actions");

            entity.Property(e => e.MrbDirectCostBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Direct costs of the risk before executing preventive actions");

            entity.Property(e => e.MrbDownTimeAfter)
                .HasColumnType("decimal(18, 4)")
                .HasDefaultValueSql("((0))")
                .HasComment(
                    "Down time after executing preventive actions (in hours) caused by the risk (not used) (!)");

            entity.Property(e => e.MrbDownTimeBefore)
                .HasColumnType("decimal(18, 4)")
                .HasDefaultValueSql("((0))")
                .HasComment("Down time after preventive actions (in hours) caused by the risk (not used) (!)");

            entity.Property(e => e.MrbEffectAfter)
                .HasColumnType("decimal(18, 2)")
                .HasComment("FMECA effect after executing preventive actions for the risk");

            entity.Property(e => e.MrbEffectBefore)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "FMECA effect before preventive actions for the risk are executed (needed for Priority)");

            entity.Property(e => e.MrbExclOptCb)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValueSql("(N'false')")
                .HasComment("?");

            entity.Property(e => e.MrbFailureCategorie1)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Failure category 1, used for the risk (FK to LookupFailCat)");

            entity.Property(e => e.MrbFailureCategorie2)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Failure category 2, used for the risk (FK to LookupFailCat)");

            entity.Property(e => e.MrbFailureCause)
                .IsUnicode(false)
                .HasComment("Cause of the risk  (does not seem to be used in the AMprover software !)");

            entity.Property(e => e.MrbFailureConsequences)
                .IsUnicode(false)
                .HasComment("Consequences, should the described risk occur ");

            entity.Property(e => e.MrbFailureMode)
                .HasComment("ID of the failure mode used for the risk (FK to LookupFailMode)");

            entity.HasOne(r => r.FailureMode)
                .WithMany(fm => fm.Risks)
                .HasForeignKey(r => r.MrbFailureMode);

            entity.Property(e => e.MrbFmecaSelect)
                .IsUnicode(false)
                .HasComment(
                    "Selected values of the FMECA matrix before and after preventive actions. Values are stored in XML format. ");

            entity.Property(e => e.MrbFmecaVersion).HasComment("Version of the FMECA matrix used for this risk");

            entity.Property(e => e.MrbInitiatedBy)
                .IsUnicode(false)
                .HasComment("Created by this user with this username");

            entity.Property(e => e.MrbMasterId)
                .HasColumnName("MrbMasterID")
                .HasComment("ID of the risk this risk was copied from (master risk) (FK to MRB)");

            entity.Property(e => e.MrbModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.MrbMtbfAfter)
                .HasComment("MTBF in years after executing preventive actions for the risk");

            entity.Property(e => e.MrbMtbfBefore)
                .HasComment("MTBF in years before executing preventive actions for the risk");

            entity.Property(e => e.MrbName)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasComment("Name describing the risk");

            entity.Property(e => e.MrbNorm)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasComment("Norm for the risk (seems to not be in use) (!)");

            entity.Property(e => e.MrbOpsProcedure)
                .IsUnicode(false)
                .HasComment("? Not used in amprover software");

            entity.Property(e => e.MrbOptimalCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Optimal costs for preventive actions for the risk (based on risk after preventive action)");

            entity.Property(e => e.MrbRemarks)
                .IsUnicode(false)
                .HasComment("Remarks for the risk, which provides a place to leave any extra information ");

            entity.Property(e => e.MrbRemarks1)
                .IsUnicode(false)
                .HasComment("Remarks of the risk (not used) (!)");

            entity.Property(e => e.MrbResponsible)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasComment(
                    "Person or department responsible for the risk (? responsible for solving it, or monitoring?)");

            entity.Property(e => e.MrbRiskAfter)
                .HasColumnType("decimal(18, 2)")
                .HasDefaultValueSql("((0))")
                .HasComment("Risk cost after executing preventive actions");

            entity.Property(e => e.MrbRiskBefore)
                .HasColumnType("decimal(18, 2)")
                .HasDefaultValueSql("((0))")
                .HasComment("Risk cost before executing preventive actions");

            entity.Property(e => e.MrbRiskObject)
                .HasComment("ID of the risk object the risk belongs to (FK to RiskObject)");

            entity.Property(e => e.MrbSpareCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Costs of spare parts that are needed for preventive/corrective measures");

            entity.Property(e => e.MrbSpareManageCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Costs made to manage the spare parts that are needed for corrective/preventive measures (not used)");

            entity.Property(e => e.MrbState)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasComment("State of the risk (? Is this still used?)");

            entity.Property(e => e.MrbStatus)
                .HasComment("Status of the risk (master, slave, or lock) (status is defined in Lookup)");

            entity.HasOne(e => e.MrbStatusNavigation)
                .WithMany(l => l.Risks)
                .HasForeignKey(l => l.MrbStatus);
        });

        modelBuilder.Entity<MrbImage>(entity =>
        {
            entity.HasKey(x => x.MrbImageId);

            entity.Property(e => e.MrbImageId)
                .HasColumnName("MrbImageID")
                .ValueGeneratedNever();

            entity.Property(e => e.MrbImageDate)
                .HasColumnType("datetime")
                .HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.MrbImageNavigation)
                .WithOne(p => p.MrbImage)
                .HasForeignKey<MrbImage>(d => d.MrbImageId)
                .HasConstraintName("FK_MrbImageID_MrB");
        });

        modelBuilder.Entity<Entities.AM.Object>(entity =>
        {
            entity.HasKey(e => e.ObjId);

            entity.HasComment(
                @"Stores objects that are used for modelling risks. The object level determines what the object is. The levels and mapping to object type/name are defined in master data, by the items contained within the functional objects treenode. 

Objects define a part of the system we're modelling, and can describe an object, installation, system, assembly or component. The user can name 5 levels of objects. (?)");

            entity.Property(e => e.ObjId)
                .HasColumnName("ObjID")
                .HasComment("Unique ID (PK of Object)");

            entity.Property(e => e.ObjAvailableTime).HasComment(
                "Available time of the object. (Time the object is available for use) (Does not seem to be used by Amprover software)");

            entity.Property(e => e.ObjDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.ObjDescription)
                .IsUnicode(false)
                .HasComment("Object description");

            entity.Property(e => e.ObjFunction)
                .IsUnicode(false)
                .HasComment(
                    "Function of the object. Provides a description that explains the functionality of this specific part of the model.");

            entity.Property(e => e.ObjImage)
                .HasColumnType("image")
                .HasComment(" (does not seem to be used by the AMprover software) (!)");

            entity.Property(e => e.ObjLevel)
                .HasComment(
                    "Level (hierarchy) of the object. The level determines what the object actually is. (a whole system, an assembly, etc.)");

            entity.Property(e => e.ObjModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.ObjName)
                .IsRequired()
                .IsUnicode(false)
                .HasComment("Name of the object");

            entity.Property(e => e.ObjNewValue)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Replacement value of the object.");

            entity.Property(e => e.ObjProductionTime).HasComment(
                "Production time. (Time the object can be used in production?) (Does not seem to be used by Amprover software)");

            entity.Property(e => e.ObjShortKey)
                .IsRequired()
                .IsUnicode(false)
                .HasComment("Short key of the object");

            entity.Property(e => e.ObjSiCategory)
                .HasComment(
                    "Relation to the kind of technical objects. SiCategories are defined in masterdata. (FK to LookupUserDefined) ");

            entity.Property(e => e.ObjSiType)
                .IsUnicode(false)
                .HasComment("The type of the related technical objects (?)");

            entity.Property(e => e.ObjUsableTime)
                .HasComment(
                    "Usable time of the object. (Time the object can be used) (What's the difference with Utilization time ?) (Does not seem to be used by Amprover software)");

            entity.Property(e => e.ObjUtilizationTime).HasComment(
                "Utilization time of the object (time the object can be used for its specific function) (?) (Does not seem to be used by Amprover software)");
        });

        modelBuilder.Entity<OpexData>(entity =>
        {
            entity.HasKey(e => e.OpexDataId);

            entity.HasComment(
                "Contains data that defines the operating expenditures (opex) caused by specific failure causes. The operating expenditures can be defined in the masterdata, and are used by the LCC calculations. (?)");

            entity.Property(e => e.OpexDataId)
                .HasColumnName("OpexDataID")
                .HasComment("Unique ID (PK of OpexData)");

            entity.Property(e => e.OpexDataDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.OpexDataInflationGroupId)
                .HasColumnName("OpexDataInflationGroupID")
                .HasComment("ID of the inflation group that is used by the opex data (FK to LookupInflationGroup)");

            entity.Property(e => e.OpexDataMethod)
                .HasComment("Opex factor method value which is used for the 'opex data' (FK to Lookup)");

            entity.Property(e => e.OpexDataModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.OpexDataName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the 'opex data'");

            entity.Property(e => e.OpexDataPercentage)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Precentage used for the 'opex data'");

            entity.Property(e => e.OpexDataValue)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Value used for the 'opex data'");
        });

        modelBuilder.Entity<OpexFactor>(entity =>
        {
            entity.HasKey(e => e.OpexFactId);

            entity.HasComment(
                "Contains factors that are used in operating expenditure calculations. 0 or more of these factors can be defined for each Opex data item, from the masterdata. (?)");

            entity.Property(e => e.OpexFactId)
                .HasColumnName("OpexFactID")
                .HasComment("Unique ID (PK to OpexFactor)");

            entity.Property(e => e.OpexFactDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.OpexFactLookupValue)
                .HasColumnType("decimal(18, 2)")
                .HasComment("? Defined in Masterdata.");

            entity.Property(e => e.OpexFactModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.OpexFactOpexDataId)
                .HasColumnName("OpexFactOpexDataID")
                .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

            entity.Property(e => e.OpexFactValue)
                .HasColumnType("decimal(18, 2)")
                .HasComment("? Defined in Masterdata.");

            entity.HasOne(d => d.OpexFactOpexData)
                .WithMany(p => p.OpexFactor)
                .HasForeignKey(d => d.OpexFactOpexDataId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_OpexFactor_OpexData");
        });

        modelBuilder.Entity<OpexToLcc>(entity =>
        {
            entity.HasKey(e => e.OpexLccId);

            entity.ToTable("OpexToLCC");

            entity.HasComment(
                "Contains operating expenditure (opex) data that is used by life cycle costs (LCC) calculation. These opex items can be found and defined from the LCC module, in the Opex tab (On the bottom half of the screen).");

            entity.Property(e => e.OpexLccId)
                .HasColumnName("OpexLccID")
                .HasComment("Unique ID (PK to OpexToLCC)");

            entity.Property(e => e.OpexLccColorName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the line color used for the 'opex to lcc' graph");

            entity.Property(e => e.OpexLccLccId)
                .HasColumnName("OpexLccLccID")
                .HasComment("Lcc ID the 'opex to lcc' belongs to (FK to Lcc)");

            entity.Property(e => e.OpexLccName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the 'opex to lcc'");

            entity.Property(e => e.OpexLccOpexDataId1)
                .HasColumnName("OpexLccOpexDataID1")
                .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

            entity.Property(e => e.OpexLccOpexDataId2)
                .HasColumnName("OpexLccOpexDataID2")
                .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

            entity.Property(e => e.OpexLccOpexDataId3)
                .HasColumnName("OpexLccOpexDataID3")
                .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

            entity.Property(e => e.OpexLccPrice)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Price for the 'opex to lcc' (price per unit/quantity)");

            entity.Property(e => e.OpexLccQuantity)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Physical quantity applicable for the 'opex to lcc'");

            entity.Property(e => e.OpexLccSequence).HasComment(
                "? Sequence used to keep the order of Opex to LCC items within the grid that is used to display them.");

            entity.Property(e => e.OpexLccShowInGraph)
                .HasComment("Boolean to show the 'opex to lcc' in the graph (shows up in graph when true)");

            entity.Property(e => e.OpexLccSumItem).HasComment("?");

            entity.Property(e => e.OpexLccUnits)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Number of units applicable for the 'opex to lcc'");

            entity.HasOne(d => d.OpexLccOpexDataId1Navigation)
                .WithMany(p => p.OpexToLccOpexLccOpexDataId1Navigation)
                .HasForeignKey(d => d.OpexLccOpexDataId1)
                .HasConstraintName("FK_OpexToLCC_OpexData");

            entity.HasOne(d => d.OpexLccOpexDataId2Navigation)
                .WithMany(p => p.OpexToLccOpexLccOpexDataId2Navigation)
                .HasForeignKey(d => d.OpexLccOpexDataId2)
                .HasConstraintName("FK_OpexToLCC_OpexData1");

            entity.HasOne(d => d.OpexLccOpexDataId3Navigation)
                .WithMany(p => p.OpexToLccOpexLccOpexDataId3Navigation)
                .HasForeignKey(d => d.OpexLccOpexDataId3)
                .HasConstraintName("FK_OpexToLCC_OpexData2");
        });

        modelBuilder.Entity<OpexToLccDetail>(entity =>
        {
            entity.HasKey(e => e.OpexLccDetId);

            entity.HasComment(
                "Contains operating expenditure (opex) data that is used to help calculate life cycle costing (LCC) detail costs. Will contain records for each LCC item, for each year of the LCC calculation. This provides a way to see and edit the costs per year of the life cycle of an item defined in the risk analysis. ");

            entity.Property(e => e.OpexLccDetId)
                .HasColumnName("OpexLccDetID")
                .HasComment("Unique ID (PK of OpexToLCCDetail)");

            entity.Property(e => e.OpexLccDetAec)
                .HasColumnName("OpexLccDetAEC")
                .HasColumnType("decimal(18, 2)")
                .HasComment("Annual equivalent cost for the 'opex to lcc detail'");

            entity.Property(e => e.OpexLccDetCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Costs for this year of this 'opex to lcc detail'");

            entity.Property(e => e.OpexLccDetLccId)
                .HasColumnName("OpexLccDetLccID")
                .HasComment("Lcc ID to which the 'opex to lcc detail' belongs (FK to Lcc)");

            entity.Property(e => e.OpexLccDetNpv)
                .HasColumnName("OpexLccDetNPV")
                .HasColumnType("decimal(18, 2)")
                .HasComment("Net present value for the 'opex to lcc detail'");

            entity.Property(e => e.OpexLccDetOpexLccId)
                .HasColumnName("OpexLccDetOpexLccID")
                .HasComment("Opex lcc ID to which the 'opex to lcc detail' belongs (FK to OpexToLcc)");

            entity.Property(e => e.OpexLccDetPv)
                .HasColumnName("OpexLccDetPV")
                .HasColumnType("decimal(18, 2)")
                .HasComment("Present value for the 'opex to lcc detail'");

            entity.Property(e => e.OpexLccDetYear)
                .HasComment("Year for which the 'opex to lcc detail' contains opex data");
        });

        modelBuilder.Entity<PickSi>(entity =>
        {
            entity.HasKey(e => e.PckSiId)
                .HasName("PK_PickMSI");

            entity.Property(e => e.PckSiId).HasColumnName("PckSiID");

            entity.Property(e => e.PckSiLinkFilterId).HasColumnName("PckSiLinkFilterID");

            entity.Property(e => e.PckSiMrbId).HasColumnName("PckSiMrbID");

            entity.Property(e => e.PckSiSiId).HasColumnName("PckSiSiID");

            entity.HasOne(d => d.PckSiLinkFilter)
                .WithMany(p => p.PickSi)
                .HasForeignKey(d => d.PckSiLinkFilterId)
                .HasConstraintName("FK_PickSI_SiLinkFilters");

            entity.HasOne(d => d.PckSiMrb)
                .WithMany(p => p.PickSi)
                .HasForeignKey(d => d.PckSiMrbId)
                .HasConstraintName("FK_PickMSI_MRB");

            entity.HasOne(d => d.PckSiSi)
                .WithMany(p => p.PickSi)
                .HasForeignKey(d => d.PckSiSiId)
                .HasConstraintName("FK_PickSI_Si");
        });

        modelBuilder.Entity<Priority>(entity =>
        {
            entity.HasKey(e => e.PrioId);

            entity.HasComment(
                "Contains priority groups, which are generated from data in PriorityTask and PriorityBudget. A priority group combines groups of tasks that belong together (like tasks that need to be performed in one geographical location). (?)");

            entity.HasIndex(e => e.PrioPartOf)
                .HasName("IX_PriorityPartOf");

            entity.Property(e => e.PrioId)
                .HasColumnName("PrioID")
                .HasComment("Unique ID (PK of Priority)");

            entity.Property(e => e.PrioAutoGenerated)
                .HasComment("Boolean that display if the priority was auto generated (true if it was generated)");

            entity.Property(e => e.PrioDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.PrioDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.PrioDescription)
                .IsUnicode(false)
                .HasComment(
                    "Description of the priority, can be used to add any remarks that could clarify the existance of the priority group. ");

            entity.Property(e => e.PrioInitiatedBy)
                .IsUnicode(false)
                .HasComment("Created by user with this username");

            entity.Property(e => e.PrioModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.PrioName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name describing the priority group");

            entity.Property(e => e.PrioPartOf).HasComment("Priority ID of the parent priority (FK to Priority)");

            entity.Property(e => e.PrioPath)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasComment(
                    "Path to display the way the priority is linked to other priorities. Each linked item is an FK to Priority, they'll be separated by a '-' char.");

            entity.Property(e => e.PrioRemark)
                .IsUnicode(false)
                .HasComment("Remarks that should clarify the reason for the existance of the priority. ");

            entity.Property(e => e.PrioResponsible)
                .IsUnicode(false)
                .HasComment("People or departments that are responsible for execution of the priority");

            entity.Property(e => e.PrioShortKey)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasComment("Short key of the priority");

            entity.Property(e => e.PrioSiCategory)
                .HasComment("Si category ID the chosen significant item belongs to (FK to LookupUserDefined)");

            entity.Property(e => e.PrioSiId)
                .HasColumnName("PrioSiID")
                .HasComment("Significant item ID the priority is bound to (FK to Si)");

            entity.Property(e => e.PrioStatus)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Priority status, user can enter any string here. (?)");
        });

        modelBuilder.Entity<PriorityBudget>(entity =>
        {
            entity.HasKey(e => e.PrioBudId);

            entity.HasComment(
                "Defines budgets for each item in the prioritygroup. Editable in the priority groups control. ");

            entity.HasIndex(e => new {e.PrioBudPriorityId, e.PrioBudVersion})
                .HasName("IX_PriorityBudget")
                .IsUnique();

            entity.Property(e => e.PrioBudId)
                .HasColumnName("PrioBudID")
                .HasComment("Unique ID (PK to PriorityBudget)");

            entity.Property(e => e.PrioBudAccepted)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Calculated value for the accepted costs of the priority budget");

            entity.Property(e => e.PrioBudBudget)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Budget for priority group (amount) (?)");

            entity.Property(e => e.PrioBudBudgetCostYear1)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority budget costs for year 1");

            entity.Property(e => e.PrioBudBudgetCostYear2)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority budget costs for year 2");

            entity.Property(e => e.PrioBudBudgetCostYear3)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority budget costs for year 3");

            entity.Property(e => e.PrioBudBudgetCostYear4)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority budget costs for year 4");

            entity.Property(e => e.PrioBudBudgetCostYear5)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority budget costs for year 5");

            entity.Property(e => e.PrioBudBudgetSumOfParts)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Calculated value for budget, is built from a sum of all priority item budgets from this priority group. (?) ");

            entity.Property(e => e.PrioBudCostSelected)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Calculated value. Selected cost for the priority budget");

            entity.Property(e => e.PrioBudDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.PrioBudDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.PrioBudEnableTaskSelect).HasComment(
                "? Boolean that determines if the selected costs are added or not. (?) (true adds them)");

            entity.Property(e => e.PrioBudInitiatedBy)
                .IsUnicode(false)
                .HasComment("Created by user with this username");

            entity.Property(e => e.PrioBudModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.PrioBudOriginalId)
                .HasColumnName("PrioBudOriginalID")
                .HasComment(
                    "Refers to itself, to keep track of where the priority budget was originally from (?) (FK to PriorityBudget)");

            entity.Property(e => e.PrioBudPostponed)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Calculated value for the postponed costs of the priority budget");

            entity.Property(e => e.PrioBudPriorityId)
                .HasColumnName("PrioBudPriorityID")
                .HasComment("Priority item the priorityBudget refers to (FK to Priority)");

            entity.Property(e => e.PrioBudProposed)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Calculated value for the proposed costs of the priority budget");

            entity.Property(e => e.PrioBudRejected)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Calculated value for the rejected costs of the priority budget");

            entity.Property(e => e.PrioBudRemark)
                .IsUnicode(false)
                .HasComment("Remarks that can clarify priority budget");

            entity.Property(e => e.PrioBudRisk)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Risk costs for the current year");

            entity.Property(e => e.PrioBudRiskDelta)
                .HasColumnType("decimal(18, 2)")
                .HasComment("? Risk delta for the whole priority period, which is used for... (?) ");

            entity.Property(e => e.PrioBudStatus)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Status of the priority budget (under review etc.)");

            entity.Property(e => e.PrioBudTaskSelectDirty)
                .HasComment("? Boolean that allows a different way of selecting task selection. (the dirty way) ");

            entity.Property(e => e.PrioBudVersion).HasComment(
                "Version (year and number) of the priority version which the priority budget is bound to (defined in PriorityVersion, but no FK)");
        });

        modelBuilder.Entity<PriorityCost>(entity =>
        {
            entity.HasKey(e => new {e.PrioCstVersion, e.PrioCstId});

            entity.HasComment("Defines the costs for each prioritygroup, for each year. (?)");

            entity.Property(e => e.PrioCstVersion).HasComment(
                "Version (year and number) of the priority version which the priority cost is bound to (PriorityVersion)");

            entity.Property(e => e.PrioCstId)
                .HasColumnName("PrioCstID")
                .HasComment("Unique ID (PK of PriorityCost)");

            entity.Property(e => e.PrioCstCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("The actual costs for this priority for this year ");

            entity.Property(e => e.PrioCstDirectCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "The direct cost is the price that can be completely attributed to the production of specific goods and services. These costs refer to materials, labor and expenses related to the production of a product. ");

            entity.Property(e => e.PrioCstFailRate)
                .HasColumnType("decimal(18, 6)")
                .HasComment(
                    "Failure rate is the frequency with which an engineered system or component fails, expressed in failures per hour. (?)");

            entity.Property(e => e.PrioCstNumberOfTasks)
                .HasComment("Number of tasks that are bound to this priority cost item");

            entity.Property(e => e.PrioCstPrioId)
                .HasColumnName("PrioCstPrioID")
                .HasComment("ID of the priority the priority cost is bound to (FK to Priority)");

            entity.Property(e => e.PrioCstPrioTskId)
                .HasColumnName("PrioCstPrioTskID")
                .HasComment("ID of the priority task the priority cost is bound to (FK to PriorityTask)");

            entity.Property(e => e.PrioCstRiskCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("The projected risk costs for this priority for this year (?)");

            entity.Property(e => e.PrioCstRiskDelta)
                .HasColumnType("decimal(18, 2)")
                .HasComment("?");

            entity.Property(e => e.PrioCstYear)
                .HasComment("The year the priority costs have been calculated for. (?)");
        });

        modelBuilder.Entity<PriorityTask>(entity =>
        {
            entity.HasKey(e => e.PrioTskId);

            entity.HasComment(
                "Contains the tasks that have been prioritized. This data is generated from the priority box command center. Data is gathered from Mrb, RIskObject, Task, and Priority.");

            entity.HasIndex(e => e.PrioTskPriorityId)
                .HasName("IX_PriorityTaskPriorityID");

            entity.HasIndex(e => e.PrioTskVersion)
                .HasName("IX_PriorityTaskVersion");

            entity.Property(e => e.PrioTskId)
                .HasColumnName("PrioTskID")
                .HasComment("Unique ID (PK of PriorityTask)")
                .ValueGeneratedNever();

            entity.Property(e => e.PrioTskAutoSelected).HasComment(
                "? Boolean that determines if a task will be auto-selected for postponement. Set to true for auto-select. ");

            entity.Property(e => e.PrioTskBudgetCostYear1)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority task costs for year 1");

            entity.Property(e => e.PrioTskBudgetCostYear2)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority task costs for year 2");

            entity.Property(e => e.PrioTskBudgetCostYear3)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority task costs for year 3");

            entity.Property(e => e.PrioTskBudgetCostYear4)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority task costs for year 4");

            entity.Property(e => e.PrioTskBudgetCostYear5)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Priority task costs for year 5");

            entity.Property(e => e.PrioTskClusterCostPerUnit)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Cluster cost per unit of the priority task. Retrieved from CltpCLusterCostPerUnit or CmnTaskCosts.");

            entity.Property(e => e.PrioTskClusterPlanId)
                .HasColumnName("PrioTskClusterPlanID")
                .HasComment("ID of the cluster plan the priority task is bound to (FK to ClusterTaskPlan)");

            entity.Property(e => e.PrioTskCommonTaskId)
                .HasColumnName("PrioTskCommonTaskID")
                .HasComment("ID of the common action the priority task is bound to (FK to CommonTask)");

            entity.Property(e => e.PrioTskCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Original cost for the priority task as calculated by the clusterTaskPlan");

            entity.Property(e => e.PrioTskDateDue)
                .HasColumnType("smalldatetime")
                .HasComment("Date when the priority task is due (deadline for task completion)");

            entity.Property(e => e.PrioTskDateExecuted)
                .HasColumnType("smalldatetime")
                .HasComment("Date when the priority task was executed");

            entity.Property(e => e.PrioTskDateGenerated)
                .HasColumnType("smalldatetime")
                .HasComment("The priority task was generated on this date and time.");

            entity.Property(e => e.PrioTskDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Description of the priority task");

            entity.Property(e => e.PrioTskDirectCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "? Direct costs associated with this priority task. (costs for materials, man hours, etc) Does not seem to be used in the AMprover software (!) ");

            entity.Property(e => e.PrioTskDownTime)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Downtime needed to complete task (in hours)");

            entity.Property(e => e.PrioTskDuration)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Time spent on task (in hours)");

            entity.Property(e => e.PrioTskExecuteStatus)
                .HasComment("Status of the priority task (postponed, proposed etc.)");

            entity.Property(e => e.PrioTskExecutionYear).HasComment(
                "We need to keep track of modifications in the execution date. The year will be used when the execution is modified. (?)");

            entity.Property(e => e.PrioTskFromReference)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment(
                    @"This is a composite field, containing several items, delimited by the '-' char. The field contains the following items:
Date due, RiskID, PrioTaskID, SiID, TaskType, and TaskID.

The field will be used for importing corrective tasks based on an inspection. ");

            entity.Property(e => e.PrioTskImported).HasComment(
                "Boolean that sets the imported status for the priority task (Does not seem to be used in the AMprover software) (!)");

            entity.Property(e => e.PrioTskIntervalPerYear)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Number of times the task is performed per year");

            entity.Property(e => e.PrioTskNumberOfTimes)
                .HasColumnType("decimal(18, 3)")
                .HasComment(
                    "? The amount of times a priority task will be executed. Based on the interval per year and the interval count.");

            entity.Property(e => e.PrioTskObjectId)
                .HasColumnName("PrioTskObjectID")
                .HasComment("ID of the object the priority task is bound to (FK to Object)");

            entity.Property(e => e.PrioTskOriginalId)
                .HasColumnName("PrioTskOriginalID")
                .HasComment(
                    "Task ID that is used to keep track of which task the priotask was originally created for. Seems to only be filled with the PrioTskID, is this still needed? (?)");

            entity.Property(e => e.PrioTskPostponePct)
                .HasColumnType("decimal(18, 2)")
                .HasComment("? Used in year cost calculation, as inflation correction factor.  ");

            entity.Property(e => e.PrioTskPriorityCode)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "? A code that defines the inspection priority. Defaults to 1. Value can be 1-100. (?)");

            entity.Property(e => e.PrioTskPriorityId)
                .HasColumnName("PrioTskPriorityID")
                .HasComment("ID of the priority the priority task is bound to (FK to Priority)");

            entity.Property(e => e.PrioTskQualityScore).HasComment(
                "Business value of the bound significant item. Contains the quality score of the linked significant item. (if that's present)");

            entity.Property(e => e.PrioTskReferenceId)
                .HasColumnName("PrioTskReferenceID")
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment(
                    @"This is a composite field, containing several items, delimited by the '-' char. The field contains the following items:
Date due, RiskID, PrioTaskID, SiID, TaskType, and TaskID.

The field will be used for importing corrective tasks based on an inspection. ");

            entity.Property(e => e.PrioTskRemarks)
                .IsUnicode(false)
                .HasComment("Remarks of the priority task");

            entity.Property(e => e.PrioTskRiskBudget)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "The risk budget is defined as the costs per task. It can be calculated in different ways: By dividing the PrioTskRiskCost (or MrbRiskAfter) by the number of tasks, by looking at failrate type, failrate, age and effect after (and dividing that by number of tasks).");

            entity.Property(e => e.PrioTskRiskCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("? Filled with value of Mrb Risk after");

            entity.Property(e => e.PrioTskRiskDelta)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Calculated value that is used for determining priority of tasks. Calculated by subtracting the PrioTskRiskbudget from the MrbRiskbefore, and dividing that by number of tasks. The delta is the difference between the \"normal\" risk, and the risk after prioritizing tasks. (?)");

            entity.Property(e => e.PrioTskRiskFactor)
                .HasColumnType("decimal(18, 3)")
                .HasComment(
                    "? A variable used in risk calculations. Determined by looking at failure type, fail rate, age and mtbf.");

            entity.Property(e => e.PrioTskRiskId)
                .HasColumnName("PrioTskRiskID")
                .HasComment("ID of the risk the priority task is bound to (FK to MRB)");

            entity.Property(e => e.PrioTskSealed)
                .HasComment(
                    "Boolean that seals the priority task. When this is true, the priority task is sealed. (which means it is no longer editable) ");

            entity.Property(e => e.PrioTskSelectionSeq)
                .HasComment("? Determines the selection sequence. A lower number will be processed sooner. ");

            entity.Property(e => e.PrioTskSequence).HasComment(
                "A calculation that uses fields: PrioTskRiskFactor, PrioTskQualityScore, PrioTskPriorityCode to determine what prioritytask should be executed first. (?)");

            entity.Property(e => e.PrioTskSiDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Description of the bound significant item, is filled with the Si name");

            entity.Property(e => e.PrioTskSiId)
                .HasColumnName("PrioTskSiID")
                .HasComment("ID of the significant item the priority task is bound to (FK to Si)");

            entity.Property(e => e.PrioTskSiUnits)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Number of significant item units for this priority task. Filled with value of CltpSiUnits.");

            entity.Property(e => e.PrioTskSlack)
                .HasComment(
                    "Slack for the priority task, which is a period of time a task can be postponed. Slack is measured in years. ");

            entity.Property(e => e.PrioTskSlackIntervalType).HasComment(
                "Interval unit ID of the slack for the priority task (FK to LookupIntervalUnit) The value is taken from CltpSlackIntervalType.");

            entity.Property(e => e.PrioTskTaskId)
                .HasColumnName("PrioTskTaskID")
                .HasComment("ID of the task the priority task is bound to (FK to Task)");

            entity.Property(e => e.PrioTskVersion).HasComment(
                "Version (year and number) of the priority task (versions need to be present in PriorityVersion)");
        });

        modelBuilder.Entity<PriorityVersion>(entity =>
        {
            entity.HasKey(e => e.PrioVerId);

            entity.HasComment(
                @"Each priority calculation is created around a PriorityVersion. Defining a priority version allows the user to generate different priority groups, which enables the user to see the differences in the resulting risks and costs between several priority configurations. 

The priority version is created from the priority box command center. A Priority version can be created for each scenario.
(?)");

            entity.Property(e => e.PrioVerId)
                .HasColumnName("PrioVerID")
                .HasComment("Unique ID (PK of PriorityVersion)");

            entity.Property(e => e.PrioVerDescription)
                .IsUnicode(false)
                .HasComment("Description of the priority version");

            entity.Property(e => e.PrioVerName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the priority version");

            entity.Property(e => e.PrioVerRemark)
                .IsUnicode(false)
                .HasComment("Remarks of the priority version");

            entity.Property(e => e.PrioVerScenario).HasComment("Scenario the priority version was created for.");

            entity.Property(e => e.PrioVerSealed)
                .HasComment("Boolean to seal the priority version. Setting this to true seals it. ");

            entity.Property(e => e.PrioVerVersion).HasComment("Version (year and number) of the priority version");
        });

        modelBuilder.Entity<Rams>(entity =>
        {
            entity.HasKey(e => e.RamsId).HasName("PK_RAMS");

            entity.HasComment(
                "This table defines RAMS objects. RAMS is a set of methods which are used to visualize the performance of a system, looking specifically at the system Reliability, Availability, Maintainability and Safety. (RAMS) The RAMS objects are user defined from the RAMS controls.");

            entity.Property(e => e.RamsDiagramId)
                .HasColumnName("RamsDiagramID")
                .HasComment("ID of the RAMS diagram this RAMS block belongs to (FK to RamsDiagram)");

            entity.Property(e => e.RamsId)
                .HasColumnName("RamsID")
                .ValueGeneratedOnAdd()
                .HasComment("Unique ID (PK of Rams)");

            entity.Property(e => e.RamsAvailabilityInput)
                .HasComment("Availability of the input of the RAMS block. The value lies between 0 and 1.");

            entity.Property(e => e.RamsAvailabilityOutput)
                .HasComment("Availability of the output of the RAMS block. The value lies between 0 and 1.");

            entity.Property(e => e.RamsBeta).HasComment(
                "Weibull factor for this RAMS block. In reliability analysis, the weibull factor is used to clarify age-to-failure data. The weibull factor shows if the maintenance strategy should be better aimed at preventive or corrective maintenance. The value lies between 0 and 1.");

            entity.Property(e => e.RamsBitmap)
                .HasColumnType("image")
                .HasComment(
                    "Picture of the RAMS block. Was added to allow the user to store a picture of the physical structure the RAMS block represents. (Does not seem to be used in AMprover software) (!)");

            entity.Property(e => e.RamsCharacteristicLife)
                .HasColumnType("decimal(18, 2)")
                .HasComment("The characteristic life (Ƞ) is the moment where 63.2% of the units will fail");

            entity.Property(e => e.RamsCircuitDepCorrCost)
                .HasColumnType("decimal(18, 0)")
                .HasComment(
                    "? Production and corrective costs dependent on the MTBF of this RAMS block. In RAMS containers, this cost will be determined by the sum of the underlying blocks.");

            entity.Property(e => e.RamsClassDc)
                .HasColumnName("RamsClassDC")
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasComment(
                    "Diagnostic Coverage (DC) class of the RAMS block. This class determines the level of detection of dangerous situations, and thus describes if a danger to this RAMS block would be detected. (dangerous detected, dangers undetected, etc.) ");

            entity.Property(e => e.RamsCollapsed).HasComment(
                "Boolean that collapses the RAMS blocks within the RAMS block to one block. This is used to keep track of the state of each of the RAMS blocks, so the user will see the same configuration when opening the same diagram.");

            entity.Property(e => e.RamsCompleted)
                .HasComment(
                    "Boolean that set the analysis of the block to complete. The user decides when a RAMS block is complete, and can set that using a checkbox.");

            entity.Property(e => e.RamsContainer)
                .HasComment(
                    "Boolean that makes the RAMS block behave as a container. When creating a RAMS block with the \"add a new container\" toolstrip button, this boolean will be set.");

            entity.Property(e => e.RamsCostLinked).HasComment(
                "Boolean that, when set, lets RAMS blocks retrieve their costs from linked FMECA object. Can only be set when the block is linked to FMECA, and the cost owner bit is set. ");

            entity.Property(e => e.RamsCostOwner).HasComment(
                "The RamsCostOwner is a bit that is set for containers that will then be the owner of the cost. When this is set, input of costs to blocks belonging to that container will be blocked.");

            entity.Property(e => e.RamsDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.RamsDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.RamsDcd)
                .HasColumnName("RamsDCd")
                .HasComment(
                    "Diagnostic coverage of dangerous failures (DCD). This is a percentage for this RAMS block, that describes how many of the possibly dangerous failures that can occur would be detected. The value lies between 0 and 1. (?)");

            entity.Property(e => e.RamsDescr)
                .IsUnicode(false)
                .HasComment("Description of the RAMS block");

            entity.Property(e => e.RamsDiagramRefId)
                .HasColumnName("RamsDiagramRefID")
                .HasComment(
                    "When a rams diagram is linked to another rams diagram, that reference will be stored here. When this reference is present, the rams block will not allow the user to alter any of the values used in calculations. ");

            entity.Property(e => e.RamsDistributionType)
                .HasComment("The distribution type specific to this RAMS block");

            entity.Property(e => e.RamsEcoFunct).HasComment(
                "Functional eco score of the RAMS block.  Eco score is a measurement of effect to the environment, and is measured with a score from 0 to 100. (closer to 100 means more environmentally friendly)");

            entity.Property(e => e.RamsEcoTechn).HasComment(
                "Technical eco score of the RAMS block. Eco score is a measurement of effect to the environment, and is measured with a score from 0 to 100. (closer to 100 means more environmentally friendly).");

            entity.Property(e => e.RamsFailCorrCost)
                .HasColumnType("decimal(18, 0)")
                .HasComment("Costs of the corrective measures taken after failure of this RAMS block.");

            entity.Property(e => e.RamsFailureMode)
                .HasComment("ID of the failure mode used for the RAMS block (FK to LookupFailMode)");

            entity.Property(e => e.RamsFunctionalDemand)
                .IsUnicode(false)
                .HasComment(
                    "? (Does not seem to be used by the AMprover software) Functional demand is defined as a demand for specific functionality, such as accessiblity and fire safety.");

            entity.Property(e => e.RamsHft).HasColumnName("RamsHFT");

            entity.Property(e => e.RamsIdentical).HasComment(
                "Boolean that allows the software to set the properties of all the blocks within the RAMS block to values identical to the first RAMS block. This user can set this during design. ");

            entity.Property(e => e.RamsInitiatedBy)
                .IsUnicode(false)
                .HasComment("Created by user with this username");

            entity.Property(e => e.RamsLccusePfd).HasColumnName("RamsLCCUsePFD");

            entity.Property(e => e.RamsLinkMethod).HasComment(
                "RAMS link method determines how the RAMS is linked to Mrb items. (Rams link methods are defined in Lookup, though it's the enum value that gets stored.) (?)");

            entity.Property(e => e.RamsLinkType).HasComment(
                "Linktype shows what the RAMS block is linked to (RAMS, RiskObject, Object, Risk, or Notset). This value determines how a RAMS block will be processed during LCC calculations.  ");

            entity.Property(e => e.RamsLinkedLeft).HasComment(
                "ID of the RAMS block on the left side of the RAMS block (FK to Rams). Is set to -1 when there is no RAMS block to the left. ");

            entity.Property(e => e.RamsLinkedRight).HasComment(
                "ID of the RAMS block on the right side of the RAMS block (FK to Rams). Is set to -1 when there is no RAMS block to the right. ");

            entity.Property(e => e.RamsModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.RamsMtbffunct)
                .HasColumnName("RamsMTBFFunct")
                .HasComment("Functional mean time between failure (MTBF) for the RAMS block");

            entity.Property(e => e.RamsMtbftechn)
                .HasColumnName("RamsMTBFTechn")
                .HasComment("Technical mean time between failure (MTBF) for this RAMS block. ");

            entity.Property(e => e.RamsMttr)
                .HasColumnName("RamsMTTR")
                .HasComment(
                    "Mean time to repair (MTTR) for this RAMS block. (The time it takes to repair the block when a failure occurs)");

            entity.Property(e => e.RamsName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the RAMS block");

            entity.Property(e => e.RamsObjectId)
                .HasColumnName("RamsObjectID")
                .HasComment("ID of the object the RAMS block belongs to (FK to Object)");

            entity.Property(e => e.RamsParallelBlocks).HasComment(
                "Number of parallel RAMS blocks in this RAMS block. Will only be visible for containers, and is set automatically during design of RAMS diagram.");

            entity.Property(e => e.RamsPartOf).HasComment("RAMS ID of the parent RAMS block (FK to Rams)");

            entity.Property(e => e.RamsPfd)
                .HasColumnName("RamsPFD")
                .HasComment(
                    "Probability of failure on demand (PFD) for this RAMS block, which is the probability this RAMS block will fail when it is actually used. This value can be used to determine the Safety Integrity Level (SIL) ");

            entity.Property(e => e.RamsPreventiveCost)
                .HasColumnType("decimal(18, 0)")
                .HasComment("Total costs of preventive measures taken for this RAMS block");

            entity.Property(e => e.RamsProductivityFunct)
                .HasComment("Functional productivity (time) of this RAMS block");

            entity.Property(e => e.RamsProductivityTechn)
                .HasComment("Technical productivity (time) of this RAMS block");

            entity.Property(e => e.RamsReadSequence).HasComment(
                "? The read sequence is used to rebuild a nested diagram in the correct order. The field is set automatically by methods DiagramToRows and DoDiagramToRows (in SyncRamsData).");

            entity.Property(e => e.RamsRemark)
                .IsUnicode(false)
                .HasComment("Remarks for this RAMS block");

            entity.Property(e => e.RamsRiskId)
                .HasColumnName("RamsRiskID")
                .HasComment("ID of the risk the RAMS block belongs to (FK to MRB)");

            entity.Property(e => e.RamsRiskObjectId)
                .HasColumnName("RamsRiskObjectID")
                .HasComment("ID of the risk object the RAMS block belongs to (FK to RiskObject)");

            entity.Property(e => e.RamsSff).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.RamsSiId)
                .HasColumnName("RamsSiID")
                .HasComment("ID of the significant item the RAMS block belongs to (FK to Si)");

            entity.Property(e => e.RamsSil)
                .HasMaxLength(5)
                .IsUnicode(false);

            entity.Property(e => e.RamsSilAc)
                .HasColumnName("RamsSilAC")
                .HasMaxLength(5)
                .IsUnicode(false);

            entity.Property(e => e.RamsStatus)
                .HasComment("Status of the RAMS block. The possible statuses are defined in Lookup. ");

            entity.Property(e => e.RamsTechnCorrCost)
                .HasColumnType("decimal(18, 0)")
                .HasComment("Total costs of technical corrective measures taken for this RAMS block (?)");

            entity.Property(e => e.RamsTotalCost)
                .HasColumnType("decimal(18, 0)")
                .HasComment("Total costs for this RAMS block");

            entity.Property(e => e.RamsType)
                .HasMaxLength(6)
                .IsUnicode(false);

            entity.Property(e => e.RamsUtilizationFunct).HasComment(
                "Functional utilization (time) of this RAMS block. Asset utilization represents the total revenue earned for every dollar of assets a company owns. (?)");

            entity.Property(e => e.RamsUtilizationTechn).HasComment(
                "Technical utilization (time) of this RAMS block. Asset utilization represents the total revenue earned for every dollar of assets a company owns.  (?)");

            entity.Property(e => e.RamsWantLcc)
                .HasColumnName("RamsWantLCC")
                .HasComment(
                    "Boolean that makes the RAMS block available for LCC calculations. When a RAMS block is defined as a container, this value will be set. The user can also enable this by setting the checkbox.");

            entity.Property(e => e.RamsWeibullShape)
                .HasColumnType("decimal(18, 2)")
                .HasComment("The Weibull shape (β) defines the slope of the current location on the Weibull curve");

            entity.Property(e => e.RamsXooN)
                .HasComment(
                    "RAMS XooN is a number that shows how many RAMS blocks are contained within this RAMS block. Is only set to anything other than 1 for containers. (?)");

            entity.Property(e => e.RamsXposition)
                .HasColumnName("RamsXPosition")
                .HasComment(
                    "X-position of the RAMS block in the RAMS diagram. Used to more easily rebuild the RAMS blocks in the correct relative order.");

            entity.Property(e => e.RamsYear)
                .HasComment("? Year the object the RAMS block represents was first used.");

            entity.Property(e => e.RamsYposition)
                .HasColumnName("RamsYPosition")
                .HasComment(
                    "Y-position of the RAMS block in the RAMS diagram. Used to more easily rebuild the RAMS blocks in the correct relative order.");

            entity.HasOne(d => d.RamsDiagram)
                .WithMany(p => p.Rams)
                .HasForeignKey(d => d.RamsDiagramId)
                .HasConstraintName("FK_RAMS_RamsDiagram");

            entity.HasOne(d => d.RamsSi)
                .WithMany(p => p.Rams)
                .HasForeignKey(d => d.RamsSiId)
                .HasConstraintName("FK_Rams_Si");
        });

        modelBuilder.Entity<RamsDiagram>(entity =>
        {
            entity.HasKey(e => e.RamsDgId)
                .HasName("PK_RamsDetail_1");

            entity.HasComment(
                "Stores the graphical representation of the RAMS analysis. Some specific properties can be set through the controls of the properties tab within the RAMS analysis.");

            entity.Property(e => e.RamsDgId)
                .HasColumnName("RamsDgID")
                .HasComment("Unique ID");

            entity.Property(e => e.RamsDgAvailableTime).HasDefaultValueSql("((8760))");

            entity.Property(e => e.RamsDgDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.RamsDgDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.RamsDgDescr)
                .IsUnicode(false)
                .HasComment("Description of the RAMS diagram");

            entity.Property(e => e.RamsDgHorizon).HasComment("Period applied to the RAMS diagram. (?)");

            entity.Property(e => e.RamsDgInitiatedBy)
                .IsUnicode(false)
                .HasComment("Created by user");

            entity.Property(e => e.RamsDgModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.RamsDgName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the RAMS diagram");

            entity.Property(e => e.RamsDgPageBreaks).IsUnicode(false);

            entity.Property(e => e.RamsDgPeriodFrom).HasColumnType("decimal(18, 10)");

            entity.Property(e => e.RamsDgPeriodTo)
                .HasColumnType("decimal(18, 10)")
                .HasDefaultValueSql("((1))");

            entity.Property(e => e.RamsDgPrerequisites).IsUnicode(false);

            entity.Property(e => e.RamsDgReferenceId)
                .HasColumnName("RamsDgReferenceID")
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("Reference ID of the RAMS diagram (not bound and used) (!)");

            entity.Property(e => e.RamsDgRemark)
                .IsUnicode(false)
                .HasComment("Remarks of the RAMS diagram");

            entity.Property(e => e.RamsDgRiskObject)
                .HasComment("ID of the risk object linked to the RAMS diagram (FK to RiskObject)");

            entity.Property(e => e.RamsDgScenId)
                .HasColumnName("RamsDgScenID")
                .HasComment("ID of the scenario the RAMS diagram belongs to (FK to Scenario)");

            entity.Property(e => e.RamsDgSiId)
                .HasColumnName("RamsDgSiID")
                .HasComment("ID of the significant item bound to the RAMS diagram (FK to Si)");

            entity.Property(e => e.RamsDgSiRefId)
                .HasColumnName("RamsDgSiRefID")
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment(
                    "Reference ID of the significant item bound to the RAMS diagram. (Does not seem to be in use in the AMprover software) (!)");

            entity.Property(e => e.RamsDgStatus)
                .HasComment(
                    "Rams status value of the RAMS diagram (FK to Lookup) (Does not seem to be used in AMprover software) (!)");

            entity.Property(e => e.RamsDgTestInterval).HasComment("Test interval for the RAMS diagram. ");

            entity.Property(e => e.RamsDgWantLcc)
                .HasColumnName("RamsDgWantLCC")
                .HasComment("Boolean that makes the RAMS diagram available for LCC calculations");
        });

        modelBuilder.Entity<RiskObject>(entity =>
        {
            entity.HasKey(e => e.RiskObjId);

            entity.HasComment(
                "Stores riskobjects, which are defined within the risk analysis. A risk object is an asset, of which we want to know the risks that are present during normal usage. A risk object contains risks, or systems. (?)   ");

            entity.HasIndex(e => e.RiskObjScenarioId);

            entity.Property(e => e.RiskObjId)
                .HasColumnName("RiskObjID")
                .HasComment("Unique ID (PK to RiskObject)");

            entity.Property(e => e.RiskObjAbmstate)
                .HasColumnName("RiskObjABMstate")
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("State of the risk object (master, revision etc.) (? what is ABM in this context?)");

            entity.Property(e => e.RiskObjAmrbattended)
                .HasColumnName("RiskObjAMRBattended")
                .IsUnicode(false)
                .HasComment(
                    "People who have attended the risk analysis session. (Does not seem to be used by AMprover software) (!)");

            entity.Property(e => e.RiskObjAnalyseType)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasComment(
                    "Analysis type of the risk object. The analysis type is defined from the masterdata, though the string value is stored here. (Lookup)");

            entity.Property(e => e.RiskObjCopiedFrom).HasComment(
                "When a risk object was copied from another risk object, this field contains the ID of the source risk object (master risk object) (FK to RiskObject)");

            entity.Property(e => e.RiskObjDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.RiskObjFmecaId)
                .HasColumnName("RiskObjFmecaID")
                .HasComment("ID of the FMECA matrix used for this risk object. (FK to Fmeca)");

            entity.Property(e => e.RiskObjFuncDecomp)
                .HasColumnType("image")
                .HasComment("Picture of the functional decomposition of the risk object");

            entity.Property(e => e.RiskObjLccyear)
                .HasColumnName("RiskObjLCCYear")
                .HasComment("Optimal lifetime of the risk object (not used)");

            entity.Property(e => e.RiskObjModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.RiskObjMrbstartPoint)
                .HasColumnName("RiskObjMRBstartPoint")
                .IsUnicode(false)
                .HasComment(
                    "Start point of the risk object. (Does not seem to be used in the AMprover software) (!) (?)");

            entity.Property(e => e.RiskObjName)
                .IsUnicode(false)
                .HasComment("Name of the risk object");

            entity.Property(e => e.RiskObjNoOfInstallation).HasComment(
                "Number of objects contained within the risk object. (? Or the number of installations of its kind?) Used in LCC calculations.");

            entity.Property(e => e.RiskObjObjectId)
                .HasColumnName("RiskObjObjectID")
                .HasComment("ID of the object, which is a level 1 risk object (FK to Object) (?)");

            entity.Property(e => e.RiskObjParentObjectId)
                .HasColumnName("RiskObjParentObjectID")
                .HasComment("ID of the parent object, which is a level 0 risk object (FK to Object)");

            entity.Property(e => e.RiskObjProdCostHour)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Production costs (per hour) for this risk object");

            entity.Property(e => e.RiskObjResponsible)
                .IsUnicode(false)
                .HasComment("Person or department that are responsible for this risk object");

            entity.Property(e => e.RiskObjRforSpares)
                .HasColumnType("decimal(18, 4)")
                .HasDefaultValueSql("((0.95))")
                .HasComment("Given reliability for the spare parts");

            entity.Property(e => e.RiskObjScenarioId)
                .HasColumnName("RiskObjScenarioID")
                .HasComment("ID of the scenario that contains the risk object (FK to Scenario)");

            entity.Property(e => e.RiskObjVolgNo)
                .HasComment(
                    "Serial number of the risk object. (Does not seem to be used by the AMprover software) (!)");

            entity.HasMany(x => x.Risks)
                .WithOne(x => x.RiskObject)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(d => d.RiskObjFmeca)
                .WithMany(p => p.RiskObject)
                .HasForeignKey(d => d.RiskObjFmecaId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RiskObject_tblFMECAV30");

            entity.HasOne(d => d.RiskObjObject)
                .WithMany(p => p.RiskObject)
                .HasForeignKey(d => d.RiskObjObjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RiskObject_Object");

            entity.HasOne(d => d.RiskObjParentObject)
                .WithMany(p => p.ChildRiskObjects)
                .HasForeignKey(d => d.RiskObjParentObjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RiskObject_ParentObject");

            entity.HasOne(d => d.RiskObjScenario)
                .WithMany(p => p.RiskObject)
                .HasForeignKey(d => d.RiskObjScenarioId)
                .HasConstraintName("FK_RiskObject_Scenario");

            entity.HasOne(e => e.RiskObjStatusNavigation)
                .WithMany(l => l.RiskObjects)
                .HasForeignKey(l => l.RiskObjStatus);
        });

        modelBuilder.Entity<SapaCollection>(entity =>
        {
            entity.HasKey(e => e.SapaCollId)
                .HasName("PK_SapaCollection");

            entity.HasOne(e => e.RiskObject)
                .WithMany(r => r.SapaCollections)
                .HasForeignKey(e => e.SapaCollRiskObjId)
                .HasConstraintName("FK_SapaCollection_RiskObject")
                .OnDelete(DeleteBehavior.ClientCascade);
        });

        modelBuilder.Entity<Sapa>(entity =>
        {
            entity.HasKey(e => e.SapaId)
                .HasName("PK_Sapa");

            entity.HasOne(e => e.SapaCollection)
                .WithMany(s => s.Sapas)
                .HasForeignKey(e => e.SapaCollectionId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Executor)
                .WithMany(p => p.Sapa)
                .HasForeignKey(e => e.SapaExecutorId)
                .HasConstraintName("FK_Sapa_LookupExecutor");

            entity.HasOne(e => e.Initiator)
                .WithMany(p => p.Sapa)
                .HasForeignKey(e => e.SapaInitiatorId)
                .HasConstraintName("FK_Sapa_LookupInitiator");

            entity.HasOne(e => e.RiskObject)
                .WithMany(r => r.RiskObjSapa)
                .HasForeignKey(e => e.SapaRiskObjId)
                .HasConstraintName("FK_Sapa_RiskObject")
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.SapaWorkpackage)
                .WithMany(w => w.Sapa)
                .HasForeignKey(e => e.SapaWorkpackageId)
                .HasConstraintName("FK_Sapa_WorkPackage")
                .OnDelete(DeleteBehavior.Cascade)
                .IsRequired(false);
        });

        modelBuilder.Entity<SapaYear>(entity =>
        {
            entity.HasKey(e => e.SapaYearId)
                .HasName("PK_SapaYear");

            entity.HasOne(e => e.Sapa)
                .WithMany(s => s.Years)
                .HasForeignKey(e => e.SapaYearSapaId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<SapaDetail>(entity =>
        {
            entity.HasKey(e => e.SapaDetId)
                .HasName("PK_SapaDetail");

            entity.HasOne(e => e.SapaYear)
                .WithMany(s => s.Details)
                .HasForeignKey(e => e.SapaDetYearId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Risk)
                .WithMany(r => r.SapaDetails)
                .HasForeignKey(e => e.SapaDetMrbId)
                .HasConstraintName("FK_SapaDetail_Mrb")
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Task)
                .WithMany(r => r.SapaDetails)
                .HasForeignKey(e => e.SapaDetTskId)
                .HasConstraintName("FK_SapaDetail_Task")
                .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<SapaWorkpackage>(entity =>
        {
            entity.HasKey(e => e.SapaWpId)
                .HasName("PK_SapaWorkpackage");

            entity.HasMany(e => e.Task)
                .WithOne(s => s.TskSapaWorkpackageNavigation)
                .HasForeignKey(e => e.TskSapaWorkpackage)
                .OnDelete(DeleteBehavior.SetNull);
        });

        modelBuilder.Entity<Scenario>(entity =>
        {
            entity.HasKey(e => e.ScenId)
                .HasName("PK_Scen");

            entity.HasComment(
                "Allows the user to define risks differently, which enables them to see the differences in outcome when taking a different approach to maintenance.");

            entity.Property(e => e.ScenId)
                .HasColumnName("ScenID")
                .HasComment("Unique ID (PK of Scenario)");

            entity.Property(e => e.ScenChildType).HasComment(
                @"Determines the typeof relation the scenario has with its parent.
It can be a plain copy (version 3.15 and before) 
or
Derived scenario for creating a scenario with overriden risks / tasks but keeping the parent risks/tasks
");

            entity.Property(e => e.ScenCopiedFrom).HasComment(
                @"Used when scenario was copied from another scenario. Contains the ID of the scenario which was the source of the copy (master scenario) (FK to Scenario)
This ID is used in the tree for building hierarchical scenario's");

            entity.Property(e => e.ScenDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.ScenDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.ScenDescr)
                .HasColumnType("text")
                .HasComment("Description of the scenario");

            entity.Property(e => e.ScenInitiatedBy)
                .IsUnicode(false)
                .HasComment("Created by user");

            entity.Property(e => e.ScenModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.ScenName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the scenario");

            entity.Property(e => e.ScenShrtKey)
                .IsRequired()
                .HasMaxLength(4)
                .IsUnicode(false)
                .IsFixedLength()
                .HasComment("Short key of the scenario");

            entity.Property(e => e.ScenStartPoint)
                .HasColumnType("text")
                .HasComment("Start point of the scenario");

            entity.HasOne(e => e.ScenStatusNavigation)
                .WithMany(l => l.Scenarios)
                .HasForeignKey(l => l.ScenStatus);
        });

        modelBuilder.Entity<CriticalityRanking>(entity =>
        {
            entity.HasKey(e => e.CritId)
                .HasName("PK_TblCriticalityRanking");

            entity.Property(e => e.CritSiName)
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Ignore(e => e.CritSi);
        });

        modelBuilder.Entity<Si>(entity =>
        {
            entity.HasKey(e => e.SiId)
                .HasName("PK_MSI");

            entity.Ignore(e => e.SiCriticalityRanking);

            entity.HasComment(
                "Contains significant items (SI's). A significant item is an item which represents a physical object we want to measure the risks for. The significant item can be assigned to parts of the functional decomposition. The significant items and their  relationships are defined in the master data. Assignment of significant items to risk objects occurs in the Risk analysis.");

            entity.HasIndex(e => e.SiCategory)
                .HasName("IX_Si_Category");

            entity.HasIndex(e => e.SiName)
                .HasName("IX_Si");

            entity.HasIndex(e => e.SiPartOf)
                .HasName("IX_tblSiPartOf");

            entity.Property(e => e.SiId)
                .HasColumnName("SiID")
                .HasComment("Unique ID (PK of Si)");

            entity.Property(e => e.SiAssetManager)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the responsible asset manager of the item");

            entity.Property(e => e.SiAssetOwner)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the owner of the item");

            entity.Property(e => e.SiAssetType)
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasComment("Logical classification of the item (eg elevator)");

            entity.Property(e => e.SiBvId)
                .HasColumnName("SiBvID")
                .HasComment("The ID of the businessvalue where the item belongs to");

            entity.Property(e => e.SiCategory).HasComment(
                "SI category value the significant item belongs to (stores value of SICategory item from LookupUserDefined, which can be defined in the master data) (! should be named SICategoryValue?)");

            entity.Property(e => e.SiContractEnd)
                .HasColumnType("datetime")
                .HasComment("End of contract date for the significant item ");

            entity.Property(e => e.SiDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.SiDescription)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasComment("Description of the significant item");

            entity.Property(e => e.SiHeight)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Height of the significant item");

            entity.Property(e => e.SiLength)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Length of the significant item");

            entity.Property(e => e.SiLocation)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Geographical location of the item");

            entity.Property(e => e.SiMiscBitField1)
                .HasComment("Boolean that sets the significant item to SHE critical. (what is SHE critical?) ");

            entity.Property(e => e.SiMiscDateField1)
                .HasColumnType("datetime")
                .HasComment("Miscellaneous date field for the significant item (not used)");

            entity.Property(e => e.SiMiscDecimalField1)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Miscellaneous decimal field for the significant item (not used) (!)");

            entity.Property(e => e.SiMiscDecimalField2)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Miscellaneous decimal field for the significant item (not used) (!)");

            entity.Property(e => e.SiMiscTextField1)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment(
                    "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            entity.Property(e => e.SiMiscTextField2)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment(
                    "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            entity.Property(e => e.SiMiscTextField3)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment(
                    "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            entity.Property(e => e.SiMiscTextField4)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment(
                    "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            entity.Property(e => e.SiMiscTextField5)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment(
                    "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            entity.Property(e => e.SiMiscTextField6)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment(
                    "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            entity.Property(e => e.SiMiscTextField7)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment(
                    "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            entity.Property(e => e.SiModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.SiMtbf)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Mean time between failures for this significant item. (?) (not used)");

            entity.Property(e => e.SiName)
                .IsRequired()
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasComment("Name of the significant item");

            entity.Property(e => e.SiParentName)
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasComment("Name of the parent item");

            entity.Property(e => e.SiPartOf).HasComment("Si ID of the parent significant item (FK to Si)");

            entity.Property(e => e.SiPrice)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Price of the significant item. Used for LCC calculations.");

            entity.Property(e => e.SiQualityScore).HasComment(
                "Business value of the significant item (Quality scores are defined in Mca, but user can enter any number here) (?)");

            entity.Property(e => e.SiReferenceId)
                .HasColumnName("SiReferenceID")
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasComment(
                    "Reference ID of the significant item. This shows what this significant item is attached to. Each connected significant item is separated by a '-'. (?)");

            entity.Property(e => e.SiRemarks)
                .IsUnicode(false)
                .HasComment("Remarks of the significant item");

            entity.Property(e => e.SiSerialNumber)
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasComment("Unique serial number of the item");

            entity.Property(e => e.SiServiceManager)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the service manager of the item");

            entity.Property(e => e.SiServiceProvider)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the service provider of the item");

            entity.Property(e => e.SiSite)
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.SiSupplierBitField1).HasComment("Supplier boolean field (not used) (!)");

            entity.Property(e => e.SiSupplierDateField1)
                .HasColumnType("datetime")
                .HasComment("Supplier date field (not used) (!)");

            entity.Property(e => e.SiSupplierIntField1).HasComment("Supplier integer field (not used) (!)");

            entity.Property(e => e.SiSupplierTextField1)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment("Supplier of the significant item.");

            entity.Property(e => e.SiSupplierTextField2)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment("Supplier text field (not used) (!)");

            entity.Property(e => e.SiSupplierTextField3)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasComment("Supplier text field (not used) (!)");

            entity.Property(e => e.SiTotalUnits)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Total number of units for this significant item");

            entity.Property(e => e.SiType)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("Type of significant item. User can enter any string here.");

            entity.Property(e => e.SiUnitType)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasComment(
                    "SI unit type which applies to the significant item (possible values are stored in Lookup)");

            entity.Property(e => e.SiUnits)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Number of units for this significant item");

            entity.Property(e => e.SiUnitsTypeValues)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasComment("Value of each unit type, stored in a sequence, items are separated by '|'");

            entity.Property(e => e.SiVendor)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasComment("Vendor of the significant item");

            entity.Property(e => e.SiWarrantyPeriod).HasComment(
                "Number of years of warranty for the significant item. The warranty will run out this amount of years after the construction year. (SiYear) ");

            entity.Property(e => e.SiWidth)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Width of the significant item");

            entity.Property(e => e.SiYear)
                .HasColumnType("datetime")
                .HasComment("Construction year of the significant item");

            entity.HasOne(x => x.SiCategoryNavigation)
                .WithMany(x => x.Si);
        });

        modelBuilder.Entity<SiLinkFilters>(entity =>
        {
            entity.HasKey(e => e.SifId);

            entity.HasComment(
                @"Contains a variation of the filters stored in Filter, which are specific to (linking) significant items. These filters are used to limit the amount of data the user has to search through when linking significant items to functional objects. 

Filters are defined from the Risk analysis, switch to Si assignment tab, and click on the advanced button to start defining a new filter.");

            entity.HasIndex(e => e.SifFilterId);

            entity.HasIndex(e => e.SifObjectId);

            entity.HasIndex(e => e.SifRiskId);

            entity.Property(e => e.SifId)
                .HasColumnName("SifID")
                .HasComment("Unique ID (PK of SiLinkFilters)");

            entity.Property(e => e.SifCategory).HasComment(
                "Sif category value the significant item belongs to (stores value of SICategory item from LookupUserDefined, which can be defined in the master data) (! should be named SifCategoryValue?)");

            entity.Property(e => e.SifChildObject1Id).HasColumnName("SifChildObject1ID");

            entity.Property(e => e.SifChildObject2Id).HasColumnName("SifChildObject2ID");

            entity.Property(e => e.SifChildObject3Id).HasColumnName("SifChildObject3ID");

            entity.Property(e => e.SifChildObject4Id).HasColumnName("SifChildObject4ID");

            entity.Property(e => e.SifChildObjectId).HasColumnName("SifChildObjectID");

            entity.Property(e => e.SifDescription)
                .IsUnicode(false)
                .HasComment(
                    "Contains a text representation of the defined filter. This representation is human readable, and was added to allow for some oversight while defining filters.");

            entity.Property(e => e.SifFilterId).HasColumnName("SifFilterID");

            entity.Property(e => e.SifName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Descriptive name for the filter");

            entity.Property(e => e.SifObjectId)
                .HasColumnName("SifObjectID")
                .HasComment("ID of the object the selected Si items are a part of. (FK to Object)");

            entity.Property(e => e.SifRiskId)
                .HasColumnName("SifRiskID")
                .HasComment("ID of the risk object the selected Si items are a part of. (FK to RiskObject)");

            entity.Property(e => e.SifRiskObjectId).HasColumnName("SifRiskObjectID");

            entity.Property(e => e.SifTaskId).HasColumnName("SifTaskID");

            entity.HasOne(d => d.SifChildObject1)
                .WithMany(p => p.SiLinkFiltersSifChildObject1)
                .HasForeignKey(d => d.SifChildObject1Id)
                .HasConstraintName("FK_SiLinkFilters_Object1");

            entity.HasOne(d => d.SifChildObject2)
                .WithMany(p => p.SiLinkFiltersSifChildObject2)
                .HasForeignKey(d => d.SifChildObject2Id)
                .HasConstraintName("FK_SiLinkFilters_Object2");

            entity.HasOne(d => d.SifChildObject3)
                .WithMany(p => p.SiLinkFiltersSifChildObject3)
                .HasForeignKey(d => d.SifChildObject3Id)
                .HasConstraintName("FK_SiLinkFilters_Object3");

            entity.HasOne(d => d.SifChildObject4)
                .WithMany(p => p.SiLinkFiltersSifChildObject4)
                .HasForeignKey(d => d.SifChildObject4Id)
                .HasConstraintName("FK_SiLinkFilters_Object4");

            entity.HasOne(d => d.SifChildObject)
                .WithMany(p => p.SiLinkFiltersSifChildObject)
                .HasForeignKey(d => d.SifChildObjectId)
                .HasConstraintName("FK_SiLinkFilters_Object");

            entity.HasOne(d => d.SifFilterNavigation)
                .WithOne(p => p.SiLinkFilters)
                .HasForeignKey<SiLinkFilters>(d => d.SifFilterId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("SiLinkFilters_SifFilterID");

            entity.HasOne(d => d.SifRisk)
                .WithMany(p => p.SiLinkFilters)
                .HasForeignKey(d => d.SifRiskId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_SiLinkFilters_MRB");

            entity.HasOne(d => d.SifRiskObject)
                .WithMany(p => p.SiLinkFilters)
                .HasForeignKey(d => d.SifRiskObjectId)
                .HasConstraintName("FK_SiLinkFilters_RiskObject");

            entity.HasOne(d => d.SifTask)
                .WithMany(p => p.SiLinkFilters)
                .HasForeignKey(d => d.SifTaskId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_SiLinkFilters_Task");
        });

        modelBuilder.Entity<SiRisks>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("SiRisks");

            entity.Property(e => e.SiRiskAfterCustom).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.SiRiskAfterValue).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.SiRiskBeforeCustom).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.SiRiskBeforeValue).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.SiRiskBvId).HasColumnName("SiRiskBvID");

            entity.Property(e => e.SiRiskFmecaSelData).IsUnicode(false);

            entity.Property(e => e.SiRiskId).HasColumnName("SiRiskID");

            entity.Property(e => e.SiRiskMtbfAfter).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.SiRiskMtbfBefore).HasColumnType("decimal(18, 3)");

            entity.Property(e => e.SiRiskRiskAfter).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.SiRiskRiskBefore).HasColumnType("decimal(18, 2)");

            entity.Property(e => e.SiRiskRiskId).HasColumnName("SiRiskRiskID");

            entity.Property(e => e.SiRiskSiId).HasColumnName("SiRiskSiID");
        });

        modelBuilder.Entity<SiStatistics>(entity =>
        {
            entity.HasKey(e => e.SiStatId)
                .HasName("PK_SiStatistics");

            entity.HasIndex(e => e.SiStatScenarioId)
                .HasName("IX_SiStatistics_ScenarioId");

            entity.HasIndex(e => e.SiStatSiId)
                .HasName("IX_SiStatisticsSiID");

            entity.Property(e => e.SiStatId).HasColumnName("SiStatID");

            entity.Property(e => e.SiStatColorListAfter).IsUnicode(false);

            entity.Property(e => e.SiStatColorListBefore).IsUnicode(false);

            entity.Property(e => e.SiStatDateModified).HasColumnType("smalldatetime");

            entity.Property(e => e.SiStatFmecaId).HasColumnName("SiStatFmecaID");

            entity.Property(e => e.SiStatModifiedBy)
                .IsUnicode(false);

            entity.Property(e => e.SiStatRiskAfter)
                .HasColumnType("decimal(18, 0)")
                .HasDefaultValueSql("((0))");

            entity.Property(e => e.SiStatRiskBefore)
                .HasColumnType("decimal(18, 0)")
                .HasDefaultValueSql("((0))");

            entity.Property(e => e.SiStatScenarioId).HasColumnName("SiStatScenarioID");

            entity.Property(e => e.SiStatSiId).HasColumnName("SiStatSiID");

            entity.Property(e => e.SiStatSumListAfter).IsUnicode(false);

            entity.Property(e => e.SiStatSumListBefore).IsUnicode(false);
        });

        modelBuilder.Entity<Spare>(entity =>
        {
            entity.HasKey(e => e.SpareId);

            entity.HasComment(@"Contains spare parts, their costs and other data associated with storing them. 

Spare parts can be defined using the Spare parts grid control found in the Risk analysis screen, and can be added to any Risk.");

            // entity.HasOne(x => x.SpareMrb).WithMany(x => x.Spares).HasForeignKey(x => x.SpareMrbId);

            entity.Property(e => e.SpareId)
                .HasColumnName("SpareID")
                .HasComment("Unique ID (PK to Spare)");

            entity.Property(e => e.SpareCategory)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Category the spare part belongs to (?)");

            entity.Property(e => e.SpareCopiedFrom)
                .HasComment("For derived scenarios's you will need this field to keep track of changes");

            entity.Property(e => e.SpareCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cost of the spare part. ");

            entity.Property(e => e.SpareDepreciationPct)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Depreciation percentage of the spare part. ");

            entity.Property(e => e.SpareMrbId)
                .HasColumnName("SpareMrbID")
                .HasComment("Risk ID the spare part belongs to (FK to MRB)");

            entity.Property(e => e.SpareName)
                .HasMaxLength(60)
                .IsUnicode(false)
                .HasComment("Name of the spare part");

            entity.Property(e => e.SpareNoOfItems)
                .HasDefaultValueSql("((1))")
                .HasComment("Number of units of the spare part that are needed. (used in price calculation)");

            entity.Property(e => e.SpareObjectCount)
                .HasComment("Number of objects that can make use of the spare part.");

            entity.Property(e => e.SpareOrderLeadTime)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Lead time of the spare part. (The time it takes between ordering the part, and receiving it where it's needed)");

            entity.Property(e => e.SparePurchasePrice)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Purchase price of the spare part");

            entity.Property(e => e.SparePurchaseYear).HasComment("Year the spare part was purchased.");

            entity.Property(e => e.SpareReferenceId)
                .HasColumnName("SpareReferenceID")
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Reference ID of the spare part (not bound) (!)");

            entity.Property(e => e.SpareReliability).HasColumnType("decimal(18, 5)");

            entity.Property(e => e.SpareRemarks)
                .IsUnicode(false)
                .HasComment("Remarks of the spare part");

            entity.Property(e => e.SpareStockNumber).HasComment("Stock number of the spare part. ");

            entity.Property(e => e.SpareSupplierId)
                .HasColumnName("SpareSupplierID")
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Supplier ID of the spare part, which can be used to order the spare part directly. ");

            entity.Property(e => e.SpareVendorId)
                .HasColumnName("SpareVendorID")
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("The vendor that sells the spare part (! not an ID)");

            entity.Property(e => e.SpareYearlyCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Yearly costs for the spare part (storage, depreciation, etc) (! does not seem to be used by AMprover software)");
        });

        modelBuilder.Entity<Task>(entity =>
        {
            entity.HasKey(e => e.TskId);

            entity.HasOne(x => x.TskMrb).WithMany(x => x.Tasks).HasForeignKey(x => x.TskMrbId);

            entity.HasOne(x => x.TskParent)
                .WithMany(x => x.TskChildren)
                .HasForeignKey(x => x.TskPartOf)
                .OnDelete(DeleteBehavior.NoAction);

            entity.HasMany(x => x.ClusterCost)
                .WithOne(x => x.ClcTask)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasComment(
                @"Contains maintenance tasks, which are linked to tasks stored in the customers maintenance information system. 
In some places within the code, tasks will be called actions. This is due to the naming conventions used in AMprover 2.6, references to actions will slowly be removed from the code. 
Tasks can be imported directly into the database (manually), or entered by hand in the preventive actions tab within the Risk analysis. 
");

            entity.HasIndex(e => e.TskCluster)
                .HasName("IX_TaskClusterID");

            entity.HasIndex(e => e.TskMrbId)
                .HasName("IX_TaskMrbID");

            entity.Property(e => e.TskId)
                .HasColumnName("TskID")
                .HasComment("Unique ID (PK of Task)");

            entity.Property(e => e.TskCluster).HasComment("Cluster ID the task belongs to (FK to Cluster)");

            entity.Property(e => e.TskClusterCostMember)
                .HasDefaultValueSql("((1))")
                .HasComment(
                    "Boolean that makes the task cluster cost a member (Cluster costs are spread evenly over the tasks that were set as cost member)");

            entity.Property(e => e.TskClusterCostPerUnit)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cost per unit for the task calculated during clustering (?)");

            entity.Property(e => e.TskClusterCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Cost for the task calculated during clustering");

            entity.Property(e => e.TskCommonActionId)
                .HasColumnName("TskCommonActionID")
                .HasComment("ID of the common task this task is a part of (FK to CommonTask)");

            entity.Property(e => e.TskCopiedFrom)
                .HasComment(
                    "Source ID the task was copied from (master task) (FK to Task) Will only be filled for tasks that were created as copies of other tasks. ");

            entity.Property(e => e.TskCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Contains the costs associated with this task. (? total, estimated?)");

            entity.Property(e => e.TskDateInitiated)
                .HasColumnType("smalldatetime")
                .HasComment("Creation date");

            entity.Property(e => e.TskDateModified)
                .HasColumnType("smalldatetime")
                .HasComment("Date the record was last modified. (null if never modified)");

            entity.Property(e => e.TskDerived).HasComment("Is the task derived from another task");

            entity.Property(e => e.TskDescription)
                .IsUnicode(false)
                .HasComment("Description of the task");

            entity.Property(e => e.TskDownTime)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Downtime needed to complete the task (in hours)");

            entity.Property(e => e.TskDuration)
                .HasColumnType("decimal(18, 4)")
                .HasComment("Time spent executing the task (in hours)");

            entity.Property(e => e.TskEstCostPerUnit)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Estimated cost per unit for this task");

            entity.Property(e => e.TskEstCosts)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Estimated cost for the task");

            entity.Property(e => e.TskExecutionDate)
                .HasColumnType("smalldatetime")
                .HasComment("Date when the task must be executed");

            entity.Property(e => e.TskExecutor).HasComment("ID of the executor of the task (FK to LookupExecutor)");

            entity.Property(e => e.TskExtraBool2)
                .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

            entity.Property(e => e.TskExtraBool3)
                .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

            entity.Property(e => e.TskExtraBool4)
                .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

            entity.Property(e => e.TskFinishDate)
                .HasColumnType("smalldatetime")
                .HasComment("Date when the task needs to have been executed");

            entity.Property(e => e.TskFmecaEffect)
                .IsUnicode(false)
                .HasComment(
                    "Effect that the task has for each FMECA column. This field contains an xml representation of an entire fmeca matrix.");

            entity.Property(e => e.TskFmecaEffectPct)
                .HasColumnType("numeric(18, 2)")
                .HasComment("Effect that the task has on each FMECA column (?)");

            entity.Property(e => e.TskFmecaVersion).HasComment(
                "Version of the FMECA matrix that is used. Allows tasks to be defined for different fmeca configurations.");

            entity.Property(e => e.TskGeneralDescription)
                .IsUnicode(false)
                .HasComment("General description of the task");

            entity.Property(e => e.TskInitiatedBy)
                .IsUnicode(false)
                .HasComment("Created by user");

            entity.Property(e => e.TskInitiator)
                .HasComment("ID of the initiator of the task (FK to LookupInitiator)");

            entity.Property(e => e.TskInterval)
                .HasColumnType("decimal(18, 4)")
                .HasComment(
                    "Interval of the task. The interval unit determines what the interval stands for (times yearly, monthly, etc) ");

            entity.Property(e => e.TskIntervalUnit).HasComment(
                "ID of the the interval unit of the task (FK to LookupIntervalUnit). The interval unit determines what the value stored in TskInterval really means.");

            entity.Property(e => e.TskLcceffect)
                .HasColumnName("TskLCCEffect")
                .IsUnicode(false)
                .HasComment(
                    "? Effect that the task has for LCC calculations. This field contains an xml representation of an entire fmeca matrix, but now containing LCC effects for each field in the matrix.");

            entity.Property(e => e.TskMaster)
                .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

            entity.Property(e => e.TskModifiedBy)
                .IsUnicode(false)
                .HasComment("User name of person that made the last modification to this record");

            entity.Property(e => e.TskMrbId)
                .HasColumnName("TskMrbID")
                .HasComment("Risk ID the task belongs to (FK to MRB)");

            entity.Property(e => e.TskMxPolicy)
                .HasComment("ID of the maintenance policy of the task (FK to LookupMxPolicy)");

            entity.Property(e => e.TskName)
                .IsRequired()
                .HasMaxLength(60)
                .IsUnicode(false)
                .HasComment("Name of the task");

            entity.Property(e => e.TskNorm)
                .IsUnicode(false)
                .HasComment("Norm for the task");

            entity.Property(e => e.TskOptimalCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Optimal cost for the task");

            entity.Property(e => e.TskPartOf).HasComment("Task ID of the parent task (FK to Task)");

            entity.Property(e => e.TskPermit)
                .IsUnicode(false)
                .HasComment("Permit(s) needed before being allowed to execute the task");

            entity.Property(e => e.TskPriorityCode)
                .HasComment("? Does not seem to be used by AMprover software (!)");

            entity.Property(e => e.TskReferenceId).HasColumnName("TskReferenceID");

            entity.Property(e => e.TskRemark)
                .IsUnicode(false)
                .HasComment("Remarks of the task");

            entity.Property(e => e.TskRemoved)
                .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

            entity.Property(e => e.TskResponsible)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment(
                    "Person or department responsible for execution of the task. (Does not seem to be used by AMprover software) (!)");

            entity.Property(e => e.TskSkipInLcc)
                .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

            entity.Property(e => e.TskSortOrder)
                .HasComment(
                    "Custom way to order the tasks. User can manually alter the order in which tasks will be executed by changing the numbers of this field. ");

            entity.Property(e => e.TskStatus).HasComment("Status of the task (master or copy)");

            entity.Property(e => e.TskType)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment(
                    "Type of task (task, procedure etc.). The possible types are defined in Lookup, MeasureType. (?)");

            entity.Property(e => e.TskUnitType)
                .HasComment("Unit type ID used for the task (FK to LookupUserDefined)");

            entity.Property(e => e.TskUnits)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Number of units needed for the task");

            entity.Property(e => e.TskValidFromYear).HasComment(
                "The year when the common task becomes valid. (In LCC calculations, items with a ValidFromYear < the current year will not be part of the calculations.)");

            entity.Property(e => e.TskValidUntilYear).HasComment("The last year this task is still valid");

            entity.Property(e => e.TskWorkInspCost)
                .HasColumnType("decimal(18, 2)")
                .HasComment("Costs made for inspection during/for this task");

            entity.Property(e => e.TskWorkpackage)
                .HasComment("ID of the work package the task belongs to (FK to Workpackage)");

            entity.HasOne(d => d.TskClusterNavigation)
                .WithMany(p => p.Task)
                .HasForeignKey(d => d.TskCluster)
                .HasConstraintName("FK_Task_Cluster");

            entity.HasOne(d => d.TskCommonAction)
                .WithMany(p => p.Task)
                .HasForeignKey(d => d.TskCommonActionId)
                .HasConstraintName("FK_TblTask_TblCommonTask")
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(d => d.TskExecutorNavigation)
                .WithMany(p => p.Task)
                .HasForeignKey(d => d.TskExecutor)
                .HasConstraintName("FK_Task_LookupExecutor");

            entity.HasOne(d => d.TskInitiatorNavigation)
                .WithMany(p => p.Task)
                .HasForeignKey(d => d.TskInitiator)
                .HasConstraintName("FK_Task_LookupInitiator");

            entity.HasOne(d => d.TskIntervalUnitNavigation)
                .WithMany(p => p.Task)
                .HasForeignKey(d => d.TskIntervalUnit)
                .HasConstraintName("FK_Task_LookupIntervalUnit");

            entity.HasOne(d => d.TskMxPolicyNavigation)
                .WithMany(p => p.Task)
                .HasForeignKey(d => d.TskMxPolicy)
                .HasConstraintName("FK_Task_LookupMxPolicy");

            entity.HasOne(d => d.TskWorkpackageNavigation)
                .WithMany(p => p.Task)
                .HasForeignKey(d => d.TskWorkpackage)
                .HasConstraintName("FK_Task_Workpackage");

            entity.HasOne(d => d.TskSapaWorkpackageNavigation)
                .WithMany(p => p.Task)
                .HasForeignKey(d => d.TskSapaWorkpackage)
                .HasConstraintName("FK_Task_SapaWorkpackage");
        });

        modelBuilder.Entity<Workpackage>(entity =>
        {
            entity.HasKey(e => e.WpId)
                .HasName("PK_Werkpakket");

            entity.HasComment(
                "Contains workpackages, which are containers that are used to group tasks that can be more efficiently executed together. (for example daily and weekly tasks). Workpackages are created from the masterdata. ");

            entity.Property(e => e.WpId)
                .HasColumnName("WpID")
                .HasComment("Unique ID (PK of WorkPackage)");

            entity.Property(e => e.WpDescription)
                .IsUnicode(false)
                .HasComment("Description of the work package");

            entity.Property(e => e.WpExecutor).HasComment("ID of the executor (FK to LookupExecutor)");

            entity.Property(e => e.WpInterval)
                .HasColumnType("decimal(18, 2)")
                .HasComment(
                    "Interval of the work package. (a number that, combined with the interval unit, shows with what interval the work package needs to be executed)");

            entity.Property(e => e.WpIntervalUnit)
                .HasComment("ID of the the interval unit of the work package (FK to LookupIntervalUnit)");

            entity.Property(e => e.WpName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Name of the work package");

            entity.Property(e => e.WpShortDescription)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasComment("Short key of the work package");

            entity.HasOne(d => d.WpExecutorNavigation)
                .WithMany(p => p.Workpackage)
                .HasForeignKey(d => d.WpExecutor)
                .HasConstraintName("FK_Workpackage_LookupExecutor");

            entity.HasOne(d => d.WpIntervalUnitNavigation)
                .WithMany(p => p.Workpackage)
                .HasForeignKey(d => d.WpIntervalUnit)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Workpackage_LookupIntervalUnit");
        });

        modelBuilder.Entity<VwSiFilter>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("vwSiFilter");

            entity.Property(e => e.PartOfName)
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Siassetmanager)
                .HasColumnName("siassetmanager")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.Siassetowner)
                .HasColumnName("siassetowner")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.Siassettype)
                .HasColumnName("siassettype")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Sibvid).HasColumnName("sibvid");

            entity.Property(e => e.Sicategory).HasColumnName("sicategory");

            entity.Property(e => e.Sicontractend)
                .HasColumnName("sicontractend")
                .HasColumnType("datetime");

            entity.Property(e => e.Sidatemodified)
                .HasColumnName("sidatemodified")
                .HasColumnType("smalldatetime");

            entity.Property(e => e.Sidescription)
                .HasColumnName("sidescription")
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.Property(e => e.Siheight)
                .HasColumnName("siheight")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Siid).HasColumnName("siid");

            entity.Property(e => e.Silength)
                .HasColumnName("silength")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Silocation)
                .HasColumnName("silocation")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.Simiscbitfield1).HasColumnName("simiscbitfield1");

            entity.Property(e => e.Simiscdatefield1)
                .HasColumnName("simiscdatefield1")
                .HasColumnType("datetime");

            entity.Property(e => e.Simiscdecimalfield1)
                .HasColumnName("simiscdecimalfield1")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Simiscdecimalfield2)
                .HasColumnName("simiscdecimalfield2")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Simisctextfield1)
                .HasColumnName("simisctextfield1")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Simisctextfield2)
                .HasColumnName("simisctextfield2")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Simisctextfield3)
                .HasColumnName("simisctextfield3")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Simisctextfield4)
                .HasColumnName("simisctextfield4")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Simisctextfield5)
                .HasColumnName("simisctextfield5")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Simisctextfield6)
                .HasColumnName("simisctextfield6")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Simisctextfield7)
                .HasColumnName("simisctextfield7")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Simodifiedby)
                .HasColumnName("simodifiedby")
                .IsUnicode(false);

            entity.Property(e => e.Simtbf)
                .HasColumnName("simtbf")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Siname)
                .IsRequired()
                .HasColumnName("siname")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Sipartof).HasColumnName("sipartof");

            entity.Property(e => e.Siprice)
                .HasColumnName("siprice")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Siqualityscore).HasColumnName("siqualityscore");

            entity.Property(e => e.Sireferenceid)
                .HasColumnName("sireferenceid")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Siremarks)
                .HasColumnName("siremarks")
                .IsUnicode(false);

            entity.Property(e => e.Siserialnumber)
                .HasColumnName("siserialnumber")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Siservicemanager)
                .HasColumnName("siservicemanager")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.Siserviceprovider)
                .HasColumnName("siserviceprovider")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.Sisite)
                .HasColumnName("sisite")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Sistatus).HasColumnName("sistatus");

            entity.Property(e => e.Sisupplierbitfield1).HasColumnName("sisupplierbitfield1");

            entity.Property(e => e.Sisupplierdatefield1)
                .HasColumnName("sisupplierdatefield1")
                .HasColumnType("datetime");

            entity.Property(e => e.Sisupplierintfield1).HasColumnName("sisupplierintfield1");

            entity.Property(e => e.Sisuppliertextfield1)
                .HasColumnName("sisuppliertextfield1")
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.Sisuppliertextfield2)
                .HasColumnName("sisuppliertextfield2")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Sisuppliertextfield3)
                .HasColumnName("sisuppliertextfield3")
                .HasMaxLength(40)
                .IsUnicode(false);

            entity.Property(e => e.Sitotalunits)
                .HasColumnName("sitotalunits")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Sitype)
                .HasColumnName("sitype")
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.Siunits)
                .HasColumnName("siunits")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Siunitstypevalues)
                .HasColumnName("siunitstypevalues")
                .HasMaxLength(150)
                .IsUnicode(false);

            entity.Property(e => e.Siunittype)
                .HasColumnName("siunittype")
                .HasMaxLength(15)
                .IsUnicode(false);

            entity.Property(e => e.Sivendor)
                .HasColumnName("sivendor")
                .HasMaxLength(30)
                .IsUnicode(false);

            entity.Property(e => e.Siwarrantyperiod).HasColumnName("siwarrantyperiod");

            entity.Property(e => e.Siwidth)
                .HasColumnName("siwidth")
                .HasColumnType("decimal(18, 2)");

            entity.Property(e => e.Siyear)
                .HasColumnName("siyear")
                .HasColumnType("datetime");
        });
    }
}