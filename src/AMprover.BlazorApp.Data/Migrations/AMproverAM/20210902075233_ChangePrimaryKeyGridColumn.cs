using Microsoft.EntityFrameworkCore.Migrations;

namespace AMprover.Data.Migrations.AMproverAM;

public partial class ChangePrimaryKeyGridColumn : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        //Remove duplicate data before changing primary key. We need to change primary to allow for column header changes
        migrationBuilder.Sql(
            @"With duplicates As (Select *, ROW_NUMBER() Over (PARTITION by [ControlName],[FieldName],[ColumnName] Order by [ControlName],[FieldName],[ColumnName]) as Duplicate From LookupGridColumn)
                delete From duplicates
                Where Duplicate > 1 ;");

        migrationBuilder.DropPrimaryKey(
            "PK_LookupGridColumn_ID",
            "LookupGridColumn");

        migrationBuilder.AlterColumn<string>(
            "ColumnHeader",
            "LookupGridColumn",
            "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200);

        migrationBuilder.AddPrimaryKey(
            "PK_LookupGridColumn_ID",
            "LookupGridColumn",
            new[] { "ControlName", "FieldName", "ColumnName" });
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropPrimaryKey(
            "PK_LookupGridColumn_ID",
            "LookupGridColumn");

        migrationBuilder.AlterColumn<string>(
            "ColumnHeader",
            "LookupGridColumn",
            "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AddPrimaryKey(
            "PK_LookupGridColumn_ID",
            "LookupGridColumn",
            new[] { "ControlName", "FieldName", "ColumnName", "ColumnHeader" });
    }
}