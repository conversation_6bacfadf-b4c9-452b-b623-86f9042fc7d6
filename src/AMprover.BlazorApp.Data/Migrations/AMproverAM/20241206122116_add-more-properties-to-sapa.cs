using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class addmorepropertiestosapa : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "SapaYearBudgetApproved",
                table: "TblSapaYear",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SapaYearBudgetRequest",
                table: "TblSapaYear",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "SapaDetMotivation",
                table: "TblSapaDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "SapaApproveForAllYears",
                table: "TblSapa",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "SapaBudgetApproved",
                table: "TblSapa",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SapaBudgetRequest",
                table: "TblSapa",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "SapaCbiItem",
                table: "TblSapa",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "SapaCbiScore",
                table: "TblSapa",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "SapaName",
                table: "TblSapa",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SapaRemark",
                table: "TblSapa",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SapaYearBudgetApproved",
                table: "TblSapaYear");

            migrationBuilder.DropColumn(
                name: "SapaYearBudgetRequest",
                table: "TblSapaYear");

            migrationBuilder.DropColumn(
                name: "SapaDetMotivation",
                table: "TblSapaDetail");

            migrationBuilder.DropColumn(
                name: "SapaApproveForAllYears",
                table: "TblSapa");

            migrationBuilder.DropColumn(
                name: "SapaBudgetApproved",
                table: "TblSapa");

            migrationBuilder.DropColumn(
                name: "SapaBudgetRequest",
                table: "TblSapa");

            migrationBuilder.DropColumn(
                name: "SapaCbiItem",
                table: "TblSapa");

            migrationBuilder.DropColumn(
                name: "SapaCbiScore",
                table: "TblSapa");

            migrationBuilder.DropColumn(
                name: "SapaName",
                table: "TblSapa");

            migrationBuilder.DropColumn(
                name: "SapaRemark",
                table: "TblSapa");
        }
    }
}
