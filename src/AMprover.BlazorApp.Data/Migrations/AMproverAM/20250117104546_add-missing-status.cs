using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class addmissingstatus : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Manual Change:
            migrationBuilder.Sql(@"
                IF NOT EXISTS (
                    SELECT lookupId
                    FROM Lookup
                    WHERE LookupFilter = 'StatusTypes' and LookupShortDescription = 'Budgeting'
                )
                BEGIN
                    INSERT INTO Lookup (LookupFilter, LookupShortDescription, LookupLongDescription, LookupValue)
                    VALUES ('StatusTypes', 'Budgeting', 'Budgeting', 6);
                END;");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Manual Change:
            migrationBuilder.Sql("delete from Lookup where LookupFilter = 'StatusTypes' and LookupShortDescription = 'Budgeting'");
        }
    }
}
