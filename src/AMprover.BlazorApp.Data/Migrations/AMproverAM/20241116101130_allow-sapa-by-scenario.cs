using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class allowsapabyscenario : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TblSapa_SapaRiskObjId",
                table: "TblSapa");

            migrationBuilder.AlterColumn<int>(
                name: "SapaRiskObjId",
                table: "TblSapa",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<int>(
                name: "SapaScenarioId",
                table: "TblSapa",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TblSapa_SapaRiskObjId",
                table: "TblSapa",
                column: "SapaRiskObjId",
                unique: true,
                filter: "[SapaRiskObjId] IS NOT NULL");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TblSapa_SapaRiskObjId",
                table: "TblSapa");

            migrationBuilder.DropColumn(
                name: "SapaScenarioId",
                table: "TblSapa");

            migrationBuilder.AlterColumn<int>(
                name: "SapaRiskObjId",
                table: "TblSapa",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TblSapa_SapaRiskObjId",
                table: "TblSapa",
                column: "SapaRiskObjId",
                unique: true);
        }
    }
}
