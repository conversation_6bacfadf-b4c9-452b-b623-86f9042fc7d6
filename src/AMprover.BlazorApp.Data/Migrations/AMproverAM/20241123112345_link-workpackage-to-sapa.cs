using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class linkworkpackagetosapa : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "SapaWorkPackageId",
                table: "TblSapa",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TblSapa_SapaWorkPackageId",
                table: "TblSapa",
                column: "SapaWorkPackageId");

            migrationBuilder.AddForeignKey(
                name: "FK_Sapa_WorkPackage",
                table: "TblSapa",
                column: "SapaWorkPackageId",
                principalTable: "TblWorkPackage",
                principalColumn: "WpID",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Sapa_WorkPackage",
                table: "TblSapa");

            migrationBuilder.DropIndex(
                name: "IX_TblSapa_SapaWorkPackageId",
                table: "TblSapa");

            migrationBuilder.DropColumn(
                name: "SapaWorkPackageId",
                table: "TblSapa");
        }
    }
}
