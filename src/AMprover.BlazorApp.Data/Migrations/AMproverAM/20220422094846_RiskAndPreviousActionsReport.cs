using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class RiskAndPreviousActionsReport : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //Cluster report SP
            var sqlGetRiskAndPreviousActionsReport = @"
	 	    	IF OBJECT_ID('GetRiskAndPreviousActionsReport', 'P') IS NOT NULL
                DROP PROC GetRiskAndPreviousActionsReport
                GO
     
                CREATE PROCEDURE [dbo].[GetRiskAndPreviousActionsReport]
                    @FilterChildObject0 int = NULL,
                    @FilterChildObject1 int = NULL,
                    @FilterChildObject2 int = NULL,
                    @FilterScenario int = NULL
                AS
                BEGIN
                    SELECT MRBId, MrbName, MrbDescription, Scenario, Object, RiskObject, RiskObjName, RiskObjNoOfInstallation, Object1, Object2, Object3, <PERSON>ailMode, TskID, TskName, TskDescription, TskGeneralDescription, TskRemark, TskCosts, TskMxPolicy, 
                  TskInterval, TskIntervalUnit, TskExecutor, TskInitiator, TskWorkPackage, TskDuration, TskDownTime, TskType, TskUnitType, TskUnits, TskCostPerUnit, TskCost, UserDefinedFilter, MrbFailureMode, MrbFailureCause, 
                  MrbFailureConsequences, MrbFailureCategorie1, MrbFailureCategorie2, MrbDownTimeAfter, MrbDownTimeBefore, MrbRemarks, MrbOptimalCosts, MrbActionCosts, MrbSpareCosts, MrbSpareManageCost, MrbMasterID, 
                  MrbResponsible, MrbNorm, MrbState, MrbEffectBefore, MrbCustomBefore, MrbMtbfBefore, MrbRiskBefore, MrbEffectAfter, MrbMtbfAfter, MrbRiskAfter, MrbCustomAfter, MrbDirectCostAfter, MrbCustomEffectBefore, 
                  MrbCustomEffectAfter, MrbDirectCostBefore, TskCopiedFrom, TskValidFromYear, TskSortOrder, TskPermit, TskClustID, TskClustName, TskOptimalCost, TskFinishDate, TskValidUntilYear, TskExecutionDate, TskCommonActionID, 
                  TskFmecaEffectPct, TskClusterCostPerUnit, TskClusterCosts, TskPartOf, TskNorm, TskClusterCostMember, MrbStatus
FROM     (SELECT TOP (2147483647) TblMRB.MRBId, TblMRB.MrbName, TblMRB.MrbDescription, TblScenario.ScenName AS Scenario, TblObject_6.ObjName AS Object, TblObject.ObjName AS RiskObject, TblRiskObject.RiskObjName, 
                TblRiskObject.RiskObjNoOfInstallation, TblMRB.MrbStatus, TblObject_3.ObjName AS Object1, TblObject_4.ObjName AS Object2, TblObject_5.ObjName AS Object3, LookupFailMode.FailMode, TblTask.TskID, TblTask.TskName, 
                TblTask.TskDescription, TblTask.TskGeneralDescription, TblTask.TskRemark, TblTask.TskCosts, LookupMxPolicy.PolName AS TskMxPolicy, TblTask.TskInterval, LookupIntervalUnit.IntUnitName AS TskIntervalUnit, 
                LookupExecutor.ExecutorName AS TskExecutor, LookupInitiator.InitiatorName AS TskInitiator, TblWorkpackage.WpName AS TskWorkPackage, TblTask.TskDuration, TblTask.TskDownTime, TblTask.TskType, 
                LookupUserDefined.UserDefinedShortDescription AS TskUnitType, TblTask.TskUnits, TblTask.TskEstCostPerUnit AS TskCostPerUnit, TblTask.TskEstCosts AS TskCost, LookupUserDefined.UserDefinedFilter, 
                TblMRB.MrbFailureMode, TblMRB.MrbFailureCause, TblMRB.MrbFailureConsequences, TblMRB.MrbFailureCategorie1, TblMRB.MrbFailureCategorie2, TblMRB.MrbDownTimeAfter, TblMRB.MrbDownTimeBefore, 
                TblMRB.MrbRemarks, TblMRB.MrbOptimalCosts, TblMRB.MrbActionCosts, TblMRB.MrbSpareCosts, TblMRB.MrbSpareManageCost, TblMRB.MrbMasterID, TblMRB.MrbResponsible, TblMRB.MrbNorm, TblMRB.MrbState, 
                TblMRB.MrbEffectBefore, TblMRB.MrbCustomBefore, TblMRB.MrbMtbfBefore, TblMRB.MrbRiskBefore, TblMRB.MrbEffectAfter, TblMRB.MrbMtbfAfter, TblMRB.MrbRiskAfter, TblMRB.MrbCustomAfter, 
                TblMRB.MrbDirectCostAfter, TblMRB.MrbCustomEffectBefore, TblMRB.MrbCustomEffectAfter, TblMRB.MrbDirectCostBefore, TblTask.TskStatus, TblTask.TskCopiedFrom, TblTask.TskValidFromYear, TblTask.TskSortOrder, 
                TblTask.TskPermit, TblTask.TskCluster AS TskClustID, TblCluster.ClustName AS TskClustName, TblTask.TskOptimalCost, TblTask.TskFinishDate, TblTask.TskValidUntilYear, TblTask.TskExecutionDate, 
                TblTask.TskCommonActionID, TblTask.TskFmecaEffectPct, TblTask.TskClusterCostPerUnit, TblTask.TskClusterCosts, TblTask.TskPartOf, TblTask.TskNorm, TblTask.TskClusterCostMember
                FROM TblWorkpackage RIGHT OUTER JOIN
                TblTask LEFT OUTER JOIN
                TblCluster ON TblTask.TskCluster = TblCluster.ClustID LEFT OUTER JOIN
                LookupUserDefined ON TblTask.TskUnitType = LookupUserDefined.UserDefinedValue LEFT OUTER JOIN
                LookupExecutor ON TblTask.TskExecutor = LookupExecutor.ExecutorID LEFT OUTER JOIN
                LookupInitiator ON TblTask.TskInitiator = LookupInitiator.InitiatorID LEFT OUTER JOIN
                LookupIntervalUnit ON TblTask.TskIntervalUnit = LookupIntervalUnit.IntUnitID ON TblWorkpackage.WpID = TblTask.TskWorkpackage LEFT OUTER JOIN
                LookupMxPolicy ON TblTask.TskMxPolicy = LookupMxPolicy.PolID RIGHT OUTER JOIN
                TblObject AS TblObject_6 RIGHT OUTER JOIN
                TblMRB LEFT OUTER JOIN
                LookupFailMode ON TblMRB.MrbFailureMode = LookupFailMode.FailID LEFT OUTER JOIN
                TblObject AS TblObject_5 ON TblMRB.MrbChildObject4 = TblObject_5.ObjID LEFT OUTER JOIN
                TblObject AS TblObject_4 ON TblMRB.MrbChildObject3 = TblObject_4.ObjID LEFT OUTER JOIN
                TblObject AS TblObject_3 ON TblMRB.MrbChildObject2 = TblObject_3.ObjID LEFT OUTER JOIN
                TblScenario INNER JOIN
                TblRiskObject ON TblScenario.ScenID = TblRiskObject.RiskObjScenarioID INNER JOIN
                TblObject ON TblRiskObject.RiskObjObjectID = TblObject.ObjID ON TblMRB.MrbRiskObject = TblRiskObject.RiskObjID ON TblObject_6.ObjID = TblRiskObject.RiskObjParentObjectID ON TblTask.TskMrbID = TblMRB.MRBId
                WHERE (LookupUserDefined.UserDefinedFilter = 'UnitTypes') 
                AND (TblTask.TskRemoved IS NULL OR TblTask.TskRemoved = 0)
                AND (@FilterChildObject0 IS NULL OR TblMRB.MrbChildObject = @FilterChildObject0)
                AND (@FilterChildObject1 IS NULL OR TblMRB.MrbChildObject1 = @FilterChildObject1)
                AND (@FilterChildObject2 IS NULL OR TblMRB.MrbChildObject2 = @FilterChildObject2)
                AND (@FilterScenario IS NULL OR TblRiskObject.RiskObjScenarioID = @FilterScenario)
                ORDER BY Scenario, Object, RiskObject, Object1, Object2, Object3) AS derivedtbl_1
                END";

            migrationBuilder.Sql(sqlGetRiskAndPreviousActionsReport);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
        }
    }
}
