using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class add_properties_to_si : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "SiAction",
                table: "TblSi",
                type: "varchar(50)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "SiCapexEst",
                table: "TblSi",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SiCapexInd",
                table: "TblSi",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SiCapexPercentage",
                table: "TblSi",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "SiCondEcon",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SiCondQual",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SiCondShe",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SiCondTechn",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SiCondYear",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "SiRemark",
                table: "TblSi",
                type: "varchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SiReplYearCalc",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SiReplYearManual",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SiReplYearReal",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SiReplYearTechnLt",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "SiRestLifeTimeAv",
                table: "TblSi",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SiRestLifeTimeCb",
                table: "TblSi",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SiRestLifeTimeTh",
                table: "TblSi",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SiTechLifeTime",
                table: "TblSi",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "SiUrgency",
                table: "TblSi",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SiAction",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiCapexEst",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiCapexInd",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiCapexPercentage",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiCondEcon",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiCondQual",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiCondShe",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiCondTechn",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiCondYear",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiRemark",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiReplYearCalc",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiReplYearManual",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiReplYearReal",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiReplYearTechnLt",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiRestLifeTimeAv",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiRestLifeTimeCb",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiRestLifeTimeTh",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiTechLifeTime",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiUrgency",
                table: "TblSi");
        }
    }
}
