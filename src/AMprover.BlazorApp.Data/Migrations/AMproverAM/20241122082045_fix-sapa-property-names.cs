using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class fixsapapropertynames : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "SapaStatus",
                table: "TblSapa",
                newName: "SapaStatusId");

            migrationBuilder.RenameColumn(
                name: "SapaInitiator",
                table: "TblSapa",
                newName: "SapaInitiatorId");

            migrationBuilder.RenameColumn(
                name: "SapaExecutor",
                table: "TblSapa",
                newName: "SapaExecutorId");

            migrationBuilder.RenameIndex(
                name: "IX_TblSapa_SapaInitiator",
                table: "TblSapa",
                newName: "IX_TblSapa_SapaInitiatorId");

            migrationBuilder.RenameIndex(
                name: "IX_TblSapa_SapaExecutor",
                table: "TblSapa",
                newName: "IX_TblSapa_SapaExecutorId");

            // Manual Change:
            var sql = "delete from LookupGridColumn where ControlName in ('Sapa_Overview', 'Sapa_Details')";
            migrationBuilder.Sql(sql);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "SapaStatusId",
                table: "TblSapa",
                newName: "SapaStatus");

            migrationBuilder.RenameColumn(
                name: "SapaInitiatorId",
                table: "TblSapa",
                newName: "SapaInitiator");

            migrationBuilder.RenameColumn(
                name: "SapaExecutorId",
                table: "TblSapa",
                newName: "SapaExecutor");

            migrationBuilder.RenameIndex(
                name: "IX_TblSapa_SapaInitiatorId",
                table: "TblSapa",
                newName: "IX_TblSapa_SapaInitiator");

            migrationBuilder.RenameIndex(
                name: "IX_TblSapa_SapaExecutorId",
                table: "TblSapa",
                newName: "IX_TblSapa_SapaExecutor");
        }
    }
}
