using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class Fixcircuitaffectedcostplumbing : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CircuitAffectedCostPmo",
                table: "TblMRB",
                newName: "MrbCircuitAffectedCostPmo");

            migrationBuilder.RenameColumn(
                name: "CircuitAffectedCostBefore",
                table: "TblMRB",
                newName: "MrbCircuitAffectedCostBefore");

            migrationBuilder.RenameColumn(
                name: "CircuitAffectedCostAfter",
                table: "TblMRB",
                newName: "MrbCircuitAffectedCostAfter");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "MrbCircuitAffectedCostPmo",
                table: "TblMRB",
                newName: "CircuitAffectedCostPmo");

            migrationBuilder.RenameColumn(
                name: "MrbCircuitAffectedCostBefore",
                table: "TblMRB",
                newName: "CircuitAffectedCostBefore");

            migrationBuilder.RenameColumn(
                name: "MrbCircuitAffectedCostAfter",
                table: "TblMRB",
                newName: "CircuitAffectedCostAfter");
        }
    }
}
