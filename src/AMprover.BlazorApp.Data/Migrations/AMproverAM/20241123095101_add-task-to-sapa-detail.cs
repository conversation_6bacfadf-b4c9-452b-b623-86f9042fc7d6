using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class addtasktosapadetail : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Manual cleanup:
            var sql = "delete from tblSapa; delete from TblSapaYear; delete from tblSapaDetail;";
            migrationBuilder.Sql(sql);

            migrationBuilder.AddColumn<int>(
                name: "SapaDetTskId",
                table: "TblSapaDetail",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_TblSapaDetail_SapaDetTskId",
                table: "TblSapaDetail",
                column: "SapaDetTskId");

            migrationBuilder.AddForeignKey(
                name: "FK_SapaDetail_Task",
                table: "TblSapaDetail",
                column: "SapaDetTskId",
                principalTable: "TblTask",
                principalColumn: "TskID",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SapaDetail_Task",
                table: "TblSapaDetail");

            migrationBuilder.DropIndex(
                name: "IX_TblSapaDetail_SapaDetTskId",
                table: "TblSapaDetail");

            migrationBuilder.DropColumn(
                name: "SapaDetTskId",
                table: "TblSapaDetail");
        }
    }
}
