using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class addattachmenttable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "TblAttachment",
                columns: table => new
                {
                    AtchId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AtchTitle = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AtchUri = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AtchSapaId = table.Column<int>(type: "int", nullable: true),
                    AtchMrbId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TblAttachment", x => x.AtchId);
                    table.ForeignKey(
                        name: "FK_TblAttachment_TblMRB_AtchMrbId",
                        column: x => x.AtchMrbId,
                        principalTable: "TblMRB",
                        principalColumn: "MRBId",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_TblAttachment_TblSapa_AtchSapaId",
                        column: x => x.AtchSapaId,
                        principalTable: "TblSapa",
                        principalColumn: "SapaId",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TblAttachment_AtchMrbId",
                table: "TblAttachment",
                column: "AtchMrbId");

            migrationBuilder.CreateIndex(
                name: "IX_TblAttachment_AtchSapaId",
                table: "TblAttachment",
                column: "AtchSapaId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TblAttachment");
        }
    }
}
