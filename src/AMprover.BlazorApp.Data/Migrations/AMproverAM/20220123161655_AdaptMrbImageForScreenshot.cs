using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class AdaptMrbImageForScreenshot : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<byte[]>(
                "TblMrbImageImageAfter",
                "TblMrbImage",
                "varbinary(max)",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "image",
                oldNullable: false);
            
            migrationBuilder.AlterColumn<byte[]>(
                "TblMrbImageImageBefore",
                "TblMrbImage",
                "varbinary(max)",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "image",
                oldNullable: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
        }
    }
}
