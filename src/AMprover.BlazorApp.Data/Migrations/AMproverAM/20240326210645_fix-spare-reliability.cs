using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class fixsparereliability : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // When Migrating from AMprover 4 to 5 the SpareReliability Percentage gets converted incorrectly.
            // As discussed with <PERSON> we will just multiply all values between 0 and 1 by 100, since such a low reliability does not make sense
            migrationBuilder.Sql(@"
                    update tblSpare 
                    set SpareReliability = SpareReliability * 100 
                    where SpareReliability is not null and SpareReliability > 0 and SpareReliability < 1");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
