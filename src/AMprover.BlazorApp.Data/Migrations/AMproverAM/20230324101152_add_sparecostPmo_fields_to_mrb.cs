using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class add_sparecostPmo_fields_to_mrb : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "MrbSpareCostsPmo",
                table: "TblMRB",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MrbSpareManageCostPmo",
                table: "TblMRB",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MrbSpareCostsPmo",
                table: "TblMRB");

            migrationBuilder.DropColumn(
                name: "MrbSpareManageCostPmo",
                table: "TblMRB");
        }
    }
}
