using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class allowmultiplesapaperriskobject : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TblSapa_SapaRiskObjId",
                table: "TblSapa");

            migrationBuilder.CreateIndex(
                name: "IX_TblSapa_SapaRiskObjId",
                table: "TblSapa",
                column: "SapaRiskObjId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TblSapa_SapaRiskObjId",
                table: "TblSapa");

            migrationBuilder.CreateIndex(
                name: "IX_TblSapa_SapaRiskObjId",
                table: "TblSapa",
                column: "SapaRiskObjId",
                unique: true,
                filter: "[SapaRiskObjId] IS NOT NULL");
        }
    }
}
