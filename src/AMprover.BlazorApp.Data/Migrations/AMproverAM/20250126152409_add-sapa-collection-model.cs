using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class addsapacollectionmodel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Manual Edit
            migrationBuilder.Sql("delete from tblSapa");

            migrationBuilder.AddColumn<int>(
                name: "SapaCollectionId",
                table: "TblSapa",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "TblSapaCollection",
                columns: table => new
                {
                    SapaCollId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SapaCollName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SapaCollDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SapaCollScenarioId = table.Column<int>(type: "int", nullable: true),
                    SapaCollRiskObjId = table.Column<int>(type: "int", nullable: true),
                    ScenarioScenId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SapaCollection", x => x.SapaCollId);
                    table.ForeignKey(
                        name: "FK_SapaCollection_RiskObject",
                        column: x => x.SapaCollRiskObjId,
                        principalTable: "TblRiskObject",
                        principalColumn: "RiskObjID");
                    table.ForeignKey(
                        name: "FK_TblSapaCollection_TblScenario_ScenarioScenId",
                        column: x => x.ScenarioScenId,
                        principalTable: "TblScenario",
                        principalColumn: "ScenID");
                });

            migrationBuilder.CreateIndex(
                name: "IX_TblSapa_SapaCollectionId",
                table: "TblSapa",
                column: "SapaCollectionId");

            migrationBuilder.CreateIndex(
                name: "IX_TblSapaCollection_SapaCollRiskObjId",
                table: "TblSapaCollection",
                column: "SapaCollRiskObjId");

            migrationBuilder.CreateIndex(
                name: "IX_TblSapaCollection_ScenarioScenId",
                table: "TblSapaCollection",
                column: "ScenarioScenId");

            migrationBuilder.AddForeignKey(
                name: "FK_TblSapa_TblSapaCollection_SapaCollectionId",
                table: "TblSapa",
                column: "SapaCollectionId",
                principalTable: "TblSapaCollection",
                principalColumn: "SapaCollId",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TblSapa_TblSapaCollection_SapaCollectionId",
                table: "TblSapa");

            migrationBuilder.DropTable(
                name: "TblSapaCollection");

            migrationBuilder.DropIndex(
                name: "IX_TblSapa_SapaCollectionId",
                table: "TblSapa");

            migrationBuilder.DropColumn(
                name: "SapaCollectionId",
                table: "TblSapa");
        }
    }
}
