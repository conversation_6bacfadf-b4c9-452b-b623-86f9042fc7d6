using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class ClusterReportSP : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //Cluster report SP
            var sqlClusterReport = @"
	 	    	IF OBJECT_ID('GetClusterReport', 'P') IS NOT NULL
                DROP PROC GetClusterReport
                GO
     
                CREATE PROCEDURE [dbo].[GetClusterReport]
                AS
                BEGIN
                    SET NOCOUNT ON;
                    SELECT ClustID as ClustId, 
                    ClustName, 
                    ClustDescription, ClustLevel, 
                    ClustPartOf, 
                    ClustInterval, 
                    ClustIntervalUnit, 
                    ClustInitiator, 
                    ClustExecutor, 
                    ClustEstTaskCosts, 
                    ClustTaskCosts, 
                    ClustSharedCosts, 
                    ClustDisciplineCosts, 
                    ClustMaterialCosts, 
                    ClustEnergyCosts, 
                    ClustToolCosts, 
                    ClustTotalCmnCost, 
                    ClustDownTime, 
                    ClustDuration, 
                    ClustStatus, 
                    ClustShortKey, 
                    ClustRemark, 
                    ClustResponsible, 
                    ClustSecondValues, 
                    ClustCostDescr, 
                    ClustCostTaskID as ClustCostTaskId, 
                    ClustCostQuantity, 
                    ClustCostUnits, 
                    ClustCostPrice, 
                    ClustCostCost, 
                    ClustCostRemarks, 
                    TskID as TskId, 
                    TskName, 
                    TskGeneralDescription, 
                    TskRemark, 
                    TskDescription, 
                    TskInterval, 
                    TskIntervalUnit, 
                    TskWorkPackage, 
                    TskPolicy, 
                    TskExecutor, 
                    TskInitiator, 
                    TskMrbID as TskMrbId, 
                    TskCosts, 
                    TskDuration, 
                    TskDownTime, 
                    TskType, 
                    TskCostTskID as TskCostTskId, 
                    TskCostUnits, 
                    TskCostQuantity, 
                    TskCostPrice, 
                    TskCostCost, 
                    TskCostDescription, 
                    Scenario, 
                    RiskObject, 
                    MrbObject2, 
                    MrbObject3, 
                    MrbObject4, 
                    ClcID as ClcId, 
                    MrbID as MrbId, 
                    CmnCostType, 
                    RiskObjName, 
                    MrbObject0, 
                    RiskObjectDesc, 
                    TskSortOrder, 
                    MrbFailureCause, 
                    MrbFailureConsequences, 
                    ParentClusterName, 
                    TskRemoved, 
                    TskDerived, 
                    TskPermit, 
                    ClustName0, 
                    ClustName1, 
                    ClustName2, 
                    ClustName3
                    FROM ClusterReport As Cluster
                END";

            migrationBuilder.Sql(sqlClusterReport);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
        }
    }
}