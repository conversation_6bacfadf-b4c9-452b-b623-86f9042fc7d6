using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class FixUserFieldsLengthContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.DropPrimaryKey(
            //     name: "PK_TblPriority",
            //     table: "TblPriority");
            //
            // migrationBuilder.DropPrimaryKey(
            //     name: "PK_PriorityBudget",
            //     table: "PriorityBudget");
            //
            // migrationBuilder.DropPrimaryKey(
            //     name: "PK_OpexData",
            //     table: "OpexData");
            //
            // migrationBuilder.RenameTable(
            //     name: "TblPriority",
            //     newName: "Priority");
            //
            // migrationBuilder.RenameTable(
            //     name: "Rams",
            //     newName: "TblRams");
            //
            // migrationBuilder.RenameTable(
            //     name: "PriorityBudget",
            //     newName: "TblPriorityBudget");
            //
            // migrationBuilder.RenameTable(
            //     name: "OpexData",
            //     newName: "TblOpexData");
            //
            // migrationBuilder.RenameTable(
            //     name: "Amprover3_TblPickSI",
            //     newName: "Amprover3_PickSI");
            //
            // migrationBuilder.RenameTable(
            //     name: "Amprover3_TblEditLog",
            //     newName: "Amprover3_EditLog");
            //
            // migrationBuilder.RenameIndex(
            //     name: "IX_Rams_RamsSiID",
            //     table: "TblRams",
            //     newName: "IX_TblRams_RamsSiID");

            migrationBuilder.AlterColumn<string>(
                name: "UserModifiedBy",
                table: "TblUser",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(max)",
                oldUnicode: false,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "UserInitiatedBy",
                table: "TblUser",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(max)",
                oldUnicode: false,
                oldNullable: true,
                oldComment: "Created by user");

            // migrationBuilder.AddPrimaryKey(
            //     name: "PK_Priority",
            //     table: "Priority",
            //     column: "PrioID");
            //
            // migrationBuilder.AddPrimaryKey(
            //     name: "PK_TblPriorityBudget",
            //     table: "TblPriorityBudget",
            //     column: "PrioBudID");
            //
            // migrationBuilder.AddPrimaryKey(
            //     name: "PK_TblOpexData",
            //     table: "TblOpexData",
            //     column: "OpexDataID");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.DropPrimaryKey(
            //     name: "PK_TblPriorityBudget",
            //     table: "TblPriorityBudget");
            //
            // migrationBuilder.DropPrimaryKey(
            //     name: "PK_TblOpexData",
            //     table: "TblOpexData");
            //
            // migrationBuilder.DropPrimaryKey(
            //     name: "PK_Priority",
            //     table: "Priority");
            //
            // migrationBuilder.RenameTable(
            //     name: "TblRams",
            //     newName: "Rams");
            //
            // migrationBuilder.RenameTable(
            //     name: "TblPriorityBudget",
            //     newName: "PriorityBudget");
            //
            // migrationBuilder.RenameTable(
            //     name: "TblOpexData",
            //     newName: "OpexData");
            //
            // migrationBuilder.RenameTable(
            //     name: "Priority",
            //     newName: "TblPriority");
            //
            // migrationBuilder.RenameTable(
            //     name: "Amprover3_PickSI",
            //     newName: "Amprover3_TblPickSI");
            //
            // migrationBuilder.RenameTable(
            //     name: "Amprover3_EditLog",
            //     newName: "Amprover3_TblEditLog");
            //
            // migrationBuilder.RenameIndex(
            //     name: "IX_TblRams_RamsSiID",
            //     table: "Rams",
            //     newName: "IX_Rams_RamsSiID");

            migrationBuilder.AlterColumn<string>(
                name: "UserModifiedBy",
                table: "TblUser",
                type: "varchar(max)",
                unicode: false,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "UserInitiatedBy",
                table: "TblUser",
                type: "varchar(max)",
                unicode: false,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Created by user");

            // migrationBuilder.AddPrimaryKey(
            //     name: "PK_PriorityBudget",
            //     table: "PriorityBudget",
            //     column: "PrioBudID");
            //
            // migrationBuilder.AddPrimaryKey(
            //     name: "PK_OpexData",
            //     table: "OpexData",
            //     column: "OpexDataID");
            //
            // migrationBuilder.AddPrimaryKey(
            //     name: "PK_TblPriority",
            //     table: "TblPriority",
            //     column: "PrioID");
        }
    }
}
