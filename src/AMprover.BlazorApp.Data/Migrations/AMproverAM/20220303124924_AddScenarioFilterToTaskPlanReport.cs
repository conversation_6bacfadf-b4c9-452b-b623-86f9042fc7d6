using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class AddScenarioFilterToTaskPlanReport : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //Added stored procedure for the task plan report
            var sql = @"
	 	    	IF OBJECT_ID('GetTaskPlanExtendedReport', 'P') IS NOT NULL
                DROP PROC GetTaskPlanExtendedReport
                GO
     
                CREATE PROCEDURE [dbo].[GetTaskPlanExtendedReport]
                    @FilterSiCategory int = NULL, 
                    @FilterScenario int = NULL
                AS
                BEGIN
                    SET NOCOUNT ON;
                    SELECT TblTask.TskName AS CltpName,         
                    TblClusterTaskPlan.CltpSiID as Id,
                    TblTask.TskDescription AS CltpDescription,
                    TblTask.TskGeneralDescription AS CltpInstruction, 
                    TblTask.TskRemark AS CltpRemarks,
                    TblTask.TskValidFromYear AS CltpValidFrom, 
                    TblTask.TskValidUntilYear AS CltpValidUntil,
                    TblClusterTaskPlan.CltpDuration, 
                    TblClusterTaskPlan.CltpDownTime,
                    TblClusterTaskPlan.CltpDateExecuted, 
                    TblClusterTaskPlan.CltpClusterCostPerUnit,
                    TblClusterTaskPlan.CltpQualityScore,
                    TblTask.TskInterval AS CltpInterval, 
                    TblClusterTaskPlan.CltpDateGenerated,
                    TblClusterTaskPlan.CltpRiskID, 
                    TblClusterTaskPlan.CltpExecutionDate, 
                    TblClusterTaskPlan.CltpSiUnits,
                    LookupIntervalUnit.IntUnitName AS CltpIntervalUnit,
                    LookupExecutor.ExecutorName AS CltpExecutor,
                    LookupMxPolicy.PolName AS CltpMxPolicy,
                    LookupInitiator.InitiatorName AS CltpInitiator,
                    TblCluster.ClustName, TblMRB.MrbName AS Risk,
                    TblSi_2.SiName,
                    TblSi_2.SiDescription,
                    TblSi_2.SiMiscBitField1 AS SiSHECritical,
                    TblSi_2.SiMiscTextField1 AS SiAttribute1, 
                    TblSi_2.SiMiscTextField2 AS SiAttribute2, 
                    TblSi_2.SiMiscTextField3 AS SiAttribute3,
                    TblSi_2.SiMiscTextField4 AS SiAttribute4, 
                    TblSi_2.SiMiscTextField5 AS SiAttribute5, 
                    TblSi_2.SiMiscTextField6 AS SiAttribute6, 
                    TblSi_2.SiMiscTextField7 AS SiAttribute7,
                    TblCluster.ClustPartOf,
                    TblRiskObject.RiskObjName,
                    TblScenario.ScenName AS Scenario, 
                    TblMRB.MrbFailureCause,
                    TblMRB.MrbFailureConsequences, 
                    TblSi.SiName AS PartOfSi,
                    TblSi_1.SiName AS PartOfSiPartOfSi,
                    TblTask.TskID, 
                    TblTask.TskType 
                    FROM TblSi AS TblSi_2 LEFT OUTER JOIN TblSi AS TblSi_1 
                    RIGHT OUTER JOIN TblSi ON TblSi_1.SiID = TblSi.SiPartOf ON TblSi_2.SiPartOf = TblSi.SiID 
                    RIGHT OUTER JOIN TblClusterTaskPlan ON TblSi_2.SiID = TblClusterTaskPlan.CltpSiID 
                    LEFT OUTER JOIN TblRiskObject INNER JOIN TblMRB ON TblRiskObject.RiskObjID = TblMRB.MrbRiskObject 
                    INNER JOIN TblScenario ON TblRiskObject.RiskObjScenarioID = TblScenario.ScenID ON TblClusterTaskPlan.CltpRiskID = TblMRB.MrbID 
                    LEFT OUTER JOIN TblCluster ON TblClusterTaskPlan.CltpClusterID = TblCluster.ClustID 
                    LEFT OUTER JOIN LookupInitiator
                    RIGHT OUTER JOIN TblTask ON LookupInitiator.InitiatorID = TblTask.TskInitiator
                    LEFT OUTER JOIN LookupExecutor ON TblTask.TskExecutor = LookupExecutor.ExecutorID
                    LEFT OUTER JOIN LookupMxPolicy ON TblTask.TskMxPolicy = LookupMxPolicy.PolID 
                    LEFT OUTER JOIN LookupIntervalUnit ON TblTask.TskIntervalUnit = LookupIntervalUnit.IntUnitID ON TblClusterTaskPlan.CltpTaskID = TblTask.TskID
                    WHERE (@FilterSiCategory IS NULL OR TblSi.SiCategory = @FilterSiCategory) 
                    AND (@FilterScenario IS NULL OR TblScenario.ScenID = @FilterScenario)
                END";

            migrationBuilder.Sql(sql);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TaskPlanReportItems");
        }
    }
}