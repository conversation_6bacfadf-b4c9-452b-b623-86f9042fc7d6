using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class CleanupoldassignABSfilters : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Delete PickSI entries based on corrupted old filters
            migrationBuilder.Sql(@"
                DELETE FROM tblPickSI 
                WHERE pckSILinkFilterId IN 
                (select sifId FROM TblSiLinkFilters 
                where SifRiskObject<PERSON> is null
                and SifChildObjectID is null
                and sifChildObject1Id is null)");

            // Delete All Filters following the old structure. These filters are linked to a specific item.
            // This means they don't follow the whole inherited filter structure, they are also not visible
            // within The AMProver 5 application currently
            migrationBuilder.Sql(@"
                DELETE FROM TblSiLinkFilters 
                where SifRiskObjectID is null
                and SifChildObjectID is null
                and sifChildObject1Id is null");

            // Delete newly orphaned TblFilters Rows
            migrationBuilder.Sql(@"
                DELETE FROM tblFilters
                WHERE sqlSelGroup = 'LinkRiskSI' 
                and sqlSelSubgroup = 'LinkRiskSI' 
                and sqlSelId not in (SELECT sifFilterId FROM TblSiLinkFilters)");

            // Delete newly orphaned entries in TblFilterSelectionList
            migrationBuilder.Sql(@"
                DELETE from TblFiltersSelectionList
                where sqlSelId not in (SELECT sqlSelId FROM tblFilters)");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
