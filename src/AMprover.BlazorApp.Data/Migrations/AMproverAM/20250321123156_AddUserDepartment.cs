using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations
{
    public partial class AddUserDepartment : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'TblUser') BEGIN DROP TABLE [TblUser] END");
            
            migrationBuilder.CreateTable(
                name: "TblUserDepartment",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    DepartmentId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TblUserDepartment", x => new { x.UserId, x.DepartmentId });
                    table.ForeignKey(
                        name: "FK_TblUserDepartment_TblDepartment_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "TblDepartment",
                        principalColumn: "DepID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TblUserDepartment_DepartmentId",
                table: "TblUserDepartment",
                column: "DepartmentId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TblUserDepartment");
            
                        migrationBuilder.CreateTable(
                name: "TblUser",
                columns: table => new
                {
                    UserID = table.Column<int>(type: "int", nullable: false, comment: "Unique ID (PK of User)")
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserDateInitiated = table.Column<DateTime>(type: "smalldatetime", nullable: true, comment: "Date of creation"),
                    UserDateModified = table.Column<DateTime>(type: "smalldatetime", nullable: true, comment: "Date the record was last modified. (null if never modified)"),
                    UserDepartment = table.Column<int>(type: "int", nullable: true, comment: "ID of the the department to which the user belongs (FK to Department)"),
                    UserFirstName = table.Column<string>(type: "varchar(50)", unicode: false, maxLength: 50, nullable: true, comment: "First name of the user"),
                    UserInitiatedBy = table.Column<string>(type: "varchar(50)", unicode: false, maxLength: 50, nullable: true, comment: "Created by user"),
                    UserIsGroup = table.Column<bool>(type: "bit", nullable: true),
                    UserLastName = table.Column<string>(type: "varchar(50)", unicode: false, maxLength: 50, nullable: true, comment: "Surname of the user"),
                    UserLocked = table.Column<bool>(type: "bit", nullable: true),
                    UserLoginName = table.Column<string>(type: "varchar(50)", unicode: false, maxLength: 50, nullable: false, comment: "Name of the user"),
                    UserModifiedBy = table.Column<string>(type: "varchar(50)", unicode: false, maxLength: 50, nullable: true, comment: "User name of person that made the last modification to this record"),
                    UserPartOfGroup = table.Column<int>(type: "int", nullable: true),
                    UserPassword = table.Column<string>(type: "varchar(100)", unicode: false, maxLength: 100, nullable: true, comment: "Password of the user"),
                    UserRights = table.Column<string>(type: "varchar(max)", unicode: false, nullable: true, comment: "XML structure with all individual UserRights"),
                    UserSetPassword = table.Column<bool>(type: "bit", nullable: true),
                    UserType = table.Column<int>(type: "int", nullable: true, comment: "Four kind of types 0 =normal users 1 = read only users  2=Administrators")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TblUser", x => x.UserID);
                },
                comment: "Contains all data needed to define a user. This table is not in use from the Amprover software (!)");

            migrationBuilder.CreateIndex(
                name: "IX_User",
                table: "TblUser",
                column: "UserLoginName",
                unique: true);
        }
    }
}
