using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class RemoveBrokenFkSiUserDefined : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TblSi_LookupUserDefined_SiCategory",
                table: "TblSi");

            migrationBuilder.AddColumn<int>(
                name: "SiCategoryNavigationUserDefinedId",
                table: "TblSi",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TblSi_SiCategoryNavigationUserDefinedId",
                table: "TblSi",
                column: "SiCategoryNavigationUserDefinedId");

            migrationBuilder.AddForeignKey(
                name: "FK_TblSi_LookupUserDefined_SiCategoryNavigationUserDefinedId",
                table: "TblSi",
                column: "SiCategoryNavigationUserDefinedId",
                principalTable: "LookupUserDefined",
                principalColumn: "UserDefinedID");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TblSi_LookupUserDefined_SiCategoryNavigationUserDefinedId",
                table: "TblSi");

            migrationBuilder.DropIndex(
                name: "IX_TblSi_SiCategoryNavigationUserDefinedId",
                table: "TblSi");

            migrationBuilder.DropColumn(
                name: "SiCategoryNavigationUserDefinedId",
                table: "TblSi");

            migrationBuilder.AddForeignKey(
                name: "FK_TblSi_LookupUserDefined_SiCategory",
                table: "TblSi",
                column: "SiCategory",
                principalTable: "LookupUserDefined",
                principalColumn: "UserDefinedID",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
