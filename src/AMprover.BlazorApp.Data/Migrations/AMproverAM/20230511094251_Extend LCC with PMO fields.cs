using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class ExtendLCCwithPMOfields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctActionCostPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctCorrectiveCostPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctCustomPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctCustomRiskPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctDirectCostPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctFmecaPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctOptimalCostPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctPreventiveCostPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctRiskPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctSparePartCostPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctTaskFmecaCustomPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccEfctTaskFmecaPmo",
                table: "TblLccEffectDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetAECPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetActionCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetAvailabilityTechnicalPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetAverageOptimalCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetAveragePmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetAverageRealCorrectiveCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetAverageRealPreventiveCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetCapexCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetCorrectiveCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetDirectCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetFailureRatePmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetFmecaCustomPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetFmecaCustomRiskPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetFmecaPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetMaintenanceCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetModificationCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetOpexCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetOptimalCorrectiveCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetOptimalPreventiveCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetPreventiveCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetProcedureCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetRealCorrectiveCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetRealOpexCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetRealPreventiveCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetRealTotalCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetReliabilityPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetRiskPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetSpareCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetTaskCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetTotalCostPmo",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccAECPmo",
                table: "TblLCC",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccAverageOptimalCostPmo",
                table: "TblLCC",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccInputAvailabilityTechnicalPmo",
                table: "TblLCC",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccMTBFTechnicalPmo",
                table: "TblLCC",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccMcRavPmo",
                table: "TblLCC",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccNPVPmo",
                table: "TblLCC",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LccNPVyearPmo",
                table: "TblLCC",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccOptimalAverageCorrectiveCostPmo",
                table: "TblLCC",
                type: "decimal(18,0)",
                precision: 18,
                scale: 0,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccOptimalAveragePreventiveCostPmo",
                table: "TblLCC",
                type: "decimal(18,0)",
                precision: 18,
                scale: 0,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccOutputReliabilityTechnicalPmo",
                table: "TblLCC",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccPotentialPmo",
                table: "TblLCC",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccTotalAverageCostPmo",
                table: "TblLCC",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LccEfctActionCostPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctCorrectiveCostPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctCustomPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctCustomRiskPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctDirectCostPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctFmecaPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctOptimalCostPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctPreventiveCostPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctRiskPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctSparePartCostPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctTaskFmecaCustomPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccEfctTaskFmecaPmo",
                table: "TblLccEffectDetail");

            migrationBuilder.DropColumn(
                name: "LccDetAECPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetActionCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetAvailabilityTechnicalPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetAverageOptimalCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetAveragePmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetAverageRealCorrectiveCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetAverageRealPreventiveCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetCapexCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetCorrectiveCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetDirectCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetFailureRatePmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetFmecaCustomPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetFmecaCustomRiskPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetFmecaPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetMaintenanceCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetModificationCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetOpexCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetOptimalCorrectiveCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetOptimalPreventiveCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetPreventiveCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetProcedureCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetRealCorrectiveCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetRealOpexCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetRealPreventiveCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetRealTotalCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetReliabilityPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetRiskPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetSpareCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetTaskCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccDetTotalCostPmo",
                table: "TblLCCDetail");

            migrationBuilder.DropColumn(
                name: "LccAECPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccAverageOptimalCostPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccInputAvailabilityTechnicalPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccMTBFTechnicalPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccMcRavPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccNPVPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccNPVyearPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccOptimalAverageCorrectiveCostPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccOptimalAveragePreventiveCostPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccOutputReliabilityTechnicalPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccPotentialPmo",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccTotalAverageCostPmo",
                table: "TblLCC");
        }
    }
}
