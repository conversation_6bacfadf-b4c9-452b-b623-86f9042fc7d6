using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class filterriskonabsonmrb : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(
@"
/****** Object:  StoredProcedure [dbo].[GetRiskOnAbs]    Script Date: 23/05/2025 14:57:51 ******/
DROP PROCEDURE [dbo].[GetRiskOnAbs]
GO

/****** Object:  StoredProcedure [dbo].[GetRiskOnAbs]    Script Date: 23/05/2025 14:57:51 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[GetRiskOnAbs]
	@FmecaId int
	AS
	BEGIN
		SET NOCOUNT ON;
		select
        TblSi.SiID AssetId,
        tblSi.SiName AssetCode,
        tblSi.SiParentName AssetParentCode,
        tblSi.SiDescription AssetName,
        tblMrb.MRBId RiskId,
        tblMrb.MrbRiskObject RiskObjectId,
		TblRiskObject.RiskObjAnalyseType AnalysisType,
        tblMrb.MrbName RiskName,
        TblRiskObject.RiskObjFmecaID FmecaId,
        tblMrb.MrbFmecaMtbfBefore FmecaMtbfBefore,
        tblMrb.MrbFmecaMtbfAfter FmecaMtbfAfter,
        tblMrb.MrbFmecaMtbfPmo FmecaMtbfPmo,
        tblMrb.MrbFmecaSelectionBefore FmecaSelectionBefore,
        tblMrb.MrbFmecaSelectionAfter FmecaSelectionAfter,
        tblMrb.MrbFmecaSelectionPmo FmecaSelectionPmo,
        tblSi.SiCategory CategoryId,
        LookupUserDefined.UserDefinedShortDescription Category
        from TblSi
        left join LookupUserDefined on TblSi.SiCategoryNavigationUserDefinedId = LookupUserDefined.UserDefinedID
        left join TblPickSI on TblPickSI.PckSiSiID = TblSi.SiID

		-- Filtering needs to happen on TblMrb based on the Matrix value in TblRiskObject
		 LEFT JOIN tblMrb ON tblMrb.MRBId = TblPickSI.PckSiMrbID
			AND tblMrb.MrbRiskObject IN (
				SELECT RiskObjID
				FROM TblRiskObject
				WHERE RiskObjFmecaID IS NULL OR RiskObjFmecaID = @FmecaId
			)

		-- Filtering happend already, so we can just left join TblRiskObject now
		left join TblRiskObject on TblRiskObject.RiskObjID = tblMrb.MrbRiskObject

        order by assetId
	END
GO");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(
@"
/****** Object:  StoredProcedure [dbo].[GetRiskOnAbs]    Script Date: 23/05/2025 14:57:51 ******/
DROP PROCEDURE [dbo].[GetRiskOnAbs]
GO

/****** Object:  StoredProcedure [dbo].[GetRiskOnAbs]    Script Date: 23/05/2025 14:57:51 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[GetRiskOnAbs]
	@FmecaId int
	AS
	BEGIN
		SET NOCOUNT ON;
		select
        TblSi.SiID AssetId,
        tblSi.SiName AssetCode,
        tblSi.SiParentName AssetParentCode,
        tblSi.SiDescription AssetName,
        tblMrb.MRBId RiskId,
        tblMrb.MrbRiskObject RiskObjectId,
		filteredRiskObject.RiskObjAnalyseType AnalysisType,
        tblMrb.MrbName RiskName,
        filteredRiskObject.RiskObjFmecaID FmecaId,
        tblMrb.MrbFmecaMtbfBefore FmecaMtbfBefore,
        tblMrb.MrbFmecaMtbfAfter FmecaMtbfAfter,
        tblMrb.MrbFmecaMtbfPmo FmecaMtbfPmo,
        tblMrb.MrbFmecaSelectionBefore FmecaSelectionBefore,
        tblMrb.MrbFmecaSelectionAfter FmecaSelectionAfter,
        tblMrb.MrbFmecaSelectionPmo FmecaSelectionPmo,
        tblSi.SiCategory CategoryId,
        LookupUserDefined.UserDefinedShortDescription Category
        from TblSi
        left join LookupUserDefined on TblSi.SiCategoryNavigationUserDefinedId = LookupUserDefined.UserDefinedID
        left join TblPickSI on TblPickSI.PckSiSiID = TblSi.SiID
        left join tblMrb on tblMrb.mrbId = TblPickSI.PckSiMrbID

		-- First Filter then Join. Otherwise the Tree breaks
		LEFT JOIN (
			SELECT * 
			FROM TblRiskObject
			WHERE RiskObjFmecaID IS NULL OR RiskObjFmecaID = @FmecaId
		) AS filteredRiskObject ON filteredRiskObject.RiskObjID = tblMrb.MrbRiskObject

        order by assetId
	END
GO");
        }
    }
}
