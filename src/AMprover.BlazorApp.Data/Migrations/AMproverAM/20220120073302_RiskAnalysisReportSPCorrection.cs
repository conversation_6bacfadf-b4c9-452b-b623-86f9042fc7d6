using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class RiskAnalysisReportSPCorrection : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //Correction Risk analysis SP
            var sqlRiskAnalysis = @"
	 	    	IF OBJECT_ID('GetRiskAnalysisReport', 'P') IS NOT NULL
                DROP PROC GetRiskAnalysisReport
                GO
     
                CREATE PROCEDURE [dbo].GetRiskAnalysisReport
                AS
                BEGIN
                    SET NOCOUNT ON;
                    SELECT TblMRB.MRBId as Id, 
                    TblMRB.MrbName as Name, 
                    TblMRB.MrbFailureCause as FailureCause, 
                    TblMRB.MrbFailureConsequences as FailureConsequences, 
                    TblMRB.MrbFailureCategorie1 as FailureCategory1, 
                    TblMRB.MrbFailureCategorie2 as FailureCategory2, 
                    TblMRB.MrbDownTimeAfter as DownTimeAfter, 
                    TblMRB.MrbDownTimeBefore as DownTimeBefore, 
                    TblMRB.MrbRemarks as Remarks, 
                    TblMRB.MrbSpareCosts as RiskSpareCosts, 
                    TblMRB.MrbActionCosts as ActionCosts, 
                    TblMRB.MrbOptimalCosts as OptimalCosts, 
                    TblMRB.MrbFmecaSelect as FmecaSelect, 
                    TblMRB.MrbStatus as Status, 
                    TblMRB.MrbFmecaVersion as FmecaVersion, 
                    TblMRB.MrbSpareManageCost as SpareManagementCost, 
                    TblMRB.MrbEffectBefore as EffectBefore, 
                    TblMRB.MrbCustomBefore as CustomBefore, 
                    TblMRB.MrbMtbfBefore as MtbfBefore, 
                    TblMRB.MrbRiskBefore as RiskBefore, 
                    TblMRB.MrbEffectAfter as EffectAfter, 
                    TblMRB.MrbMtbfAfter as MtbfAfter, 
                    TblMRB.MrbRiskAfter as RiskAfter, 
                    TblMRB.MrbCustomAfter as CustomAfter, 
                    TblMRB.MrbDescription as Description, 
                    TblScenario.ScenName AS Scenario, 
                    TblObject_6.ObjName AS OBJECT, 
                    TblObject.ObjName AS RiskObject, 
                    TblObject_3.ObjName AS Object1, 
                    TblObject_4.ObjName AS Object2, 
                    TblObject_5.ObjName AS Object3, 
                    LookupFailMode.FailMode, 
                    TblTask.TskID, 
                    TblTask.TskName, 
                    TblTask.TskDescription, 
                    TblTask.TskGeneralDescription, 
                    TblTask.TskRemark, 
                    TblTask.TskCosts, 
                    LookupMxPolicy.PolName AS TskMxPolicy, 
                    TblTask.TskInterval, 
                    LookupIntervalUnit.IntUnitName AS TskIntervalUnit, 
                    LookupExecutor.ExecutorName AS TskExecutor, 
                    LookupInitiator.InitiatorName AS TskInitiator, 
                    TblWorkpackage.WpName AS TskWorkPackage, 
                    TblCluster.ClustName AS TskCluster, 
                    TblTask.TskDuration, 
                    TblTask.TskDownTime, 
                    TblTask.TskType, 
                    TblSpare.SpareName, 
                    TblSpare.SpareRemarks, 
                    TblSpare.SpareCategory, 
                    TblSpare.SpareStockNumber, 
                    TblSpare.SpareOrderLeadTime, 
                    TblSpare.SparePurchasePrice, 
                    TblSpare.SpareYearlyCost, 
                    TblSpare.SpareSupplierID, 
                    TblSpare.SpareVendorID, 
                    TblSpare.SpareReferenceID, 
                    TblSpare.SpareNoOfItems, 
                    TblSpare.SpareCosts, 
                    TblSpare.SparePurchaseYear, 
                    TblSpare.SpareObjectCount, 
                    TblSpare.SpareDepreciationPct, 
                    TblSpare.SpareID, 
                    TblRiskObject.RiskObjName, 
                    TblMRB.MrbStatus AS Expr1, 
                    TblRiskObject.RiskObjAnalyseType, 
                    TblSpare.SpareReliability 
                    FROM TblObject AS TblObject_6 
                    RIGHT OUTER JOIN TblMRB 
                    LEFT OUTER JOIN TblSpare ON TblMRB.MRBId = TblSpare.SpareMrbID 
                    LEFT OUTER JOIN LookupFailMode ON TblMRB.MrbFailureMode = LookupFailMode.FailID 
                    LEFT OUTER JOIN TblObject AS TblObject_5 ON TblMRB.MrbChildObject4 = TblObject_5.ObjID 
                    LEFT OUTER JOIN TblObject AS TblObject_4 ON TblMRB.MrbChildObject3 = TblObject_4.ObjID 
                    LEFT OUTER JOIN TblObject AS TblObject_3 ON TblMRB.MrbChildObject2 = TblObject_3.ObjID 
                    LEFT OUTER JOIN TblScenario INNER JOIN TblRiskObject ON TblScenario.ScenID = TblRiskObject.RiskObjScenarioID 
                    INNER JOIN TblObject ON TblRiskObject.RiskObjObjectID = TblObject.ObjID ON TblMRB.MrbRiskObject = TblRiskObject.RiskObjID ON TblObject_6.ObjID = TblRiskObject.RiskObjParentObjectID 
                    LEFT OUTER JOIN TblCluster 
                    RIGHT OUTER JOIN TblTask ON TblCluster.ClustID = TblTask.TskCluster 
                    LEFT OUTER JOIN LookupExecutor ON TblTask.TskExecutor = LookupExecutor.ExecutorID 
                    LEFT OUTER JOIN LookupInitiator ON TblTask.TskInitiator = LookupInitiator.InitiatorID 
                    LEFT OUTER JOIN LookupIntervalUnit ON TblTask.TskIntervalUnit = LookupIntervalUnit.IntUnitID 
                    LEFT OUTER JOIN TblWorkpackage ON TblTask.TskWorkpackage = TblWorkpackage.WpID
                    LEFT OUTER JOIN LookupMxPolicy ON TblTask.TskMxPolicy = LookupMxPolicy.PolID ON TblMRB.MRBId = TblTask.TskMrbID 
                    WHERE (TblTask.TskRemoved IS NULL) OR (TblTask.TskRemoved = 0) 
                    ORDER BY Scenario, OBJECT, RiskObject, Object1, Object2, Object3
                END";

            migrationBuilder.Sql(sqlRiskAnalysis);
            
            //Correct significant item SP
            var sqlSignificantItem = @"
	 	    	IF OBJECT_ID('GetSignificantItemsReport', 'P') IS NOT NULL
                DROP PROC GetSignificantItemsReport
                GO
     
                CREATE PROCEDURE [dbo].GetSignificantItemsReport
                AS
                BEGIN
                    SET NOCOUNT ON;
                    SELECT SiID as Id, 
                    SiName as Name, 
                    SiDescription as Description, 
                    SiType as Type, 
                    SiPrice as Price, 
                    SiYear as Year, 
                    SiWarrantyPeriod as WarrantyPeriod, 
                    SiContractEnd as ContractEnd, 
                    SiUnitType as UnitType, 
                    SiLength as Length, 
                    SiHeight as Height, 
                    SiWidth as Width, 
                    SiUnits as Units, 
                    SiTotalUnits as TotalUnits, 
                    SiQualityScore as QualityScore, 
                    SiReferenceID as ReferenceId, 
                    SiUnitsTypeValues as UnitTypeValues,                         
                    SiSupplierTextField1 as SupplierTextField1, 
                    SiMiscTextField1 as MiscTextField1, 
                    SiMiscTextField2 as MiscTextField2, 
                    SiMiscTextField3 as MiscTextField3, 
                    SiMiscTextField4 as MiscTextField4, 
                    SiMiscTextField5 as MiscTextField5, 
                    SiMiscTextField6 as MiscTextField6, 
                    SiMiscTextField7 as MiscTextField7, 
                    SiMiscBitField1 as MiscBitField1, 
                    SiMtbf as Mtbf, 
                    SiStatus as Status, 
                    PartOfSiName,                          
                    SICategory as Category, 
                    SiRemarks as Remarks, 
                    SiVendor as Vendor, 
                    HasMrb
                    FROM            
                    (SELECT SiID, 
                    SiName, 
                    SiDescription, 
                    SiType, 
                    SiPrice, 
                    SiYear, 
                    SiWarrantyPeriod, 
                    SiContractEnd, 
                    SiUnitType, 
                    SiLength, 
                    SiHeight, 
                    SiWidth, 
                    SiUnits, 
                    SiTotalUnits, 
                    SiQualityScore, 
                    SiReferenceID, 
                    SiUnitsTypeValues, 
                    SiSupplierTextField1, 
                    SiMiscTextField1, 
                    SiMiscTextField2, 
                    SiMiscTextField3, 
                    SiMiscTextField4, 
                    SiMiscTextField5, 
                    SiMiscTextField6, 
                    SiMiscTextField7, 
                    SiMiscBitField1, 
                    SiMtbf,                                                     
                    SiStatus,          
                    (SELECT SiName           
                    FROM TblSi AS TblSiPartOf                                                          
                    WHERE (TblSi.SiPartOf = SiID)) AS PartOfSiName,                                         
                    (SELECT UserDefinedShortDescription                     
                    FROM LookupUserDefined        
                    WHERE (TblSi.SiCategory = UserDefinedValue) AND (UserDefinedFilter = 'SICategory')) AS SICategory, SiRemarks, SiVendor,                                                        
                    (SELECT CASE WHEN EXISTS (SELECT 'x' FROM TblMRB INNER JOIN TblPickSI ON TblMRB.MrbID = TblPickSI.PckSiMrbID                                                      
                    WHERE TblPickSI.PckSiSiID = TblSi.SiID) THEN 1 ELSE 0 END AS Expr1) AS HasMrb FROM TblSi) AS SI
                END";

            migrationBuilder.Sql(sqlSignificantItem);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
        }
    }
}