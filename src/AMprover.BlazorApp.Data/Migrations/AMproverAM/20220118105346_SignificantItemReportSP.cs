using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class SignificantItemReportSP : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var sql = @"
	 	    	IF OBJECT_ID('GetSignificantItemsReport', 'P') IS NOT NULL
                DROP PROC GetSignificantItemsReport
                GO
     
                CREATE PROCEDURE [dbo].GetSignificantItemsReport
                AS
                BEGIN
                    SET NOCOUNT ON;
                    SELECT SiID as Id, 
                    SiName as Name, 
                    SiDescription as Description, 
                    SiType as Type, 
                    SiPrice as Price, 
                    SiYear as Year, 
                    SiWarrantyPeriod as WarrantyPeriod, 
                    SiContractEnd as ContractEnd, 
                    SiUnitType as UnitType, 
                    SiLength as Length, 
                    SiHeight as Height, 
                    SiWidth as Width, 
                    SiUnits as Units, 
                    SiTotalUnits as TotalUnits, 
                    SiQualityScore as QualityScore, 
                    SiReferenceID as ReferenceId, 
                    SiUnitsTypeValues as UnitTypeValues,                         
                    SiSupplierTextField1 as SupplierTextField1, 
                    SiMiscTextField1 as MiscTextField1, 
                    SiMiscTextField2 as MiscTextField2, 
                    SiMiscTextField3 as MiscTextField3, 
                    SiMiscTextField4 as MiscTextField4, 
                    SiMiscTextField5 as MiscTextField5, 
                    SiMiscTextField6 as MiscTextField6, 
                    SiMiscTextField7 as MiscTextField7, 
                    SiMiscBitField1 as MiscBitField1, 
                    SiMtbf as Mtbf, 
                    SiStatus as Status, 
                    PartOfSiName,                          
                    SICategory as Category, 
                    SiRemarks as Remarks, 
                    SiVendor as Vendor, 
                    HasMrb
                    FROM            
                    (SELECT SiID, 
                    SiName, 
                    SiDescription, 
                    SiType, 
                    SiPrice, 
                    SiYear, 
                    SiWarrantyPeriod, 
                    SiContractEnd, 
                    SiUnitType, 
                    SiLength, 
                    SiHeight, 
                    SiWidth, 
                    SiUnits, 
                    SiTotalUnits, 
                    SiQualityScore, 
                    SiReferenceID, 
                    SiUnitsTypeValues, 
                    SiSupplierTextField1, 
                    SiMiscTextField1, 
                    SiMiscTextField2, 
                    SiMiscTextField3, 
                    SiMiscTextField4, 
                    SiMiscTextField5, 
                    SiMiscTextField6, 
                    SiMiscTextField7, 
                    SiMiscBitField1, 
                    SiMtbf,                                                     
                    SiStatus,          
                    (SELECT SiName           
                    FROM TblSi AS TblSiPartOf                                                          
                    WHERE (TblSi.SiPartOf = SiID)) AS PartOfSiName,                                         
                    (SELECT UserDefinedShortDescription                     
                    FROM LookupUserDefined        
                    WHERE (TblSi.SiCategory = UserDefinedValue) AND (UserDefinedFilter = 'SICategory')) AS SICategory, SiRemarks, SiVendor,                                                        
                    (SELECT CASE WHEN EXISTS (SELECT 'x' FROM TblMRB INNER JOIN TblPickSI ON TblMRB.MrbID = TblPickSI.PckSiMrbID                                                      
                    WHERE TblPickSI.PckSiSiID = TblSi.SiID) THEN 1 ELSE 0 END AS Expr1) AS HasMrb FROM TblSi) AS SI
                END";

            migrationBuilder.Sql(sql);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            
        }
    }
}
