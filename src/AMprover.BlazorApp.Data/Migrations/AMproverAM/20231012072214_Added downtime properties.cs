using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations
{
    public partial class Addeddowntimeproperties : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "VdmDtCostTasks",
                table: "TblVdmxl",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TskDtCost",
                table: "TblTask",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LccDetDtCostTasks",
                table: "TblLCCDetail",
                type: "decimal(18,2)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "VdmDtCostTasks",
                table: "TblVdmxl");

            migrationBuilder.DropColumn(
                name: "TskDtCost",
                table: "TblTask");

            migrationBuilder.DropColumn(
                name: "LccDetDtCostTasks",
                table: "TblLCCDetail");
        }
    }
}
