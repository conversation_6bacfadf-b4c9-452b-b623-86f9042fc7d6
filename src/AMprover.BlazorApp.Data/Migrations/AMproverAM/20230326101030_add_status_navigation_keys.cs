using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class add_status_navigation_keys : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Scenario
            // Change Status Lookup to use Primary key from lookup table instead of Value property. This way we can use EFcore to include objects
            migrationBuilder.Sql(
                @"  update TblScenario
                    set scenStatus = LookupID
                    from TblScenario
                    inner join Lookup on TblScenario.scenStatus = LookupValue
                    where Lookup.LookupFilter = 'StatusTypes'
                    GO");

            // Clear all orphaned status properties
            migrationBuilder.Sql(
                @"  update TblScenario
                    set scenStatus = null
                    where scenStatus not in (select lookupId from Lookup where LookupFilter = 'StatusTypes')
                    GO");

            // Risk Object
            migrationBuilder.Sql(
                @"  update TblRiskObject
                    set riskObjStatus = LookupID
                    from TblRiskObject
                    inner join Lookup on TblRiskObject.riskObjStatus = LookupValue
                    where Lookup.LookupFilter = 'StatusTypes'
                    GO");

            migrationBuilder.Sql(
                @"  update TblRiskObject
                    set RiskObjStatus = null
                    where RiskObjStatus not in (select lookupId from Lookup where LookupFilter = 'StatusTypes')
                    GO");

            // Cluster
            migrationBuilder.Sql(
                @"  update TblCluster
                    set ClustStatus = LookupID
                    from TblCluster
                    inner join Lookup on TblCluster.ClustStatus = LookupValue
                    where Lookup.LookupFilter = 'StatusTypes'
                    GO");

            migrationBuilder.Sql(
                @"  update TblCluster
                    set ClustStatus = null
                    where ClustStatus not in (select lookupId from Lookup where LookupFilter = 'StatusTypes')
                    GO");

            // Mrb
            migrationBuilder.Sql(
                @"  update TblMRB
                    set mrbStatus = LookupID
                    from tblMrb
                    inner join Lookup on tblMrb.MrbStatus = LookupValue
                    where Lookup.LookupFilter = 'StatusTypes'
                    GO");

            migrationBuilder.Sql(
                @"  update tblMrb
                    set MrbStatus = null
                    where mrbStatus not in (select lookupId from Lookup where LookupFilter = 'StatusTypes')
                    GO");

            // Clear Renamed GridColumns Properties
            migrationBuilder.Sql(
                @"  delete from LookupGridColumn where ControlName like 'Export%'
                    GO");

            // Auto Generated
            migrationBuilder.CreateIndex(
                name: "IX_TblScenario_ScenStatus",
                table: "TblScenario",
                column: "ScenStatus");

            migrationBuilder.CreateIndex(
                name: "IX_TblRiskObject_RiskObjStatus",
                table: "TblRiskObject",
                column: "RiskObjStatus");

            migrationBuilder.CreateIndex(
                name: "IX_TblMRB_MrbStatus",
                table: "TblMRB",
                column: "MrbStatus");

            migrationBuilder.CreateIndex(
                name: "IX_TblCluster_ClustStatus",
                table: "TblCluster",
                column: "ClustStatus");

            migrationBuilder.AddForeignKey(
                name: "FK_TblCluster_Lookup_ClustStatus",
                table: "TblCluster",
                column: "ClustStatus",
                principalTable: "Lookup",
                principalColumn: "LookupID");

            migrationBuilder.AddForeignKey(
                name: "FK_TblMRB_Lookup_MrbStatus",
                table: "TblMRB",
                column: "MrbStatus",
                principalTable: "Lookup",
                principalColumn: "LookupID");

            migrationBuilder.AddForeignKey(
                name: "FK_TblRiskObject_Lookup_RiskObjStatus",
                table: "TblRiskObject",
                column: "RiskObjStatus",
                principalTable: "Lookup",
                principalColumn: "LookupID");

            migrationBuilder.AddForeignKey(
                name: "FK_TblScenario_Lookup_ScenStatus",
                table: "TblScenario",
                column: "ScenStatus",
                principalTable: "Lookup",
                principalColumn: "LookupID");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TblCluster_Lookup_ClustStatus",
                table: "TblCluster");

            migrationBuilder.DropForeignKey(
                name: "FK_TblMRB_Lookup_MrbStatus",
                table: "TblMRB");

            migrationBuilder.DropForeignKey(
                name: "FK_TblRiskObject_Lookup_RiskObjStatus",
                table: "TblRiskObject");

            migrationBuilder.DropForeignKey(
                name: "FK_TblScenario_Lookup_ScenStatus",
                table: "TblScenario");

            migrationBuilder.DropIndex(
                name: "IX_TblScenario_ScenStatus",
                table: "TblScenario");

            migrationBuilder.DropIndex(
                name: "IX_TblRiskObject_RiskObjStatus",
                table: "TblRiskObject");

            migrationBuilder.DropIndex(
                name: "IX_TblMRB_MrbStatus",
                table: "TblMRB");

            migrationBuilder.DropIndex(
                name: "IX_TblCluster_ClustStatus",
                table: "TblCluster");
        }
    }
}
