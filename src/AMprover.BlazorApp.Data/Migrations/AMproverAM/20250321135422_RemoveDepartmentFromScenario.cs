using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations
{
    public partial class RemoveDepartmentFromScenario : Migration
    {
       protected override void Up(MigrationBuilder migrationBuilder)
{
    // First, identify all foreign keys on the column and drop them
    migrationBuilder.Sql(@"
        DECLARE @ForeignKeyName NVARCHAR(MAX)
        
        -- Find all foreign keys referencing the column
        SELECT @ForeignKeyName = OBJECT_NAME(f.object_id)
        FROM sys.foreign_keys AS f
        INNER JOIN sys.foreign_key_columns AS fc ON f.object_id = fc.constraint_object_id
        INNER JOIN sys.columns AS c ON fc.parent_column_id = c.column_id AND fc.parent_object_id = c.object_id
        WHERE c.name = 'ScenDepartment' 
        AND OBJECT_NAME(f.parent_object_id) = 'TblScenario'
        
        -- If a foreign key exists, drop it
        IF @ForeignKeyName IS NOT NULL
        BEGIN
            DECLARE @SQL NVARCHAR(MAX) = 'ALTER TABLE [TblScenario] DROP CONSTRAINT [' + @ForeignKeyName + ']'
            EXEC sp_executesql @SQL
            PRINT 'Dropped foreign key: ' + @ForeignKeyName
        END
        ELSE
        BEGIN
            PRINT 'No foreign key found on column ScenDepartment'
        END
    ");

    // Now drop the index
    migrationBuilder.Sql(@"
        IF EXISTS (
            SELECT * 
            FROM sys.indexes 
            WHERE name = 'IX_TblScenario_ScenDepartment' 
            AND object_id = OBJECT_ID('TblScenario')
        )
        BEGIN
            DROP INDEX [IX_TblScenario_ScenDepartment] ON [TblScenario]
            PRINT 'Dropped index: IX_TblScenario_ScenDepartment'
        END
        ELSE
        BEGIN
            PRINT 'Index IX_TblScenario_ScenDepartment does not exist'
        END
    ");

    // Finally drop the column
    migrationBuilder.Sql(@"
        IF EXISTS (
            SELECT * 
            FROM sys.columns 
            WHERE name = 'ScenDepartment' 
            AND object_id = OBJECT_ID('TblScenario')
        )
        BEGIN
            ALTER TABLE [TblScenario] DROP COLUMN [ScenDepartment]
            PRINT 'Dropped column: ScenDepartment'
        END
        ELSE
        BEGIN
            PRINT 'Column ScenDepartment does not exist'
        END
    ");
}

protected override void Down(MigrationBuilder migrationBuilder)
{
    // Recreate the column
    migrationBuilder.Sql(@"
        IF NOT EXISTS (
            SELECT * 
            FROM sys.columns 
            WHERE name = 'ScenDepartment' 
            AND object_id = OBJECT_ID('TblScenario')
        )
        BEGIN
            ALTER TABLE [TblScenario] ADD [ScenDepartment] INT NULL
        END
    ");

    // Recreate the index
    migrationBuilder.Sql(@"
        IF NOT EXISTS (
            SELECT * 
            FROM sys.indexes 
            WHERE name = 'IX_TblScenario_ScenDepartment' 
            AND object_id = OBJECT_ID('TblScenario')
        )
        BEGIN
            CREATE INDEX [IX_TblScenario_ScenDepartment] ON [TblScenario] ([ScenDepartment])
        END
    ");

    // Recreate the foreign key
    migrationBuilder.Sql(@"
        IF NOT EXISTS (
            SELECT * 
            FROM sys.foreign_keys 
            WHERE name = 'FK_TblScenario_TblDepartment' 
            AND parent_object_id = OBJECT_ID('TblScenario')
        )
        BEGIN
            ALTER TABLE [TblScenario] ADD CONSTRAINT [FK_TblScenario_TblDepartment] 
            FOREIGN KEY ([ScenDepartment]) REFERENCES [TblDepartment] ([DepID])
        END
    ");
}
    }
}
