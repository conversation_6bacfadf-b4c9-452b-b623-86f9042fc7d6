using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations
{
    public partial class AddRiskObjectOnCluster : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var sql1 = @"
                DELETE FROM TblCluster 
                WHERE ClustRiskObjectId IS NOT NULL 
                  AND ClustRiskObjectId NOT IN (SELECT RiskObjId FROM TblRiskObject);";

            migrationBuilder.Sql(sql1);
            
            migrationBuilder.CreateIndex(
                name: "IX_TblCluster_ClustRiskObjectID",
                table: "TblCluster",
                column: "ClustRiskObjectID");

            migrationBuilder.AddForeignKey(
                name: "FK_TblCluster_TblRiskObject_ClustRiskObjectID",
                table: "TblCluster",
                column: "ClustRiskObjectID",
                principalTable: "TblRiskObject",
                principalColumn: "RiskObjID");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TblCluster_TblRiskObject_ClustRiskObjectID",
                table: "TblCluster");

            migrationBuilder.DropIndex(
                name: "IX_TblCluster_ClustRiskObjectID",
                table: "TblCluster");
        }
    }
}
