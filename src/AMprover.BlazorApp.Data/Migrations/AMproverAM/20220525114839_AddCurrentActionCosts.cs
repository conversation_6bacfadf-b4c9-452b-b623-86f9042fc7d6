using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class AddCurrentActionCosts : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "MrbSpareCosts",
                table: "TblMRB",
                type: "decimal(18,2)",
                nullable: true,
                comment: "Costs of spare parts that are needed for preventive/corrective measures",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldComment: "Costs of spare parts that are needed for preventive/corrective measures");

            migrationBuilder.AlterColumn<decimal>(
                name: "MrbOptimalCosts",
                table: "TblMRB",
                type: "decimal(18,2)",
                nullable: true,
                comment: "Optimal costs for preventive actions for the risk (based on risk after preventive action)",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldComment: "Optimal costs for preventive actions for the risk (based on risk after preventive action)");

            migrationBuilder.AlterColumn<decimal>(
                name: "MrbActionCosts",
                table: "TblMRB",
                type: "decimal(18,2)",
                nullable: true,
                comment: "Total cost per year for all tasks that are bound to the risk",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldComment: "Total cost per year for all tasks that are bound to the risk");

            migrationBuilder.AddColumn<decimal>(
                name: "MrbCurrentActionCosts",
                table: "TblMRB",
                type: "decimal(18,2)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MrbCurrentActionCosts",
                table: "TblMRB");

            migrationBuilder.AlterColumn<decimal>(
                name: "MrbSpareCosts",
                table: "TblMRB",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                comment: "Costs of spare parts that are needed for preventive/corrective measures",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true,
                oldComment: "Costs of spare parts that are needed for preventive/corrective measures");

            migrationBuilder.AlterColumn<decimal>(
                name: "MrbOptimalCosts",
                table: "TblMRB",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                comment: "Optimal costs for preventive actions for the risk (based on risk after preventive action)",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true,
                oldComment: "Optimal costs for preventive actions for the risk (based on risk after preventive action)");

            migrationBuilder.AlterColumn<decimal>(
                name: "MrbActionCosts",
                table: "TblMRB",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                comment: "Total cost per year for all tasks that are bound to the risk",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true,
                oldComment: "Total cost per year for all tasks that are bound to the risk");
        }
    }
}
