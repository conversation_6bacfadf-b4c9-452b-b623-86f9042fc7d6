using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class MakeIntervalUnitNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Task_LookupIntervalUnit",
                table: "TblTask");

            migrationBuilder.AlterColumn<int>(
                name: "TskIntervalUnit",
                table: "TblTask",
                type: "int",
                nullable: true,
                comment: "ID of the the interval unit of the task (FK to LookupIntervalUnit). The interval unit determines what the value stored in TskInterval really means.",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "ID of the the interval unit of the task (FK to LookupIntervalUnit). The interval unit determines what the value stored in TskInterval really means.");

            migrationBuilder.AddForeignKey(
                name: "FK_Task_LookupIntervalUnit",
                table: "TblTask",
                column: "TskIntervalUnit",
                principalTable: "LookupIntervalUnit",
                principalColumn: "IntUnitID");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Task_LookupIntervalUnit",
                table: "TblTask");

            migrationBuilder.AlterColumn<int>(
                name: "TskIntervalUnit",
                table: "TblTask",
                type: "int",
                nullable: false,
                defaultValue: 0,
                comment: "ID of the the interval unit of the task (FK to LookupIntervalUnit). The interval unit determines what the value stored in TskInterval really means.",
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldComment: "ID of the the interval unit of the task (FK to LookupIntervalUnit). The interval unit determines what the value stored in TskInterval really means.");

            migrationBuilder.AddForeignKey(
                name: "FK_Task_LookupIntervalUnit",
                table: "TblTask",
                column: "TskIntervalUnit",
                principalTable: "LookupIntervalUnit",
                principalColumn: "IntUnitID",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
