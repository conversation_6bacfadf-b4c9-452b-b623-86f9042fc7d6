using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    public partial class EamPolicy_and_critFailureMechanism : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CritFailureMechanism",
                table: "TblCriticalityRanking",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PolEamPolicy",
                table: "LookupMxPolicy",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CritFailureMechanism",
                table: "TblCriticalityRanking");

            migrationBuilder.DropColumn(
                name: "PolEamPolicy",
                table: "LookupMxPolicy");
        }
    }
}
