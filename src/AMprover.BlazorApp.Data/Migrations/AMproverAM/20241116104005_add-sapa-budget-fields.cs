using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb
{
    public partial class addsapabudgetfields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "SapaYearBudget",
                table: "TblSapaYear",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<bool>(
                name: "SapaDetApproved",
                table: "TblSapaDetail",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "SapaBudget",
                table: "TblSapa",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>paYearBudget",
                table: "TblSapaYear");

            migrationBuilder.DropColumn(
                name: "SapaDetApproved",
                table: "TblSapaDetail");

            migrationBuilder.DropColumn(
                name: "SapaBudget",
                table: "TblSapa");
        }
    }
}
