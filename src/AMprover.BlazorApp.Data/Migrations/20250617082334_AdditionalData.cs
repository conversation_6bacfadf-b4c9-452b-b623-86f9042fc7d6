using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations
{
    public partial class AdditionalData : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "MrbAdditionalDataId",
                table: "TblMRB",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "LookupAdditionalData",
                columns: table => new
                {
                    AdditionalDataId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AdditionalDataName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    AdditionalDataDescription = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LookupAdditionalData", x => x.AdditionalDataId);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TblMRB_MrbAdditionalDataId",
                table: "TblMRB",
                column: "MrbAdditionalDataId");

            migrationBuilder.AddForeignKey(
                name: "FK_TblMRB_LookupAdditionalData_MrbAdditionalDataId",
                table: "TblMRB",
                column: "MrbAdditionalDataId",
                principalTable: "LookupAdditionalData",
                principalColumn: "AdditionalDataId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TblMRB_LookupAdditionalData_MrbAdditionalDataId",
                table: "TblMRB");

            migrationBuilder.DropTable(
                name: "LookupAdditionalData");

            migrationBuilder.DropIndex(
                name: "IX_TblMRB_MrbAdditionalDataId",
                table: "TblMRB");

            migrationBuilder.DropColumn(
                name: "MrbAdditionalDataId",
                table: "TblMRB");
        }
    }
}
