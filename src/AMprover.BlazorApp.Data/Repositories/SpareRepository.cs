using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;

namespace AMprover.Data.Repositories;

public interface ISpareRepository : IAssetManagementBaseRepository<Spare>
{
    List<Spare> GetSpareByRisk(int id);
}

public class SpareRepository : AssetManagementBaseRepository<Spare>, ISpareRepository
{
    public SpareRepository(AssetManagementDbContext dbContext, ILogger<AssetManagementBaseRepository<Spare>> logger) : base(dbContext, logger)
    {
    }

    public List<Spare> GetSpareByRisk(int id)
    {
        return _dbContext.Spare.Where(x => x.SpareMrbId == id).ToList();
    }
}