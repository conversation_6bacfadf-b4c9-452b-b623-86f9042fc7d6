using Microsoft.AspNetCore.Identity;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.Identity;

public class UserAccount : IdentityUser
{
    [MaxLength(256)]
    [ProtectedPersonalData]
    [Required]
    public string Name { get; set; }

    /// <summary>
    /// Username is the unique e-mail address, which is enforced as the configured .Email property as well.
    /// </summary>
    /// <remarks>overriden to be able to add the [Required] attribute for (edit)form validation.</remarks>
    [ProtectedPersonalData]
    [Required]
    [EmailAddress]
    public override string UserName { get => base.UserName; set => base.UserName = value; }
    
    [MaxLength(50)]
    public string Company { get; set; }

    public ICollection<PortfolioAssignment> PortfolioAssignments { get; set; }
}