namespace AMprover.Data.Entities.AM;

public class MrbTree
{
    public int? MrbId { get; set; }
    public int? ScenarioId { get; set; }
    public int? RiskObjId { get; set; }
    public int? ChildObjectId { get; set; }
    public int? ChildObject1Id { get; set; }
    public int? ChildObject2Id { get; set; }
    public int? ChildObject3Id { get; set; }
    public int? ChildObject4Id { get; set; }
    public string MrbName { get; set; }
    public string ScenarioName { get; set; }
    public string RiskObjName { get; set; }
    public string ChildObjectName { get; set; }
    public string ChildObject1Name { get; set; }
    public string ChildObject2Name { get; set; }
    public string ChildObject3Name { get; set; }
    public string ChildObject4Name { get; set; }
}