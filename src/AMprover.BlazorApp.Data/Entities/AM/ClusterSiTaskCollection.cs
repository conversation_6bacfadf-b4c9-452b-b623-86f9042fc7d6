using System;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// View not a table
/// </summary>
public class ClusterSiTaskCollection
{
    public int TskId { get; set; }
    public int? TskMrbId { get; set; }
    public int? TskCluster { get; set; }
    public string TskPermit { get; set; }
    public decimal? TskCosts { get; set; }
    public decimal? TskInterval { get; set; }
    public int TskIntervalUnit { get; set; }
    public decimal? TskDownTime { get; set; }
    public int? TskCopiedFrom { get; set; }
    public int? TskWorkpackage { get; set; }
    public decimal? TskDuration { get; set; }
    public int? TskValidFromYear { get; set; }
    public string TskType { get; set; }
    public decimal? TskOptimalCost { get; set; }
    public DateTime? TskFinishDate { get; set; }
    public decimal? TskWorkInspCost { get; set; }
    public int? TskValidUntilYear { get; set; }
    public int? TskPriorityCode { get; set; }
    public DateTime? TskExecutionDate { get; set; }
    public int? TskCommonActionId { get; set; }
    public decimal? TskFmecaEffectPct { get; set; }
    public decimal? TskUnits { get; set; }
    public decimal? TskClusterCostPerUnit { get; set; }
    public int TskUnitType { get; set; }
    public string SiUnitsTypeValues { get; set; }
    public int? SiQualityScore { get; set; }
    public DateTime? SiYear { get; set; }
    public int PckSiSiId { get; set; }
    public int PckSiId { get; set; }
    public decimal? MrbCustomBefore { get; set; }
    public decimal? MrbCustomAfter { get; set; }
    public decimal? MrbRiskBefore { get; set; }
    public decimal? MrbRiskAfter { get; set; }
    public int? SiCategory { get; set; }
    public string TskName { get; set; }
    public string SiName { get; set; }
    public decimal? TskEstCostPerUnit { get; set; }
    public decimal? IntUnitCalculationKey { get; set; }
    public decimal? MrbDirectCostAfter { get; set; }
    public decimal? MrbDirectCostBefore { get; set; }
    public int? MrbFailureMode { get; set; }
    public decimal? MrbEffectBefore { get; set; }
    public decimal? MrbMtbfBefore { get; set; }
    public decimal? MrbEffectAfter { get; set; }
    public decimal? MrbMtbfAfter { get; set; }
    public int? NumberOfTasks { get; set; }
    public decimal? MrbCustomEffectAfter { get; set; }
    public decimal? Mrbcustomeffectbefore { get; set; }
    public int? TskStatus { get; set; }
    public int? FailRateId { get; set; }
}