namespace AMprover.Data.Entities.AM;

public class PriorityCost
{
    public int PrioCstVersion { get; set; }
    public int PrioCstId { get; set; }
    public int PrioCstYear { get; set; }
    public int PrioCstPrioId { get; set; }
    public int? PrioCstPrioTskId { get; set; }
    public decimal PrioCstCosts { get; set; }
    public decimal? PrioCstRiskCosts { get; set; }
    public decimal? PrioCstDirectCost { get; set; }
    public decimal? PrioCstRiskDelta { get; set; }
    public decimal? PrioCstFailRate { get; set; }
    public int PrioCstNumberOfTasks { get; set; }
}