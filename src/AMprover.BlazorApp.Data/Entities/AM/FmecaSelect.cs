namespace AMprover.Data.Entities.AM;

public class FmecaSelect
{
    public int MfsId { get; set; }
    public int MfsRefrenceType { get; set; }
    public int MfsReferenceId { get; set; }
    public int? MfsTaskId { get; set; }
    public int? MfsLccId { get; set; }
    public int MfsColIndex { get; set; }
    public int? MfsSelectBefore { get; set; }
    public double? MfsValueBefore { get; set; }
    public double? MfsCustomBefore { get; set; }
    public int? MfsSelectAfter { get; set; }
    public double? MfsValueAfter { get; set; }
    public double? MfsCustomAfter { get; set; }
    public string MfsDescription { get; set; }
}