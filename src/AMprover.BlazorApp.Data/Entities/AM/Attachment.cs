using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM
{
    [Table("TblAttachment")]
    public class Attachment
    {
        [Key]
        public int AtchId { get; set; }

        public string AtchTitle { get; set; }

        public string AtchDescription { get; set; }

        public string AtchUri { get; set; }

        public int? AtchCatgoryId { get; set; }

        public int? AtchSapaId { get; set; }

        public int? AtchMrbId { get; set; }

        public int? AtchTskId { get; set; }

        public int? AtchRiskObjectId { get; set; }

        public virtual AttachmentCategory AttachmentCategory { get; set; }

        public virtual Sapa Sapa { get; set; }

        public virtual Mrb Risk { get; set; }

        public virtual Task Task { get; set; }

        public virtual RiskObject RiskObject { get; set; }
    }
}
