using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblSi")]
public class Si
{
    [Key]
    public int SiId { get; set; }
    public string SiName { get; set; }
    public string SiParentName { get; set; }
    public string SiDescription { get; set; }
    public string SiType { get; set; }
    public decimal? SiPrice { get; set; }
    public DateTime? SiYear { get; set; }
    public int? SiWarrantyPeriod { get; set; }
    public DateTime? SiContractEnd { get; set; }
    public string SiUnitType { get; set; }
    public decimal? SiLength { get; set; }
    public decimal? SiHeight { get; set; }
    public decimal? SiWidth { get; set; }
    public decimal? SiUnits { get; set; }
    public decimal? SiTotalUnits { get; set; }
    public int SiCategory { get; set; }
    public int? SiPartOf { get; set; }
    public string SiRemarks { get; set; }
    public string SiVendor { get; set; }
    public int? SiQualityScore { get; set; }
    public string SiReferenceId { get; set; }
    public string SiUnitsTypeValues { get; set; }
    public string SiSupplierTextField1 { get; set; }
    public string SiSupplierTextField2 { get; set; }
    public string SiSupplierTextField3 { get; set; }
    public DateTime? SiSupplierDateField1 { get; set; }
    public bool? SiSupplierBitField1 { get; set; }
    public int? SiSupplierIntField1 { get; set; }
    public string SiMiscTextField1 { get; set; }
    public string SiMiscTextField2 { get; set; }
    public string SiMiscTextField3 { get; set; }
    public string SiMiscTextField4 { get; set; }
    public string SiMiscTextField5 { get; set; }
    public string SiMiscTextField6 { get; set; }
    public string SiMiscTextField7 { get; set; }
    public decimal? SiMiscDecimalField1 { get; set; }
    public decimal? SiMiscDecimalField2 { get; set; }
    public bool? SiMiscBitField1 { get; set; }
    public DateTime? SiMiscDateField1 { get; set; }
    [StringLength(50)] public string SiModifiedBy { get; set; }
    public DateTime? SiDateModified { get; set; }
    public decimal? SiMtbf { get; set; }
    public int? SiBvId { get; set; }
    public string SiSite { get; set; }
    public string SiSerialNumber { get; set; }
    public string SiAssetType { get; set; }
    public string SiAssetManager { get; set; }
    public string SiAssetOwner { get; set; }
    public string SiServiceProvider { get; set; }
    public string SiServiceManager { get; set; }
    public string SiLocation { get; set; }
    public int? SiStatus { get; set; }

    [Column(TypeName = "decimal(18,2)")] public decimal SiTechLifeTime { get; set; }
    public int SiReplYearTechnLt { get; set; }
    public int SiCondTechn { get; set; }
    public int SiCondEcon { get; set; }
    public int SiCondShe { get; set; }
    public int SiCondQual { get; set; }
    public int SiCondYear { get; set; }
    public int SiUrgency { get; set; }
    [Column(TypeName = "decimal(18,2)")] public decimal SiRestLifeTimeCb { get; set; }
    [Column(TypeName = "decimal(18,2)")] public decimal SiRestLifeTimeTh { get; set; }
    [Column(TypeName = "decimal(18,2)")] public decimal SiRestLifeTimeAv { get; set; }
    public int SiReplYearCalc { get; set; }
    public int SiReplYearManual { get; set; }
    public int SiReplYearReal { get; set; }
    [Column(TypeName = "varchar(50)")] public string SiAction { get; set; }
    [Column(TypeName = "decimal(18,2)")] public decimal SiCapexInd { get; set; }
    [Column(TypeName = "decimal(18,2)")] public decimal SiCapexPercentage { get; set; }
    [Column(TypeName = "decimal(18,2)")] public decimal SiCapexEst { get; set; }
    [Column(TypeName = "varchar(max)")] public string SiRemark { get; set; }

    public virtual BvSiItems BvSiItems { get; set; }
    public virtual ICollection<ClusterTaskPlan> ClusterTaskPlan { get; set; } = new HashSet<ClusterTaskPlan>();
    public virtual ICollection<PickSi> PickSi { get; set; } = new HashSet<PickSi>();
    public virtual ICollection<Rams> Rams { get; set; } = new HashSet<Rams>();
    public virtual ICollection<Lcc> Lccs { get; set; } = new HashSet<Lcc>();
    public virtual CriticalityRanking SiCriticalityRanking { get; set; }
    public virtual LookupUserDefined SiCategoryNavigation { get; set; }

    // public virtual Si Parent { get; set; }
    // public virtual ICollection<Si> Children { get; set; }
}