using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class Descriptions
{
    [Key]
    public int DescId { get; set; }
    public int? DescTaskId { get; set; }
    public int? DescRiskId { get; set; }
    public int? DescRiskObjectId { get; set; }
    public int? DescExtraId { get; set; }
    public string DescExtraType { get; set; }
    public int? DescFieldType { get; set; }
    public string DescDescription { get; set; }
}