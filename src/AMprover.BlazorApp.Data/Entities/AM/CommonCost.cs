using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblCommonCost")]
public class CommonCost
{
    [Key]
    public int CmnCostId { get; set; }
    public string CmnCostType { get; set; }
    public string CmnCostDescription { get; set; }
    public string CmnCostCalculationType { get; set; }
    public decimal? CmnCostUnits { get; set; }
    public decimal? CmnCostNumber { get; set; }
    public decimal? CmnCostPrice { get; set; }
    public decimal? CmnCostExtraCost { get; set; }
    public string CmnCostShortKey { get; set; }
    public string CmnCostRemarks { get; set; }
    public int? CmnCostPriceIndexYear { get; set; }
    public int? CmnCostPriceGroup { get; set; }
    public string CmnCostUnitType { get; set; }
    [StringLength(50)]
    public string CmnCostModifiedBy { get; set; }
    public DateTime? CmnCostDateModified { get; set; }
    public string CmnCostReferenceCode { get; set; }
    public string CmnCostVendorCode { get; set; }
    public int? CmnCostStatus { get; set; }
    public DateTime? CmnCostStatusDate { get; set; }
    public bool? CmnCostSpare { get; set; }
    public bool? CmnCostRotating { get; set; }
    public string CmnCostOrgId { get; set; }
    public string CmnCostSubType { get; set; }
    public string CmnCostSubSubType { get; set; }

    public virtual LookupInflationGroup CmnCostPriceGroupNavigation { get; set; }
    public virtual ICollection<ClusterCost> ClusterCost { get; set; } = new HashSet<ClusterCost>();
    public virtual ICollection<CommonTaskCost> CommonTaskCost { get; set; } = new HashSet<CommonTaskCost>();
}