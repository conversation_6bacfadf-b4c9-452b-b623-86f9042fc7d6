using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class PrioTaskCollection
{
    [Key]
    public int CltpId { get; set; }
    public int? CltpClusterId { get; set; }
    public int? CltpRiskId { get; set; }
    public int? CltpObjectId { get; set; }
    public int? CltpTaskId { get; set; }
    public int? CltpSiId { get; set; }
    public int? CltpQualityScore { get; set; }
    public decimal? CltpClusterCostPerUnit { get; set; }
    public decimal? CltpSiUnits { get; set; }
    public decimal? CltpClusterCosts { get; set; }
    public decimal? CltpDuration { get; set; }
    public decimal? CltpDownTime { get; set; }
    public decimal? CltpToolCosts { get; set; }
    public DateTime? CltpDateGenerated { get; set; }
    public string CltpReferenceId { get; set; }
    public DateTime? CltpDateExecuted { get; set; }
    public int? CltpExecuteStatus { get; set; }
    public int? CltpSlack { get; set; }
    public int? CltpSlackIntervalType { get; set; }
    public DateTime? CltpExecutionDate { get; set; }
    public string CltpRemarks { get; set; }
    public int? CltpSequence { get; set; }
    public decimal? TskInterval { get; set; }
    public int? SiCategory { get; set; }
    public int? SiPartOf { get; set; }
    public decimal? MrbCustomAfter { get; set; }
    public decimal? MrbCustomBefore { get; set; }
    public decimal? MrbRiskBefore { get; set; }
    public decimal? MrbRiskAfter { get; set; }
    public decimal? IntUnitCalculationKey { get; set; }
    public string SiName { get; set; }
    public string TskName { get; set; }
    public int? TskCommonActionId { get; set; }
    public string TskType { get; set; }
    public int? SiQualityScore { get; set; }
    public decimal? MrbEffectAfter { get; set; }
    public decimal? MrbMtbfAfter { get; set; }
    public decimal? MrbMtbfBefore { get; set; }
    public decimal? MrbEffectBefore { get; set; }
    public int? CltpPriority { get; set; }
    public bool? CltpInterruptable { get; set; }
    public int? CltpShiftStartDate { get; set; }
    public int? CltpShiftEndDate { get; set; }
    public bool? CltpUseLastDateExecuted { get; set; }
}