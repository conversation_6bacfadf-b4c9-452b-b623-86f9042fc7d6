using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("TblLccEffectDetail")]
public class LcceffectDetail
{
    [Key]
    public int LccEfctId { get; set; }

    public int? LccEfctLccId { get; set; }
    public virtual Lcc LccEfctLcc { get; set; }

    public int? LccEfctLccDetailId { get; set; }
    public virtual Lccdetail LccEfctLccDetail { get; set; }

    public int? LccEfctRiskId { get; set; }
    public int? LccEfctTaskId { get; set; }
    public int? LccEfctYear { get; set; }
    public int? LccEfctEffectColumn { get; set; }
    public string LccEfctEffectName { get; set; }
    public int? LccEfctType { get; set; }
    public decimal? LccEfctFmecaBefore { get; set; }
    public decimal? LccEfctRiskBefore { get; set; }
    public decimal? LccEfctCustomBefore { get; set; }
    public decimal? LccEfctCustomRiskBefore { get; set; }
    public decimal? LccEfctFmecaAfter { get; set; }
    public decimal? LccEfctRiskAfter { get; set; }
    public decimal? LccEfctCustomAfter { get; set; }
    public decimal? LccEfctCustomRiskAfter { get; set; }
    public decimal? LccEfctPreventiveCost { get; set; }
    public decimal? LccEfctCorrectiveCost { get; set; }
    public decimal? LccEfctOptimalCost { get; set; }
    public decimal? LccEfctSparePartCost { get; set; }
    public decimal? LccEfctActionCost { get; set; }
    public decimal? LccEfctTaskFmeca { get; set; }
    public decimal? LccEfctTaskFmecaCustom { get; set; }
    public decimal? LccEfctDirectCost { get; set; }
    public int? LccEfctRamsId { get; set; }
    public int? LccEfctRamsDiagramId { get; set; }

    #region PMO fields

    [Precision(18, 2)] public decimal? LccEfctFmecaPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctRiskPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctCustomPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctCustomRiskPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctPreventiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctCorrectiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctOptimalCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctSparePartCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctActionCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctTaskFmecaPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctTaskFmecaCustomPmo { get; set; }
    [Precision(18, 2)] public decimal? LccEfctDirectCostPmo { get; set; }

    #endregion
}