using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// View, not a table
/// </summary>
public class ClusterReport
{
    [Key]
    public int ClustId { get; set; }
    public string ClustName { get; set; }
    public string ClustDescription { get; set; }
    public int ClustLevel { get; set; }
    public int? ClustPartOf { get; set; }
    public decimal? ClustInterval { get; set; }
    public string ClustIntervalUnit { get; set; }
    public string ClustInitiator { get; set; }
    [MaxLength(50)]
    public string ClustExecutor { get; set; }
    public decimal? ClustEstTaskCosts { get; set; }
    public decimal? ClustTaskCosts { get; set; }
    public decimal? ClustSharedCosts { get; set; }
    public decimal? ClustDisciplineCosts { get; set; }
    public decimal? ClustMaterialCosts { get; set; }
    public decimal? ClustEnergyCosts { get; set; }
    public decimal? ClustToolCosts { get; set; }
    public decimal? ClustTotalCmnCost { get; set; }
    public decimal? ClustDownTime { get; set; }
    public decimal? ClustDuration { get; set; }
    public int? ClustStatus { get; set; }
    public string ClustShortKey { get; set; }
    public string ClustRemark { get; set; }
    public string ClustResponsible { get; set; }
    public string ClustSecondValues { get; set; }
    [MaxLength(40)]
    public string ClustCostDescr { get; set; }
    public string CmnCostType { get; set; }
    public int? ClcId { get; set; }
    public int? ClustCostTaskId { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustCostQuantity { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustCostUnits { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustCostPrice { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustCostCost { get; set; }
    public string ClustCostRemarks { get; set; }
    public int? TskId { get; set; }
    public string TskName { get; set; }
    public string TskGeneralDescription { get; set; }
    public string TskRemark { get; set; }
    public string TskDescription { get; set; }
    public decimal? TskInterval { get; set; }
    public string TskIntervalUnit { get; set; }
    public string TskWorkPackage { get; set; }
    public string TskPolicy { get; set; }
    public string TskExecutor { get; set; }
    public string TskInitiator { get; set; }
    public int? TskMrbId { get; set; }
    public decimal? TskCosts { get; set; }
    public decimal? TskDuration { get; set; }
    public decimal? TskDownTime { get; set; }
    public string TskType { get; set; }
    public int? TskSortOrder { get; set; }
    public bool? TskRemoved { get; set; }
    public bool? TskDerived { get; set; }
    public string TskPermit { get; set; }
    public int? TskCostTskId { get; set; }
    public decimal? TskCostUnits { get; set; }
    public decimal? TskCostQuantity { get; set; }
    public decimal? TskCostPrice { get; set; }
    public decimal? TskCostCost { get; set; }
    public string TskCostDescription { get; set; }
    public string Scenario { get; set; }
    public string RiskObject { get; set; }
    public string RiskObjectDesc { get; set; }
    public string RiskObjName { get; set; }
    public string MrbObject2 { get; set; }
    public string MrbObject3 { get; set; }
    public string MrbObject4 { get; set; }
    public int? MrbId { get; set; }
    public string MrbObject0 { get; set; }
    public string MrbFailureCause { get; set; }
    public string MrbFailureConsequences { get; set; }
    public string ParentClusterName { get; set; }
    public string ClustName0 { get; set; }
    public string ClustName1 { get; set; }
    public string ClustName2 { get; set; }
    public string ClustName3 { get; set; }
}