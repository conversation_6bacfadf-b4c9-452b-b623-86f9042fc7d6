using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class PriorityGridData
{
    [Key]
    public int PrioId { get; set; }
    public string PrioName { get; set; }
    public int? PrioSiId { get; set; }
    public int? PrioSiCategory { get; set; }
    public string PrioStatus { get; set; }
    public int PrioBudId { get; set; }
    public int? PrioBudPriorityId { get; set; }
    public int? PrioBudVersion { get; set; }
    public string PrioBudRemark { get; set; }
    public decimal? PrioBudBudget { get; set; }
    public decimal? PrioBudAccepted { get; set; }
    public decimal? PrioBudRejected { get; set; }
    public decimal? PrioBudPostponed { get; set; }
    public decimal? PrioBudProposed { get; set; }
    public string PrioBudStatus { get; set; }
    [StringLength(50)] public string PrioBudInitiatedBy { get; set; }
    public DateTime? PrioBudDateInitiated { get; set; }
    [StringLength(50)] public string PrioBudModifiedBy { get; set; }
    public DateTime? PrioBudDateModified { get; set; }
    public decimal? PrioBudBudgetCostYear1 { get; set; }
    public decimal? PrioBudBudgetCostYear2 { get; set; }
    public decimal? PrioBudBudgetCostYear3 { get; set; }
    public decimal? PrioBudBudgetCostYear4 { get; set; }
    public decimal? PrioBudBudgetCostYear5 { get; set; }
    public decimal? PrioBudBudgetSumOfParts { get; set; }
    public int? PrioPartOf { get; set; }
    public bool? PrioBudTaskSelectDirty { get; set; }
    public bool? PrioBudEnableTaskSelect { get; set; }
    public decimal? PrioBudCostSelected { get; set; }
    public int? PrioBudOriginalId { get; set; }
}