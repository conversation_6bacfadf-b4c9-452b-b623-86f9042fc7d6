using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblBvAspectSets")]
public class BvAspectSets
{
    public int BvAspSetId { get; set; }
    public string BvAspSetName { get; set; }
    public string BvAspAspects { get; set; }
    [StringLength(50)]
    public string BvAspModifiedBy { get; set; }
    public DateTime? BvAspDateModified { get; set; }

    public virtual ICollection<BvWeightingModels> BvWeightingModels { get; set; } = new HashSet<BvWeightingModels>();
}