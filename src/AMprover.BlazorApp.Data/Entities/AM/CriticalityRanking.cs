using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblCriticalityRanking")]
public class CriticalityRanking
{
    [Key] public int CritId { get; set; }

    public int CritSiId { get; set; }

    public string CritSiName { get; set; }

    public virtual Si CritSi { get; set; }

    [MaxLength(50)] public string CritName { get; set; }

    public string CritDescription { get; set; }

    public int? CritFailureMode { get; set; }

    [MaxLength(50)]
    public string CritFailureMechanism { get; set; }

    public string CritFailureCause { get; set; }

    public string CritFailureConsequences { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritDownTimeAfter { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritMTTR { get; set; }

    public string CritRemarks { get; set; }

    [StringLength(50)] public string CritInitiatedBy { get; set; }

    public DateTime CritDateInitiated { get; set; }

    [StringLength(50)] public string CritModifiedBy { get; set; }

    public DateTime CritDateModified { get; set; }

    public int? CritStatus { get; set; }

    [MaxLength(150)] public string CritResponsible { get; set; }

    public string CritFmecaSelect { get; set; }

    public int? CritFmecaVersion { get; set; }

    [MaxLength(15)] public string CritRedundant { get; set; }

    public int? CritKooN { get; set; }

    [MaxLength(15)] public string CritCategory { get; set; }

    public int? CritFmeca { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritFmeca1Value { get; set; }

    public int? CritFmeca2 { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritFmeca2Value { get; set; }

    public int? CritFmeca3 { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritFmeca3Value { get; set; }

    public int? CritFmeca4 { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritFmeca4Value { get; set; }

    public int? CritFmeca5 { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritFmeca5Value { get; set; }

    public int? CritFmeca6 { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritFmeca6Value { get; set; }

    public int? CritFmeca7 { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritFmeca7Value { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritTotalValue { get; set; }

    public int? CritTotal { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritProbability { get; set; }

    public string CritReStrategy { get; set; }

    public int? CritMtbf { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? CritMtbfValue { get; set; }
}