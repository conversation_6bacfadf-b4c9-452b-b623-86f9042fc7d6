using System;

namespace AMprover.Data.Entities.AM;

public class LccOutdated
{
    public int? MrbRiskObject { get; set; }
    public DateTime? MrbLastModified { get; set; }
    public int LccId { get; set; }
    public DateTime? LccdateCalculated { get; set; }
    public DateTime? RamsLastModified { get; set; }
    public int? LccRamsDiagramId { get; set; }
    public int? RamsDiagramId { get; set; }
    public int? LccRamsId { get; set; }
    public DateTime? LccDateModified { get; set; }
    public DateTime? TaskDateModified { get; set; }
    public int? TskMrbId { get; set; }
}