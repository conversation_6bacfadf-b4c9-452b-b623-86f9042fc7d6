using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LookupFailMode
{
    [Key]
    public int FailId { get; set; }
    public string FailMode { get; set; }
    public string FailDescription { get; set; }
    [StringLength(50)] public string FailModifiedBy { get; set; }
    public DateTime? FailDateModified { get; set; }
    public decimal? FailShape { get; set; }
    public decimal? FailWeibullLocation { get; set; }
    public int? FailIntervalUnit { get; set; }
    public int FailRateId { get; set; }
    public int? FailRiskTypeId { get; set; }
    public int? FailDistributionId { get; set; }

    public virtual ICollection<Mrb> Risks { get; set; } =
        new HashSet<Mrb>(); // Manually added, was missing in AMprover4 database scheme
}