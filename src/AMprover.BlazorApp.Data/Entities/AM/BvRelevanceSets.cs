using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblBvRelevanceSets")]
public class BvRelevanceSets
{
    public int BvRelSetId { get; set; }
    public string BvRelSetName { get; set; }
    public string BvRelRelevances { get; set; }
    [StringLength(50)]
    public string BvRelModifiedBy { get; set; }
    public DateTime? BvRelDateModified { get; set; }
}