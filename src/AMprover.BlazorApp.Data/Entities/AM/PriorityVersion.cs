using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class PriorityVersion
{
    [Key]
    public int PrioVerId { get; set; }
    public string PrioVerName { get; set; }
    public string PrioVerDescription { get; set; }
    public int? PrioVerVersion { get; set; }
    public string PrioVerRemark { get; set; }
    public bool? PrioVerSealed { get; set; }
    public int? PrioVerScenario { get; set; }
}