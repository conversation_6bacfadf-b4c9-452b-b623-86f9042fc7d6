using System;

namespace AMprover.Data.Entities.AM;

public class ExportTasks
{
    public int? PrioTskPriorityId { get; set; }
    public int? PrioTskVersion { get; set; }
    public int? PrioTskRiskId { get; set; }
    public int? PrioTskObjectId { get; set; }
    public int? PrioTskCommonTaskId { get; set; }
    public string PrioTskDescription { get; set; }
    public int? PrioTskSiId { get; set; }
    public string PrioTskSiDescription { get; set; }
    public decimal? PrioTskPriorityCode { get; set; }
    public decimal? PrioTskRiskBudget { get; set; }
    public decimal? PrioTskClusterCostPerUnit { get; set; }
    public decimal? PrioTskSiUnits { get; set; }
    public string PrioTskReferenceId { get; set; }
    public DateTime? PrioTskDateExecuted { get; set; }
    public DateTime? PrioTskDateDue { get; set; }
    public int? PrioTskExecutionYear { get; set; }
    public decimal? PrioTskCosts { get; set; }
    public int? PrioTskTaskId { get; set; }
    public string PrioTskFromReference { get; set; }
}