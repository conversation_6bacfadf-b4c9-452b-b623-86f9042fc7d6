using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblDerModified")]
public class DerModified
{
    [Key]
    public int DerModId { get; set; }
    public int DerModObjectType { get; set; }
    public bool? DerModDeleted { get; set; }
    public int? DerModObjectId { get; set; }
    public int? DerModCopiedFrom { get; set; }
    public string DerModObjectKey { get; set; }
    public string DerModModifications { get; set; }
}