using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("Amprover3_TblPickSI")]
public class Amprover3PickSi
{
    public int PckSiId { get; set; }
    public int PckSiMrbId { get; set; }
    public int PckSiSiId { get; set; }
    public string PckSiReferenceId { get; set; }
    public string PckSiDescription { get; set; }
    public bool? PckSiXoutofN { get; set; }
    [StringLength(50)] public string PckSiInitiatedBy { get; set; }
    public DateTime? PckSiDateInitiated { get; set; }
    [StringLength(50)] public string PckSiModifiedBy { get; set; }
    public DateTime? PckSiDateModified { get; set; }
    public bool PckSiActive { get; set; }
    public DateTime? PckSiExecutionYear { get; set; }
    public bool? PckSiExpand { get; set; }
    public int? PckSiTaskId { get; set; }
    public int? PckSiSiCategory { get; set; }
    public string PckSiSiType { get; set; }
    public int? PckSiLinkFilterId { get; set; }
    public int? PckSiItems { get; set; }
}