using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// This is not a table but a view
/// </summary>
public class BudgetCost
{
    public int? PrioBudVersion { get; set; }
    public int? Category { get; set; }
    public int? PrioPartOf { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? Budget { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? BudgetSum { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? Accepted { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? Rejected { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? Postponed { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? Proposed { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? CostsYear1 { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? CostsYear2 { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? CostsYear3 { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? CostsYear4 { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? CostsYear5 { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? Risk { get; set; }
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? RiskDelta { get; set; }
}