using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LookupExecutor
{
    [Key]
    public int ExecutorId { get; set; }
    public string ExecutorName { get; set; }
    public string ExecutorDescription { get; set; }

    public virtual ICollection<CommonTask> CommonTask { get; set; } = new HashSet<CommonTask>();
    public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
    public virtual ICollection<Workpackage> Workpackage { get; set; } = new HashSet<Workpackage>();
    public virtual ICollection<Sapa> Sapa { get; set; } = new HashSet<Sapa>();
}