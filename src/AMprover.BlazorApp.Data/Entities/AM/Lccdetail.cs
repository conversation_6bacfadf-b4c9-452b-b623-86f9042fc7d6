using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("TblLCCDetail")]
public class Lccdetail
{
    [Key] public int LccDetId { get; set; }

    public int? LccDetLccId { get; set; }
    public virtual Lcc Lcc { get; set; }

    public int? LccDetYear { get; set; }
    public decimal? LccDetFmecaBefore { get; set; }
    public decimal? LccDetAverageBefore { get; set; }
    public decimal? LccDetRiskBefore { get; set; }
    public decimal? LccDetFmecaCustomBefore { get; set; }
    public decimal? LccDetFmecaCustomRiskBefore { get; set; }
    public decimal? LccDetFmecaAfter { get; set; }
    public decimal? LccDetAverageAfter { get; set; }
    public decimal? LccDetRiskAfter { get; set; }
    public decimal? LccDetFmecaCustomAfter { get; set; }
    public decimal? LccDetFmecaCustomRiskAfter { get; set; }
    public decimal? LccDetDepreciation { get; set; }
    public decimal? LccDetMaintenanceCost { get; set; }
    public decimal? LccDetTaskCost { get; set; }
    public decimal? LccDetActionCost { get; set; }
    public decimal? LccDetProcedureCost { get; set; }
    public decimal? LccDetModificationCost { get; set; }
    [Column(TypeName = "decimal(18, 2)")] 
    public decimal? LccDetDtCostTasks { get; set; }
    public decimal? LccDetSpareCost { get; set; }
    public decimal? LccDetTotalCost { get; set; }
    public decimal? LccDetReliability { get; set; }
    public decimal? LccDetTotalNpv { get; set; }
    public decimal? LccDetRealTotalCost { get; set; }
    public decimal? LccDetRealPreventiveCost { get; set; }
    public decimal? LccDetRealCorrectiveCost { get; set; }
    public decimal? LccDetAverageRealPreventiveCost { get; set; }
    public decimal? LccDetAverageRealCorrectiveCost { get; set; }
    public decimal? LccDetOptimalPreventiveCost { get; set; }
    public decimal? LccDetOptimalCorrectiveCost { get; set; }
    public decimal? LccDetAverageOptimalCost { get; set; }
    public decimal? LccDetAec { get; set; }
    public decimal? LccDetFailureRate { get; set; }
    public decimal? LccDetAvailabilityTechnical { get; set; }
    public decimal? LccDetUtilizationTechnichal { get; set; }
    public decimal? LccDetProductivityTechnical { get; set; }
    public decimal? LccDetEcoTechnical { get; set; }
    public decimal? LccDetPreventiveCost { get; set; }
    public decimal? LccDetCorrectiveCost { get; set; }
    public decimal? LccDetDirectCost { get; set; }
    public decimal? LccDetOpexCost { get; set; }
    public decimal? LccDetRealOpexCost { get; set; }
    public bool? LccDetExcluded { get; set; }
    public decimal? LccDetCapexCost { get; set; }

    public virtual ICollection<LcceffectDetail> EffectDetails { get; set; }

    #region PMO fields

    [Precision(18, 2)] public decimal? LccDetFmecaPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetAveragePmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetRiskPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetFmecaCustomPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetFmecaCustomRiskPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetMaintenanceCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetTaskCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetActionCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetProcedureCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetModificationCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetSpareCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetTotalCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetReliabilityPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetRealTotalCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetRealPreventiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetRealCorrectiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetAverageRealPreventiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetAverageRealCorrectiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetOptimalPreventiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetOptimalCorrectiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetAverageOptimalCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetAECPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetFailureRatePmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetAvailabilityTechnicalPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetPreventiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetCorrectiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetDirectCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetOpexCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetRealOpexCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccDetCapexCostPmo { get; set; }

    #endregion
}