using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblEditLog")]
public class EditLog
{
    public int LogId { get; set; }
    public DateTime LogDate { get; set; }
    [StringLength(50)]
    public string LogUserId { get; set; }
    public string LogObjectType { get; set; }
    public int? LogObjectId { get; set; }
    public string LogObjectVarId { get; set; }
    public string LogModificationType { get; set; }
    public string LogModifications { get; set; }
}