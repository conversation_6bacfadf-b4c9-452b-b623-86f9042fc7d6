using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class Lookup
{
    [Key]
    public int LookupId { get; set; }
    public string LookupFilter { get; set; }
    public string LookupShortDescription { get; set; }
    public string LookupLongDescription { get; set; }
    public int? LookupValue { get; set; }

    public virtual ICollection<Mrb> Risks { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<RiskObject> RiskObjects { get; set; } = new HashSet<RiskObject>();
    public virtual ICollection<Cluster> Clusters { get; set; } = new HashSet<Cluster>();
    public virtual ICollection<Scenario> Scenarios { get; set; } = new HashSet<Scenario>();
}