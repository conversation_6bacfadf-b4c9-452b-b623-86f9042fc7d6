using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM
{
    [Table("TblSapaCollection")]
    public class SapaCollection
    {
        [Key]
        public int SapaCollId { get; set; }

        public string SapaCollName { get; set; }

        public string SapaCollDescription { get; set; }

        public string SapaCollRemark { get; set; }

        public bool SapaCollSelected { get; set; }

        public int SapaCollRiskObjId { get; set; }

        public virtual ICollection<Sapa> Sapas { get; set; }

        public virtual RiskObject RiskObject { get; set; }
    }
}
