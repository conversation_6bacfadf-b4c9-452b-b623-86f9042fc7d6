using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblMrbImage")]
public class MrbImage
{
    public string MrbImageImagePathBefore { get; set; }
    public string MrbImageImagePathAfter { get; set; }
    public string MrbImageImagePathPmo { get; set; }
    
    [Key]
    public int MrbImageId { get; set; }
    public DateTime MrbImageDate { get; set; }

    public virtual Mrb MrbImageNavigation { get; set; }
}