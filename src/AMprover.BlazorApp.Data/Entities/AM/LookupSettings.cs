using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LookupSettings
{
    public string AmsProperty { get; set; }
    public string AmsTextValue { get; set; }
    public decimal? AmsDecimalValue { get; set; }
    public int? AmsIntValue { get; set; }
    [StringLength(50)] public string AmsInitiatedBy { get; set; }
    public DateTime? AmsDateInitiated { get; set; }
    [StringLength(50)] public string AmsModifiedBy { get; set; }
    public DateTime? AmsDateModified { get; set; }
    public bool? AmsModifiable { get; set; }
}