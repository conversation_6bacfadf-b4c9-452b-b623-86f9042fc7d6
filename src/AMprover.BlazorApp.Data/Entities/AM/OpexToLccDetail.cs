using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class OpexToLccDetail
{
    [Key]
    public int OpexLccDetId { get; set; }
    public int? OpexLccDetLccId { get; set; }
    public int? OpexLccDetOpexLccId { get; set; }
    public int? OpexLccDetYear { get; set; }
    public decimal? OpexLccDetCost { get; set; }
    public decimal? OpexLccDetPv { get; set; }
    public decimal? OpexLccDetNpv { get; set; }
    public decimal? OpexLccDetAec { get; set; }
}