using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LccGrid
{
    [Key]
    public int LccId { get; set; }
    public int? LccScenarioId { get; set; }
    public int? LccRiskObject { get; set; }
    public int? LccChildObject { get; set; }
    public int? LccChildObject1 { get; set; }
    public int? LccChildObject2 { get; set; }
    public int? LccChildObject3 { get; set; }
    public int? LccChildObject4 { get; set; }
    public string LccName { get; set; }
    public int? LccPartOf { get; set; }
    public decimal? LccNpv { get; set; }
    public int? LccNpvyear { get; set; }
    public string LccRemark { get; set; }
    public decimal? LccTotalAverageCost { get; set; }
    public decimal? LccAverageOptimalCost { get; set; }
    public decimal? LccPotential { get; set; }
    public decimal? LccAec { get; set; }
    public decimal? LccMcRav { get; set; }
    public decimal? LccInputAvailabilityTechnical { get; set; }
    public decimal? LccInputReliabilityFunctional { get; set; }
    public decimal? LccOutputAvailabilityTechnical { get; set; }
    public decimal? LccOutputReliabilityTechnical { get; set; }
    public byte[] LccOptimalImage { get; set; }
    public byte[] LccRealCostImage { get; set; }
    public byte[] LccRamsImage { get; set; }
    public decimal? LccReplacementValue { get; set; }
    public decimal? LccMtbftechnical { get; set; }
    public decimal? LccMtbffunctional { get; set; }
    public decimal? LccOptimalAverageCorrectiveCost { get; set; }
    public decimal? LccOptimalAveragePreventiveCost { get; set; }
    public decimal? LccOverallProductionCost { get; set; }
    public decimal? LccUtilizationTechnical { get; set; }
    public decimal? LccUtilizationFunctional { get; set; }
    public decimal? LccProductivityTechnical { get; set; }
    public decimal? LccProductivityFunctional { get; set; }
    public decimal? LccEcoTechnical { get; set; }
    public decimal? LccEcoFunctional { get; set; }
    public string RiskObjName { get; set; }
    public string ObjName { get; set; }
    public string ChildObject2 { get; set; }
    public string ChildObject3 { get; set; }
    public string ChildObject4 { get; set; }
    public decimal? RiskObjProdCostHour { get; set; }
    public int? ObjProductionTime { get; set; }
    public int? ObjUsableTime { get; set; }
    public int? ObjUtilizationTime { get; set; }
    public int? ObjAvailableTime { get; set; }
    public decimal? LccDiscountRate { get; set; }
    public int? RiskObjNoOfInstallation { get; set; }
    public string PartOfName { get; set; }
    public decimal? LccProductionCost { get; set; }
    public string FmecaMatrix { get; set; }
    public int? FmecaVersion { get; set; }
    public decimal? ObjNewValue { get; set; }
    public int? ObjId { get; set; }
    public int? LccMaxYears { get; set; }
    public decimal? Obj2NewValue { get; set; }
    public decimal? Obj3NewValue { get; set; }
    public decimal? Obj4NewValue { get; set; }
    public int? LccRamsDiagramId { get; set; }
    public int? LccRamsId { get; set; }
    public bool? LccExclude { get; set; }
    [StringLength(50)] public string LccModifiedBy { get; set; }
    public DateTime? LccDateModified { get; set; }
    public DateTime? LccdateCalculated { get; set; }
    public int? LccSiId { get; set; }
}