namespace AMprover.Data.Entities.AM;

public class LccOpexCalc
{
    public int? OpexLccOpexDataId1 { get; set; }
    public int? OpexLccOpexDataId2 { get; set; }
    public int? OpexLccOpexDataId3 { get; set; }
    public int? OpexLccLccId { get; set; }
    public string OpexLccName { get; set; }
    public string OpexLccColorName { get; set; }
    public bool? OpexLccShowInGraph { get; set; }
    public int OpexLccId { get; set; }
    public string OpexDataName { get; set; }
    public int? OpexDataInflationGroupId { get; set; }
    public decimal? OpexDataPercentage { get; set; }
    public string OpexDataName2 { get; set; }
    public int? OpexDataInflationGroupId2 { get; set; }
    public decimal? OpexDataPercentage2 { get; set; }
    public string OpexDataName3 { get; set; }
    public int? OpexDataInflationGroupId3 { get; set; }
    public decimal? OpexDataPercentage3 { get; set; }
    public decimal? OpexLccUnits { get; set; }
    public decimal? OpexLccQuantity { get; set; }
    public decimal? OpexLccPrice { get; set; }
    public int? OpexDataMethod { get; set; }
    public int? Expr1 { get; set; }
    public int? Expr2 { get; set; }
}