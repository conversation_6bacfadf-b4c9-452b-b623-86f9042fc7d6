using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblObject")]
public class Object
{
    [Key]
    public int ObjId { get; set; }
    public string ObjDescription { get; set; }
    public string ObjFunction { get; set; }
    public byte[] ObjImage { get; set; }
    
    [MaxLength(20)]
    public string ObjFilterRef { get; set; }

    [MaxLength(50)]
    public string ObjShortKey { get; set; }

    [MaxLength(50)]
    public string ObjName { get; set; }

    public int ObjLevel { get; set; }
    public decimal? ObjNewValue { get; set; }
    public int? ObjProductionTime { get; set; }
    public int? ObjUsableTime { get; set; }
    public int? ObjUtilizationTime { get; set; }
    public int? ObjAvailableTime { get; set; }
    public int? ObjSiCategory { get; set; }

    [MaxLength(50)]
    public string ObjSiType { get; set; }

    [StringLength(50)]
    public string ObjModifiedBy { get; set; }
    public DateTime? ObjDateModified { get; set; }

    public virtual ICollection<RiskObject> RiskObject { get; set; } = new HashSet<RiskObject>();

    public virtual ICollection<RiskObject> ChildRiskObjects { get; set; } = new HashSet<RiskObject>();

    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject { get; set; } = new HashSet<SiLinkFilters>();
    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject1 { get; set; } = new HashSet<SiLinkFilters>();
    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject2 { get; set; } = new HashSet<SiLinkFilters>();
    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject3 { get; set; } = new HashSet<SiLinkFilters>();
    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject4 { get; set; } = new HashSet<SiLinkFilters>();

    public virtual ICollection<Lcc> ChildObjects { get; set; } = new HashSet<Lcc>();
    public virtual ICollection<Lcc> ChildObjects1 { get; set; } = new HashSet<Lcc>();
    public virtual ICollection<Lcc> ChildObjects2 { get; set; } = new HashSet<Lcc>();
    public virtual ICollection<Lcc> ChildObjects3 { get; set; } = new HashSet<Lcc>();
    public virtual ICollection<Lcc> ChildObjects4 { get; set; } = new HashSet<Lcc>();
        
    public virtual ICollection<Mrb> MrbChildObjects { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<Mrb> MrbChildObjects1 { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<Mrb> MrbChildObjects2 { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<Mrb> MrbChildObjects3 { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<Mrb> MrbChildObjects4 { get; set; } = new HashSet<Mrb>();
}