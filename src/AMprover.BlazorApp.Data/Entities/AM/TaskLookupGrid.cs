using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class TaskLookupGrid
{
    [Key]
    public int TskId { get; set; }
    public string TskName { get; set; }
    public string TskDescription { get; set; }
    public int? TskMrbId { get; set; }
    public string MrbName { get; set; }
    public int? TskCluster { get; set; }
    public string ClustName { get; set; }
    public int TskInitiator { get; set; }
    public string InitiatorName { get; set; }
    public int? TskMxPolicy { get; set; }
    public string PolName { get; set; }
    public string TskGeneralDescription { get; set; }
    public string TskRemark { get; set; }
    public string TskPermit { get; set; }
    public string TskResponsible { get; set; }
    public int? TskExecutor { get; set; }
    public decimal? TskCosts { get; set; }
    public decimal? TskInterval { get; set; }
    public string IntUnitName { get; set; }
    public int TskIntervalUnit { get; set; }
    [StringLength(50)] public string TskInitiatedBy { get; set; }
    public DateTime? TskDateInitiated { get; set; }
    [StringLength(50)] public string TskModifiedBy { get; set; }
    public DateTime? TskDateModified { get; set; }
    public decimal? TskDownTime { get; set; }
    public int? TskSortOrder { get; set; }
    public bool? TskExtraBool2 { get; set; }
    public bool? TskExtraBool3 { get; set; }
    public bool? TskExtraBool4 { get; set; }
    public int? TskStatus { get; set; }
    public int? TskCopiedFrom { get; set; }
    public int? TskWorkpackage { get; set; }
    public string WpName { get; set; }
    public decimal? TskDuration { get; set; }
    public int? TskValidFromYear { get; set; }
    public string TskType { get; set; }
    public decimal? TskOptimalCost { get; set; }
    public DateTime? TskFinishDate { get; set; }
    public decimal? TskWorkInspCost { get; set; }
    public int? TskValidUntilYear { get; set; }
    public int? TskPriorityCode { get; set; }
    public DateTime? TskExecutionDate { get; set; }
    public int? TskCommonActionId { get; set; }
    public string TskFmecaEffect { get; set; }
    public string TskLcceffect { get; set; }
    public decimal? TskFmecaEffectPct { get; set; }
    public int TskFmecaVersion { get; set; }
    public decimal? TskUnits { get; set; }
    public decimal? TskEstCostPerUnit { get; set; }
    public int TskUnitType { get; set; }
    public decimal? TskClusterCostPerUnit { get; set; }
    public bool? TskClusterCostMember { get; set; }
    public decimal? TskClusterCosts { get; set; }
    public string RiskObjName { get; set; }
    public string ObjectName2 { get; set; }
    public string ObjectName3 { get; set; }
    public string ObjectName4 { get; set; }
    public int RiskObjId { get; set; }
    public string ExecutorName { get; set; }
    public int RiskObjScenarioId { get; set; }
    public bool? TskMaster { get; set; }
    public bool? TskRemoved { get; set; }
    public bool? TskDerived { get; set; }
    public bool? TskSkipInLcc { get; set; }
}