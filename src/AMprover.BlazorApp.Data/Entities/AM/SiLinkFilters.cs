using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblSiLinkFilters")]
public class SiLinkFilters
{
    public SiLinkFilters()
    {
        PickSi = new HashSet<PickSi>();
    }

    [Key]
    public int SifId { get; set; }
    public string SifName { get; set; }
    public int? SifScenarioId { get; set; }
    public int? SifObjectId { get; set; }
    public int? SifRiskId { get; set; }
    public int? SifCategory { get; set; }
    public string SifDescription { get; set; }
    public int? SifFilterId { get; set; }
    public bool SifExcludeFromParent { get; set; }
    public int? SifRiskObjectId { get; set; }
    public int? SifChildObjectId { get; set; }
    public int? SifChildObject1Id { get; set; }
    public int? SifChildObject2Id { get; set; }
    public int? SifChildObject3Id { get; set; }
    public int? SifChildObject4Id { get; set; }
    public int? SifTaskId { get; set; }

    public virtual Object SifChildObject { get; set; }
    public virtual Object SifChildObject1 { get; set; }
    public virtual Object SifChildObject2 { get; set; }
    public virtual Object SifChildObject3 { get; set; }
    public virtual Object SifChildObject4 { get; set; }
    public virtual Filters SifFilterNavigation { get; set; }
    public virtual Mrb SifRisk { get; set; }
    public virtual RiskObject SifRiskObject { get; set; }
    public virtual Task SifTask { get; set; }
    public virtual ICollection<PickSi> PickSi { get; set; }
}