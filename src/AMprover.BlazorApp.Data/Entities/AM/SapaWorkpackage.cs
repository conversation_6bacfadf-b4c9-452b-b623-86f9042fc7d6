using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM
{
    [Table("TblSapaWorkpackage")]
    public class SapaWorkpackage
    {
        [Key]
        public int SapaWpId { get; set; }

        [MaxLength(30)]
        public string SapaWpKey { get; set; }

        [MaxLength(100)]
        public string SapaWpName { get; set; }

        [MaxLength(255)]
        public string SapaWpDescription { get; set; }

        public virtual ICollection<Sapa> Sapa { get; set; } = new HashSet<Sapa>();

        public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
    }
}
