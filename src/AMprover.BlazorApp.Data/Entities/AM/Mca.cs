using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class Mca
{
    [Key]
    public int McaId { get; set; }
    public int McaSiId { get; set; }
    public int? McaCommercial { get; set; }
    public int? McaUtilization { get; set; }
    public int? McaSocial { get; set; }
    public int? McaUsage { get; set; }
    public int? McaIntensity { get; set; }
    public int? McaRepresentative { get; set; }
    public int? McaQualityScore { get; set; }
    public int? McaQualityLevel { get; set; }
}