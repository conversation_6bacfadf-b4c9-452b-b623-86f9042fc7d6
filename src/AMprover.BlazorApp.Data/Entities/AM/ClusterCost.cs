using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblClusterCost")]
public class ClusterCost
{
    [Key]
    public int ClcId { get; set; }
    public int? ClcClusterId { get; set; }
    public int ClcCommonCostId { get; set; }
    public int? ClcTaskId { get; set; }
    public decimal? ClcUnits { get; set; }
    public decimal? ClcQuantity { get; set; }
    public decimal ClcPrice { get; set; }
    public decimal ClcCost { get; set; }
    public string ClcRemarks { get; set; }
    public string ClcCalculationType { get; set; }
    public int? ClcPriceIndexYear { get; set; }
    public bool? ClcIsCommonTaskCost { get; set; }
    public string ClcType { get; set; }

    public virtual Cluster ClcCluster { get; set; }
    public virtual CommonCost ClcCommonCost { get; set; }
    public virtual Task ClcTask { get; set; }
}