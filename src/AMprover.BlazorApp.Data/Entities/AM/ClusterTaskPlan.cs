using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblClusterTaskPlan")]
public class ClusterTaskPlan
{
    [Key]
    public int CltpId { get; set; }
    public int? CltpClusterId { get; set; }
    public int? CltpRiskId { get; set; }
    public int? CltpObjectId { get; set; }
    public int? CltpTaskId { get; set; }
    public int? CltpCommonTaskId { get; set; }
    public int? CltpSiId { get; set; }
    public int? CltpQualityScore { get; set; }
    public decimal? CltpClusterCostPerUnit { get; set; }
    public decimal? CltpSiUnits { get; set; }
    public decimal? CltpClusterCosts { get; set; }
    public decimal? CltpDuration { get; set; }
    public decimal? CltpDownTime { get; set; }
    public decimal? CltpToolCosts { get; set; }
    [Column(TypeName = "decimal(18,3)")]public decimal? CltpEstCosts { get; set; }
    [Column(TypeName = "decimal(18,3)")]public decimal? CltpTotalCosts { get; set; }
    public DateTime? CltpDateGenerated { get; set; }
    public string CltpReferenceId { get; set; }
    public DateTime? CltpDateExecuted { get; set; }
    public int? CltpExecuteStatus { get; set; }
    public int? CltpSlack { get; set; }
    public int? CltpSlackIntervalType { get; set; }
    public DateTime? CltpExecutionDate { get; set; }
    public string CltpRemarks { get; set; }
    public int? CltpSequence { get; set; }
    public int? CltpPriority { get; set; }
    public bool? CltpInterruptable { get; set; }
    public int? CltpShiftStartDate { get; set; }
    public int? CltpShiftEndDate { get; set; }
    public bool? CltpUseLastDateExecuted { get; set; }

    public virtual Cluster CltpCluster { get; set; }
    public virtual Mrb CltpRisk { get; set; }
    public virtual Si CltpSi { get; set; }
    public virtual Task CltpTask { get; set; }
}