using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class SiStatisticsRiskQualification
{
    [Key]
    public int SiId { get; set; }
    public string SiName { get; set; }
    public string SiDescription { get; set; }
    public int SiStatId { get; set; }
    public int SiCategory { get; set; }
    public int SiStatScenarioId { get; set; }
    public decimal? RiskIndexAfter { get; set; }
    public decimal? RiskIndexBefore { get; set; }
    public decimal RiskNumberBefore { get; set; }
    public decimal RiskNumberAfter { get; set; }
}