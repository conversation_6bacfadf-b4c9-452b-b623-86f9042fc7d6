using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblSiStatistics")]
public class SiStatistics
{
    [Key]
    public int SiStatId { get; set; }
    public int SiStatScenarioId { get; set; }
    public int SiStatSiId { get; set; }
    public string SiStatSumListBefore { get; set; }
    public string SiStatSumListAfter { get; set; }
    public string SiStatColorListBefore { get; set; }
    public string SiStatColorListAfter { get; set; }
    public DateTime? SiStatDateModified { get; set; }
    [StringLength(50)] public string SiStatModifiedBy { get; set; }
    public decimal? SiStatRiskBefore { get; set; }
    public decimal? SiStatRiskAfter { get; set; }
    public int? SiStatFmecaId { get; set; }
}