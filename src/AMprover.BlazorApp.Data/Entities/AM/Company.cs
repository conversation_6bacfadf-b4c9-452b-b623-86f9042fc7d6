using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class Company
{
    [Key]
    public int CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string CompanyDescription { get; set; }
    public string CompanyContact1 { get; set; }
    public string CompanyContact2 { get; set; }
    public string CompanyAdres { get; set; }
    public string CompanyPostAdres { get; set; }
    public string CompanyZipCode { get; set; }
    public string CompanyPlace { get; set; }
    public string CompanyCountry { get; set; }
    public string CompanyPhone { get; set; }
    public string CompanyFax { get; set; }
    public string CompanyEmail { get; set; }
}