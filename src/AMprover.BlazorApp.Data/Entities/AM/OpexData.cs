using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblOpexData")]
public class OpexData
{
    public OpexData()
    {
        OpexFactor = new HashSet<OpexFactor>();
        OpexToLccOpexLccOpexDataId1Navigation = new HashSet<OpexToLcc>();
        OpexToLccOpexLccOpexDataId2Navigation = new HashSet<OpexToLcc>();
        OpexToLccOpexLccOpexDataId3Navigation = new HashSet<OpexToLcc>();
    }
    [Key]
    public int OpexDataId { get; set; }
    public int? OpexDataInflationGroupId { get; set; }
    public string OpexDataName { get; set; }
    public decimal? OpexDataValue { get; set; }
    public decimal? OpexDataPercentage { get; set; }
    public int? OpexDataMethod { get; set; }
    [StringLength(50)]
    public string OpexDataModifiedBy { get; set; }
    public DateTime? OpexDataDateModified { get; set; }
    public int? OpexDataCostType { get; set; }

    public virtual ICollection<OpexFactor> OpexFactor { get; set; }
    public virtual ICollection<OpexToLcc> OpexToLccOpexLccOpexDataId1Navigation { get; set; }
    public virtual ICollection<OpexToLcc> OpexToLccOpexLccOpexDataId2Navigation { get; set; }
    public virtual ICollection<OpexToLcc> OpexToLccOpexLccOpexDataId3Navigation { get; set; }
}