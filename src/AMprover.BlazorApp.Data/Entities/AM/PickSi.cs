using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblPickSI")]
public class PickSi
{
    [Key]
    public int PckSiId { get; set; }
    public int PckSiMrbId { get; set; }
    public int PckSiSiId { get; set; }
    public int? PckSiLinkFilterId { get; set; }

    public virtual SiLinkFilters PckSiLinkFilter { get; set; }
    public virtual Mrb PckSiMrb { get; set; }
    public virtual Si PckSiSi { get; set; }
}