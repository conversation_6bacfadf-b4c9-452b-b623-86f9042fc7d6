using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblVdmxl")]
public class Vdmxl
{
    [Key]
    public int VdmId { get; set; }

    public int VdmLccId { get; set; }

    [MaxLength(50)]
    public string VdmName { get; set; }

    [Column(TypeName = "decimal(18, 2)")] public decimal? VdmDtCostTasks { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca1Pmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca1Before { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca1After { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca2Before { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca2Pmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca2After { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca3Before { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca3Pmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca3After { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca4Before { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca4Pmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca4After { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca5Before { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca5Pmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca5After { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca6Before { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca6Pmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca6After { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca7Before { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca7Pmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca7After { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca8Before { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca8Pmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmFmeca8After { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmOpexBefore { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmOpexPmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmOpexAfter { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmCapexBefore { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmCapexPmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmCapexAfter { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmCapexPoBefore { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmCapexPoPmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmCapexPoiAfter { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmNpvBefore { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmNpvPmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmNpvAfter { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmAecBefore { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmAecPmo { get; set; }
    [Column(TypeName = "decimal(18, 2)")] public decimal VdmAecAfter { get; set; }

    public int VdmOptYearBefore { get; set; }
    public int VdmOptYearPmo { get; set; }
    public int VdmOptYearAfter { get; set; }

    public virtual Lcc Lcc { get; set; }
}