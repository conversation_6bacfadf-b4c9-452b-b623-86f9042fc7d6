using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LookupIntervalUnit
{
    public LookupIntervalUnit()
    {
        CommonTask = new HashSet<CommonTask>();
        Task = new HashSet<Task>();
        Workpackage = new HashSet<Workpackage>();
    }

    [Key]
    public int IntUnitId { get; set; }
    public string IntUnitName { get; set; }
    public decimal? IntUnitCalculationKey { get; set; }
    public string IntUnitDescription { get; set; }
    public string IntUnitShortKey { get; set; }
    [StringLength(50)]
    public string IntUnitModifiedBy { get; set; }
    public DateTime? IntUnitDateModified { get; set; }

    public virtual ICollection<CommonTask> CommonTask { get; set; }
    public virtual ICollection<Task> Task { get; set; }
    public virtual ICollection<Workpackage> Workpackage { get; set; }
}