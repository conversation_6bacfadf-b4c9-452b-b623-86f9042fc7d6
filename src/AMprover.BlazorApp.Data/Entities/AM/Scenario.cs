using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblScenario")]
public class Scenario
{
    public Scenario()
    {
        RiskObject = new HashSet<RiskObject>();
    }

    [Key]
    public int ScenId { get; set; }
    public string ScenName { get; set; }
    public string ScenDescr { get; set; }
    [StringLength(50)] public string ScenInitiatedBy { get; set; }
    public DateTime? ScenDateInitiated { get; set; }
    public string ScenShrtKey { get; set; }
    public string ScenStartPoint { get; set; }
    public int? ScenCopiedFrom { get; set; }
    [StringLength(50)] public string ScenModifiedBy { get; set; }
    public DateTime? ScenDateModified { get; set; }
    public int? ScenChildType { get; set; }

    public int? ScenStatus { get; set; }
    public virtual Lookup ScenStatusNavigation { get; set; }
    public virtual ICollection<RiskObject> RiskObject { get; set; }

    public virtual ICollection<Cluster> Clusters { get; set; }

    public override string ToString()
    {
        return ScenName;
    }
}