using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblBvWeightingModels")]
public class BvWeightingModels
{
    public BvWeightingModels()
    {
        BvSiItems = new HashSet<BvSiItems>();
    }

    public int BvModelId { get; set; }
    public string BvModelName { get; set; }
    public int? BvModelAspectSetId { get; set; }
    public int? BvModelFmecaId { get; set; }
    public string BvModelEffectWeightSet { get; set; }
    [StringLength(50)]
    public string BvModelModifiedBy { get; set; }
    public DateTime? BvModelDateModified { get; set; }

    public virtual BvAspectSets BvModelAspectSet { get; set; }
    public virtual Fmeca BvModelFmeca { get; set; }
    public virtual ICollection<BvSiItems> BvSiItems { get; set; }
}