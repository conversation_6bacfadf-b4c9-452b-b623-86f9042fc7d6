using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblCommonTask")]
public class CommonTask
{
    [Key]
    public int CmnTaskId { get; set; }
    [StringLength(60)]
    public string CmnTaskName { get; set; }
    public string CmnTaskDescription { get; set; }
    [StringLength(30)]
    public string CmnTaskReferenceId { get; set; }
    public int? CmnTaskInitiator { get; set; }
    public int? CmnTaskExecutor { get; set; }
    public int? CmnTaskMxPolicy { get; set; }
    public string CmnTaskGeneralDescription { get; set; }
    public string CmnTaskRemark { get; set; }
    [StringLength(10)]
    public string CmnTaskPermit { get; set; }
    [StringLength(50)]
    public string CmnTaskResponsible { get; set; }
    public decimal? CmnTaskCosts { get; set; }
    public decimal? CmnTaskInterval { get; set; }
    public int? CmnTaskIntervalUnit { get; set; }
    [StringLength(50)]
    public string CmnTaskInitiatedBy { get; set; }
    public DateTime? CmnTaskDateInitiated { get; set; }
    [StringLength(50)]
    public string CmnTaskModifiedBy { get; set; }
    public DateTime? CmnTaskDateModified { get; set; }
    public decimal? CmnTaskDownTime { get; set; }
    public int? CmnTaskSortOrder { get; set; }
    public int? CmnTaskMasterId { get; set; }
    public int? CmnTaskWorkPackage { get; set; }
    public decimal? CmnTaskDuration { get; set; }
    public int? CmnTaskValidFromYear { get; set; }
    [StringLength(20)]
    public string CmnTaskType { get; set; }
    public decimal? CmnTaskWorkInspCost { get; set; }
    public int? CmnTaskValidUntilYear { get; set; }
    public int? CmnTaskPriorityCode { get; set; }
    public string CmnTaskFieldRights { get; set; }
    public bool? CmnTaskCostModifiable { get; set; }
    public bool? CmnTaskInitiatorModifiable { get; set; }
    public bool? CmnTaskExecutorModifiable { get; set; }
    public bool? CmnTaskWorkPackageModifiable { get; set; }
    public bool? CmnTaskIntervalModifiable { get; set; }
    public int CmnTaskUnitType { get; set; }
    public int? CmnTaskSiCategory { get; set; }
    
    [MaxLength(20)]
    public string CmnTaskFilterRef { get; set; }

    public virtual LookupExecutor CmnTaskExecutorNavigation { get; set; }
    public virtual LookupInitiator CmnTaskInitiatorNavigation { get; set; }
    public virtual LookupIntervalUnit CmnTaskIntervalUnitNavigation { get; set; }
    public virtual LookupMxPolicy CmnTaskMxPolicyNavigation { get; set; }
    public virtual Workpackage CmnTaskWorkPackageNavigation { get; set; }
    public virtual ICollection<CommonTaskCost> CommonTaskCost { get; set; } = new HashSet<CommonTaskCost>();
    public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
}