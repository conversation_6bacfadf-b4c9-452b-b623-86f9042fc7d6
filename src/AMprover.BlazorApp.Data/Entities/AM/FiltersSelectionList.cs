using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblFiltersSelectionList")]
public class FiltersSelectionList
{
    public int SqlSelId { get; set; }
    public int Sequence { get; set; }
    public string FieldName { get; set; }
    public string FieldType { get; set; }
    public string Selection { get; set; }
    public string Criterium { get; set; }
    public string AndOr { get; set; }
    public int? SortOrder { get; set; }
    public string SortType { get; set; }
    public string FriendlyName { get; set; }
    public bool IsAdvanced { get; set; }
    public short Ident { get; set; }

    public virtual Filters SqlSel { get; set; }

    public override string ToString()
    {
        return $"{nameof(FiltersSelectionList)} - {FieldName}.{Criterium}({Selection})";
    }
}