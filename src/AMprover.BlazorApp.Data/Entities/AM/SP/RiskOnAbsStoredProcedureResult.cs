using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM.SP
{
    [Keyless]
    public class RiskOnAbsStoredProcedureResult
    {
        public int? AssetId { get; set; }

        public string AssetCode { get; set; }

        public string AssetParentCode { get; set; }

        public string AssetName { get; set; }

        public int? RiskId { get; set; }

        public int? RiskObjectId { get; set; }

        public string RiskName { get; set; }

        public int? FmecaId { get; set; }

        public int? FmecaMtbfBefore { get; set; }

        public int? FmecaMtbfAfter { get; set; }

        public int? FmecaMtbfPmo { get; set; }

        public string FmecaSelectionBefore { get; set; }

        public string FmecaSelectionAfter { get; set; }

        public string FmecaSelectionPmo { get; set; }

        public int? CategoryId { get; set; }

        public string Category { get; set; }
    }
}
