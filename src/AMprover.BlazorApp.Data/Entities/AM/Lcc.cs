using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("TblLCC")]
public class Lcc
{
    [Key] public int LccId { get; set; }
    public int? LccScenarioId { get; set; }
    public virtual Scenario LccScenario { get; set; }

    public int? LccRiskObject { get; set; }
    public virtual RiskObject RiskObject { get; set; }

    public int? LccChildObject { get; set; }
    public virtual Object ChildObject { get; set; }

    //Installation
    public int? LccChildObject1 { get; set; }
    public virtual Object ChildObject1 { get; set; }

    //system
    public int? LccChildObject2 { get; set; }
    public virtual Object ChildObject2 { get; set; }

    //component
    public int? LccChildObject3 { get; set; }
    public virtual Object ChildObject3 { get; set; }

    //assembly
    public int? LccChildObject4 { get; set; }
    public virtual Object ChildObject4 { get; set; }

    public string LccName { get; set; }
    public int? LccPartOf { get; set; }
    public virtual Lcc LccPartOfLcc { get; set; }
    public virtual ICollection<Lcc> LccChildren { get; set; }

    public int LccStartYear { get; set; }
    public int LccAge { get; set; }
    public decimal? LccDiscountRate { get; set; }
    public decimal? LccNpv { get; set; }
    public int? LccNpvyear { get; set; }
    public string LccRemark { get; set; }
    public decimal? LccTotalAverageCost { get; set; }
    public decimal? LccAverageOptimalCost { get; set; }
    public decimal? LccPotential { get; set; }
    public decimal? LccAec { get; set; }
    public decimal? LccMcRav { get; set; }
    public decimal? LccInputAvailabilityTechnical { get; set; }
    public decimal? LccInputReliabilityFunctional { get; set; }
    public decimal? LccOutputAvailabilityTechnical { get; set; }
    public decimal? LccOutputReliabilityTechnical { get; set; }
    public byte[] LccOptimalImage { get; set; }
    public byte[] LccRealCostImage { get; set; }
    public byte[] LccRamsImage { get; set; }
    public decimal? LccReplacementValue { get; set; }
    public decimal? LccMtbftechnical { get; set; }
    public decimal? LccMtbffunctional { get; set; }
    public decimal? LccOptimalAverageCorrectiveCost { get; set; }
    public decimal? LccOptimalAveragePreventiveCost { get; set; }
    public decimal? LccOverallProductionCost { get; set; }
    public decimal? LccUtilizationTechnical { get; set; }
    public decimal? LccUtilizationFunctional { get; set; }
    public decimal? LccProductivityTechnical { get; set; }
    public decimal? LccProductivityFunctional { get; set; }
    public decimal? LccEcoTechnical { get; set; }
    public decimal? LccEcoFunctional { get; set; }
    public decimal? LccProductionCost { get; set; }
    public int? LccMaxYears { get; set; }

    public int? LccRamsDiagramId { get; set; }

    public int? LccRamsId { get; set; }

    public bool? LccExclude { get; set; }
    [StringLength(50)] public string LccModifiedBy { get; set; }
    public DateTime? LccDateModified { get; set; }
    public DateTime? LccdateCalculated { get; set; }

    public int? LccSiId { get; set; }
    public virtual Si LccSi { get; set; }

    public virtual ICollection<Lccdetail> Details { get; set; }

    public virtual Vdmxl VdmxlItem { get; set; }

    #region PMO fields

    [Precision(18, 4)] public decimal? LccNPVPmo { get; set; }
    public int? LccNPVyearPmo { get; set; }
    [Precision(18, 4)] public decimal? LccTotalAverageCostPmo { get; set; }
    [Precision(18, 4)] public decimal? LccAverageOptimalCostPmo { get; set; }
    [Precision(18, 2)] public decimal? LccPotentialPmo { get; set; }
    [Precision(18, 2)] public decimal? LccAECPmo { get; set; }
    [Precision(18, 2)] public decimal? LccMcRavPmo { get; set; }
    [Precision(18, 6)] public decimal? LccInputAvailabilityTechnicalPmo { get; set; }
    [Precision(18, 6)] public decimal? LccOutputReliabilityTechnicalPmo { get; set; }
    [Precision(18, 6)] public decimal? LccMTBFTechnicalPmo { get; set; }
    [Precision(18, 0)] public decimal? LccOptimalAverageCorrectiveCostPmo { get; set; }
    [Precision(18, 0)] public decimal? LccOptimalAveragePreventiveCostPmo { get; set; }

    #endregion
}