using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblSpare")]
public class Spare
{
    [Key]
    public int SpareId { get; set; }
    public int SpareMrbId { get; set; }
    public virtual Mrb SpareMrb{ get; set; }

    public string SpareName { get; set; }
    public string SpareRemarks { get; set; }
    public string SpareCategory { get; set; }
    public int? SpareStockNumber { get; set; }
    public decimal? SpareOrderLeadTime { get; set; }
    public decimal? SparePurchasePrice { get; set; }
    public decimal? SpareYearlyCost { get; set; }
    public string SpareSupplierId { get; set; }
    public string SpareVendorId { get; set; }
    public string SpareReferenceId { get; set; }
    public int? SpareNoOfItems { get; set; }
    public decimal? SpareDepreciationPct { get; set; }
    public decimal? SpareCosts { get; set; }
    public int? SparePurchaseYear { get; set; }
    public int? SpareObjectCount { get; set; }
    public decimal? SpareReliability { get; set; }
    public int? SpareCopiedFrom { get; set; }
    public int? SpareChildType { get; set; }
    public bool SpareDerived { get; set; }
    public bool SparePmo { get; set; }
}