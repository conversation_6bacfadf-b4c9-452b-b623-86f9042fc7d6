using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblFilters")]
public class Filters
{
    public Filters()
    {
        FiltersSelectionList = new HashSet<FiltersSelectionList>();
        SiLinkFilters = new SiLinkFilters();
    }

    public int SqlSelId { get; set; }
    public string SqlSelGroup { get; set; }
    public string SqlSelSubGroup { get; set; }
    public string SqlSelName { get; set; }
    public string SqlSelShortKey { get; set; }

    public virtual ICollection<FiltersSelectionList> FiltersSelectionList { get; set; }

    public virtual SiLinkFilters SiLinkFilters { get; set; }
}