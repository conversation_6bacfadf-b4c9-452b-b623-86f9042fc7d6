using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class Priority
{
    [Key]
    public int PrioId { get; set; }
    public string PrioName { get; set; }
    public string PrioDescription { get; set; }
    public int? PrioSiId { get; set; }
    public int? PrioSiCategory { get; set; }
    public int? PrioPartOf { get; set; }
    public bool? PrioAutoGenerated { get; set; }
    public string PrioStatus { get; set; }
    public string PrioShortKey { get; set; }
    [StringLength(50)] public string PrioInitiatedBy { get; set; }
    public DateTime? PrioDateInitiated { get; set; }
    [StringLength(50)] public string PrioModifiedBy { get; set; }
    public DateTime? PrioDateModified { get; set; }
    public string PrioRemark { get; set; }
    public string PrioResponsible { get; set; }
    public string PrioPath { get; set; }
}