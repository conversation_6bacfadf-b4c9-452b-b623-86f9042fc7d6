using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblWorkPackage")]
public class Workpackage
{
    [Key]
    public int WpId { get; set; }
    public string WpName { get; set; }
    public string WpDescription { get; set; }
    public decimal WpInterval { get; set; }
    public int WpIntervalUnit { get; set; }
    public int? WpExecutor { get; set; }
    public string WpShortDescription { get; set; }

    public virtual LookupExecutor WpExecutorNavigation { get; set; }
    public virtual LookupIntervalUnit WpIntervalUnitNavigation { get; set; }
    public virtual ICollection<CommonTask> CommonTask { get; set; } = new HashSet<CommonTask>();
    public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
}