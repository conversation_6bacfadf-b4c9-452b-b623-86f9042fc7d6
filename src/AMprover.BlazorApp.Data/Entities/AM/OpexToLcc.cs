namespace AMprover.Data.Entities.AM;

public class OpexToLcc
{
    public int OpexLccId { get; set; }
    public int? OpexLccOpexDataId1 { get; set; }
    public int? OpexLccOpexDataId2 { get; set; }
    public int? OpexLccOpexDataId3 { get; set; }
    public int? OpexLccLccId { get; set; }
    public string OpexLccName { get; set; }
    public decimal? OpexLccUnits { get; set; }
    public decimal? OpexLccQuantity { get; set; }
    public decimal? OpexLccPrice { get; set; }
    public string OpexLccColorName { get; set; }
    public bool? OpexLccShowInGraph { get; set; }
    public int? OpexLccSumItem { get; set; }
    public int? OpexLccSequence { get; set; }
    public int? OpexLccCycle { get; set; }

    public virtual OpexData OpexLccOpexDataId1Navigation { get; set; }
    public virtual OpexData OpexLccOpexDataId2Navigation { get; set; }
    public virtual OpexData OpexLccOpexDataId3Navigation { get; set; }
}