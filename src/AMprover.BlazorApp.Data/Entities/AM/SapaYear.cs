using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM
{
    [Table("TblSapaYear")]
    public class SapaYear
    {
        [Key]
        public int SapaYearId { get; set; }

        public int SapaYearSapaId { get; set; }

        public int SapaYearYear { get; set; }

        [Precision(18, 2)]
        public decimal SapaYearBudget { get; set; }

        /// <summary>
        /// This value is calculated by adding all SapaDetCostYear1 together
        /// </summary>
        [Precision(18, 2)]
        public decimal SapaYearBudgetRequest { get; set; }

        /// <summary>
        /// This value is calculated by adding all Approved SapaDetCostYear1 together
        /// </summary>
        [Precision(18, 2)]
        public decimal SapaYearBudgetApproved { get; set; }

        public virtual ICollection<SapaDetail> Details { get; set; }

        public virtual Sapa Sapa { get; set; }
    }
}
