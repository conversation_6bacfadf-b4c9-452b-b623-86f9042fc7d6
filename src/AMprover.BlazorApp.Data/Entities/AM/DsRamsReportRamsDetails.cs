using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class DsRamsReportRamsDetails
{
    public int RamsDgId { get; set; }
    public string RamsDgReferenceId { get; set; }
    public string RamsDgName { get; set; }
    public string RamsDgDescr { get; set; }
    public string RamsDgRemark { get; set; }
    public string RamsDgSiRefId { get; set; }
    public int? RamsDgSiId { get; set; }
    [StringLength(50)] public string RamsDgInitiatedBy { get; set; }
    public DateTime? RamsDgDateInitiated { get; set; }
    [StringLength(50)] public string RamsDgModifiedBy { get; set; }
    public DateTime? RamsDgDateModified { get; set; }
    public int? RamsDgStatus { get; set; }
    public int? RamsDgTestInterval { get; set; }
    public int? RamsDgHorizon { get; set; }
    public bool? RamsDgWantLcc { get; set; }
    public int RamsId { get; set; }
    public int? RamsDiagramRefId { get; set; }
    public int? RamsRiskObjectId { get; set; }
    public int? RamsObjectId { get; set; }
    public int? RamsRiskId { get; set; }
    public string RamsDescr { get; set; }
    public double? RamsAvailabilityInput { get; set; }
    public double? RamsAvailabilityOutput { get; set; }
    public double? RamsMtbftechn { get; set; }
    public double? RamsMtbffunct { get; set; }
    public double? RamsMttr { get; set; }
    public string RamsFunctionalDemand { get; set; }
    public decimal? RamsTotalCost { get; set; }
    public byte[] RamsBitmap { get; set; }
    public string RamsRemark { get; set; }
    public double? RamsPfd { get; set; }
    public double? RamsDcd { get; set; }
    public string RamsClassDc { get; set; }
    public double? RamsBeta { get; set; }
    public DateTime? RamsDateModified { get; set; }
    public double? RamsUtilizationTechn { get; set; }
    public double? RamsUtilizationFunct { get; set; }
    public double? RamsProductivityTechn { get; set; }
    public double? RamsProductivityFunct { get; set; }
    public double? RamsEcoTechn { get; set; }
    public double? RamsEcoFunct { get; set; }
    public int RamsPartOf { get; set; }
    public bool RamsContainer { get; set; }
    public int? RamsXposition { get; set; }
    public int? RamsYposition { get; set; }
    public int RamsLinkedLeft { get; set; }
    public int RamsLinkedRight { get; set; }
    public int? RamsYear { get; set; }
    public int? RamsXooN { get; set; }
    public int? RamsStatus { get; set; }
    public int? RamsParallelBlocks { get; set; }
    public int? RamsReadSequence { get; set; }
    public bool? RamsIdentical { get; set; }
    public bool? RamsCompleted { get; set; }
    public decimal? RamsPreventiveCost { get; set; }
    public decimal? RamsTechnCorrCost { get; set; }
    public decimal? RamsCircuitDepCorrCost { get; set; }
    public decimal? RamsFailCorrCost { get; set; }
    public int? RamsLinkType { get; set; }
    public int? RamsLinkMethod { get; set; }
    public bool? RamsCollapsed { get; set; }
    public bool? RamsWantLcc { get; set; }
    public bool? RamsCostOwner { get; set; }
    public bool? RamsCostLinked { get; set; }
    public string RamsInitiatedBy { get; set; }
    public DateTime? RamsDateInitiated { get; set; }
    public string RamsModifiedBy { get; set; }
    public string RamsName { get; set; }
    public int? RamsSiId { get; set; }
    public string RamsDgScenario { get; set; }
    public string RamsDgRiskObject { get; set; }
    public double? RamsFuncReliability { get; set; }
    public double? RamsTechnReliability { get; set; }
}