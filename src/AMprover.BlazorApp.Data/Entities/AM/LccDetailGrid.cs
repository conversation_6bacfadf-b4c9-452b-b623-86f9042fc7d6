using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LccDetailGrid
{
    [Key]
    public int LccDetId { get; set; }
    public int? LccDetLccId { get; set; }
    public int? LccDetYear { get; set; }
    public string LccName { get; set; }
    public decimal? LccDetFmecaBefore { get; set; }
    public decimal? LccDetAverageBefore { get; set; }
    public decimal? LccDetRiskBefore { get; set; }
    public decimal? LccDetFmecaCustomBefore { get; set; }
    public decimal? LccDetFmecaCustomRiskBefore { get; set; }
    public decimal? LccDetFmecaAfter { get; set; }
    public decimal? LccDetAverageAfter { get; set; }
    public decimal? LccDetRiskAfter { get; set; }
    public decimal? LccDetFmecaCustomAfter { get; set; }
    public decimal? LccDetFmecaCustomRiskAfter { get; set; }
    public decimal? LccDetDepreciation { get; set; }
    public decimal? LccDetMaintenanceCost { get; set; }
    public decimal? LccDetTaskCost { get; set; }
    public decimal? LccDetActionCost { get; set; }
    public decimal? LccDetProcedureCost { get; set; }
    public decimal? LccDetModificationCost { get; set; }
    public decimal? LccDetSpareCost { get; set; }
    public decimal? LccDetTotalCost { get; set; }
    public decimal? LccDetPreventiveCost { get; set; }
    public decimal? LccDetCorrectiveCost { get; set; }
    public decimal? LccDetReliability { get; set; }
    public decimal? LccDetTotalNpv { get; set; }
    public decimal? LccDetRealTotalCost { get; set; }
    public decimal? LccDetRealPreventiveCost { get; set; }
    public decimal? LccDetRealCorrectiveCost { get; set; }
    public decimal? LccDetAverageRealPreventiveCost { get; set; }
    public decimal? LccDetAverageRealCorrectiveCost { get; set; }
    public decimal? LccDetOptimalPreventiveCost { get; set; }
    public decimal? LccDetOptimalCorrectiveCost { get; set; }
    public decimal? LccDetAverageOptimalCost { get; set; }
    public decimal? LccDetAec { get; set; }
    public decimal? LccDetFailureRate { get; set; }
    public decimal? LccDetAvailabilityTechnical { get; set; }
    public decimal? LccDetUtilizationTechnichal { get; set; }
    public decimal? LccDetProductivityTechnical { get; set; }
    public decimal? LccDetEcoTechnical { get; set; }
    public decimal? LccProductionCost { get; set; }
    public decimal? LccDetDirectCost { get; set; }
    public decimal? LccDetOpexCost { get; set; }
    public decimal? LccDetRealOpexCost { get; set; }
}