using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM
{
    [Table("TblSapa")]
    public class Sapa
    {
        [Key]
        public int SapaId { get; set; }

        public int SapaCollectionId { get; set; }

        public int SapaRiskObjId { get; set; }

        public int? SapaWorkpackageId { get; set; }

        public int? SapaExecutorId { get; set; }

        public int? SapaInitiatorId { get; set; }

        public int? SapaStatusId { get; set; }

        public bool SapaApproveForAllYears { get; set; }

        public string SapaName { get; set; }

        public string SapaRemark { get; set; }

        public string SapaDescription { get; set; }

        [StringLength(50)]
        public string SapaResponsible { get; set; }

        /// <summary>
        /// This value is calculated by adding all YearBudgets together
        /// </summary>
        [Precision(18, 2)]
        public decimal SapaBudget { get; set; }

        /// <summary>
        /// This value is calculated by adding all yearBudgetRequests together
        /// </summary>
        [Precision(18, 2)]
        public decimal SapaBudgetRequest { get; set; }

        /// <summary>
        /// This value is calculated by adding all yearBudgetApproved together
        /// </summary>
        [Precision(18, 2)]
        public decimal SapaBudgetApproved { get; set; }

        [Precision(18, 2)]
        public decimal SapaCbiScore { get; set; }

        public string SapaCbiItem { get; set; }

        public bool SapaAccepted { get; set; }

        public int? SapaFirstYear { get; set; }

        public int? SapaLastYear { get; set; }

        public virtual ICollection<SapaYear> Years { get; set; }

        public virtual ICollection<Attachment> Attachments { get; set; }

        public virtual RiskObject RiskObject { get; set; }

        public virtual LookupExecutor Executor { get; set; }

        public virtual LookupInitiator Initiator { get; set; }

        public virtual SapaWorkpackage SapaWorkpackage { get; set; }

        public virtual SapaCollection SapaCollection { get; set; }
    }
}
