using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class SiRisks
{
    [Key]
    public int SiRiskId { get; set; }
    public int SiRiskRiskId { get; set; }
    public int SiRiskSiId { get; set; }
    public int? SiRiskBvId { get; set; }
    public string SiRiskFmecaSelData { get; set; }
    public decimal? SiRiskBeforeValue { get; set; }
    public decimal? SiRiskBeforeCustom { get; set; }
    public decimal? SiRiskAfterValue { get; set; }
    public decimal? SiRiskAfterCustom { get; set; }
    public decimal? SiRiskMtbfBefore { get; set; }
    public decimal? SiRiskMtbfAfter { get; set; }
    public decimal? SiRiskRiskBefore { get; set; }
    public decimal? SiRiskRiskAfter { get; set; }
    public int SiRiskRiskFactorBefore { get; set; }
    public int SiRiskRiskfactorAfter { get; set; }
    [StringLength(50)] public int? SiRiskModifiedBy { get; set; }
    public int? SiRiskDateModified { get; set; }
}