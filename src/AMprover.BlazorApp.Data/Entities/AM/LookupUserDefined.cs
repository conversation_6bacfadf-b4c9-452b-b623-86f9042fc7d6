using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LookupUserDefined
{
    [Key]
    public int UserDefinedId { get; set; }
    public string UserDefinedFilter { get; set; }
    public string UserDefinedShortDescription { get; set; }
    public string UserDefinedLongDescription { get; set; }
    public int? UserDefinedValue { get; set; }
    [StringLength(50)] public string UserDefinedModifiedBy { get; set; }
    public DateTime? UserDefinedDateModified { get; set; }

    public virtual ICollection<Si> Si { get; set; } = new HashSet<Si>();
}