namespace AMprover.Data.Entities.AM;

public class PriorityTaskCost
{
    public int? PrioTskVersion { get; set; }
    public int? PrioTskPriorityId { get; set; }
    public int? PrioTskExecuteStatus { get; set; }
    public decimal? Year1 { get; set; }
    public decimal? Year2 { get; set; }
    public decimal? Year3 { get; set; }
    public decimal? Year4 { get; set; }
    public decimal? Year5 { get; set; }
    public decimal? TaskCost { get; set; }
    public decimal? RiskCost { get; set; }
    public decimal? RiskBudget { get; set; }
    public decimal? RiskDelta { get; set; }
}