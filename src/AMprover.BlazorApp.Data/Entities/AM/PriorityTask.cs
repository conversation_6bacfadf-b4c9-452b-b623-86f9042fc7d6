using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class PriorityTask
{
    [Key]
    public int PrioTskId { get; set; }
    public int? PrioTskPriorityId { get; set; }
    public int? PrioTskVersion { get; set; }
    public int? PrioTskOriginalId { get; set; }
    public int? PrioTskRiskId { get; set; }
    public int? PrioTskObjectId { get; set; }
    public int? PrioTskTaskId { get; set; }
    public string PrioTskDescription { get; set; }
    public int? PrioTskCommonTaskId { get; set; }
    public int? PrioTskSiId { get; set; }
    public string PrioTskSiDescription { get; set; }
    public int? PrioTskClusterPlanId { get; set; }
    public int? PrioTskQualityScore { get; set; }
    public decimal? PrioTskRiskFactor { get; set; }
    public decimal? PrioTskPriorityCode { get; set; }
    public int? PrioTskSequence { get; set; }
    public decimal? PrioTskPostponePct { get; set; }
    public decimal? PrioTskRiskCosts { get; set; }
    public decimal? PrioTskRiskBudget { get; set; }
    public decimal? PrioTskDirectCost { get; set; }
    public decimal? PrioTskRiskDelta { get; set; }
    public decimal? PrioTskClusterCostPerUnit { get; set; }
    public decimal? PrioTskSiUnits { get; set; }
    public decimal? PrioTskDuration { get; set; }
    public decimal? PrioTskDownTime { get; set; }
    public DateTime? PrioTskDateGenerated { get; set; }
    public string PrioTskFromReference { get; set; }
    public string PrioTskReferenceId { get; set; }
    public DateTime? PrioTskDateExecuted { get; set; }
    public int? PrioTskExecuteStatus { get; set; }
    public int? PrioTskSlack { get; set; }
    public int? PrioTskSlackIntervalType { get; set; }
    public DateTime? PrioTskDateDue { get; set; }
    public int? PrioTskExecutionYear { get; set; }
    public string PrioTskRemarks { get; set; }
    public decimal? PrioTskCosts { get; set; }
    public decimal? PrioTskBudgetCostYear1 { get; set; }
    public decimal? PrioTskBudgetCostYear2 { get; set; }
    public decimal? PrioTskBudgetCostYear3 { get; set; }
    public decimal? PrioTskBudgetCostYear4 { get; set; }
    public decimal? PrioTskBudgetCostYear5 { get; set; }
    public decimal? PrioTskNumberOfTimes { get; set; }
    public decimal? PrioTskIntervalPerYear { get; set; }
    public bool? PrioTskAutoSelected { get; set; }
    public int? PrioTskSelectionSeq { get; set; }
    public bool? PrioTskImported { get; set; }
    public bool? PrioTskSealed { get; set; }
}