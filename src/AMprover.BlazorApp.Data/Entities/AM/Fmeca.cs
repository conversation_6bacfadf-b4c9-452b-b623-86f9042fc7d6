using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblFmeca")]
public class Fmeca
{
    [Key]
    public int FmecaId { get; set; }
    public string FmecaName { get; set; }
    public string FmecaDescription { get; set; }
    public string FmecaMatrix { get; set; }
    public string FmecaShortName { get; set; }
    public int FmecaVersion { get; set; }
    public bool FmecaIsDefault { get; set; }

    public virtual ICollection<BvWeightingModels> BvWeightingModels { get; set; } = new HashSet<BvWeightingModels>();
    public virtual ICollection<RiskObject> RiskObject { get; set; } = new HashSet<RiskObject>();
}