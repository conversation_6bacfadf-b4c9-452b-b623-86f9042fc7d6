using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

//TODO: remove
public class Login
{
    public string LogInUserMachineName { get; set; }
    public DateTime? LoginLoginDate { get; set; }
    [Key]
    public int LoginId { get; set; }
    public int? LoginUserId { get; set; }
    public string LoginUserDomain { get; set; }
    public DateTime? LoginLogoutDate { get; set; }
}