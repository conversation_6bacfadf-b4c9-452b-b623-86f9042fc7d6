using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM
{
    [Table("TblSapaDetail")]
    public class SapaDetail
    {
        [Key]
        public int SapaDetId { get; set; }

        public int SapaDetYearId { get; set; }

        public int SapaDetMrbId { get; set; }

        public int SapaDetTskId { get; set; }

        public bool SapaDetApproved { get; set; }

        public string SapaDetMotivation { get; set; }

        [Precision(18, 2)]
        public decimal? SapaDetScore { get; set; }

        [Precision(18, 2)]
        public decimal SapaDetCostYear1 { get; set; }

        [Precision(18, 2)]
        public decimal SapaDetCostYear2 { get; set; }

        [Precision(18, 2)]
        public decimal SapaDetCostYear3 { get; set; }

        [Precision(18, 2)]
        public decimal SapaDetCostYear4 { get; set; }

        [Precision(18, 2)]
        public decimal SapaDetCostYear5 { get; set; }

        [Precision(18, 2)]
        public decimal SapaDetTotalCapexNeeded { get; set; }

        public virtual SapaYear SapaYear { get; set; }

        public virtual Mrb Risk { get; set; }

        public virtual Task Task { get; set; }
    }
}
