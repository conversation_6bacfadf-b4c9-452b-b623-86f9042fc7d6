namespace AMprover.Data.Entities.AM;

public class SyncRamsMrb
{
    public int MrbId { get; set; }
    public string MrbName { get; set; }
    public decimal? MrbMtbfAfter { get; set; }
    public decimal? MrbMtbfBefore { get; set; }
    public int RamsDiagramId { get; set; }
    public int RamsId { get; set; }
    public double? RamsMtbftechn { get; set; }
    public double? RamsMtbffunct { get; set; }
    public int? RamsLinkType { get; set; }
    public int? RamsLinkMethod { get; set; }
    public bool RamsContainer { get; set; }
}