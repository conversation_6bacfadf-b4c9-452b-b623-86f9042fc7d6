using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class TaskEdit
{
    [Key]
    public int TskId { get; set; }
    public string TskName { get; set; }
    public string TskDescription { get; set; }
    public int? TskMrbId { get; set; }
    public int? TskCluster { get; set; }
    public int TskInitiator { get; set; }
    public int? TskMxPolicy { get; set; }
    public string TskGeneralDescription { get; set; }
    public string TskRemark { get; set; }
    public string TskResponsible { get; set; }
    public int? TskExecutor { get; set; }
    public decimal? TskCosts { get; set; }
    public decimal? TskInterval { get; set; }
    public int TskIntervalUnit { get; set; }
    [StringLength(50)] public string TskInitiatedBy { get; set; }
    public DateTime? TskDateInitiated { get; set; }
    [StringLength(50)] public string TskModifiedBy { get; set; }
    public DateTime? TskDateModified { get; set; }
    public decimal? TskDownTime { get; set; }
    public int? TskSortOrder { get; set; }
    public bool? TskExtraBool2 { get; set; }
    public bool? TskExtraBool3 { get; set; }
    public bool? TskExtraBool4 { get; set; }
    public int? TskCopiedFrom { get; set; }
    public int? TskWorkpackage { get; set; }
    public decimal? TskDuration { get; set; }
    public int? TskValidFromYear { get; set; }
    public string TskType { get; set; }
    public decimal? TskOptimalCost { get; set; }
    public DateTime? TskFinishDate { get; set; }
    public decimal? TskWorkInspCost { get; set; }
    public int? TskValidUntilYear { get; set; }
    public int? TskPriorityCode { get; set; }
    public DateTime? TskExecutionDate { get; set; }
    public int? TskCommonActionId { get; set; }
    public string TskFmecaEffect { get; set; }
    public string TskLcceffect { get; set; }
    public decimal? TskFmecaEffectPct { get; set; }
    public int TskFmecaVersion { get; set; }
    public int TskUnitType { get; set; }
    public decimal? TskClusterCostPerUnit { get; set; }
    public bool? TskClusterCostMember { get; set; }
    public decimal? TskClusterCosts { get; set; }
    public decimal? TskEstCostPerUnit { get; set; }
    public decimal? TskEstCosts { get; set; }
    public int? TskPartOf { get; set; }
    public string TskNorm { get; set; }
    public string TskPermit { get; set; }
    public decimal? TskUnits { get; set; }
    public string ObjLevel3Name { get; set; }
    public string ObjLevel2Name { get; set; }
    public string ObjLevel4Name { get; set; }
    public string ScenName { get; set; }
    public string ObjLevel0Name { get; set; }
    public string RiskObjName { get; set; }
    public int? ScenId { get; set; }
    public int? RiskObjId { get; set; }
    public int? ScenChildType { get; set; }
    public int? TskStatus { get; set; }
    public bool? TskRemoved { get; set; }
    public int? TskReferenceId { get; set; }
    public bool? TskDerived { get; set; }
    public bool? TskMaster { get; set; }
    public bool? TskSkipInLcc { get; set; }
}