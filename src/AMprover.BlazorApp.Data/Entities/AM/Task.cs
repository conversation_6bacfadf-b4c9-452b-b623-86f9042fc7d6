using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblTask")]
public class Task
{
    [Key]
    public int TskId { get; set; }
    public string TskName { get; set; }
    public string TskDescription { get; set; }
    public int? TskMrbId { get; set; }
    public virtual Mrb TskMrb { get; set; }
    public int? TskCluster { get; set; }
    public virtual Cluster TskClusterNavigation { get; set; }

    public int? TskInitiator { get; set; }
    public int? TskMxPolicy { get; set; }
    public string TskGeneralDescription { get; set; }
    public string TskRemark { get; set; }
    public string TskPermit { get; set; }
    public string TskResponsible { get; set; }
    public int? TskExecutor { get; set; }
    public decimal? TskCosts { get; set; }
    public decimal? TskInterval { get; set; }
    public int? TskIntervalUnit { get; set; }
    [StringLength(50)] public string TskInitiatedBy { get; set; }
    public DateTime? TskDateInitiated { get; set; }
    [StringLength(50)] public string TskModifiedBy { get; set; }
    public DateTime? TskDateModified { get; set; }
    public decimal? TskDownTime { get; set; }
    public int? TskSortOrder { get; set; }
    public bool? TskExtraBool2 { get; set; }
    public bool? TskExtraBool3 { get; set; }
    public bool? TskExtraBool4 { get; set; }
    public int? TskStatus { get; set; }
    public int? TskCopiedFrom { get; set; }
    public int? TskWorkpackage { get; set; }
    public int? TskSapaWorkpackage { get; set; }
    public decimal? TskDuration { get; set; }
    public int? TskValidFromYear { get; set; }
    public string TskType { get; set; }
    public decimal? TskOptimalCost { get; set; }
    public DateTime? TskFinishDate { get; set; }
    public decimal? TskWorkInspCost { get; set; }
    public int? TskValidUntilYear { get; set; }
    public int? TskPriorityCode { get; set; }
    public DateTime? TskExecutionDate { get; set; }
    public int? TskCommonActionId { get; set; }
    public string TskFmecaEffect { get; set; }
    public string TskLcceffect { get; set; }
    public decimal? TskFmecaEffectPct { get; set; }
    public int TskFmecaVersion { get; set; }
    public decimal? TskUnits { get; set; }
    public decimal? TskEstCostPerUnit { get; set; }
    public int TskUnitType { get; set; }
    public decimal? TskClusterCostPerUnit { get; set; }
    public bool? TskClusterCostMember { get; set; }
    public decimal? TskClusterCosts { get; set; }
    [Column(TypeName = "decimal(18, 2)")] 
    public decimal? TskDtCost { get; set; }
    public decimal? TskEstCosts { get; set; }
    public int? TskPartOf { get; set; }
    public virtual Task TskParent { get; set; }
    public virtual ICollection<Task> TskChildren { get; set; } = new HashSet<Task>();
    public string TskNorm { get; set; }
    public bool? TskDerived { get; set; }
    public bool? TskRemoved { get; set; }
    public bool? TskMaster { get; set; }
    [StringLength(30)]
    public string TskReferenceId { get; set; }
    public bool? TskSkipInLcc { get; set; }
    public bool TskPmo { get; set; }

    [Precision(18, 2)] public decimal? TskCostY1 { get; set; }
    [Precision(18, 2)] public decimal? TskCostY2 { get; set; }
    [Precision(18, 2)] public decimal? TskCostY3 { get; set; }
    [Precision(18, 2)] public decimal? TskCostY4 { get; set; }
    [Precision(18, 2)] public decimal? TskCostY5 { get; set; }

    public virtual CommonTask TskCommonAction { get; set; }
    public virtual LookupExecutor TskExecutorNavigation { get; set; }
    public virtual LookupInitiator TskInitiatorNavigation { get; set; }
    public virtual LookupIntervalUnit TskIntervalUnitNavigation { get; set; }
    public virtual LookupMxPolicy TskMxPolicyNavigation { get; set; }
    public virtual Workpackage TskWorkpackageNavigation { get; set; }
    public virtual SapaWorkpackage TskSapaWorkpackageNavigation { get; set; }
    public virtual ICollection<ClusterCost> ClusterCost { get; set; } = new HashSet<ClusterCost>();
    public virtual ICollection<ClusterTaskPlan> ClusterTaskPlan { get; set; } = new HashSet<ClusterTaskPlan>();
    public virtual ICollection<SiLinkFilters> SiLinkFilters { get; set; } = new HashSet<SiLinkFilters>();
    public virtual ICollection<SapaDetail> SapaDetails { get; set; } = new HashSet<SapaDetail>();
    public virtual ICollection<Attachment> Attachments { get; set; } = new HashSet<Attachment>();

    public void PrepareForCopyPasteRisk(Mrb targetRisk)
    {
        TskId = 0;
        TskMrbId = null;
        TskPartOf = null;
        TskMrb = targetRisk;
    }
}