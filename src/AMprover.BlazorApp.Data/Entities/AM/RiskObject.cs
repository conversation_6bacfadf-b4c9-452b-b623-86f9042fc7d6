using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblRiskObject")]
public class RiskObject
{
    [Key]
    public int RiskObjId { get; set; }
    [MaxLength(50)]
    public string RiskObjName { get; set; }
    public int RiskObjScenarioId { get; set; }
    public int? RiskObjDepartmentId { get; set; }
    public int RiskObjObjectId { get; set; }
    public int? RiskObjParentObjectId { get; set; }
    public string RiskObjAnalyseType { get; set; }
    public int RiskObjFmecaId { get; set; }
    public int? RiskObjVolgNo { get; set; }
    public string RiskObjMrbstartPoint { get; set; }
    public byte[] RiskObjFuncDecomp { get; set; }
    public decimal? RiskObjProdCostHour { get; set; }
    public int? RiskObjNoOfInstallation { get; set; }
    public string RiskObjResponsible { get; set; }
    public decimal? RiskObjRforSpares { get; set; }
    public string RiskObjAmrbattended { get; set; }
    public string RiskObjAbmstate { get; set; }
    public int? RiskObjLccyear { get; set; }
    public int? RiskObjCopiedFrom { get; set; }
    [StringLength(50)]
    public string RiskObjModifiedBy { get; set; }
    public DateTime? RiskObjDateModified { get; set; }

    [Precision(18, 2)] public decimal RiskObjDownTimeCost { get; set; }
    [Precision(18, 2)] public decimal? RiskObjDirectCorrectiveCostBefore { get; set; }
    [Precision(18, 2)] public decimal? RiskObjCorrectiveCostBefore { get; set; }
    [Precision(18, 2)] public decimal? RiskObjPreventiveCostAfter { get; set; }
    [Precision(18, 2)] public decimal? RiskObjDirectCorrectiveCostAfter { get; set; }
    [Precision(18, 2)] public decimal? RiskObjCorrectiveCostAfter { get; set; }
    [Precision(18, 2)] public decimal? RiskObjPreventiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? RiskObjDirectCorrectiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? RiskObjCorrectiveCostPmo { get; set; }
    [Precision(18, 2)] public decimal? RiskObjCorrectiveCostLcc { get; set; }
    [Precision(18, 2)] public decimal? RiskObjPreventiveCostLcc { get; set; }
    [Precision(18, 2)] public decimal? RiskObjDirectCorrectiveCostLcc { get; set; }
    [Precision(18, 2)] public decimal? RiskObjNpv { get; set; }
    [Precision(18, 2)] public decimal? RiskObjAec { get; set; }
    [Precision(18, 2)] public decimal? RiskObjLccPotential { get; set; }

    public int? RiskObjStatus { get; set; }
    public virtual Lookup RiskObjStatusNavigation { get; set; }

    public virtual ICollection<Mrb> Risks { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<Lcc> LccItems { get; set; } = new HashSet<Lcc>();
    public virtual Fmeca RiskObjFmeca { get; set; }
    public virtual Object RiskObjObject { get; set; }
    public virtual Object RiskObjParentObject { get; set; }
    public virtual Scenario RiskObjScenario { get; set; }
    
    public virtual Department RiskObjDepartment { get; set; }

    public virtual ICollection<SapaCollection> SapaCollections { get; set; } = new HashSet<SapaCollection>();
    public virtual ICollection<Sapa> RiskObjSapa { get; set; } = new HashSet<Sapa>();
    public virtual ICollection<Cluster> Clusters { get; set; } = new HashSet<Cluster>();
    public virtual ICollection<SiLinkFilters> SiLinkFilters { get; set; } = new HashSet<SiLinkFilters>();
    public virtual ICollection<Attachment> Attachments { get; set; } = new HashSet<Attachment>();
}