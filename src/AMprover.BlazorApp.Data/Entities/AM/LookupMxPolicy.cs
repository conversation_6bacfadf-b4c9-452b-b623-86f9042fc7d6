using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LookupMxPolicy
{
    [Key]
    public int PolId { get; set; }
    public string PolName { get; set; }
    public string PolDescription { get; set; }
    public string PolEamPolicy { get; set; }

    public virtual ICollection<CommonTask> CommonTask { get; set; } = new HashSet<CommonTask>();
    public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
}