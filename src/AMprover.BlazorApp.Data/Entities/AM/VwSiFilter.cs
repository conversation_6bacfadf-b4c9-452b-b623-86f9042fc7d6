using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class VwSiFilter
{
    [Key]
    public int Siid { get; set; }
    public string Siname { get; set; }
    public string Sidescription { get; set; }
    public string Sitype { get; set; }
    public decimal? Siprice { get; set; }
    public int? Siwarrantyperiod { get; set; }
    public string Siunittype { get; set; }
    public decimal? Silength { get; set; }
    public decimal? Siheight { get; set; }
    public decimal? Siwidth { get; set; }
    public decimal? Siunits { get; set; }
    public decimal? Sitotalunits { get; set; }
    public int Sicategory { get; set; }
    public int? Sipartof { get; set; }
    public string Siremarks { get; set; }
    public string Sivendor { get; set; }
    public string Sisuppliertextfield1 { get; set; }
    public string Sisuppliertextfield2 { get; set; }
    public string Sisuppliertextfield3 { get; set; }
    public DateTime? Sisupplierdatefield1 { get; set; }
    public bool? Sisupplierbitfield1 { get; set; }
    public int? Sisupplierintfield1 { get; set; }
    public string Simisctextfield1 { get; set; }
    public string Simisctextfield2 { get; set; }
    public decimal? Simiscdecimalfield1 { get; set; }
    public DateTime? Simiscdatefield1 { get; set; }
    public bool? Simiscbitfield1 { get; set; }
    public string Siunitstypevalues { get; set; }
    public int? Siqualityscore { get; set; }
    public string Sireferenceid { get; set; }
    public string Simisctextfield3 { get; set; }
    public string Simisctextfield4 { get; set; }
    public string Simisctextfield5 { get; set; }
    public string Simisctextfield6 { get; set; }
    public string Simisctextfield7 { get; set; }
    public decimal? Simiscdecimalfield2 { get; set; }
    [StringLength(50)] public string Simodifiedby { get; set; }
    public DateTime? Sidatemodified { get; set; }
    public DateTime? Sicontractend { get; set; }
    public DateTime? Siyear { get; set; }
    public decimal? Simtbf { get; set; }
    public string PartOfName { get; set; }
    public int? PartOfCategory { get; set; }
    public int? Sibvid { get; set; }
    public string Sisite { get; set; }
    public string Siserialnumber { get; set; }
    public string Siassettype { get; set; }
    public string Siassetmanager { get; set; }
    public string Siassetowner { get; set; }
    public string Siserviceprovider { get; set; }
    public string Silocation { get; set; }
    public int? Sistatus { get; set; }
    public string Siservicemanager { get; set; }
}