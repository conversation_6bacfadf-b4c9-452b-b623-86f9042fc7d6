using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LookupInitiator
{
    [Key]
    public int InitiatorId { get; set; }
    public string InitiatorName { get; set; }
    public string InitiatorDescription { get; set; }

    public virtual ICollection<CommonTask> CommonTask { get; set; } = new HashSet<CommonTask>();
    public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
    public virtual ICollection<Sapa> Sapa { get; set; } = new HashSet<Sapa>();
}