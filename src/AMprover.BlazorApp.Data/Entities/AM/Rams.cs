using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblRams")]
public class Rams
{
    public int RamsDiagramId { get; set; }
    [Key]
    public int RamsId { get; set; }
    public Guid RamsNodeId { get; set; }
    public int? RamsDiagramRefId { get; set; }
    public int? RamsRiskObjectId { get; set; }
    public int? RamsObjectId { get; set; }
    public int? RamsRiskId { get; set; }
    public int? RamsSiId { get; set; }
    public string RamsName { get; set; }
    public string RamsDescr { get; set; }
    public double? RamsAvailabilityInput { get; set; }
    public double? RamsAvailabilityOutput { get; set; }
    public double? RamsMtbftechn { get; set; }
    public double? RamsMtbffunct { get; set; }
    public double? RamsMttr { get; set; }
    public string RamsFunctionalDemand { get; set; }
    public decimal? RamsTotalCost { get; set; }
    public byte[] RamsBitmap { get; set; }
    public string RamsRemark { get; set; }
    public double? RamsPfd { get; set; }
    public double? RamsDcd { get; set; }
    public string RamsClassDc { get; set; }
    public double? RamsBeta { get; set; }
    public DateTime? RamsDateModified { get; set; }
    public double? RamsUtilizationTechn { get; set; }
    public double? RamsUtilizationFunct { get; set; }
    public double? RamsProductivityTechn { get; set; }
    public double? RamsProductivityFunct { get; set; }
    public double? RamsEcoTechn { get; set; }
    public double? RamsEcoFunct { get; set; }
    public int RamsPartOf { get; set; }
    public bool RamsContainer { get; set; }
    public int? RamsXposition { get; set; }
    public int? RamsYposition { get; set; }
    public int RamsLinkedLeft { get; set; }
    public int RamsLinkedRight { get; set; }
    public int? RamsYear { get; set; }
    public int? RamsXooN { get; set; }
    public int? RamsStatus { get; set; }
    public int? RamsParallelBlocks { get; set; }
    public int? RamsReadSequence { get; set; }
    public bool? RamsIdentical { get; set; }
    public bool? RamsCompleted { get; set; }
    public decimal? RamsPreventiveCost { get; set; }
    public decimal? RamsTechnCorrCost { get; set; }
    public decimal? RamsCircuitDepCorrCost { get; set; }
    public decimal? RamsFailCorrCost { get; set; }
    public int? RamsLinkType { get; set; }
    public int? RamsLinkMethod { get; set; }
    public bool? RamsCollapsed { get; set; }
    public bool? RamsWantLcc { get; set; }
    public bool? RamsCostOwner { get; set; }
    public bool? RamsCostLinked { get; set; }
    [StringLength(50)] public string RamsInitiatedBy { get; set; }
    public DateTime? RamsDateInitiated { get; set; }
    [StringLength(50)] public string RamsModifiedBy { get; set; }
    public int? RamsFailureMode { get; set; }
    public decimal? RamsWeibullShape { get; set; }
    public decimal? RamsCharacteristicLife { get; set; }
    public int? RamsDistributionType { get; set; }
    public double? RamsReliabilityFunctional { get; set; }
    public double? RamsReliabilityTechnical { get; set; }
    public double? RamsLabdaFunctional { get; set; }
    public double? RamsLabdaTechnical { get; set; }
    public double? RamsMtbfFuncCalced { get; set; }
    public double? RamsMtbfTecCalced { get; set; }
    public bool RamsLccusePfd { get; set; }
    public double? RamsBufferTime { get; set; }
    public int? RamsTestInterval { get; set; }
    public string RamsSil { get; set; }
    public string RamsSilAc { get; set; }
    public decimal? RamsSff { get; set; }
    public string RamsType { get; set; }
    public int? RamsHft { get; set; }

    public virtual RamsDiagram RamsDiagram { get; set; }
    public virtual Si RamsSi { get; set; }
}