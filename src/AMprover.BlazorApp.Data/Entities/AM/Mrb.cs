using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblMRB")]
public class Mrb
{
    public string MrbName { get; set; }

    public int MrbRiskObject { get; set; }
    public virtual RiskObject RiskObject { get; set; }

    public int? MrbChildObject { get; set; }

    public virtual Object ChildObject { get; set; }

    public int? MrbChildObject1 { get; set; }
    public virtual Object Installation { get; set; }
    public int? MrbChildObject2 { get; set; }
    public virtual Object System { get; set; }
    public int? MrbChildObject3 { get; set; }
    public virtual Object Component { get; set; }
    public int? MrbChildObject4 { get; set; }
    public virtual Object Assembly { get; set; }

    public string MrbDescription { get; set; }

    public int? MrbFailureMode { get; set; }
    public virtual LookupFailMode FailureMode { get; set; }

    public string MrbFailureCause { get; set; }
    public string MrbFailureConsequences { get; set; }
    public string MrbFailureCategorie1 { get; set; }
    public string MrbFailureCategorie2 { get; set; }
    public decimal? MrbDownTimeAfter { get; set; }
    public decimal? MrbDownTimeBefore { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbDownTimePmo { get; set; }
    public string MrbRemarks { get; set; }
    public string MrbRemarks1 { get; set; }
    public string MrbFunction { get; set; }
    public string MrbOpsProcedure { get; set; }
    public decimal? MrbSpareCosts { get; set; }
    [Column(TypeName = "decimal(18,2)")] public decimal? MrbSpareCostsPmo { get; set; }
    public decimal? MrbCapCosts { get; set; }
    public decimal? MrbActionCosts { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbCurrentActionCosts { get; set; }
    public decimal? MrbOptimalCosts { get; set; }
    [StringLength(50)] public string MrbInitiatedBy { get; set; }
    public DateTime? MrbDateInitiated { get; set; }
    [StringLength(50)] public string MrbModifiedBy { get; set; }
    public DateTime? MrbDateModified { get; set; }

    public int? MrbStatus { get; set; }
    public virtual Lookup MrbStatusNavigation { get; set; }

    public int? MrbMasterId { get; set; }
    public string MrbResponsible { get; set; }
    public string MrbNorm { get; set; }
    public string MrbState { get; set; }
    public string MrbExclOptCb { get; set; }
    public string MrbFmecaSelect { get; set; }

    public int MrbFmecaVersion { get; set; }
    public decimal? MrbCustomBefore { get; set; }
    public decimal? MrbCustomAfter { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbCustomPmo { get; set; }

    [Column(TypeName = "decimal(18,4)")] public decimal? MrbMtbfBefore { get; set; }
    [Column(TypeName = "decimal(18,4)")] public decimal? MrbMtbfAfter { get; set; }
    [Column(TypeName = "decimal(18,4)")] public decimal? MrbMtbfPmo { get; set; }

    public decimal? MrbRiskBefore { get; set; }
    public decimal? MrbRiskAfter { get; set; }

    [Column(TypeName = "decimal(18,3)")] public decimal? MrbRiskPmo { get; set; }
    public decimal? MrbEffectBefore { get; set; }
    public decimal? MrbEffectAfter { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbEffectPmo { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbPointsBefore { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbPointsAfter { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbPointsPmo { get; set; }
    public decimal MrbSpareManageCost { get; set; }
    [Column(TypeName = "decimal(18,2)")] public decimal MrbSpareManageCostPmo { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbDirectCostBefore { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbDirectCostAfter { get; set; }
    
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbDirectCostPmo { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbCustomEffectBefore { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbCustomEffectAfter { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbCustomEffectPmo { get; set; }
    
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbPointsEffectBefore { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbPointsEffectAfter { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbPointsEffectPmo { get; set; }
    
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbCircuitAffectedCostBefore { get; set; }
    [Column(TypeName = "decimal(18,3)")]  public decimal? MrbCircuitAffectedCostAfter { get; set; }
    [Column(TypeName = "decimal(18,3)")] public decimal? MrbCircuitAffectedCostPmo { get; set; }
    
    public int? MrbChildType { get; set; }
    public int? MrbAdditionalDataId { get; set; }
    
    [Key]
    public int Mrbid { get; set; }

    // used for matching Tasks and Spares when importing data
    public int? MrbImportId { get; set; }

    [MaxLength(255)] public string MrbFmecaSelectionBefore { get; set; }
    [MaxLength(255)] public string MrbFmecaSelectionAfter { get; set; }
    [MaxLength(255)] public string MrbFmecaSelectionPmo { get; set; }

    public int? MrbFmecaMtbfBefore { get; set; }
    public int? MrbFmecaMtbfAfter { get; set; }
    public int? MrbFmecaMtbfPmo { get; set; }
     
    [Column(TypeName = "decimal(18,4)")] public decimal? MrbAec { get; set; }
    [Column(TypeName = "decimal(18,4)")] public decimal? MrbSapaIndex { get; set; }
    [Column(TypeName = "varchar(50)")] public string MrbSafetyBefore { get; set; }
    [Column(TypeName = "varchar(50)")] public string MrbSafetyAfter { get; set; }
    [Column(TypeName = "varchar(50)")] public string MrbSafetyPmo { get; set; }
    [Column(TypeName = "varchar(50)")] public string MrbMarketBefore { get; set; }
    [Column(TypeName = "varchar(50)")] public string MrbMarketAfter { get; set; }
    [Column(TypeName = "varchar(50)")] public string MrbMarketPmo { get; set; }

    public int MrbSortOrder { get; set; }

    
    public virtual LookupAdditionalData MrbAdditionalData { get; set; }
    public virtual MrbImage MrbImage { get; set; }
    public virtual ICollection<Spare> Spares { get; set; } = new HashSet<Spare>();

    public virtual ICollection<Task> Tasks { get; set; } = new HashSet<Task>();

    public virtual ICollection<ClusterTaskPlan> ClusterTaskPlan { get; set; } = new HashSet<ClusterTaskPlan>();
    public virtual ICollection<PickSi> PickSi { get; set; } = new HashSet<PickSi>();
    public virtual ICollection<SiLinkFilters> SiLinkFilters { get; set; } = new HashSet<SiLinkFilters>();
    public virtual ICollection<SapaDetail> SapaDetails { get; set; } = new HashSet<SapaDetail>();
    public virtual ICollection<Attachment> Attachments { get; set; } = new HashSet<Attachment>();
}