namespace AMprover.Data.Entities.AM;

public class LccMrbCalc
{
    public int? Mrbfailuremode { get; set; }
    public int Mrbriskobject { get; set; }
    public int? Mrbchildobject { get; set; }
    public int? Mrbchildobject1 { get; set; }
    public int? Mrbchildobject2 { get; set; }
    public int? Mrbchildobject3 { get; set; }
    public int? Mrbchildobject4 { get; set; }
    public string Mrbfmecaselect { get; set; }
    public int Mrbfmecaversion { get; set; }
    public string Mrbfailurecategorie1 { get; set; }
    public string Mrbfailurecategorie2 { get; set; }
    public decimal Mrbsparecosts { get; set; }
    public decimal? Mrbcapcosts { get; set; }
    public decimal Mrboptimalcosts { get; set; }
    public int Mrbid { get; set; }
    public decimal? Mrbriskbefore { get; set; }
    public decimal? Mrbriskafter { get; set; }
    public decimal? Mrbcustombefore { get; set; }
    public decimal? Mrbcustomafter { get; set; }
    public decimal? Mrbeffectbefore { get; set; }
    public decimal? Mrbeffectafter { get; set; }
    public decimal? Mrbmtbfbefore { get; set; }
    public decimal? Mrbmtbfafter { get; set; }
    public decimal Mrbactioncosts { get; set; }
    public decimal? Mrbdowntimeafter { get; set; }
    public decimal? Mrbdowntimebefore { get; set; }
    public string Mrbname { get; set; }
    public decimal? NewValue { get; set; }
    public decimal? NewValue1 { get; set; }
    public decimal? NewValue2 { get; set; }
    public decimal? NewValue3 { get; set; }
    public decimal? NewValue4 { get; set; }
    public int? Riskobjnoofinstallation { get; set; }
    public decimal Mrbsparemanagecost { get; set; }
    public decimal? Mrbdirectcostafter { get; set; }
    public decimal? Mrbdirectcostbefore { get; set; }
    public string Fmecamatrix { get; set; }
    public int? Fmecaversion { get; set; }
    public string Failmode { get; set; }
    public int Failrateid { get; set; }
}