using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblBvSiItems")]
public class BvSiItems
{
    public int BvId { get; set; }
    public int BvSiId { get; set; }
    public int? BvWeightingModelId { get; set; }
    public int? BvBusinessvalue { get; set; }
    public string BvRelevanceSelectSet { get; set; }
    public string BvPerEffectSet { get; set; }
    [StringLength(50)]
    public string BvModifiedBy { get; set; }
    public DateTime? BvDateModified { get; set; }

    public virtual Si BvSi { get; set; }
    public virtual BvWeightingModels BvWeightingModel { get; set; }
}