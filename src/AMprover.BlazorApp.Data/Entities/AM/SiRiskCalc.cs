using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class SiRiskCalc
{
    [Key]
    public int PckSiId { get; set; }
    public int PckSiMrbId { get; set; }
    public int PckSiSiId { get; set; }
    public int? PckSiModifiedBy { get; set; }
    public int? PckSiDateModified { get; set; }
    public int? PckSiExecutionYear { get; set; }
    public int? SifTaskId { get; set; }
    public int? PckSiLinkFilterId { get; set; }
    public int? PckSiItems { get; set; }
    public string MrbFmecaSelect { get; set; }
    public int? BvId { get; set; }
    public decimal? SiMtbf { get; set; }
    public DateTime? BvDateModified { get; set; }
    public DateTime? MrbDateModified { get; set; }
    public int? BvWeightingModelId { get; set; }
    public string BvPerEffectSet { get; set; }
    public int PckSiActive { get; set; }
    public int? MrbFmecaVersion { get; set; }
}