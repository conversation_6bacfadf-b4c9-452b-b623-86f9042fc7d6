using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblCommonTaskCost")]
public class CommonTaskCost
{
    [Key]
    public int CtcId { get; set; }
    public int? CtcCommonTaskId { get; set; }
    public decimal? CtcUnits { get; set; }
    public decimal? CtcQuantity { get; set; }
    public decimal CtcPrice { get; set; }
    public decimal CtcCost { get; set; }
    public int CtcPriceIndexYear { get; set; }
    public int CtcCommonCostId { get; set; }
    public string CtcCalculationType { get; set; }

    public virtual CommonCost CtcCommonCost { get; set; }
    public virtual CommonTask CtcCommonTask { get; set; }
}