using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class LookupInflationGroup
{
    [Key]
    public int InflId { get; set; }
    public string InflName { get; set; }
    public decimal? InflPercentage { get; set; }
    [StringLength(50)]
    public string InflModifiedBy { get; set; }
    public DateTime? InflDateModified { get; set; }

    public virtual ICollection<CommonCost> CommonCost { get; set; } = new HashSet<CommonCost>();
}