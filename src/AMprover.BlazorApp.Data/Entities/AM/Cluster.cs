using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblCluster")]
public class Cluster
{
    [Key]
    public int ClustId { get; set; }
    public string ClustName { get; set; }
    public string ClustDescription { get; set; }
    public int ClustLevel { get; set; }

    public int? ClustPartOf { get; set; }
    public virtual Cluster ClustPartOfCluster { get; set; }
    public virtual ICollection<Cluster> ClusterChildren { get; set; }

    public decimal? ClustInterval { get; set; }
    public int? ClustIntervalUnit { get; set; }
    public decimal? ClustEstTaskCosts { get; set; }
    public decimal? ClustTaskCosts { get; set; }
    public decimal? ClustSharedCosts { get; set; }
    public decimal? ClustDisciplineCosts { get; set; }
    public decimal? ClustMaterialCosts { get; set; }
    public decimal? ClustEnergyCosts { get; set; }
    public decimal? ClustToolCosts { get; set; }
    public decimal? ClustTotalCmnCost { get; set; }
    public decimal? ClustDownTime { get; set; }
    public decimal? ClustDuration { get; set; }
    public int? ClustExecutor { get; set; }
    public int? ClustInitiator { get; set; }

    public int? ClustStatus { get; set; }
    public virtual Lookup ClustStatusNavigation { get; set; }

    public string ClustShortKey { get; set; }
    [StringLength(50)]
    public string ClustInitiatedBy { get; set; }
    public DateTime? ClustDateInitiated { get; set; }
    [StringLength(50)]
    public string ClustModifiedBy { get; set; }
    public DateTime? ClustDateModified { get; set; }
    public string ClustRemark { get; set; }
    public string ClustResponsible { get; set; }
    public string ClustSecondValues { get; set; }
    public int? ClustWorkpackageId { get; set; }
    public bool? ClustDivideDuration { get; set; }
    public bool? ClustDivideDownTime { get; set; }
    public string ClustSiteId { get; set; }
    public string ClustLocation { get; set; }
    public bool? ClustInterruptable { get; set; }
    public int? ClustShiftStartDate { get; set; }
    public int? ClustShiftEndDate { get; set; }
    public string ClustTemplateType { get; set; }
    public string ClustOrgId { get; set; }
    public int? ClustPriority { get; set; }
    public int? ClustSequence { get; set; }
    public string ClustReferenceId { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal? ClustBudget { get; set; }

    public int? ClustScenarioId { get; set; }
    public virtual Scenario Scenario { get; set; }
    
    public int? ClustRiskObjectId { get; set; }
    public virtual RiskObject? RiskObject { get; set; }
    public virtual ICollection<ClusterCost> ClusterCost { get; set; } = new HashSet<ClusterCost>();
    public virtual ICollection<ClusterTaskPlan> ClusterTaskPlan { get; set; } = new HashSet<ClusterTaskPlan>();
    public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
}