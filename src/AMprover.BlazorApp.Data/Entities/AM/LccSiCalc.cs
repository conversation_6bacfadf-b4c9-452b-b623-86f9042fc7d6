namespace AMprover.Data.Entities.AM;

public class LccSiCalc
{
    public int MrbId { get; set; }
    public string MrbName { get; set; }
    public int MrbRiskObject { get; set; }
    public int? MrbFailureMode { get; set; }
    public string MrbFailureCategorie1 { get; set; }
    public string MrbFailureCategorie2 { get; set; }
    public decimal? MrbDownTimeAfter { get; set; }
    public decimal? MrbDownTimeBefore { get; set; }
    public decimal? MrbSpareCosts { get; set; }
    public decimal? MrbCapCosts { get; set; }
    public decimal? MrbActionCosts { get; set; }
    public decimal? MrbOptimalCosts { get; set; }
    public string MrbFmecaSelect { get; set; }
    public int MrbFmecaVersion { get; set; }
    public decimal? MrbEffectBefore { get; set; }
    public decimal? MrbCustomBefore { get; set; }
    public decimal? MrbMtbfBefore { get; set; }
    public decimal? MrbRiskBefore { get; set; }
    public decimal? MrbEffectAfter { get; set; }
    public decimal? MrbMtbfAfter { get; set; }
    public decimal? MrbRiskAfter { get; set; }
    public decimal? MrbCustomAfter { get; set; }
    public decimal? MrbSpareManageCost { get; set; }
    public decimal? MrbDirectCostAfter { get; set; }
    public decimal? MrbDirectCostBefore { get; set; }
    public string FailMode { get; set; }
    public int PckSiSiId { get; set; }
    public string FmecaMatrix { get; set; }
    public int FmecaVersion { get; set; }
    public int FailRateId { get; set; }
}