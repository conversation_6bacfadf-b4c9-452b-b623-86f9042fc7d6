using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("Amprover3_TblSiLinkFilters")]
public class Amprover3SiLinkFilters
{
    public int SifId { get; set; }
    public string SifGroup { get; set; }
    public string SifSubGroup { get; set; }
    public string SifName { get; set; }
    public int SifType { get; set; }
    public int? SifParentId { get; set; }
    public int? SifObjectId { get; set; }
    public int? SifRiskId { get; set; }
    public int? SifTaskId { get; set; }
    public int? SifCategory { get; set; }
    public string SifFilter { get; set; }
    public string SifDescription { get; set; }
    public bool SifInheritParent { get; set; }
    public int? SifCompactCatId { get; set; }
    public bool? SifCompactDetail { get; set; }
}