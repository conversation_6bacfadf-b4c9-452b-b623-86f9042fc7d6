using System;
using System.ComponentModel.DataAnnotations;

namespace AMprover.Data.Entities.AM;

public class OpexFactor
{
    [Key]
    public int OpexFactId { get; set; }
    public int? OpexFactOpexDataId { get; set; }
    public decimal? OpexFactLookupValue { get; set; }
    public decimal? OpexFactValue { get; set; }
    [StringLength(50)] public string OpexFactModifiedBy { get; set; }
    public DateTime? OpexFactDateModified { get; set; }

    public virtual OpexData OpexFactOpexData { get; set; }
}