using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Extensions;

public static class EfChildrenExtension
{
    /// <summary>
    /// Tracks changes on childs models by comparing with latest database state.
    /// </summary>
    /// <typeparam name="T">The type of model to track.</typeparam>
    /// <param name="context">The database context tracking changes.</param>
    /// <param name="children">The children to update, detached from the context.</param>
    /// <param name="existingChildren">The latest existing data, attached to the context.</param>
    /// <param name="match">A function to match models by their primary key(s).</param>
    public static void TrackChildChanges<T>(this DbContext context, IList<T> children, IList<T> existingChildren,
        Func<T, T, bool> match)
        where T : class
    {
        // Delete children.
        foreach (var existing in existingChildren.ToList())
        {
            if (!children.Any(c => match(c, existing)))
            {
                existingChildren.Remove(existing);
            }
        }

        // Update and Insert childs.
        var existingChildrenCopy = existingChildren.ToList();
        foreach (var item in children.ToList())
        {
            var existing = existingChildrenCopy
                .SingleOrDefault(c => match(c, item));

            if (existing != null)
            {
                // Update child.
                context.Entry(existing).CurrentValues.SetValues(item);
            }
            else
            {
                // Insert child.
                existingChildren.Add(item);
            }
        }
    }

    /// <summary>
    /// Saves changes to a detached model by comparing it with the latest data.
    /// </summary>
    /// <typeparam name="T">The type of model to save.</typeparam>
    /// <param name="context">The database context tracking changes.</param>
    /// <param name="model">The model object to save.</param>
    /// <param name="existing">The latest model data.</param>
    public static void SaveChanges<T>(this DbContext context, T model, T existing)
        where T : class
    {
        context.Entry(existing).CurrentValues.SetValues(model);
        context.SaveChanges();
    }

    /// <summary>
    /// Saves changes to a detached model by comparing it with the latest data.
    /// </summary>
    /// <typeparam name="T">The type of model to save.</typeparam>
    /// <param name="context">The database context tracking changes.</param>
    /// <param name="model">The model object to save.</param>
    /// <param name="existing">The latest model data.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns></returns>
    public static async Task SaveChangesAsync<T>(this DbContext context, T model, T existing,
        CancellationToken cancellationToken = default)
        where T : class
    {
        context.Entry(existing).CurrentValues.SetValues(model);
        await context.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}