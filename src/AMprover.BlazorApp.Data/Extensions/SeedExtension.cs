using System;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Extensions;

public static class SeedExtension
{
    public static void SeedPortfolios(this ModelBuilder builder)
    {
        //Has data doesnt work with autogenerated ID's so we have to define our own id's
        builder.Entity<Portfolio>().HasData(
            new Portfolio { Id = 1, Name = "AMprover 5 Demo", DatabaseName = "Mainnovation_AMprover_Dev_Demo", LastUpdatedOn = DateTime.Now, CreatedOn = DateTime.Now },
            new Portfolio { Id = 2, Name = "Waterschap Noorderzijlvest", DatabaseName = "Mainnovation_AMprover_Dev_Waterschap_Noorderzijlvest", LastUpdatedOn = DateTime.Now, CreatedOn = DateTime.Now },
            new Portfolio { Id = 3, Name = "AMprover 5 Training", DatabaseName = "Mainnovation_AMprover_Dev_Training", LastUpdatedOn = DateTime.Now, CreatedOn = DateTime.Now }
        );

        builder.Entity<PortfolioAssignment>().HasData(
            new PortfolioAssignment { UserId = "1461abf6-0f98-41ea-9365-9cbb52127abe", PortfolioId = 1 },
            new PortfolioAssignment { UserId = "1461abf6-0f98-41ea-9365-9cbb52127abe", PortfolioId = 2 },
            new PortfolioAssignment { UserId = "1461abf6-0f98-41ea-9365-9cbb52127abe", PortfolioId = 3 }
        );
    }

    public static void SeedRoles(this ModelBuilder builder)
    {
        builder.Entity<IdentityRole>().HasData(
            new IdentityRole
            {
                Id = RoleConstants.AssetManagementRoleId,
                Name = RoleConstants.AssetManagement,
                NormalizedName = RoleConstants.AssetManagement.ToUpper(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            },
            new IdentityRole
            {
                Id = RoleConstants.FinancialControlRoleId,
                Name = RoleConstants.FinancialControl,
                NormalizedName = RoleConstants.FinancialControl.ToUpper(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            },
            new IdentityRole
            {
                Id = RoleConstants.MaintenanceEngineeringRoleId,
                Name = RoleConstants.MaintenanceEngineering,
                NormalizedName = RoleConstants.MaintenanceEngineering.ToUpper(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            },
            new IdentityRole
            {
                Id = RoleConstants.PortfolioAdministratorsRoleId,
                Name = RoleConstants.PortfolioAdministrators,
                NormalizedName = RoleConstants.PortfolioAdministrators.ToUpper(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            }
        );

        //Existing role for most portfolios
        builder.Entity<IdentityRole>().HasData(
            new IdentityRole
            {
                Id = RoleConstants.AdministratorsRoleId,
                Name = RoleConstants.Administrators,
                NormalizedName = RoleConstants.Administrators.ToUpper(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            });
    }

    public static void SeedUsers(this ModelBuilder builder)
    {
        string administratorEmail = "<EMAIL>";
        string administratorName = "Support Darestep";
        string adminPassword = "==25_hunger_NEAR_step_94==";

        var hasher = new PasswordHasher<UserAccount>();

        builder.Entity<UserAccount>().HasData(new UserAccount
        {
            Id = RoleConstants.AdministratorsRoleId,
            Email = administratorEmail,
            Name = administratorName,
            NormalizedEmail = administratorEmail.ToUpper(),
            UserName = administratorEmail,
            NormalizedUserName = administratorEmail.ToUpper(),
            EmailConfirmed = true,
            PasswordHash = hasher.HashPassword(null, adminPassword),
            SecurityStamp = string.Empty
        });

        //seed admin into role together
        builder.Entity<IdentityUserRole<string>>().HasData(new IdentityUserRole<string>
        {
            RoleId = RoleConstants.AdministratorsRoleId,
            UserId = RoleConstants.AdministratorsRoleId
        });
    }
}