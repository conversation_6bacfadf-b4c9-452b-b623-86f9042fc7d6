using AMprover.Data.Infrastructure;
using System.Threading.Tasks;

namespace AMprover.Data.Extensions
{
    public static class AssetManagementDbExtensions
    {
        public static void SaveChangesAndClear(this AssetManagementDbContext context, string userName)
        {
            if (!context.ChangeTracker.HasChanges()) return;
            
            context.SaveAuditLog(userName);
            context.SaveChanges();
            context.ChangeTracker.Clear();
        }

        public static async Task SaveChangesAndClearAsync(this AssetManagementDbContext context, string userName)
        {
            if (!context.ChangeTracker.HasChanges()) return;
            
            context.SaveAuditLog(userName);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();
        }
    }
}