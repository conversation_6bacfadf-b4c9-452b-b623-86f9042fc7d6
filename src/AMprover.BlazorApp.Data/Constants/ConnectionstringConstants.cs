namespace AMprover.Data.Constants;

public class ConnectionstringConstants
{
    /// <summary>
    /// Connectionstring to the main AMprover database, but it serves also as connectionstring to the portfolio databases (InitialCatalog varies then by the portfolio name)
    /// </summary>
    public const string DefaultConnectionstringName = "DefaultConnection";

    /// <summary>
    /// Entity Framework Core works with migrations based on a Portfolio database with the leading schema to determines the deltas on. 
    /// </summary>
    public const string EFCoreMigrationsConnectionstringName = "EFCoreMigrationsConnection";
}