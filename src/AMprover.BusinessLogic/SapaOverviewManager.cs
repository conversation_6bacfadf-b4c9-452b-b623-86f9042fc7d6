using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.Sapa;
using AMprover.BusinessLogic.Models.Tree;
using AMprover.Data.Entities.AM;
using AMprover.Data.Extensions;
using AMprover.Data.Infrastructure;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace AMprover.BusinessLogic;

public interface ISapaOverviewManager
{
    List<string> GetSapaWorkPackages();
    TreeNodeGeneric<SapaTreeObject> GetSapaTree(List<string>? workPackages);

    SapaCollectionModel GetSapaCollection(int sapaCollectionId);
    SapaModel GetCombinedSapaModelByCollection(int collectionId, List<string>? workPackages);
    SapaModel GetCombinedSapaModelByRiskObject(int riskObjectId, List<string>? workPackages);
    SapaModel GetCombinedSapaModelByScenario(int scenarioId, List<string>? workPackages);
    SapaModel GetSapaModelBySapaId(int sapaId);
    SapaModel GetSapaModelByRiskObject(int riskObjectId, int? workpackage);
    SapaDetailModel GetSapaDetail(int taskId);

    void DeleteBySapaCollection(int collectionId);
    void DeleteSapaByRiskObject(int riskObjectId);

    SapaCollectionModel SaveSapaCollection(SapaCollectionModel sapaCollection);

    SapaModel SaveSapa(SapaModel sapaModel);
    void SetSapaStatus(int riskObjectId, Status? status);
    void NeedReviewSapa(int riskObjectId);

    SapaValidateResult ValidateGenerateSapa(int riskObjectId);
    SapaModel GenerateSapaByRiskObject(int riskObjectId, string noWorkPackageText);
    List<SapaModel> GetSapas(int riskObjectId);
    List<SapaModel> GetSapasOnScenario(int scenarioId);
    int? GetRiskObjId(int scenarioId);

    void UpdateSapaCollection(int sapaCollectionId, string noWorkPackageName);
    void UpdateSapaByRiskObject(int riskObjectId, string noWorkPackageName);

    Dictionary<int?, string> GetSelectedSapaDict(int riskObjectId);
    public List<SapaDetail> GetSheqData(int riskObjectId);

    public decimal GetAssignedBudget(int sapaId, int year);

    int? GetSelectedSapaCollection(int riskObjectId);
    void SetSelectedSapaCollection(int riskObjectId, int? sapaCollectionId);
}

public class SapaOverviewManager : ISapaOverviewManager
{
    private readonly ILookupManager _lookupManager;
    private readonly AssetManagementDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly string _userName;

    public SapaOverviewManager(
        AssetManagementDbContext dbContext,
        IMapper mapper,
        ILookupManager lookupManager,
        AuthenticationStateProvider authenticationStateProvider)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _lookupManager = lookupManager;

        _userName = authenticationStateProvider.GetLoggedInUsername().Result;
    }

    public List<string> GetSapaWorkPackages()
    {
        return _dbContext.Sapa.Select(x => x.SapaName).Distinct().ToList();
    }

    public TreeNodeGeneric<SapaTreeObject> GetSapaTree(List<string>? workPackages)
    {
        var rootNode = new TreeNodeGeneric<SapaTreeObject>(null, null);

        var riskObjects = _dbContext.RiskObject
            .Include(x => x.RiskObjScenario)
            .Where(x => x.RiskObjAnalyseType == "SAPA")
            .ToList();

        var riskObjectsGroupByScenario = riskObjects.GroupBy(x => x.RiskObjScenarioId);
        var sapaCollections = _dbContext.SapaCollection.Include(x => x.Sapas).ThenInclude(x => x.SapaWorkpackage).ToList();

        if(workPackages?.Count > 0)
        {
            foreach(var sapaCollection in sapaCollections)
            {
                sapaCollection.Sapas = sapaCollection.Sapas.Where(x => workPackages.Contains(x.SapaName)).ToList();
            }

            sapaCollections = sapaCollections.Where(x => x.Sapas?.Count > 0).ToList();
        }

        foreach (var riskObjectGroup in riskObjectsGroupByScenario)
        {
            var scenario = riskObjectGroup.First().RiskObjScenario;
            var scenarioNode = new TreeNodeGeneric<SapaTreeObject>(
            new SapaTreeObject
            {
                Name = scenario.ScenName,
                ScenarioId = scenario.ScenId,
                SapaTreeNodeType = SapaTreeNodeType.Scenario
            }, rootNode)
            {
                Icon = SapaTreeNodeType.Scenario.ToIcon(),
                Name = scenario.ScenName
            };
            rootNode.Nodes.Add(scenarioNode);

            foreach (var riskObject in riskObjectGroup)
            {
                var riksObjectNode = new TreeNodeGeneric<SapaTreeObject>(
                new SapaTreeObject
                {
                    Name = riskObject.RiskObjName,
                    RiskObjectId = riskObject.RiskObjId,
                    SapaTreeNodeType = SapaTreeNodeType.RiskObject
                }, scenarioNode)
                {
                    Icon = SapaTreeNodeType.RiskObject.ToIcon(),
                    Name = riskObject.RiskObjName,
                };

                scenarioNode.Nodes.Add(riksObjectNode);

                foreach (var sapaCollection in sapaCollections.Where(x => x.SapaCollRiskObjId == riskObject.RiskObjId).OrderBy(x => x.SapaCollName))
                {
                    var sapaCollectionNode = new TreeNodeGeneric<SapaTreeObject>(
                    new SapaTreeObject
                    {
                        Name = sapaCollection.SapaCollName,
                        RiskObjectId = riskObject.RiskObjId,
                        SapaTreeNodeType = SapaTreeNodeType.SapaCollection,
                        SapaCollectionId = sapaCollection.SapaCollId,

                    }, riksObjectNode)
                    {
                        Icon = SapaTreeNodeType.SapaCollection.ToIcon(),
                        Name = sapaCollection.SapaCollName,
                        Id = sapaCollection.SapaCollId,
                        FilteredState = sapaCollection.SapaCollSelected ? FilteredState.Self : FilteredState.None
                    };
                    riksObjectNode.Nodes.Add(sapaCollectionNode);

                    foreach (var sapa in sapaCollection.Sapas)
                    {
                        var workPackageNode = new TreeNodeGeneric<SapaTreeObject>(
                        new SapaTreeObject
                        {
                            Name = sapa.SapaName,
                            SapaId = sapa.SapaId,
                            RiskObjectId = riskObject.RiskObjId,
                            SapaTreeNodeType = SapaTreeNodeType.WorkPackage,
                            WorkPackageId = sapa.SapaWorkpackageId
                        }, sapaCollectionNode)
                        {
                            Icon = SapaTreeNodeType.WorkPackage.ToIcon(),
                            Name = sapa.SapaName,
                            Id = sapa.SapaId,
                        };

                        sapaCollectionNode.Nodes.Add(workPackageNode);
                    }
                }
            }
        }

        return rootNode;
    }

    public SapaCollectionModel GetSapaCollection(int sapaCollectionId)
    {
        var dbModel = _dbContext.SapaCollection.FirstOrDefault(x => x.SapaCollId == sapaCollectionId);

        return dbModel != null
            ? _mapper.Map<SapaCollectionModel>(dbModel)
            : null;
    }

    public SapaModel GetCombinedSapaModelByCollection(int collectionId, List<string>? workPackages)
    {
        var dbModels = _dbContext.Sapa
            .IncludeAllSapaData()
            .Where(x => x.SapaCollectionId == collectionId)
            .ToList();

        if (workPackages?.Count > 0)
            dbModels = dbModels.Where(x => workPackages.Contains(x.SapaName)).ToList();

        var sapas = _mapper.Map<List<SapaModel>>(dbModels);
        var result = CombineSapaModels(sapas, dbModels.FirstOrDefault()?.SapaCollection.SapaCollName);

        return result;
    }

    public List<SapaModel> GetSapas(int riskObjectId)
    {
        var dbModels = _dbContext.Sapa
            .Include(x => x.SapaWorkpackage)
            .Where(x => x.SapaRiskObjId == riskObjectId && x.SapaCollection.SapaCollSelected)
            .ToList();

        var sapas = _mapper.Map<List<SapaModel>>(dbModels);

        return sapas;
    }

    public int? GetRiskObjId(int scenarioId)
    {
        var riskObjectIds = _dbContext.RiskObject.Where(x => x.RiskObjAnalyseType == "SAPA").FirstOrDefault(x => x.RiskObjScenarioId == scenarioId).RiskObjId;

        return riskObjectIds;
    }

    public List<SapaModel> GetSapasOnScenario(int scenarioId)
    {
        var riskObjectIds = _dbContext.RiskObject.Where(x => x.RiskObjScenarioId == scenarioId).Select(x => x.RiskObjId).ToList();

        var dbModels = _dbContext.Sapa
            .Where(x => riskObjectIds.Contains(x.SapaRiskObjId) && x.SapaCollection.SapaCollSelected)
            .ToList();

        var sapas = _mapper.Map<List<SapaModel>>(dbModels);

        return sapas;
    }

    public SapaModel GetCombinedSapaModelByRiskObject(int riskObjectId, List<string>? workPackages)
    {
        var dbModels = _dbContext.Sapa
            .IncludeAllSapaData()
            .Where(x => x.SapaRiskObjId == riskObjectId && x.SapaCollection.SapaCollSelected)
            .ToList();

        if (workPackages?.Count > 0)
            dbModels = dbModels.Where(x => workPackages.Contains(x.SapaName)).ToList();

        var sapas = _mapper.Map<List<SapaModel>>(dbModels);
        var result = CombineSapaModels(sapas,
            _dbContext.RiskObject.FirstOrDefault(x => x.RiskObjId == riskObjectId)?.RiskObjName);

        result.RiskObjectId = riskObjectId;
        return result;
    }

    public SapaModel GetCombinedSapaModelByScenario(int scenarioId, List<string>? workPackages)
    {
        var riskObjectIds = _dbContext.RiskObject.Where(x => x.RiskObjScenarioId == scenarioId).Select(x => x.RiskObjId).ToList();

        var dbModels = _dbContext.Sapa
            .IncludeAllSapaData()
            .Where(x => riskObjectIds.Contains(x.SapaRiskObjId) && x.SapaCollection.SapaCollSelected)
            .ToList();

        if (workPackages?.Count > 0)
            dbModels = dbModels.Where(x => workPackages.Contains(x.SapaName)).ToList();

        var sapas = _mapper.Map<List<SapaModel>>(dbModels);
        var result = CombineSapaModels(sapas,
            _dbContext.Scenario.FirstOrDefault(x => x.ScenId == scenarioId)?.ScenName);

        return result;
    }

    private SapaModel CombineSapaModels(List<SapaModel> sapas, string name)
    {
        var yearNumbers = sapas.SelectMany(x => x.Years).Select(x => x.Year).Distinct().OrderBy(x => x).ToList();

        Dictionary<int, List<SapaDetailModel>> missingDetails = new();
        foreach(var yearNumber in yearNumbers)
        {
            missingDetails.Add(yearNumber, new List<SapaDetailModel>());
            var sapasWithThisYearMissing = sapas.Where(x => x.FirstYear > yearNumber).ToList();

            if (sapasWithThisYearMissing.Count == 0)
                break;

            foreach (var sapa in sapasWithThisYearMissing)
            {
                var yearDifference = sapa.FirstYear - yearNumber;
                var closestYear = sapa.Years.FirstOrDefault();

                foreach (var sapaDetail in closestYear.Details)
                {
                    var detailCopy = sapaDetail.CopyAsNew();

                    for (int i = 0; i < yearDifference; i++)
                    {
                        detailCopy = ShiftSapaDetail1Year(detailCopy);
                    }

                    missingDetails[yearNumber].Add(detailCopy);
                }
            }
        }

        var allYearsGrouped = sapas.SelectMany(x => x.Years).GroupBy(x => x.Year).ToList();
        var years = new List<SapaYearModel>();
        foreach (var yearGroup in allYearsGrouped)
        {
            var realDetails = yearGroup.SelectMany(x => x.Details).ToList();
            var artificialDetails = missingDetails.ContainsKey(yearGroup.Key)
                ? missingDetails[yearGroup.Key]
                : new List<SapaDetailModel>();

            var year = new SapaYearModel
            {
                Year = yearGroup.Key,
                Budget = yearGroup.Sum(x => x.Budget),
                BudgetApproved = yearGroup.Sum(x => x.BudgetApproved),
                BudgetRequest = yearGroup.Sum(x => x.BudgetRequest),
                Details = realDetails.Concat(artificialDetails)
            };

            years.Add(year);
        }

        return new SapaModel
        {
            Name = name,
            Status = sapas.FirstOrDefault()?.Status,
            FirstYear = sapas.Where(x => x.FirstYear > 0).OrderBy(x => x.FirstYear).FirstOrDefault()?.FirstYear,
            LastYear = sapas.Where(x => x.LastYear > 0).OrderBy(x => x.LastYear).LastOrDefault()?.LastYear,
            Budget = sapas.Sum(x => x.Budget),
            BudgetApproved = sapas.Sum(x => x.BudgetApproved),
            BudgetRequest = sapas.Sum(x => x.BudgetRequest),
            Years = years.OrderBy(x => x.Year)
        };
    }

    private static SapaDetailModel ShiftSapaDetail1Year(SapaDetailModel input)
    {
        input.CostYear5 = input.CostYear4;
        input.CostYear4 = input.CostYear3;
        input.CostYear3 = input.CostYear2;
        input.CostYear2 = input.CostYear1;
        input.CostYear1 = 0;

        return input;
    }

    public SapaModel GetSapaModelBySapaId(int sapaId)
    {
        var dbModel = _dbContext.Sapa
            .IncludeAllSapaData()
            .FirstOrDefault(x => x.SapaId == sapaId);

        return _mapper.Map<SapaModel>(dbModel);
    }

    public SapaModel GetSapaModelByRiskObject(int riskObjectId, int? workpackage)
    {
        var dbModel = _dbContext.Sapa
            .IncludeAllSapaData()
            .FirstOrDefault(x => x.SapaRiskObjId == riskObjectId && x.SapaWorkpackageId == workpackage);

        return _mapper.Map<SapaModel>(dbModel);
    }

    public void DeleteBySapaCollection(int collectionId)
    {
        var sapaCollection = _dbContext.SapaCollection
            .Include(x => x.Sapas).ThenInclude(x => x.Years).ThenInclude(x => x.Details)
            .Where(x => x.SapaCollId == collectionId)
            .FirstOrDefault();

        if (sapaCollection != null)
        {
            _dbContext.SapaCollection.Remove(sapaCollection);
            _dbContext.SaveChangesAndClear(_userName);
        }
    }

    public void DeleteSapaByRiskObject(int riskObjectId)
    {
        var sapaCollections = _dbContext.SapaCollection
            .Include(x => x.Sapas).ThenInclude(x => x.Years).ThenInclude(x => x.Details)
            .Where(x => x.SapaCollRiskObjId == riskObjectId)
            .ToList();

        if (sapaCollections?.Count > 0)
        {
            _dbContext.SapaCollection.RemoveRange(sapaCollections);
            _dbContext.SaveChangesAndClear(_userName);
        }
    }

    public SapaModel SaveSapa(SapaModel sapaModel)
    {
        if(sapaModel.Id > 0)
        {
            var dbModel = _dbContext.Sapa
                .IncludeAllSapaData()
                .FirstOrDefault(x => x.SapaId == sapaModel.Id);

            dbModel = _mapper.Map(sapaModel, dbModel);

            _dbContext.Update(dbModel);
            _dbContext.SaveChangesAndClear(_userName);

            return GetSapaModelBySapaId(sapaModel.Id);
        }

        throw new ArgumentException($"Unable to Save Sapa with Id 0");
    }

    public SapaCollectionModel SaveSapaCollection(SapaCollectionModel sapaCollection)
    {
        var dbModel = _dbContext.SapaCollection.FirstOrDefault(x => x.SapaCollId == sapaCollection.Id);

        if(dbModel != null)
        {
            _mapper.Map(sapaCollection, dbModel);
            _dbContext.Update(dbModel);
            _dbContext.SaveChangesAndClear(_userName);
            return _mapper.Map<SapaCollectionModel>(dbModel);
        }

        return null;
    }

    public void SetSapaStatus(int riskObjectId, Status? status)
    {
        // Set Status on all related Sapas
        var sapas = _dbContext.Sapa.Where(x => x.SapaRiskObjId == riskObjectId).ToList();
        sapas.ForEach(x => x.SapaStatusId = (int?)status);
        _dbContext.UpdateRange(sapas);

        // Set Status on RiskObject
        var riskObject = _dbContext.RiskObject.FirstOrDefault(x => x.RiskObjId == riskObjectId);
        riskObject.RiskObjStatus = (int?)status;
        _dbContext.Update(riskObject);

        // Set Status on all related Risks
        var mrbs = _dbContext.Mrb.Where(x => x.MrbRiskObject == riskObjectId).ToList();
        mrbs.ForEach(x => x.MrbStatus = (int?)status);
        _dbContext.UpdateRange(mrbs);

        // Set status on Tasks
        var mrbIds = mrbs.Select(x => x.Mrbid).ToList();
        var tasks = _dbContext.Task.Where(x => x.TskMrbId != null && mrbIds.Contains(x.TskMrbId.Value)).ToList();
        tasks.ForEach(x => x.TskStatus = (int?)status);
        _dbContext.UpdateRange(tasks);

        // Save
        _dbContext.SaveChangesAndClear(_userName);
    }

    public void NeedReviewSapa(int riskObjectId)
    {
        // Set all Tasks on Complete Or Need_Review based on if the tasks were approved.
        var allTasks = _dbContext.Sapa
            .Include(x => x.SapaCollection)
            .Include(x => x.Years).ThenInclude(x => x.Details).ThenInclude(x => x.Task)
            .Where(x => x.SapaRiskObjId == riskObjectId && x.SapaCollection.SapaCollSelected)
            .SelectMany(x => x.Years).SelectMany(x => x.Details)
            .AsEnumerable()
            .DistinctBy(x => x.SapaDetTskId)
            .ToList();

        var approvedTasks = allTasks.Where(x => x.SapaDetApproved).Select(x => x.Task).ToList();
        var declindeTasks = allTasks.Where(x => !x.SapaDetApproved).Select(x => x.Task).ToList();

        approvedTasks.ForEach(x => x.TskStatus = (int?)Status.Complete);
        declindeTasks.ForEach(x => x.TskStatus = (int?)Status.Need_review);
        _dbContext.UpdateRange(allTasks);

        // Do the same for Risks
        var allRiskIds = allTasks.Select(x => x.SapaDetMrbId).Distinct().ToList();
        var declinedRiskIds = declindeTasks.Where(x => x.TskMrbId != null).Select(x => x.TskMrbId.Value).Distinct().ToList();
        var approvedRiskIds = allRiskIds.Except(declinedRiskIds).ToList();

        var allRisks = _dbContext.Mrb.Where(x => allRiskIds.Contains(x.Mrbid)).ToList();
        var declinedRisks = allRisks.Where(x => declinedRiskIds.Contains(x.Mrbid)).ToList();
        var approvedRisks = allRisks.Where(x => approvedRiskIds.Contains(x.Mrbid)).ToList();

        declinedRisks.ForEach(x => x.MrbStatus = (int?)Status.Need_review);
        approvedRisks.ForEach(x => x.MrbStatus = (int?)Status.Complete);
        _dbContext.UpdateRange(allRisks);

        // Set Status on all related Sapas
        var Sapas = _dbContext.Sapa.Where(x => x.SapaRiskObjId == riskObjectId).ToList();
        Sapas.ForEach(x => x.SapaStatusId = (int?)Status.Need_review);
        _dbContext.UpdateRange(Sapas);

        // Set Status on RiskObject
        var riskObject = _dbContext.RiskObject.FirstOrDefault(x => x.RiskObjId == riskObjectId);
        riskObject.RiskObjStatus = (int?)(declinedRiskIds.Count > 0 ? Status.Need_review : Status.Complete);
        _dbContext.Update(riskObject);

        // Save
        _dbContext.SaveChangesAndClear(_userName);
    }

    public SapaValidateResult ValidateGenerateSapa(int riskObjectId)
    {
        var riskObject = _dbContext.RiskObject
            .Include(x => x.Risks).ThenInclude(x => x.Tasks)
            .Where(x => x.RiskObjId == riskObjectId)
            .FirstOrDefault();

        var tasks = riskObject.Risks.SelectMany(x => x.Tasks);
        var firstYear = tasks.Where(x => x.TskValidFromYear != null).Select(x => (int)x.TskValidFromYear).OrderBy(x => x).FirstOrDefault();
        var lastYear = tasks.Where(x => x.TskValidUntilYear != null).Select(x => (int)x.TskValidUntilYear).OrderBy(x => x).LastOrDefault();

        return new SapaValidateResult
        {
            FirstYear = firstYear,
            LastYear = lastYear
        };
    }

    public SapaModel GenerateSapaByRiskObject(int riskObjectId, string noWorkPackageText)
    {
        var riskObject = _dbContext.RiskObject
            .Include(x => x.Risks).ThenInclude(x => x.Tasks).ThenInclude(x => x.TskSapaWorkpackageNavigation)
            .Where(x => x.RiskObjId == riskObjectId && x.RiskObjAnalyseType == "SAPA")
            .FirstOrDefault();

        GenerateSapaforRiskObjects(riskObject, noWorkPackageText);
        return GetCombinedSapaModelByRiskObject(riskObjectId, null);
    }

    public void UpdateSapaByRiskObject(int riskObjectId, string noWorkPackageName)
    {
        var sapaCollectionIds = _dbContext.SapaCollection
            .Where(x => x.SapaCollRiskObjId == riskObjectId)
            .Select(x => x.SapaCollId)
            .ToList();

        foreach(var id in sapaCollectionIds)
        {
            UpdateSapaCollection(id, noWorkPackageName);
        }
    }

    public void UpdateSapaCollection(int sapaCollectionId, string noWorkPackageName)
    {
        var dbCollection = _dbContext.SapaCollection
            .Include(x => x.Sapas).ThenInclude(x => x.Years).ThenInclude(x => x.Details)
            .Where(x => x.SapaCollId == sapaCollectionId)
            .FirstOrDefault();

        var riskObject = _dbContext.RiskObject
            .Include(x => x.Risks).ThenInclude(x => x.Tasks).ThenInclude(x => x.TskSapaWorkpackageNavigation)
            .Where(x => x.RiskObjId == dbCollection.SapaCollRiskObjId)
            .FirstOrDefault();

        var alltasks = riskObject.Risks.SelectMany(x => x.Tasks).Where(x => x.TskValidFromYear != null);
        var allTaskIds = alltasks.Select(x => x.TskId).ToList();
        var wpGroups = alltasks.GroupBy(x => x.TskSapaWorkpackage).OrderBy(x => x.Key);
        var wpIds = new List<int?>();

        foreach (var workPackage in wpGroups)
        {
            wpIds.Add(workPackage.Key);
            var firstYear = workPackage.OrderBy(x => x.TskValidFromYear).FirstOrDefault()?.TskValidFromYear ?? 0;
            var lastYear = workPackage.OrderByDescending(x => x.TskValidUntilYear).FirstOrDefault()?.TskValidUntilYear ?? firstYear;
            var sapa = dbCollection.Sapas.FirstOrDefault(x => x.SapaWorkpackageId == workPackage.Key);
            var workPackageTskIds = workPackage.Select(x => x.TskId).ToList();
            var sapaName = workPackage.FirstOrDefault()?.TskSapaWorkpackageNavigation?.SapaWpName;
            if (string.IsNullOrWhiteSpace(sapaName))
                sapaName = noWorkPackageName;

            if (sapa == null)
            {
                sapa = new Sapa
                {
                    Years = new List<SapaYear>(),
                    SapaApproveForAllYears = true,
                    SapaStatusId = (int?)Status.Budgeting,
                    SapaRiskObjId = riskObject.RiskObjId
                };
                dbCollection.Sapas.Add(sapa);
            }

            sapa.SapaFirstYear = firstYear;
            sapa.SapaLastYear = lastYear;
            sapa.SapaName = sapaName;
            sapa.SapaWorkpackageId = workPackage?.Key;
            sapa.SapaCollectionId = dbCollection.SapaCollId;

            for (int year = firstYear; year <= lastYear; year++)
            {
                var sapaYear = sapa.Years.FirstOrDefault(x => x.SapaYearYear == year);
                if(sapaYear == null)
                {
                    sapaYear = new SapaYear
                    {
                        SapaYearYear = year,
                        Details = new List<SapaDetail>()
                    };
                    sapa.Years.Add(sapaYear);
                }

                var validTaskIds = workPackage.Select(t => t.TskId).ToHashSet();

                var toRemove = sapaYear.Details
                    .Where(d => !validTaskIds.Contains(d.SapaDetTskId))
                    .ToList();

                _dbContext.SapaDetail.RemoveRange(toRemove);

                sapaYear.Details = sapaYear.Details
                    .Where(d => validTaskIds.Contains(d.SapaDetTskId))
                    .ToList();

                foreach (var task in workPackage)
                {
                    var sapaDetail = sapaYear.Details.FirstOrDefault(x => x.SapaDetTskId == task.TskId);
                    sapaDetail = GetSapaDetail(task, year, sapaDetail);

                    if(sapaDetail != null && !sapaYear.Details.Any(x => x.SapaDetTskId == task.TskId))
                    {
                        sapaYear.Details.Add(sapaDetail);
                    }
                }

                sapaYear.SapaYearBudgetRequest = sapaYear.Details.Sum(x => x.SapaDetCostYear1);
            }

            var yearsToRemove = sapa.Years.Where(x => x.SapaYearYear < firstYear || x.SapaYearYear > lastYear).ToList();
            sapa.Years = sapa.Years.Except(yearsToRemove).ToList();

            if(yearsToRemove.Any())
                _dbContext.RemoveRange(yearsToRemove);
        }

        var workPackagesToRemove = dbCollection.Sapas.Where(x => !wpIds.Contains(x.SapaWorkpackageId)).ToList();
        dbCollection.Sapas = dbCollection.Sapas.Except(workPackagesToRemove).ToList();

        if (workPackagesToRemove.Any())
            _dbContext.RemoveRange(workPackagesToRemove);

        _dbContext.Update(dbCollection);
        _dbContext.SaveChangesAndClear(_userName);
    }

    public Dictionary<int?, string> GetSelectedSapaDict(int riskObjectId)
    {
        return _dbContext.SapaCollection
            .Where(x => x.SapaCollRiskObjId == riskObjectId)
            .ToDictionary(x => (int?)x.SapaCollId, x => x.SapaCollName);
    }
    
    public List<SapaDetail> GetSheqData(int riskObjectId)
    {
        var sapaDetails = _dbContext.SapaDetail
            .Where(x => x.SapaDetMrbId == riskObjectId)
            .ToList();

        var sheqData = sapaDetails.GroupBy(x => x.Risk.MrbSafetyBefore);
        
        return sapaDetails;
    }

    public decimal GetAssignedBudget(int sapaId, int year)
    {
        var sapaYear = _dbContext.SapaYear
            .Where(x => x.SapaYearSapaId == sapaId)
            .Where(x => x.SapaYearYear == year)
            .FirstOrDefault();

        if (sapaYear == null) return 0;

        return sapaYear.SapaYearBudget;
    }

    public SapaModel GenerateSapaforRiskObjects(RiskObject riskObject, string noWorkPackageName)
    {
        var alltasks = riskObject.Risks.SelectMany(x => x.Tasks);
        var wpGroups = alltasks.GroupBy(x => x.TskSapaWorkpackage).OrderBy(x => x.Key);
        var CollectionNames = _dbContext.SapaCollection.Where(x => x.SapaCollRiskObjId == riskObject.RiskObjId).Select(x => x.SapaCollName).ToList();

        var sapaCollName = "Sapa Collection";
        int index = 1;
        while (true)
        {
            if (!CollectionNames.Contains(sapaCollName))
                break;

            sapaCollName = $"Sapa Collection {index++}";
        }

        var sapaCollection = new SapaCollection
        {
            SapaCollName = sapaCollName,
            SapaCollRiskObjId = riskObject.RiskObjId,
        };
        _dbContext.SapaCollection.Add(sapaCollection);
        _dbContext.SaveChangesAndClear(_userName);

        SetSelectedSapaCollection(riskObject.RiskObjId, sapaCollection.SapaCollId);

        foreach (var workPackage in wpGroups)
        {
            var firstYear = workPackage.OrderBy(x => x.TskValidFromYear).FirstOrDefault()?.TskValidFromYear ?? 0;
            var lastYear = workPackage.OrderByDescending(x => x.TskValidUntilYear).FirstOrDefault()?.TskValidUntilYear ?? firstYear;
            var sapaName = workPackage.FirstOrDefault()?.TskSapaWorkpackageNavigation?.SapaWpName;
            if (string.IsNullOrWhiteSpace(sapaName))
                sapaName = noWorkPackageName;

            var sapa = new Sapa
            {
                SapaFirstYear = firstYear,
                SapaLastYear = lastYear,
                SapaCollectionId = sapaCollection.SapaCollId,
                Years = new List<SapaYear>(),
                SapaWorkpackageId = workPackage?.Key,
                SapaName = sapaName,
                SapaApproveForAllYears = true,
                SapaStatusId = (int?)Status.Budgeting,
                SapaRiskObjId = riskObject.RiskObjId
            };

            sapa = ProcessYears(sapa, firstYear, lastYear, workPackage.ToList());

            _dbContext.Sapa.Add(sapa);
            _dbContext.SaveChangesAndClear(_userName);
        }


        return GetSapaModelByRiskObject(riskObject.RiskObjId, wpGroups.FirstOrDefault()?.Key);
    }

    private Sapa ProcessYears(Sapa sapa, int firstYear, int lastYear, List<Task> tasks)
    {
        for (int year = firstYear; year <= lastYear; year++)
        {
            var sapaYear = new SapaYear
            {
                SapaYearYear = year,
                Details = new List<SapaDetail>()
            };

            foreach(var task in tasks)
            {
                var detail = GetSapaDetail(task, year);

                if (detail != null)
                    sapaYear.Details.Add(detail);
            }

            sapaYear.SapaYearBudgetRequest = sapaYear.Details.Sum(x => x.SapaDetCostYear1);
            sapa.Years.Add(sapaYear);
        }

        sapa.SapaBudgetRequest = sapa.Years.Sum(x => x.SapaYearBudgetRequest);
        return sapa;
    }

    public SapaDetailModel GetSapaDetail(int sapaDetailId)
    {
        var sapaDetail = _dbContext.SapaDetail
           .Where(x => x.SapaDetId == sapaDetailId)
           .Include(x => x.Risk).ThenInclude(x => x.Installation)
           .Include(x => x.Risk).ThenInclude(x => x.System)
           .Include(x => x.Risk).ThenInclude(x => x.Component)
           .Include(x => x.Risk).ThenInclude(x => x.MrbImage)
           .Include(x => x.Task).ThenInclude(x => x.TskSapaWorkpackageNavigation)
           .FirstOrDefault();

        return _mapper.Map<SapaDetailModel>(sapaDetail); 
    }

    private SapaDetail GetSapaDetail(Task task, int year, SapaDetail detail = null)
    {
        if (task.TskValidFromYear == null || year > task.TskValidUntilYear)
            return null;

        detail ??= new SapaDetail
        {
            SapaDetTskId = task.TskId,
            SapaDetMrbId = task.TskMrbId ?? 0,
            SapaDetScore = task.TskMrb.MrbRiskBefore, //TODO: Multiply with task weight percent
        };

        detail.SapaDetCostYear1 = 0;
        detail.SapaDetCostYear2 = 0;
        detail.SapaDetCostYear3 = 0;
        detail.SapaDetCostYear4 = 0;
        detail.SapaDetCostYear5 = 0;

        var offset = year - task.TskValidFromYear.Value;
        decimal[] costArr = { task.TskCostY1 ?? 0, task.TskCostY2 ?? 0, task.TskCostY3 ?? 0, task.TskCostY4 ?? 0, task.TskCostY5 ?? 0 };

        if (offset >= 0)
            detail.SapaDetCostYear1 += costArr.Skip(offset).FirstOrDefault();

        if (offset >= -1)
            detail.SapaDetCostYear2 += costArr.Skip(offset + 1).FirstOrDefault();

        if (offset >= -2)
            detail.SapaDetCostYear3 += costArr.Skip(offset + 2).FirstOrDefault();

        if (offset >= -3)
            detail.SapaDetCostYear4 += costArr.Skip(offset + 3).FirstOrDefault();

        if (offset >= -4)
            detail.SapaDetCostYear5 += costArr.Skip(offset + 4).FirstOrDefault();

        detail.SapaDetTotalCapexNeeded = detail.SapaDetCostYear1 + detail.SapaDetCostYear2 + detail.SapaDetCostYear3 + detail.SapaDetCostYear4 + detail.SapaDetCostYear5;
        return detail;
    }

    public int? GetSelectedSapaCollection(int riskObjectId)
    {
        var selected = _dbContext.SapaCollection.FirstOrDefault(x => x.SapaCollRiskObjId == riskObjectId && x.SapaCollSelected);
        return selected?.SapaCollId;
    }

    public void SetSelectedSapaCollection(int riskObjectId, int? sapaCollectionId)
    {
        var sapaCollections = _dbContext.SapaCollection.Where(x => x.SapaCollRiskObjId == riskObjectId).ToList();
        sapaCollections.ForEach(x => x.SapaCollSelected = false);

        var collection = sapaCollections.FirstOrDefault(x => x.SapaCollId == sapaCollectionId);
        if (collection != null)
            collection.SapaCollSelected = true;

        _dbContext.UpdateRange(sapaCollections);
        _dbContext.SaveChangesAndClear(_userName);
    }
}

public static class SapaExtensions
{
    public static IQueryable<Sapa> IncludeAllSapaData(this IQueryable<Sapa> query)
    {
        return query
            .Include(x => x.SapaCollection)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Risk)
                .ThenInclude(x => x.RiskObject)
                .ThenInclude(x => x.RiskObjDepartment)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Risk)
                .ThenInclude(x => x.Installation)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Risk)
                .ThenInclude(x => x.System)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Risk)
                .ThenInclude(x => x.Component)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Risk)
                .ThenInclude(x => x.Assembly)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Risk)
                .ThenInclude(x => x.MrbStatusNavigation)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Task)
                .ThenInclude(x => x.TskExecutorNavigation)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Task)
                .ThenInclude(x => x.TskInitiatorNavigation)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Task)
                .ThenInclude(x => x.TskSapaWorkpackageNavigation)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Task)
                .ThenInclude(x => x.TskIntervalUnitNavigation)
            .Include(x => x.Years)
                .ThenInclude(x => x.Details)
                .ThenInclude(x => x.Task)
                .ThenInclude(x => x.TskMxPolicyNavigation);
    }

}
