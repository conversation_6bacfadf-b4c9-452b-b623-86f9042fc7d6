using System.Collections.Generic;
using System.Xml.Serialization;

namespace AMprover.BusinessLogic.XMLEntities;

[XmlRoot(ElementName = "_fmecaMainSelGrid", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaMainSelGrid
{
    [XmlElement(ElementName = "_customMtbfAfter", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string CustomMtbfAfter { get; set; }
    [XmlElement(ElementName = "_customMtbfBefore", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string CustomMtbfBefore { get; set; }
    [XmlElement(ElementName = "_customMtbfPmo", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string CustomMtbfPmo { get; set; }
    [XmlElement(ElementName = "_effectColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string EffectColumns { get; set; }
    [XmlElement(ElementName = "_fmecaSelColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaSelColumns FmecaSelColumns { get; set; }
    [XmlElement(ElementName = "_mtbfSelectedAfter", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string MtbfSelectedAfter { get; set; }
    [XmlElement(ElementName = "_mtbfSelectedBefore", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string MtbfSelectedBefore { get; set; }
    [XmlElement(ElementName = "_mtbfSelectedPmo", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string MtbfSelectedPmo { get; set; }
}

[XmlRoot(ElementName = "Value", Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
public class DataValue
{
    [XmlElement(ElementName = "_customMtbfAfter", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string CustomMtbfAfter { get; set; }
    [XmlElement(ElementName = "_customMtbfBefore", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string CustomMtbfBefore { get; set; }
    [XmlElement(ElementName = "_customMtbfPmo", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string CustomMtbfPmo { get; set; }
    [XmlElement(ElementName = "_effectColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string EffectColumns { get; set; }
    [XmlElement(ElementName = "_fmecaSelColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaSelColumns FmecaSelColumns { get; set; }
    [XmlElement(ElementName = "_mtbfSelectedAfter", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string MtbfSelectedAfter { get; set; }
    [XmlElement(ElementName = "_mtbfSelectedBefore", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string MtbfSelectedBefore { get; set; }
    [XmlElement(ElementName = "_mtbfSelectedPmo", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string MtbfSelectedPmo { get; set; }
}

[XmlRoot(ElementName = "KeyValueOfintFmecaSelGridq9MbduM8", Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
public class DataSubGrid
{
    [XmlElement(ElementName = "Key", Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
    public string Key { get; set; }
    [XmlElement(ElementName = "Value", Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
    public DataValue Value { get; set; }
}

[XmlRoot(ElementName = "_fmecaSubGrids", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaDataSubGrids
{
    [XmlElement(ElementName = "KeyValueOfintFmecaSelGridq9MbduM8", Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
    public List<DataSubGrid> SubGrids { get; set; } = new List<DataSubGrid>();
    [XmlAttribute(AttributeName = "a", Namespace = "http://www.w3.org/2000/xmlns/")]
    public string A { get; set; }
}

[XmlRoot(ElementName = "FmecaSelData", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaSelData
{
    [XmlElement(ElementName = "_fmecaMainSelGrid", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaMainSelGrid FmecaMainSelGrid { get; set; }
    [XmlElement(ElementName = "_fmecaSubGrids", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaDataSubGrids FmecaSubGrids { get; set; }
    [XmlElement(ElementName = "_mrbID", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string MrbId { get; set; }
}