using System.Collections.Generic;
using System.Xml.Serialization;

namespace AMprover.BusinessLogic.XMLEntities;

[XmlRoot(ElementName = "_backColor")]
public class BackColor
{
    [XmlElement(ElementName = "knownColor", Namespace = "http://schemas.datacontract.org/2004/07/System.Drawing")]
    public string KnownColor { get; set; }

    [XmlElement(ElementName = "state", Namespace = "http://schemas.datacontract.org/2004/07/System.Drawing")]
    public string State { get; set; }

    [XmlElement(ElementName = "value", Namespace = "http://schemas.datacontract.org/2004/07/System.Drawing")]
    public string Value { get; set; }
}

[XmlRoot(ElementName = "FmecaCell", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaCell
{
    [XmlElement(ElementName = "_backColor", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public BackColor BackColor { get; set; } = new BackColor();

    [XmlElement(ElementName = "_colIndex", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string ColIndex { get; set; }

    [XmlElement(ElementName = "_customvalue", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Customvalue { get; set; }

    [XmlElement(ElementName = "_description", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Description { get; set; }

    [XmlElement(ElementName = "_shortDescription", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string ShortDescription { get; set; }

    [XmlElement(ElementName = "_rowIndex", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string RowIndex { get; set; }

    [XmlElement(ElementName = "_subgridID", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string SubgridId { get; set; }

    [XmlElement(ElementName = "_value", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Value { get; set; }

    //2020-12-11 new field and grid
    [XmlElement(ElementName = "_points", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Points { get; set; }
}

[XmlRoot(ElementName = "_fmecaCells", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaCells
{
    [XmlElement(ElementName = "FmecaCell", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public List<FmecaCell> FmecaCell { get; set; } = new List<FmecaCell>();
}

[XmlRoot(ElementName = "FmecaColumn", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaColumn
{
    [XmlElement(ElementName = "_isEffectColumn", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string IsEffectColumn { get; set; }

    [XmlElement(ElementName = "_backColor", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public BackColor BackColor { get; set; } = new BackColor();

    [XmlElement(ElementName = "_colIndex", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string ColIndex { get; set; }

    [XmlElement(ElementName = "_customvalue", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Customvalue { get; set; }

    [XmlElement(ElementName = "_description", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Description { get; set; }

    [XmlElement(ElementName = "_rowIndex", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string RowIndex { get; set; }

    [XmlElement(ElementName = "_subgridID", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string SubgridId { get; set; }

    [XmlElement(ElementName = "_value", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Value { get; set; }

    [XmlElement(ElementName = "_hasSubColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string HasSubColumns { get; set; }

    [XmlElement(ElementName = "_isPercentage", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string IsPercentage { get; set; }

    [XmlElement(ElementName = "_settings", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Settings { get; set; }
    
    [XmlElement(ElementName = "_types", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Types { get; set; }

    [XmlElement(ElementName = "_subColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string SubColumns { get; set; }

    [XmlElement(ElementName = "Entity", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Entity { get; set; }

    [XmlElement(ElementName = "Unity", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Unity { get; set; }
}

[XmlRoot(ElementName = "_fmecaColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaColumns
{
    [XmlElement(ElementName = "FmecaColumn", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public List<FmecaColumn> FmecaColumn { get; set; } = new List<FmecaColumn>();
}

[XmlRoot(ElementName = "_fmecaMainGrid", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaMainGrid
{
    [XmlElement(ElementName = "_colorColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string ColorColumns { get; set; }

    [XmlElement(ElementName = "_effectColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string EffectColumns { get; set; }

    [XmlElement(ElementName = "_fmecaCells", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaCells FmecaCells { get; set; } = new FmecaCells();

    [XmlElement(ElementName = "_fmecaColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaColumns FmecaColumns { get; set; } = new FmecaColumns();

    [XmlElement(ElementName = "_gridRows", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string GridRows { get; set; }
}

[XmlRoot(ElementName = "Value", Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
public class Value
{
    [XmlElement(ElementName = "_colorColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string ColorColumns { get; set; }

    [XmlElement(ElementName = "_effectColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string EffectColumns { get; set; }

    [XmlElement(ElementName = "_type", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string Type { get; set; }

    [XmlElement(ElementName = "_fmecaCells", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaCells FmecaCells { get; set; } = new FmecaCells();

    [XmlElement(ElementName = "_fmecaColumns", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaColumns FmecaColumns { get; set; } = new FmecaColumns();

    [XmlElement(ElementName = "_gridRows", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string GridRows { get; set; }
}

[XmlRoot(ElementName = "KeyValueOfintFmecaGridq9MbduM8",
    Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
public class SubGrid
{
    [XmlElement(ElementName = "Key", Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
    public string ColumnId { get; set; }

    [XmlElement(ElementName = "Value", Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
    public Value Value { get; set; } = new Value();
}

[XmlRoot(ElementName = "_fmecaSubGrids", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaSubGrids
{
    [XmlElement(ElementName = "KeyValueOfintFmecaGridq9MbduM8",
        Namespace = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
    public List<SubGrid> SubGrids { get; set; } = new List<SubGrid>();

    [XmlAttribute(AttributeName = "a", Namespace = "http://www.w3.org/2000/xmlns/")]
    public string A { get; set; }
}

[XmlRoot(ElementName = "FmecaData", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaData
{
    [XmlElement(ElementName = "_fmecaMainGrid", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaMainGrid FmecaMainGrid { get; set; } = new FmecaMainGrid();

    [XmlElement(ElementName = "_fmecaSubGrids", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public FmecaSubGrids FmecaSubGrids { get; set; } = new FmecaSubGrids();
}