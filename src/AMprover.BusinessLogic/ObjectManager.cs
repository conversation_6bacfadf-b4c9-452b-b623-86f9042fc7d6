using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Exceptions;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.Data.Entities.AM;
using AMprover.Data.Extensions;
using AMprover.Data.Infrastructure;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;

namespace AMprover.BusinessLogic;

public interface IObjectManager
{
    List<GenericObjectLevel> GetGenericObjectLevels();

    GenericObjectLevel UpdateGenericObjectLevel(GenericObjectLevel objLevel);

    Dictionary<ObjectLevel, string> GetObjectLevelNames();

    Dictionary<ObjectLevel, List<ObjectModel>> GetAllObjects();

    List<ObjectModel> GetAllSystemsFromRiskObject(int RiskObjectId);

    List<ObjectModel> GetObjects(ObjectLevel objectLevel);

    ObjectModel SaveObject(ObjectModel obj);

    ObjectModel GetObject(int id);

    void DeleteObject(int id);

    bool ObjectHasDependencies(int Id);
}

public class ObjectManager : IObjectManager
{
    private readonly AssetManagementDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly string _userName;

    public ObjectManager(
        AuthenticationStateProvider authenticationStateProvider,
        AssetManagementDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;

        _userName = authenticationStateProvider.GetLoggedInUsername().Result;
    }

    public List<GenericObjectLevel> GetGenericObjectLevels()
    {
        var dbGenericObjectsLevels = _dbContext.LookupUserDefined.Where(x => x.UserDefinedFilter == "ObjectName").OrderBy(x => x.UserDefinedId).ToList();

        if (dbGenericObjectsLevels?.Count is not 5 || !dbGenericObjectsLevels.All(lu => lu.UserDefinedValue.HasValue))
        {
            throw new DataQualityException($"Missing/Incomplete Generic Object Levels based on entity {nameof(LookupUserDefined)} with lookupKey ObjectName. Found: {dbGenericObjectsLevels?.Count} records. Ensure 5 records are present with a filter, short description and value configured.");
        }

        return dbGenericObjectsLevels.Select(_mapper.Map<GenericObjectLevel>).ToList();
    }

    public GenericObjectLevel UpdateGenericObjectLevel(GenericObjectLevel objLevel)
    {
        var dbGenericObjectsLevel = _dbContext.LookupUserDefined.FirstOrDefault(x => x.UserDefinedFilter == "ObjectName" && x.UserDefinedValue == objLevel.Level);

        if (dbGenericObjectsLevel == null) return null;

        dbGenericObjectsLevel.UserDefinedShortDescription = objLevel.Name;
        dbGenericObjectsLevel.UserDefinedLongDescription = objLevel.Name;
        dbGenericObjectsLevel.UserDefinedModifiedBy = _userName.Truncate(30);
        dbGenericObjectsLevel.UserDefinedDateModified = System.DateTime.Now;

        var result = _dbContext.LookupUserDefined.Update(dbGenericObjectsLevel);
        _dbContext.SaveChangesAndClear(_userName);

        return _mapper.Map<GenericObjectLevel>(result.Entity);
    }

    public Dictionary<ObjectLevel, string> GetObjectLevelNames()
    {
        var values = _dbContext.LookupUserDefined.Where(x => x.UserDefinedFilter == "ObjectName").OrderBy(x => x.UserDefinedValue).ToList();
        var result = new Dictionary<ObjectLevel, string>();

        for (int i = 0; i < 5; i++)
        {
            var key = (ObjectLevel)i;
            var value = values.FirstOrDefault(x => x.UserDefinedValue == i)?.UserDefinedLongDescription;
            if (string.IsNullOrWhiteSpace(value))
                value = key.ToString();

            result.Add(key, value.Trim());
        }

        return result;
    }

    public Dictionary<ObjectLevel, List<ObjectModel>> GetAllObjects()
    {
        var result = new Dictionary<ObjectLevel, List<ObjectModel>>();
        var objects = _dbContext.Object.Select(x => _mapper.Map<ObjectModel>(x)).ToList();

        foreach (ObjectLevel level in System.Enum.GetValues(typeof(ObjectLevel)))
            result.Add(level, objects.Where(x => x.Level == (int)level).ToList());

        return result;
    }

    public List<ObjectModel> GetAllSystemsFromRiskObject(int RiskObjectId)
    {
        var systems = _dbContext.Mrb
            .Include(x => x.System)
            .Where(x => x.MrbRiskObject == RiskObjectId && x.System != null)
            .Select(x => x.System)
            .ToList()
            .DistinctBy(x => x.ObjId)
            .Select(x => _mapper.Map<ObjectModel>(x))
            .ToList();

        return systems;
    }

    public void DeleteObject(int id)
    {
        var dbObjectToRemove = _dbContext.Object.Find(id);

        //If the object cannot be found
        if (dbObjectToRemove == null)
            return;
        
        _dbContext.Object.Remove(dbObjectToRemove);
        _dbContext.SaveChangesAndClear(_userName);
    }

    public ObjectModel SaveObject(ObjectModel obj)
    {
        var dbObject = _mapper.Map<Object>(obj);
        dbObject.ObjDateModified = System.DateTime.Now;
        dbObject.ObjModifiedBy = _userName;

        var dbResult = dbObject.ObjId > 0
            ? _dbContext.Object.Update(dbObject)
            : _dbContext.Object.Add(dbObject);

        _dbContext.SaveChangesAndClear(_userName);
        return _mapper.Map<ObjectModel>(dbResult.Entity);
    }

    public bool ObjectHasDependencies(int id) => 
        ObjectHasLinkedRiskObjects(id) || ObjectHasLinkedRisks(id);

    private bool ObjectHasLinkedRiskObjects(int id)
    {
        return _dbContext.Object
            .Where(x => x.ObjId == id)
            .Select(a => a.RiskObject.Any()
                      || a.ChildRiskObjects.Any())
            .FirstOrDefault();
    }

    private bool ObjectHasLinkedRisks(int id)
    {
        return _dbContext.Object
            .Where(x => x.ObjId == id)
            .Select(a => a.MrbChildObjects.Any() 
                      || a.MrbChildObjects1.Any()
                      || a.MrbChildObjects2.Any()
                      || a.MrbChildObjects3.Any()
                      || a.MrbChildObjects4.Any())
            .FirstOrDefault();
    }

    public List<ObjectModel> GetObjects(ObjectLevel objectLevel)
    {
        return _dbContext.Object
            .Where(x => x.ObjLevel == (int)objectLevel)
            .Select(x => _mapper.Map<ObjectModel>(x))
            .ToList();
    }

    public ObjectModel GetObject(int Id)
    {
        var _object = _dbContext.Object
            .FirstOrDefault(x => x.ObjId == Id);

        if (_object == null) return null;

        var objectModel = _mapper.Map<ObjectModel>(_object);

        return objectModel;
    }
}