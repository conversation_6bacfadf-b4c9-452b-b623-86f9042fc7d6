using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using AMprover.Data.Repositories;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Helpers;
using Microsoft.AspNetCore.Components.Authorization;
using SpareModel = AMprover.BusinessLogic.Models.RiskAnalysis.SpareModel;

namespace AMprover.BusinessLogic;

public interface ISparePartManager
{
    SpareModel GetSpareById(int id);

    SpareModel UpdateSpare(SpareModel spare);

    List<SpareModel> GetSparesByRiskObject(int riskObjectId);
}

public class SparePartManager : ISparePartManager
{
    private readonly AssetManagementDbContext _dbContext;
    private readonly ISpareRepository _spareRepository;
    private readonly IMapper _mapper;
    private readonly string _userName;

    public SparePartManager(AuthenticationStateProvider authenticationProvider, AssetManagementDbContext dbContext, ISpareRepository spareRepository, IMapper mapper)
    {
        _dbContext = dbContext;
        _spareRepository = spareRepository;
        _mapper = mapper;
        _userName = authenticationProvider.GetLoggedInUsername().Result;
    }
        
    public SpareModel GetSpareById(int id)
    {
        var spare  = _spareRepository.GetById(id);
        return _mapper.Map<SpareModel>(spare);
    }

    public List<SpareModel> GetSparesByRiskObject(int riskObjectId)
    {
        var riskObject = _dbContext.RiskObject
            .Include(x => x.Risks)
            .ThenInclude(x => x.Spares)
            .FirstOrDefault(x => x.RiskObjId == riskObjectId);

        return riskObject?.Risks?.SelectMany(x => x.Spares).Select(x => _mapper.Map<SpareModel>(x)).ToList() ?? new List<SpareModel>();
    }

    public SpareModel UpdateSpare(SpareModel spare)
    {
        if (spare.Id == 0)
        {
            var result = _spareRepository.Add(_mapper.Map<Spare>(spare), _userName);
            return _mapper.Map<SpareModel>(result.Item);
        }
        else
        {
            var dbSpare = _spareRepository.GetById(spare.Id);
            dbSpare = _mapper.Map(spare, dbSpare);

            var result = _spareRepository.Update(dbSpare, _userName);
            return _mapper.Map<SpareModel>(result.Item);
        }
    }
}