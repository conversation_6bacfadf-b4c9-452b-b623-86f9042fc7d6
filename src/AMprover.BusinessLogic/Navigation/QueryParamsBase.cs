using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace AMprover.BusinessLogic.Navigation;

public class QueryParamsBase
{
    private bool _initializing;
    protected string _result;
    protected ILogger _Logger;
    protected NavigationManager _navManager { get; set; }
    protected PropertyInfo[] _properties { get; set; }

    public QueryParamsBase(ILoggerFactory lf, NavigationManager nav)
    {
        _Logger = lf.CreateLogger(GetType().Name);
        _navManager = nav;
        _properties = GetType().GetProperties();

        _initializing = true;
        Initialize(nav.Uri);
        _initializing = false;
    }

    protected void UpdateUrl()
    {
        if (_initializing) return;

        var baseUri = new Uri(_navManager.Uri).GetLeftPart(UriPartial.Path);
        var newUrl = baseUri + ToQueryString();

        if (_navManager.Uri != newUrl)
            _navManager.NavigateTo(newUrl, replace: true);
    }

    private void Initialize(string uriString)
    {
        var uri = new Uri(uriString);
        var query = QueryHelpers.ParseQuery(uri.Query);

        foreach (var kvp in query)
        {
            var key = kvp.Key;    
            var rawValue = kvp.Value.ToString();

            var prop = _properties.FirstOrDefault(p =>
                p.Name.Equals(key,StringComparison.OrdinalIgnoreCase));

            if (prop == null || string.IsNullOrWhiteSpace(rawValue))
            {
                continue;
            }  

            // values:
            var targetType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
            if (targetType == typeof(string))
            {
                prop.SetValue(this, rawValue);
                continue;
            }
            if (targetType == typeof(int) && int.TryParse(rawValue, out var i))
            {
                prop.SetValue(this, i);
                continue;
            }
            if (targetType.IsEnum)
            {
                var enumVal = Enum.Parse(targetType, rawValue, true);
                prop.SetValue(this, enumVal);
                continue;
            }

            // lists:
            if (prop.PropertyType == typeof(List<string>))
            {
                var list = rawValue
                           .Split(',', StringSplitOptions.RemoveEmptyEntries)
                           .Select(s => s.Trim())
                           .ToList();
                prop.SetValue(this, list);
                continue;
            }
            if (prop.PropertyType == typeof(List<int>))
            {
                var list = rawValue
                           .Split(',', StringSplitOptions.RemoveEmptyEntries)
                           .Select(s => int.Parse(s))
                           .ToList();
                prop.SetValue(this, list);
                continue;
            }
        }
    }

    public string ToQueryString()
    {
        var dict = new Dictionary<string, string>();
        foreach (var prop in _properties)
        {
            var val = prop.GetValue(this);

            if (val is List<string> ss && ss.Any())
                dict[prop.Name] = string.Join(",", ss);
            else if (val is List<int> il && il.Any())
                dict[prop.Name] = string.Join(",", il);
            else if (val is List<int?> nil && nil.Any())
                dict[prop.Name] = string.Join(",", nil);
            else if (val is int i && i != default)
                dict[prop.Name] = i.ToString();
            else if (val is string s && !string.IsNullOrEmpty(s))
                dict[prop.Name] = s;
            else if (val is Enum e)
                dict[prop.Name] = e.ToString();
        }

        var baseUri = new Uri(_navManager.Uri).GetLeftPart(UriPartial.Path);
        return QueryHelpers.AddQueryString(baseUri, dict).Substring(baseUri.Length);
    }
}
