using System;
using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using AMprover.Data.Repositories;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.Rams.Calculations;
using AMprover.BusinessLogic.Models.Rams.Nodes;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using AMprover.Data.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Buffer = AMprover.BusinessLogic.Models.Rams.Nodes.Buffer;
using Object = AMprover.Data.Entities.AM.Object;
using Parallel = AMprover.BusinessLogic.Models.Rams.Nodes.Parallel;

namespace AMprover.BusinessLogic;

public interface IRamsManager
{
    #region Diagrams

    List<RamsDiagramModel> GetRamsDiagrams();

    List<RamsModel> GetRams(int id);

    List<RamsModel> GetRamsItemByParent(int diagramId, int parentId);

    RamsModel GetRamsItem(Guid? guid);

    RamsModel GetRamsItem(int id);

    RamsDiagramModel GetRamsDiagram(int id);

    RiskObject GetRiskObject(RamsDiagramModel ramsDiagramModel);

    Task<RamsDiagramModel> GenerateDiagramFromRiskObject(RamsDiagramModel ramsDiagram,
        CalculationParameters functionalCalculationParameters,
        CalculationParameters technicalCalculationParameters, RamsDisplayModel displayModel, string language);

    bool ImportFileUpload(int diagramId, string language, List<RamsFlatModel> ramsItems);

    void SwitchFunctionalObject(TreeNodeGeneric<RiskTreeObject> selectedNode,
        RamsComponentModel selectedItem,
        RamsModel node);

    RamsModel CreateOrEditRams(RamsModel model);

    RamsDiagramModel GetRamsDiagramContent(int id);

    RamsDiagramModel CreateOrEditRamsDiagram(RamsDiagramModel model);

    RamsDiagramModel RecalculateDiagram(RamsDiagramModel model, CalculationParameters functionalCalculationParameters,
        CalculationParameters technicalCalculationParameters, RamsDisplayModel display, bool saveCalculations,
        string language, bool loadFromDatabase = false);

    void DeleteRamsDiagram(RamsDiagramModel diagram);

    void DeleteRams(Guid id);

    #endregion

    #region Tree methods

    TreeNodeGeneric<RamsTreeObject> GetRamsDiagramTreeByScenario();

    #endregion
}

public class RamsManager : IRamsManager
{
    private readonly IRiskAnalysisManager _riskAnalysisManager;

    private readonly IAssetManagementBaseRepository<RamsDiagram> _ramsDiagramRepository;
    private readonly IAssetManagementBaseRepository<Rams> _ramsRepository;
    private readonly ILogger<RamsManager> _logger;
    private readonly IMapper _mapper;
    private readonly AssetManagementDbContext _dbContext;
    private readonly AuthenticationStateProvider _authenticationProvider;
    private readonly string _userName;

    public RamsManager(IAssetManagementBaseRepository<RamsDiagram> ramsDiagramRepository,
        IRiskAnalysisManager riskAnalysisManager, IAssetManagementBaseRepository<Rams> ramsRepository,
        ILogger<RamsManager> logger, AssetManagementDbContext dbContext, ILookupManager lookupManager,
        AuthenticationStateProvider authenticationProvider, IMapper mapper)
    {
        _ramsRepository = ramsRepository;
        _ramsDiagramRepository = ramsDiagramRepository;
        _dbContext = dbContext;
        _authenticationProvider = authenticationProvider;
        _logger = logger;
        _mapper = mapper;
        _riskAnalysisManager = riskAnalysisManager;

        _userName = _authenticationProvider.GetLoggedInUsername().Result;

        lookupManager.GetLanguage();
    }

    #region Rams GET methods

    public List<RamsDiagramModel> GetRamsDiagrams()
    {
        return _dbContext.RamsDiagram
            .Include(x => x.Scenario)
            .Select(x => _mapper.Map<RamsDiagramModel>(x)).ToList();
    }

    public List<RamsModel> GetRams(int id)
    {
        return _dbContext.Rams
            .Where(x => x.RamsDiagramId == id)
            .Select(x => _mapper.Map<RamsModel>(x)).ToList();
    }

    public RamsModel GetRamsItem(int id)
    {
        return _mapper.Map<RamsModel>(_dbContext.Rams.FirstOrDefault(x => x.RamsId == id));
    }

    public RamsModel GetRamsItem(Guid? guid)
    {
        return guid == null ? null : _mapper.Map<RamsModel>(_dbContext.Rams.FirstOrDefault(x => x.RamsNodeId == guid));
    }

    public List<RamsModel> GetRamsItemByParent(int diagramId, int parentId)
    {
        return _dbContext.Rams.Where(x => x.RamsPartOf == parentId && x.RamsDiagramId == diagramId)
            .Select(x => _mapper.Map<RamsModel>(x)).ToList();
    }

    public RamsDiagramModel GetRamsDiagram(int id)
    {
        var ramsDiagram = _ramsDiagramRepository.GetById(id);
        return ramsDiagram != null ? _mapper.Map<RamsDiagramModel>(ramsDiagram) : null;
    }

    public RamsDiagramModel GetRamsDiagramContent(int id)
    {
        var ramsDiagram = _dbContext.RamsDiagram
            .Include(x => x.Rams)
            .FirstOrDefault(x => x.RamsDgId == id);
        return _mapper.Map<RamsDiagramModel>(ramsDiagram);
    }

    #endregion

    #region RAMS Delete methods

    public void DeleteRamsDiagram(RamsDiagramModel diagram)
    {
        if (diagram == null) return;

        //Remove blocks
        _dbContext.Rams.RemoveRange(_dbContext.Rams.Where(x => x.RamsDiagramId == diagram.Id));
        _dbContext.SaveChangesAndClear(_userName);

        //Remove diagram
        _ramsDiagramRepository.Delete(_ramsDiagramRepository.GetById(diagram.Id), _userName);
    }

    public void DeleteRams(Guid id)
    {
        _ramsRepository.Delete(_dbContext.Rams.FirstOrDefault(x => x.RamsNodeId == id), _userName);
    }

    #endregion

    #region Save rams helper methods

    public bool ImportFileUpload(int diagramId, string language, List<RamsFlatModel> ramsItems)
    {
        try
        {
            var ramsDbItems = _dbContext.Rams.Where(x => x.RamsDiagramId == diagramId).ToList();

            foreach (var ramsItem in ramsItems)
            {
                var ramsDbItem = ramsDbItems.Find(x => x.RamsId == ramsItem.Id);

                if (ramsDbItem != null)
                {
                    _mapper.Map(ramsItem, ramsDbItem);
                    _dbContext.Update(ramsDbItem);
                }
            }

            var diagram = _dbContext.RamsDiagram.FirstOrDefault(x => x.RamsDgId == diagramId);

            if (diagram == null)
                return false;
            
            RamsDiagramContentModel diagramContent;
            using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(diagram.RamsDgSerialized)))
            using (var reader = new StreamReader(stream))
            using (var jsonReader = new JsonTextReader(reader))
            {
                var serializer = JsonSerializer.Create(new JsonSerializerSettings
                {
                    Culture = new CultureInfo(language)
                });
                diagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
            }

            if (diagramContent.Parts != null && diagramContent.Parts.Any())
                UpdateChildren(diagramContent.Parts, ramsItems, ramsDbItems);

            diagram.RamsDgSerialized = JsonConvert.SerializeObject(diagramContent);
            _dbContext.Update(diagram);

            _dbContext.SaveChangesAndClear(_userName);
            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e, $"Unable to import list of rams items for {diagramId}.");
        }

        return false;
    }

    private void UpdateChildren(List<RamsComponentModel> parts, List<RamsFlatModel> ramsItems, List<Rams> ramsDbItems)
    {
        foreach (var part in parts)
        {
            var ramsId = ramsDbItems.FirstOrDefault(x => x.RamsNodeId == part.Id)?.RamsId;
            part.Title = ramsItems.FirstOrDefault(x => x.Id == ramsId)?.Name ?? part.Title;

            if (part.Parts != null && part.Parts.Any())
                UpdateChildren(part.Parts, ramsItems, ramsDbItems);
        }
    }

    public RiskObject GetRiskObject(RamsDiagramModel ramsDiagramModel)
    {
        return _dbContext.RiskObject
            .Include(x => x.Risks).ThenInclude(x => x.System)
            .FirstOrDefault(x => x.RiskObjId == ramsDiagramModel.RiskObject);
    }

    /// <summary>
    /// Generate diagram based on selected risk object. The risk object is the diagram, the underlying systems are the
    /// containers and the risks are the blocks
    /// </summary>
    /// <returns></returns>
    public async Task<RamsDiagramModel> GenerateDiagramFromRiskObject(RamsDiagramModel ramsDiagramModel,
        CalculationParameters functionalCalculationParameters,
        CalculationParameters technicalCalculationParameters, RamsDisplayModel displayModel, string language)
    {
        if (ramsDiagramModel.RiskObject == null)
            return null;

        var tree = _riskAnalysisManager.GetRisksTreeNodeByRiskObject(ramsDiagramModel.RiskObject.Value);

        var riskObject = _dbContext.RiskObject
            .Include(x => x.Risks).ThenInclude(x => x.System)
            .Include(x => x.Risks).ThenInclude(x => x.Component)
            .Include(x => x.Risks).ThenInclude(x => x.Assembly)
            .FirstOrDefault(x => x.RiskObjId == ramsDiagramModel.RiskObject);

        if (riskObject == null) return null;

        RamsDiagramModel diagram;

        var dbDiagram = _dbContext.RamsDiagram.FirstOrDefault(x => x.RamsDgId == ramsDiagramModel.Id);

        var generatedGuid = Guid.NewGuid();
        //Add outer container to the diagram
        //Generate content model and add initial container
        var diagramContent = new RamsDiagramContentModel
        {
            ParallelTracks = 1, Parts = new List<RamsComponentModel>
            {
                new()
                {
                    Id = generatedGuid,
                    Type = RamsComponentType.Container,
                    Title = riskObject.RiskObjName,
                    Changed = DateTime.Now,
                    Dept = 1,
                    Results = new RamsReliabilityResultsModel
                    {
                        MtbfFunctional = 1.0,
                        MtbfTechnical = 1.0
                    }
                }
            }
        };

        if (dbDiagram != null)
        {
            diagram = _mapper.Map<RamsDiagramModel>(dbDiagram);
            _dbContext.Rams.RemoveRange(_dbContext.Rams.Where(x => x.RamsDiagramId == dbDiagram.RamsDgId));

            diagram.Serialized = JsonConvert.SerializeObject(diagramContent);

            _dbContext.SaveChangesAndClear(_userName);
        }
        else
        {
            var settings = new JsonSerializerSettings
            {
                Converters = {new AMprover.BusinessLogic.Models.Rams.Converters.InfinityConverter()},
                Formatting = Formatting.Indented // Optional formatting for better readability
            };

            diagram = new RamsDiagramModel
            {
                Name = riskObject.RiskObjName,
                ScenId = riskObject.RiskObjScenarioId,
                Serialized = JsonConvert.SerializeObject(diagramContent, settings)
            };
            diagram = CreateOrEditRamsDiagram(diagram);
        }

        var rootContainer = new RamsModel
        {
            NodeId = generatedGuid,
            Container = true,
            Descr = riskObject.RiskObjName,
            Name = riskObject.RiskObjName,
            Mtbftechn = 1,
            Mtbffunct = 1,
            MtbfFuncCalced = 1,
            MtbfTecCalced = 1,
            LabdaFunctional = 1,
            LabdaTechnical = 1,
            WeibullShape = 1,
            CharacteristicLife = 1,
            DiagramId = diagram.Id
        };

        CreateOrEditRams(rootContainer);

        foreach (var node in tree.Nodes)
        {
            TraverseTree(node, diagramContent.Parts.FirstOrDefault(), diagram, rootContainer, tree.Nodes.IndexOf(node),
                0, riskObject);
        }

        diagramContent.Parts = await ReOrderComponents(diagramContent.Parts);
        diagram.Serialized = JsonConvert.SerializeObject(diagramContent);

        var result = RecalculateDiagram(diagram, functionalCalculationParameters, technicalCalculationParameters,
            displayModel, true, language);

        return result;
    }

    #region helper methods loading a diagram based on risk analysis

    private void TraverseTree(TreeNodeGeneric<RiskTreeObject> node, RamsComponentModel parent,
        RamsDiagramModel diagram,
        RamsModel ramsParent, int order, int dept, RiskObject riskObject)
    {
        dept++;

        if (node.Source.NodeType == RiskTreeNodeType.Risk && node.Source.RiskId.HasValue)
        {
            var risk = _dbContext.Mrb.FirstOrDefault(x => x.Mrbid == node.Source.RiskId.Value);
            GenerateRisk(parent, risk, diagram, ramsParent, order, dept);
            return;
        }

        var functionalObject = node.Source.NodeType switch
        {
            RiskTreeNodeType.Collection => _dbContext.Object.FirstOrDefault(x => x.ObjId == node.Source.CollectionId),
            RiskTreeNodeType.Installation => _dbContext.Object.FirstOrDefault(
                x => x.ObjId == node.Source.InstallationId),
            RiskTreeNodeType.System => _dbContext.Object.FirstOrDefault(x => x.ObjId == node.Source.SystemId),
            RiskTreeNodeType.Component => _dbContext.Object.FirstOrDefault(x => x.ObjId == node.Source.ComponentId),
            RiskTreeNodeType.Assembly => _dbContext.Object.FirstOrDefault(x => x.ObjId == node.Source.AssemblyId),
            _ => null
        };

        if (functionalObject == null)
            return;

        GenerateContainer(functionalObject, order, dept, ramsParent, parent, diagram, riskObject, out var newParent,
            out var newContainer);

        foreach (var childNode in node.Nodes)
        {
            TraverseTree(childNode, newParent, diagram, newContainer, node.Nodes.IndexOf(childNode), dept, riskObject);
        }
    }

    private void GenerateRisk(RamsComponentModel containerPart, Mrb risk, RamsDiagramModel diagram,
        RamsModel ramsContainer, int order, int dept)
    {
        var part = new RamsComponentModel
        {
            Id = Guid.NewGuid(),
            Order = order + 1,
            GroupId = containerPart.Id,
            Title = risk.MrbName,
            ParallelTrack = 1,
            Type = RamsComponentType.Block,
            Dept = dept,
            Results = new RamsReliabilityResultsModel
            {
                MtbfFunctional = ((double?) risk.MrbMtbfAfter).IsNaNOrInfinite(1) ?? 1,
                MtbfTechnical = ((double?) risk.MrbMtbfAfter).IsNaNOrInfinite(1) ?? 1
            },
            RiskId = risk.Mrbid
        };

        var rams = new RamsModel
        {
            DiagramId = diagram.Id,
            Name = risk.MrbName,
            Descr = risk.MrbDescription,
            NodeId = part.Id,
            PartOf = ramsContainer.Id,
            LinkType = (int) RamsLinkType.LinkedToRisk,
            LinkMethod = (int) RamsLinkMethod.FmecaAfter, //Default setting
            RiskId = risk.Mrbid,
            Mtbffunct = (double?) risk.MrbMtbfAfter,
            Mtbftechn = (double?) risk.MrbMtbfAfter,
            MtbfFuncCalced = ((double?) risk.MrbMtbfAfter).IsNaNOrInfinite(1),
            MtbfTecCalced = ((double?) risk.MrbMtbfAfter).IsNaNOrInfinite(1),
        };

        containerPart.Parts.Add(part);
        CreateOrEditRams(rams);
    }

    private void GenerateContainer(Object functionalObject, int order, int dept,
        RamsModel parentContainer, RamsComponentModel parentContainerPart, RamsDiagramModel diagram,
        RiskObject riskObject, out RamsComponentModel containerPart, out RamsModel container)
    {
        containerPart = new RamsComponentModel
        {
            Id = Guid.NewGuid(),
            Collapsed = false,
            Order = order + 1,
            Title = functionalObject.ObjName.Length > 20 ? functionalObject.ObjName[..20] : functionalObject.ObjName,
            ParallelTrack = 1,
            ParallelTracks = 1,
            Dept = dept,
            Type = RamsComponentType.Container,
            Reliability = new RamsReliabilityParametersModel
            {
                Name = functionalObject.ObjName,
                FuRefId = functionalObject.ObjId,
                RiskObjectId = riskObject.RiskObjId
            },
            Results = new RamsReliabilityResultsModel
            {
                MtbfFunctional = 1,
                MtbfTechnical = 1
            },
            GroupId = parentContainer.NodeId
        };

        container = new RamsModel
        {
            DiagramId = diagram.Id,
            Name = functionalObject.ObjName.Length > 20 ? functionalObject.ObjName[..20] : functionalObject.ObjName,
            Descr = functionalObject.ObjDescription,
            Container = true,
            NodeId = containerPart.Id,
            LinkType = (int) RamsLinkType.LinkedToObject,
            ObjectId = functionalObject.ObjId,
            PartOf = parentContainer.Id,
            Mtbftechn = 1,
            Mtbffunct = 1,
            MtbfFuncCalced = 1,
            MtbfTecCalced = 1
        };

        parentContainerPart.Parts.Add(containerPart);
        container = CreateOrEditRams(container);
    }

    #endregion

    public void SwitchFunctionalObject(TreeNodeGeneric<RiskTreeObject> selectedNode, RamsComponentModel selectedItem,
        RamsModel node)
    {
        switch (selectedItem.Type)
        {
            case RamsComponentType.Block when node.RiskId != null:
            {
                var selectedRisk = _riskAnalysisManager.GetRisk(selectedNode.Source.RiskId ?? 0);
                selectedItem.RiskId = null;

                if (selectedRisk == null)
                    return;

                selectedItem.Title = selectedRisk.Name.Length > 18 ? selectedRisk.Name[..18] : selectedRisk.Name;
                selectedItem.Reliability.Name = selectedRisk.Name;
                selectedItem.Reliability.FuRefId = selectedRisk.Id;
                selectedItem.Reliability.RiskObjectId = selectedRisk.RiskObjectId;

                selectedItem.Results.MtbfFunctional = (double) (selectedRisk.MtbfAfter ?? 0);
                selectedItem.Results.MtbfTechnical = (double) (selectedRisk.MtbfAfter ?? 0);

                selectedItem.Reliability.MTTR = (double) (selectedRisk.DownTimeAfter ?? 0m);

                selectedItem.RiskId = selectedRisk.Id;

                node.Mtbffunct = (double) (selectedRisk.MtbfAfter ?? 0);
                node.Mtbftechn = (double) (selectedRisk.MtbfAfter ?? 0);
                node.Name = selectedRisk.Name;
                node.RiskId = selectedRisk.Id;
                break;
            }
            case RamsComponentType.Container when node.ObjectId != null:
            {
                var system = _dbContext.Object.FirstOrDefault(x => x.ObjId == node.ObjectId);

                if (system == null)
                    return;

                selectedItem.Title = system.ObjName;
                selectedItem.Reliability.Name = system.ObjName;
                selectedItem.Reliability.FuRefId = system.ObjId;

                node.Name = system.ObjName;
                node.ObjectId = system.ObjId;
                break;
            }
        }
    }

    public RamsDiagramModel CreateOrEditRamsDiagram(RamsDiagramModel model)
    {
        var user = _authenticationProvider.GetLoggedInUsername().Result;

        if (model.Id == 0)
        {
            var newDiagram = new RamsDiagram
            {
                RamsDgScenId = model.ScenId ?? 0,
                RamsDgName = model.Name,
                RamsDgDateModified = DateTime.Now,
                RamsDgDateInitiated = DateTime.Now,
                RamsDgCalculateAvailability = model.CalculateAvailability,
                RamsDgCalculationCompatibilityMode = model.CalculationCompatibilityMode,
                RamsDgModifiedBy = user,
                RamsDgInitiatedBy = user,
                RamsDgSerialized = model.Serialized
            };

            var newResult = _ramsDiagramRepository.Add(newDiagram, _userName);
            return _mapper.Map<RamsDiagramModel>(newResult.Item);
        }

        model.ModifiedBy = user;
        model.DateModified = DateTime.Now;

        var dbRamsDiagram = _ramsDiagramRepository.GetById(model.Id);

        dbRamsDiagram = _mapper.Map(model, dbRamsDiagram);
        var result = _ramsDiagramRepository.Update(dbRamsDiagram, _userName);

        return _mapper.Map<RamsDiagramModel>(result.Item);
    }

    public RamsModel CreateOrEditRams(RamsModel model)
    {
        var user = _authenticationProvider.GetLoggedInUsername().Result;
        Rams dbRams = null;

        model.Mtbffunct = model.Mtbffunct.IsNaNOrInfinite(1);
        model.Mtbftechn = model.Mtbftechn.IsNaNOrInfinite(1);
        model.MtbfFuncCalced = model.MtbfFuncCalced.IsNaNOrInfinite(1);
        model.MtbfTecCalced = model.MtbfTecCalced.IsNaNOrInfinite(1);
        model.ReliabilityFunctional = model.ReliabilityFunctional.IsNaNOrInfinite(1);
        model.ReliabilityTechnical = model.ReliabilityTechnical.IsNaNOrInfinite(1);
        model.Mttr = model.Mttr.IsNaNOrInfinite(1);

        if (model.Id == 0)
        {
            dbRams = _dbContext.Rams.FirstOrDefault(x => x.RamsNodeId == model.NodeId);
            if (dbRams == null)
            {
                var rams = _mapper.Map<Rams>(model);

                rams.RamsModifiedBy = user;
                rams.RamsInitiatedBy = user;

                rams.RamsDateInitiated = DateTime.Now;
                rams.RamsDateModified = DateTime.Now;

                var newResult = _ramsRepository.Add(rams, _userName);
                return _mapper.Map<RamsModel>(newResult.Item);
            }
        }

        model.ModifiedBy = user;
        model.DateModified = DateTime.Now;

        dbRams ??= _ramsRepository.GetById(model.Id);

        dbRams = _mapper.Map(model, dbRams);
        var result = _ramsRepository.Update(dbRams, _userName);

        return _mapper.Map<RamsModel>(result.Item);
    }

    private static RamsComponentModel FindComponentModelById(RamsDiagramContentModel content, Guid selectedComponent)
    {
        var containers = content.Parts.Map(x => x.Type == RamsComponentType.Container,
            m => m.Parts);
        return containers?.FirstOrDefault(x => x.Id == selectedComponent);
    }

    private void UpdateRisksByDiagramData(RamsDiagramModel model)
    {
        //TODO: get risks and update data when diagram is saved
    }

    #endregion

    #region Calculation helper methods

    private void RecalculateChildren(RamsComponentModel component, List<RamsModel> rams,
        CalculationParameters functionalCalculation, CalculationParameters technicalCalculation,
        RamsDisplayModel display, bool saveCalculations, string language, double bufferTimeNext)
    {
        //Recursive iteration through nested collections
        foreach (var part in component.Parts.OrderBy(x => x.Order))
        {
            var currentIndex = component.Parts.OrderBy(x => x.Order).ToList().IndexOf(part);
            var bufferTime = 0;

            if (component.Parts.Count > currentIndex + 1)
            {
                var next = component.Parts.OrderBy(x => x.Order).ToList()[currentIndex + 1];

                if (next is {Status: RamsBlockStatus.Buffer})
                {
                    var nextRams = rams.FirstOrDefault(x => x.NodeId == next.Id);

                    if (nextRams != null)
                    {
                        bufferTime = (int) (nextRams.BufferTime ?? 0);
                    }
                }
            }

            if (part.Type == RamsComponentType.Container)
            {
                RecalculateChildren(part, rams, functionalCalculation, technicalCalculation, display, saveCalculations,
                    language, bufferTime);
            }
            else
            {
                DetermineBlockNodeType(part);

                CalculateBlock(part, rams, functionalCalculation, technicalCalculation, display, saveCalculations,
                    bufferTime, language);
            }
        }

        //After doing nesting calculate container item
        if (component.Type == RamsComponentType.Container)
            CalculateContainer(component, rams, functionalCalculation, technicalCalculation, display, saveCalculations,
                bufferTimeNext);
    }

    private void CalculateBlock(RamsComponentModel component, List<RamsModel> rams,
        CalculationParameters functionalCalculation, CalculationParameters technicalCalculation,
        RamsDisplayModel display, bool saveCalculations, double bufferTime, string language)
    {
        var block = rams.Find(x => x.NodeId == component.Id);

        if (block == null) return;

        //Nested diagram
        if (component.DiagramId != null)
        {
            CalculateNestedDiagram(component.DiagramId.Value, language, component, block, functionalCalculation,
                technicalCalculation, display);
        }
        //Process regular blocks
        else
        {
            switch (component.NodeType)
            {
                //Single node
                case RamsNodeType.SingleNode:
                    new SingleNode().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                case RamsNodeType.ParallelWithRepair:
                    new ParallelWithRepair().Configure(component, block, rams, functionalCalculation, display,
                        bufferTime);
                    break;
                case RamsNodeType.Buffer:
                    new Buffer().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                case RamsNodeType.ColdStandBy:
                    new ColdStandby().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                case RamsNodeType.Parallel:
                    new Parallel().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                case RamsNodeType.ParallelKEqualsN:
                    new ParallelKequalsN().Configure(component, block, rams, functionalCalculation, display,
                        bufferTime);
                    break;
                case RamsNodeType.Sequential:
                    new Sequential().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            #region update Ramsmodel

            block.ReliabilityFunctional = component.ReliabilityInPeriodFunctionalFunction(functionalCalculation.Period);
            block.ReliabilityTechnical = component.ReliabilityInPeriodTechnicalFunction(technicalCalculation.Period);

            block.LabdaFunctional =
                component.LabdaInPeriodFunctionalFunction(functionalCalculation.Period) * HoursModel.OneYear;
            block.LabdaTechnical =
                component.LabdaInPeriodTechnicalFunction(technicalCalculation.Period) * HoursModel.OneYear;

            block.MtbfFuncCalced = component.MtbfInPeriodFunctionalFunction(functionalCalculation.Period);
            block.MtbfTecCalced = component.MtbfInPeriodTechnicalFunction(technicalCalculation.Period);

            block.AvailabilityInput = component.AvailabilityFunctionalFunction?.Invoke();
            block.AvailabilityOutput = component.AvailabilityTechnicalFunction?.Invoke();

            block.Mttr = component.MttrFunctionalFunction?.Invoke() ?? 0;

            if (display.ShowPfd || display.ShowSil || display.ShowSilAc)
            {
                block.Pfd = component.PfdFunction?.Invoke();
                block.Hft = component.Hft?.Invoke();
                block.Sil = component.SilAvg?.Invoke().ToString();
            }

            #endregion
        }

        #region Update RamsComponentmodel

        component.Results.LabdaFunctional = block.LabdaFunctional ?? 1;
        component.Results.LabdaTechnical = block.LabdaTechnical ?? 1;

        if (component.Status == RamsBlockStatus.NotInstalled)
        {
            component.Results.MtbfFunctional = component.Results.MtbfTechnical = 10000000;
            component.Results.ReliabilityFunctional = component.Results.ReliabilityTechnical = 1;
            component.Results.AvailFunctional = component.Results.AvailTechnical = 1;
        }
        else
        {
            block.MtbfFuncCalced = block.MtbfFuncCalced.IsNaNOrInfinite(0);
            block.MtbfTecCalced = block.MtbfTecCalced.IsNaNOrInfinite(0);

            component.Reliability.MtbfFunctional = block.MtbfFuncCalced is > 0 ? block.MtbfFuncCalced.Value / 8760D : 1;
            component.Reliability.MtbfTechnical = block.MtbfTecCalced is > 0 ? block.MtbfTecCalced.Value / 8760D : 1;

            //Update risk when RAMS is changed
            if (block.RiskId.HasValue && block.LinkMethod != (int) RamsLinkMethod.NotLinked &&
                block.LinkType == (int) RamsLinkType.LinkedToRams)
            {
                var risk = _riskAnalysisManager.GetRisk(block.RiskId.Value);

                if (risk != null)
                {
                    switch (block.LinkMethod)
                    {
                        case (int) RamsLinkMethod.FmecaAfter:
                            risk.MtbfAfter = (decimal?) block.Mtbffunct;
                            risk.MainDataGrid.CustomMtbfAfter = risk.MtbfAfter?.ToString(Strings.CustomMtbfFormat,
                                CultureInfo.InvariantCulture);
                            break;
                        case (int) RamsLinkMethod.FmecaBefore:
                            risk.MtbfBefore = (decimal?) block.Mtbffunct;
                            risk.MainDataGrid.CustomMtbfBefore = risk.MtbfBefore?.ToString(Strings.CustomMtbfFormat,
                                CultureInfo.InvariantCulture);
                            break;
                    }

                    _riskAnalysisManager.UpdateRisk(risk);
                }
            }

            component.Results.MtbfFunctional = block.Mtbffunct is > 0 ? block.Mtbffunct.Value : 1;
            component.Results.MtbfTechnical = block.Mtbftechn is > 0 ? block.Mtbftechn.Value : 1;

            component.Results.ReliabilityFunctional = block.ReliabilityFunctional is > 0
                ? block.ReliabilityFunctional.Value
                : 0;
            component.Results.ReliabilityTechnical = block.ReliabilityTechnical is > 0
                ? block.ReliabilityTechnical.Value
                : 0;

            component.Results.AvailFunctional =
                block.AvailabilityInput is > 0 ? block.AvailabilityInput.Value : 0;
            component.Results.AvailTechnical =
                block.AvailabilityOutput is > 0 ? block.AvailabilityOutput.Value : 0;
        }

        component.Results.Mttr = block.Mttr ?? 0;
        component.Results.AffectedBufferTime = bufferTime;

        component.Results.PFD = block.Pfd ?? 0;
        component.Results.HFT = block.Hft ?? 0;
        component.Results.RamsSil = block.Sil;
        component.Results.RamsSilAC = block.SilAc;

        component.Reliability.MTTR = block.Mttr ?? 0;

        #endregion

        if (saveCalculations)
            CreateOrEditRams(block);
    }

    private void CalculateNestedDiagram(int diagramId, string language, RamsComponentModel component, RamsModel block,
        CalculationParameters functionalCalculation, CalculationParameters technicalCalculation,
        RamsDisplayModel display)
    {
        var diagram = GetRamsDiagram(diagramId);
        RamsDiagramContentModel diagramContent;

        using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(diagram.Serialized)))
        using (var reader = new StreamReader(stream))
        using (var jsonReader = new JsonTextReader(reader))
        {
            var serializer = JsonSerializer.Create(new JsonSerializerSettings
            {
                Culture = new CultureInfo(language)
            });
            diagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
        }

        var rootNode = diagramContent.Parts[0];

        block.Mtbftechn = rootNode.Results.MtbfTechnical;
        block.Mtbffunct = rootNode.Results.MtbfFunctional;
        block.Mttr = rootNode.Results.MttrFunctional;
        block.LabdaFunctional = rootNode.Results.LabdaFunctional;
        block.LabdaTechnical = rootNode.Results.LabdaTechnical;
        block.ReliabilityFunctional = rootNode.Results.ReliabilityFunctional;
        block.ReliabilityTechnical = rootNode.Results.ReliabilityTechnical;
        block.AvailabilityInput = rootNode.Results.AvailFunctional;
        block.AvailabilityOutput = rootNode.Results.AvailTechnical;
        block.MtbfFuncCalced = rootNode.MtbfInPeriodFunctionalFunction(functionalCalculation.Period);
        block.MtbfTecCalced = rootNode.MtbfInPeriodTechnicalFunction(technicalCalculation.Period);

        if (display.ShowPfd || display.ShowSil || display.ShowSilAc)
        {
            block.Pfd = rootNode.PfdFunction?.Invoke();
            block.Hft = rootNode.Hft?.Invoke();
            block.Sil = rootNode.SilAvg?.Invoke().ToString();
        }

        component.Results = rootNode.Results;
    }

    private void CalculateContainer(RamsComponentModel component, List<RamsModel> rams,
        CalculationParameters functionalCalculation, CalculationParameters technicalCalculation,
        RamsDisplayModel display, bool saveCalculations, double bufferTime)
    {
        var container = rams.FirstOrDefault(x => x.NodeId == component.Id);
        if (container == null || component.Results == null) return;

        //Recalculate parallel track
        component.ParallelTracks = component.Parts
            .DistinctBy(x => x.ParallelTrack)
            .Count();

        if (component.XooN > component.ParallelTracks)
            component.XooN = component.ParallelTracks;

        switch (component.NodeType)
        {
            case RamsNodeType.Buffer:
            {
                var buffer = new Buffer();
                buffer.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //Cold standby
            case RamsNodeType.ColdStandBy:
            {
                var coldStandby = new ColdStandby();
                coldStandby.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //ParallelWithRepair
            case RamsNodeType.ParallelWithRepair:
            {
                var parallel = new ParallelWithRepair();
                parallel.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //Parallel
            case RamsNodeType.Parallel:
            {
                var parallel = new Parallel();
                parallel.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //ParallelKEqualsN
            case RamsNodeType.ParallelKEqualsN:
            {
                var parallel = new ParallelKequalsN();
                parallel.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //Sequential
            case RamsNodeType.Sequential:
            {
                var sequential = new Sequential();
                sequential.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
        }

        #region Update container

        container.XooN = component.XooN;

        container.ReliabilityFunctional = component.ReliabilityInPeriodFunctionalFunction(technicalCalculation.Period);
        container.ReliabilityTechnical = component.ReliabilityInPeriodTechnicalFunction(technicalCalculation.Period);

        container.LabdaFunctional = component.LabdaInPeriodFunctionalFunction(functionalCalculation.Period) *
                                    HoursModel.OneYear;
        container.LabdaTechnical =
            component.LabdaInPeriodTechnicalFunction(functionalCalculation.Period) * HoursModel.OneYear;

        container.MtbfFuncCalced = component.MtbfInPeriodFunctionalFunction(functionalCalculation.Period);
        container.MtbfTecCalced = component.MtbfInPeriodTechnicalFunction(technicalCalculation.Period);

        container.Mttr = component.MttrTechnicalFunction?.Invoke() ?? component.MttrFunctionalFunction?.Invoke();

        container.AvailabilityInput = component.AvailabilityFunctionalFunction?.Invoke();
        container.AvailabilityOutput = component.AvailabilityTechnicalFunction?.Invoke();

        container.Pfd = component.PfdFunction?.Invoke();
        container.Hft = component.Hft?.Invoke();
        container.Sil = component.SilAvg?.Invoke().ToString();
        container.Sff = (decimal?) component.SffFunction?.Invoke();
        container.SilAc = component.SilAcFunction?.Invoke().ToString();

        #endregion

        #region Update component

        component.Results.AvailFunctional = container.AvailabilityInput is > 0 ? container.AvailabilityInput.Value : 0;
        component.Results.AvailTechnical = container.AvailabilityOutput is > 0 ? container.AvailabilityOutput.Value : 0;

        component.Results.LabdaFunctional = container.LabdaFunctional ?? 0;
        component.Results.LabdaTechnical = container.LabdaTechnical ?? 0;

        //If infinite just use the infinite value, do not do calculations
        if (container.MtbfFuncCalced == 10000000 || component.Parts.All(x =>
                x.Status == RamsBlockStatus.NotInstalled || x.Results.MtbfFunctional == 10000000))
        {
            component.Results.MtbfTechnical = component.Results.MtbfFunctional = 10000000;
        }
        else
        {
            component.Results.MtbfTechnical = container.MtbfTecCalced / 8760D ?? 1;
            component.Results.MtbfFunctional = container.MtbfFuncCalced / 8760D ?? 1;
        }

        component.Results.ReliabilityFunctional =
            container.ReliabilityFunctional is > 0 ? container.ReliabilityFunctional.Value : 1;
        component.Results.ReliabilityTechnical =
            container.ReliabilityTechnical is > 0 ? container.ReliabilityTechnical.Value : 1;

        component.Results.PFD = container.Pfd ?? 0;
        component.Results.MttrFunctional = component.MttrFunctionalFunction?.Invoke() ?? 0;
        component.Results.MttrTechnical = component.MttrTechnicalFunction?.Invoke() ?? 0;

        component.Reliability.MTTR = component.MttrTechnicalFunction?.Invoke() ?? 0;
        component.Results.AffectedBufferTime = bufferTime;

        #endregion

        if (saveCalculations)
            CreateOrEditRams(container);
    }

    private static async Task<List<RamsComponentModel>> ReOrderComponents(List<RamsComponentModel> components,
        int dept = 0)
    {
        foreach (var component in components.OrderBy(x => x.Order).ThenByDescending(x => x.Changed))
        {
            //Reset dept
            component.Dept = dept + 1;
            //If duplicate orders exist move up a column
            var duplicateOrderItems = components
                .Where(x => x.Order == component.Order && x.ParallelTrack == component.ParallelTrack).ToList();

            //But only the oldest
            if (duplicateOrderItems.Any() && duplicateOrderItems.Count > 1 &&
                component == duplicateOrderItems.MinBy(x => x.Changed))
            {
                component.Order++;
                component.Changed = DateTime.Now;
            }

            //Check if no empty columns in between
            var i = 1;
            if (component.Order > 0)
                while (components.Where(x => x.ParallelTrack == component.ParallelTrack).OrderBy(x => x.Order)
                           .All(x => x.Order != component.Order - 1 && component.Order - 1 > 0) &&
                       i < components.MaxBy(x => x.Order).Order)
                {
                    i++;
                    component.Order--;
                }

            if (component.Type == RamsComponentType.Container)
            {
                await ReOrderComponents(component.Parts, component.Dept);
            }

            component.Connectors.Clear();

            //Connection to previous block
            if (component.Order > 1)
            {
                //Get previous blocks
                var previousBlocks = components
                    .Where(x => x.Order == component.Order - 1 && x.ParallelTrack == component.ParallelTrack)
                    .OrderBy(x => x.ParallelTrack).ToList();

                if (previousBlocks.Any())
                {
                    foreach (var previousBlock in previousBlocks.Where(previousBlock =>
                                 previousBlock != null && previousBlock.Id != component.Id))
                    {
                        component.Connectors.Add(new RamsComponentConnectorModel
                        {
                            Destination = previousBlock.Id,
                            TypeDestination = previousBlock.Type,
                            LocationDestination = RamsComponentConnectorLocation.right,
                            LocationSource = RamsComponentConnectorLocation.left
                        });
                    }
                }

                //connect from container to last block items
                if (component.Type == RamsComponentType.Container)
                {
                    var lastBlocks = component.Parts.GroupBy(x => x.ParallelTrack).Select(x => x.MaxBy(y => y.Order));

                    foreach (var previousBlock in lastBlocks.Where(x => x.Id != component.Id))
                    {
                        component.Connectors.Add(new RamsComponentConnectorModel
                        {
                            Destination = previousBlock.Id,
                            TypeDestination = previousBlock.Type,
                            LocationDestination = RamsComponentConnectorLocation.right,
                            LocationSource = RamsComponentConnectorLocation.right
                        });
                    }
                }
            }
            //connection to parent
            else if (component.Type == RamsComponentType.Block && component.GroupId != null &&
                     component.Id != component.GroupId)
            {
                component.Connectors.Add(new RamsComponentConnectorModel
                {
                    Destination = component.GroupId.Value,
                    TypeDestination = RamsComponentType.Container,
                    LocationDestination = component.Order == 1
                        ? RamsComponentConnectorLocation.left
                        : RamsComponentConnectorLocation.right,
                    LocationSource = component.Order == 1
                        ? RamsComponentConnectorLocation.left
                        : RamsComponentConnectorLocation.right
                });
            }
            else if (component.Type == RamsComponentType.Container)
            {
                //Connection to previous block inside
                if (component.Parts.Any())
                    component.Connectors.AddRange(component.Parts.GroupBy(x => x.ParallelTrack)
                        .Select(x => x.MaxBy(y => y.Order))
                        .Select(x =>
                            new RamsComponentConnectorModel
                            {
                                Destination = x.Id,
                                TypeDestination = RamsComponentType.Block,
                                LocationDestination = RamsComponentConnectorLocation.right,
                                LocationSource = RamsComponentConnectorLocation.right
                            }).ToList());

                //If nested group connect to group parent
                if (component.GroupId != null)
                {
                    component.Connectors.Add(new RamsComponentConnectorModel
                    {
                        Destination = component.GroupId.Value,
                        LocationDestination = RamsComponentConnectorLocation.left,
                        LocationSource = RamsComponentConnectorLocation.left,
                        TypeDestination = RamsComponentType.Container
                    });
                }
            }
        }

        return components;
    }

    public RamsDiagramModel RecalculateDiagram(RamsDiagramModel model, CalculationParameters functionalCalculation,
        CalculationParameters technicalCalculation,
        RamsDisplayModel display, bool saveCalculations, string language, bool loadFromDatabase = true)
    {
        //Get diagram content
        var content = new RamsDiagramContentModel();

        try
        {
            content = model.Serialized == null
                ? new RamsDiagramContentModel()
                : JsonConvert.DeserializeObject<RamsDiagramContentModel>(model.Serialized);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Deserialization of diagram failed");
        }

        //Get all rams
        var rams = loadFromDatabase ? GetRams(model.Id).ToList() : model.Rams;

        for (var index = 0; index < content.Parts.Count; index++)
        {
            var part = content.Parts[index];

            var currentIndex = content.Parts.IndexOf(part);
            var bufferTime = 0;

            if (content.Parts.Count > currentIndex + 1)
            {
                var next = content.Parts[currentIndex + 1];

                if (next is {Status: RamsBlockStatus.Buffer})
                {
                    var nextRams = rams.FirstOrDefault(x => x.NodeId == next.Id);

                    if (nextRams != null)
                    {
                        bufferTime = (int) (nextRams.BufferTime ?? 0);
                    }
                }
            }
            
            if (part.Type == RamsComponentType.Container)
            {
                DetermineContainerNodeType(part);

                //If container first process the children
                RecalculateChildren(part, rams, functionalCalculation, technicalCalculation, display, saveCalculations,
                    language, bufferTime);
            }
            else
            {
                if (!DetermineBlockNodeType(part))
                {
                    content.Parts.Remove(part);
                }

                CalculateBlock(part, rams, functionalCalculation, technicalCalculation, display, saveCalculations,
                    bufferTime, language);
            }
        }

        model.Serialized = JsonConvert.SerializeObject(content);

        if (saveCalculations)
            CreateOrEditRamsDiagram(model);

        //Update nested diagrams
        UpdateNestedDiagrams(model.Id, language);

        return model;
    }

    private void UpdateNestedDiagrams(int diagramId, string language)
    {
        //Get diagrams that are being used within diagrams
        var nestedDiagrams = _dbContext.Rams.Where(x => x.RamsDiagramRefId == diagramId);

        if (!nestedDiagrams.Any()) return;

        var diagram = GetRamsDiagram(diagramId);
        var diagramContent = JsonConvert.DeserializeObject<RamsDiagramContentModel>(diagram.Serialized,
            new JsonSerializerSettings
            {
                Culture = new CultureInfo(language)
            });

        if (diagramContent?.Parts != null && diagramContent.Parts.Any())
        {
            var rootNode = diagramContent.Parts[0];

            foreach (var nestedDiagram in nestedDiagrams)
            {
                nestedDiagram.RamsMtbftechn = rootNode.Results.MtbfTechnical;
                nestedDiagram.RamsMtbffunct = rootNode.Results.MtbfFunctional;
                nestedDiagram.RamsLabdaFunctional = rootNode.Results.LabdaFunctional;
                nestedDiagram.RamsLabdaTechnical = rootNode.Results.LabdaTechnical;

                CreateOrEditRams(_mapper.Map<RamsModel>(nestedDiagram));
            }
        }
    }

    private void DetermineContainerNodeType(RamsComponentModel part)
    {
        var container = GetRamsItem(part.Id);

        if (container == null) return;

        part.XooN = container.XooN;

        //TODO: find another way of setting N on the object. Maybe aftermap?
        container.N = () => part.N;

        //Buffer
        if (RamsNodeHelper.IsBuffered(part))
        {
            part.NodeType = RamsNodeType.Buffer;
        }
        //Cold standby
        else if (RamsNodeHelper.IsColdStandby(part))
        {
            part.NodeType = RamsNodeType.ColdStandBy;
        }
        //ParallelWithRepair
        else if (RamsNodeHelper.IsParallel(part.ParallelTracks ?? 1) && !RamsNodeHelper.IsColdStandby(part) &&
                 RamsNodeHelper.IsKooNWithRepair(part, container) && container.K() != container.N())
        {
            part.NodeType = RamsNodeType.ParallelWithRepair;
        }
        //ParallelKEqualsN
        else if (RamsNodeHelper.IsParallel(part.ParallelTracks ?? 1) &&
                 !RamsNodeHelper.IsColdStandby(part) && container.K() == container.N())
        {
            part.NodeType = RamsNodeType.ParallelKEqualsN;
        }
        //Parallel
        else if (RamsNodeHelper.IsParallel(part.ParallelTracks ?? 1) &&
                 !RamsNodeHelper.IsColdStandby(part) && !RamsNodeHelper.IsKooNWithRepair(part, container) &&
                 container.K() != container.N())
        {
            part.NodeType = RamsNodeType.Parallel;
        }
        //Sequential
        else if (!RamsNodeHelper.IsBuffered(part) &&
                 RamsNodeHelper.IsSequential(part.ParallelTracks ?? 1))
        {
            part.NodeType = RamsNodeType.Sequential;
        }

        for (var index = 0; index < part.Parts.Count; index++)
        {
            var childPart = part.Parts[index];
            if (childPart.Type == RamsComponentType.Container)
            {
                DetermineContainerNodeType(childPart);
            }
            else
            {
                if (!DetermineBlockNodeType(childPart))
                {
                    part.Parts.Remove(childPart);
                }
            }
        }
    }

    private bool DetermineBlockNodeType(RamsComponentModel part)
    {
        //var block = GetRamsItem(part.Id);

        //TODO: turned off because of allowing to drag whole diagram in other one. See if this is still needed?
        // if (block is not {DiagramRefId: null})
        //     return false;

        //Blocks are always a single node.
        part.NodeType = RamsNodeType.SingleNode;

        return true;
    }

    #endregion

    #region Tree methods

    public TreeNodeGeneric<RamsTreeObject> GetRamsDiagramTreeByScenario()
    {
        var diagrams = GetRamsDiagrams().GroupBy(x => x.Scenario.Name);

        var rootNode = GetRamsTreeNode("Root node", RamsTreeNodeType.Root);

        foreach (var scenarioGroup in diagrams.OrderBy(x => x.Key))
        {
            rootNode = AddRamsDiagramToTree(rootNode, scenarioGroup);
        }

        return rootNode;
    }

    private static TreeNodeGeneric<RamsTreeObject> AddRamsDiagramToTree(TreeNodeGeneric<RamsTreeObject> tree,
        IGrouping<string, RamsDiagramModel> scenarioGroup)
    {
        var node = tree;
        var scenario = scenarioGroup.FirstOrDefault();

        var scenarioNode = new TreeNodeGeneric<RamsTreeObject>(
                new RamsTreeObject
                    {Name = scenario?.Name, NodeType = RamsTreeNodeType.Scenario, ScenarioId = scenario?.Id ?? 0},
                node)
            {Name = scenarioGroup.Key, Icon = RamsTreeNodeType.Scenario.ToIcon()};

        foreach (var diagrams in scenarioGroup.OrderBy(x => x.Name))
        {
            scenarioNode.Nodes.Add(new TreeNodeGeneric<RamsTreeObject>(
                new RamsTreeObject
                {
                    Name = diagrams.Name,
                    NodeType = RamsTreeNodeType.Diagram,
                    Id = diagrams.Id,
                    ScenarioId = diagrams.ScenId ?? 0
                }, scenarioNode)
            {
                Name = diagrams.Name,
                Icon = RamsTreeNodeType.Diagram.ToIcon(),
                Id = diagrams.Id
            });
        }

        node.Nodes.Add(scenarioNode);
        return node;
    }

    private static TreeNodeGeneric<RamsTreeObject> GetRamsTreeNode(string name, RamsTreeNodeType type,
        TreeNodeGeneric<RamsTreeObject> parent = null)
    {
        return new TreeNodeGeneric<RamsTreeObject>(
            new RamsTreeObject
            {
                Name = name,
                NodeType = type
            },
            parent)
        {
            Icon = type.ToIcon()
        };
    }

    #endregion
}