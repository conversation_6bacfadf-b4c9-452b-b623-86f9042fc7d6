using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.Criticality;
using AMprover.Data.Entities.AM;
using AMprover.Data.Extensions;
using AMprover.Data.Infrastructure;
using AMprover.Data.Models;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;

namespace AMprover.BusinessLogic;

public interface ICriticalityRankingManager
{
    List<CriticalityRankingModelFlat> GetAllCriticalityRankings();

    CriticalityRankingModelFlat GetCriticality(string assetCode);

    List<CriticalityRankingModelFlat> ImportFromAssets();

    bool ImportFileUpload(List<CriticalityRankingModelFlat> items, out DbOperationResult<List<CriticalityRankingModelFlat>> result);

    CriticalityRankingModelFlat SaveCriticalityRanking(CriticalityRankingModelFlat model);

    void UpdateCriticalities(List<CriticalityRankingModelFlat> crits);

    void DeleteAllCriticalities();
}

public class CriticalityRankingManager : ICriticalityRankingManager
{
    private readonly AssetManagementDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly string _userName;

    public CriticalityRankingManager(AuthenticationStateProvider authenticationStateProvider
        , AssetManagementDbContext dbContext
        , IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;

        _userName = authenticationStateProvider.GetLoggedInUsername().Result;
    }

    private List<CriticalityRanking> GetCritsWithSi()
    {
        // The Relationship between criticalities and Sis is not enforced in the database.
        // This allows reimporting the the Sis without losing the from the criticalties to the Sis
        // However, this does mean that we have to manually link the objects together and we cannot use EFCore's Include
        var dbCrits = _dbContext.CriticalityRanking.ToList();
        var dbSi = _dbContext.Si.OrderBy(x => x.SiName).ToList();

        foreach (var dbCrit in dbCrits)
            dbCrit.CritSi = dbSi.FirstOrDefaultBinarySearch(x => x.SiName, dbCrit.CritSiName);

        return dbCrits;
    }

    private CriticalityRanking GetCritWithSi(string siName)
    {
        // The Relationship between criticalities and Sis is not enforced in the database.
        // This allows reimporting the the Sis without losing the from the criticalties to the Sis
        // However, this does mean that we have to manually link the objects together and we cannot use EFCore's Include
        var dbCrit = _dbContext.CriticalityRanking.FirstOrDefault(x => x.CritSiName == siName);
        var dbSi = _dbContext.Si.FirstOrDefault(x => x.SiName == siName);

        if (dbCrit != null)
            dbCrit.CritSi = dbSi;

        return dbCrit;
    }

    public List<CriticalityRankingModelFlat> GetAllCriticalityRankings()
    {
        var dbCrits = GetCritsWithSi();
        var mapped = dbCrits.Select(x => _mapper.Map<CriticalityRankingModel>(x)).ToList();
        return mapped.Select(x => _mapper.Map<CriticalityRankingModelFlat>(x)).ToList();
    }


    public CriticalityRankingModelFlat GetCriticality(string assetCode)
    {
        var asset = _dbContext.Si.FirstOrDefault(x => x.SiName == assetCode);
        var crit = _dbContext.CriticalityRanking.FirstOrDefault(x => x.CritSiName == assetCode);

        if (crit == null || asset == null)
            return null;

        crit.CritSi = asset;
        var mapped =  _mapper.Map<CriticalityRankingModel>(crit);
        return _mapper.Map<CriticalityRankingModelFlat>(mapped);
    }

    /// <summary>
    /// Returns all newly created CriticalityRankingModels
    /// </summary>
    /// <returns></returns>
    public List<CriticalityRankingModelFlat> ImportFromAssets()
    {
        var assets = _dbContext.Si.ToList();
        var crits = _dbContext.CriticalityRanking.ToList();
        var newAssets = assets.Where(a => crits.All(x => x.CritSiName != a.SiName)).ToList();
        var date = DateTime.Now;

        var newCrits = newAssets.Select(x =>
            new CriticalityRanking
            {
                CritSiName = x.SiName,
                CritDateInitiated = date,
                CritDateModified = date,
                CritInitiatedBy = _userName,
                CritModifiedBy = _userName
            }).ToList();

        _dbContext.CriticalityRanking.AddRange(newCrits);
        _dbContext.SaveChangesAndClear(_userName);

        var newModels = newCrits.Select(x => _mapper.Map<CriticalityRankingModel>(x)).ToList();
        return newModels.Select(x => _mapper.Map<CriticalityRankingModelFlat>(x)).ToList();
    }

    public bool ImportFileUpload(List<CriticalityRankingModelFlat> items, out DbOperationResult<List<CriticalityRankingModelFlat>> result)
    {
        try
        {
            var dbCrits = GetCritsWithSi();
            int notFoundCount = 0;

            foreach (var crit in items)
            {
                var dbCrit = dbCrits.FirstOrDefault(x => x.CritSi.SiName == crit.AssetCode);

                if (dbCrit != null)
                {
                    _mapper.Map(crit, dbCrit);

                    // no empty strings to prevent UI issue with pie chart
                    if (string.IsNullOrWhiteSpace(dbCrit.CritCategory))
                        dbCrit.CritCategory = null;

                    _dbContext.Update(dbCrit);
                }
                else
                {
                    notFoundCount++;
                }
            }

            _dbContext.SaveChangesAndClear(_userName);
            var models = _dbContext.CriticalityRanking.Select(x => _mapper.Map<CriticalityRankingModel>(x)).ToList();
            result = new DbOperationResult<List<CriticalityRankingModelFlat>>(models.Select(x => _mapper.Map<CriticalityRankingModelFlat>(x)).ToList())
            {
                StatusMessage = notFoundCount > 0
                    ? $"Successfully imported {items.Count - notFoundCount} items. Failed to match {notFoundCount} items."
                    : $"Successfully imported {items.Count} items."
            };
            return true;
        }
        catch (Exception ex)
        {
            result = DbOperationResult<List<CriticalityRankingModelFlat>>.Failed(null, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Update a single Criticality Ranking Row
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public CriticalityRankingModelFlat SaveCriticalityRanking(CriticalityRankingModelFlat model)
    {
        var dbModel = _dbContext.CriticalityRanking.FirstOrDefault(x => x.CritId == model.Id);

        if (dbModel == null) 
            return model;

        dbModel = _mapper.Map(model, dbModel);

        if (dbModel == null) 
            return model;

        dbModel.CritModifiedBy = _userName;
        dbModel.CritDateModified = DateTime.Now;

        _dbContext.Update(dbModel);
        _dbContext.SaveChangesAndClear(_userName);

        var dbCrit = GetCritWithSi(model.AssetCode);
        return _mapper.Map<CriticalityRankingModelFlat>(_mapper.Map<CriticalityRankingModel>(dbCrit));
    }

    public void UpdateCriticalities(List<CriticalityRankingModelFlat> crits)
    {
        var dbModels = _dbContext.CriticalityRanking.ToList();
        var dbModelsToUpdate = new List<CriticalityRanking>();

        foreach(var model in dbModels)
        {
            var crit = crits.FirstOrDefault(x => x.Id == model.CritId);

            if(crit != null)
            {
                var modelToUpdate = _mapper.Map(model, crit);
                modelToUpdate.ModifiedBy = _userName;
                modelToUpdate.DateModified = DateTime.Now;
            }
        }

        _dbContext.UpdateRange(dbModelsToUpdate);
        _dbContext.SaveChangesAndClear(_userName);
    }

    public void DeleteAllCriticalities()
    {
        _dbContext.CriticalityRanking.RemoveRange(_dbContext.CriticalityRanking);
        _dbContext.SaveChangesAndClear(_userName);
    }
}