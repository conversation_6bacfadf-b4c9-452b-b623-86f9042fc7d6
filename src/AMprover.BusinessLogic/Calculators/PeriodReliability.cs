using System;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Calculators
{
    public static class PeriodReliability
    {
        public static Func<PeriodModel, ChanceModel> ReliabilityInPeriod(Func<HoursModel, ChanceModel> R)
        {
            ChanceModel Calc(PeriodModel period)
            {
                if (R == null)
                    return 0;
                
                if (period.From >= period.To)
                {
                    return 1;
                }

                var from = R(period.From);
                var to = R(period.To);
                return from == 0 || to == 0 ? 0 : to / from;
            }

            return Calc;
        }
    }
}