using System;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Calculators
{
    public static class Labda
    {
        public static Func<HoursModel, RateModel> LabdaSystem2(Func<HoursModel, ChanceModel> r)
        {
            const double oneSecond = 1 / 60D;
            return t => PeriodLabda.LabdaInPeriod(r)(new PeriodModel(t, t + oneSecond));
        }

        public static Func<HoursModel, RateModel> LabdaSystemAssumingExponential(Func<HoursModel, ChanceModel> r)
        {
            return t => -Math.Log(r(t)) / t;
        }
    }
}