using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Calculators.Sets;
using AMprover.BusinessLogic.Models.Rams.Calculations;
using MathNet.Numerics;

namespace AMprover.BusinessLogic.Calculators
{
    public static class AvailabilityWithRepair
    {
        public static Func<PeriodModel, ChanceModel> KooNWithRepair(
            IEnumerable<Tuple<Func<HoursModel, ChanceModel>, HoursModel>> components,
            Func<int> k, Func<HoursModel> availableTime)
        {
            return period =>
            {
                var averageH = components.Select(c => new
                    {R = c.Item1, MTTR = c.Item2, h = PeriodLabda.LabdaInPeriod(c.Item1)(period)});
                var weightedAverageMttr = averageH.Sum(c => c.h * c.MTTR) / averageH.Sum(h => h.h);
                var pMTTRg = new PeriodModel(Math.Abs(period.To - weightedAverageMttr), period.To);
                var rComponents = averageH.Select(c => Reliability.Rweibull(() => 1D, () => 1 / c.h));

                var setReliability = rComponents
                    .Select(x => PeriodReliability.ReliabilityInPeriod(x))
                    .Select<Func<PeriodModel, ChanceModel>, Func<ChanceModel>>(x => () => x(pMTTRg));

                var rSysteem = SymmetricStateSwitching.SymmetricState(setReliability, k())();
                var labda = -Math.Log(rSysteem) / weightedAverageMttr;

                var componentsCount = components.Count() - k() + 1;
                var labdaBlackMatter =
                    labda * SpecialFunctions.Factorial(componentsCount < 0 ? 0 : componentsCount);

                var mtbf = 1 / labdaBlackMatter;
                return Availability.Get(p => mtbf, () => weightedAverageMttr, availableTime)(period);
            };
        }
    }
}