using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Calculators.Sets;
using AMprover.BusinessLogic.Models.Rams.Calculations;
using MathNet.Numerics;

namespace AMprover.BusinessLogic.Calculators
{
    public static class ReliabilityWithRepair
    {
        public static Func<HoursModel, ChanceModel> KooNWithRepair(
            IEnumerable<Tuple<Func<HoursModel, ChanceModel>, HoursModel>> components, Func<int> k)
        {
            return t =>
            {
                var averageH = components.Select(c => new
                    {R = c.Item1, MTTR = c.Item2, h = PeriodLabda.LabdaInPeriod(c.Item1)(new PeriodModel(0, t))});
                var weightedAverageMttr = averageH.Sum(c => c.h * c.MTTR) / averageH.Sum(h => h.h);
                var pMTTRg = new PeriodModel(Math.Abs(t - weightedAverageMttr), t);
                var rComponents = averageH.Select(c => Reliability.Rweibull(() => 1D, () => 1 / c.h));

                var setReliability = rComponents
                    .Select(PeriodReliability.ReliabilityInPeriod)
                    .Select<Func<PeriodModel, ChanceModel>, Func<ChanceModel>>(x => () => x(pMTTRg));

                var rSysteem = SymmetricStateSwitching.SymmetricState(setReliability, k())();
                var labda = -Math.Log(rSysteem) / weightedAverageMttr;

                var componentsCount = components.Count() - k() + 1;
                var labdaBlackMatter =
                    labda * SpecialFunctions.Factorial(componentsCount < 0 ? 0 : componentsCount);
                var r = Reliability.Rweibull(() => 1D, () => 1 / labdaBlackMatter);

                return r(t);
            };
        }
    }

    public class ReliabilityResults
    {
        public ChanceModel R { get; private set; }
        public RateModel Labda { get; private set; }
        public HoursModel MTBF { get; private set; }

        public ReliabilityResults(ChanceModel r, RateModel labda, HoursModel mtbf)
        {
            R = r;
            Labda = labda;
            MTBF = mtbf;
        }
    }
}