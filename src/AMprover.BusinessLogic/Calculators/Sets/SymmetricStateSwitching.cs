using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Calculators.Sets;

public static class SymmetricStateSwitching
{
    /// <summary>
    /// Availability calculation
    /// </summary>
    /// <param name="items"></param>
    /// <param name="k"></param>
    /// <returns></returns>
    public static Func<ChanceModel> SymmetricState(this IEnumerable<Func<ChanceModel>> items, int k)
    {
        var list = (items ?? Enumerable.Empty<Func<ChanceModel>>()).ToList();
        return SymmetricState(list, k, list.Count);
    }

    /// <summary>
    /// Reliability calculation
    /// </summary>
    /// <param name="items"></param>
    /// <param name="k"></param>
    /// <returns></returns>
    public static Func<HoursModel, ChanceModel> SymmetricState(this IEnumerable<Func<HoursModel, ChanceModel>> items,
        int k)
    {
        var list = (items ?? Enumerable.Empty<Func<HoursModel, ChanceModel>>()).ToList();
        return SymmetricState(list, k, list.Count);
    }

    private static Func<ChanceModel> SymmetricState(List<Func<ChanceModel>> items, int k, int n)
    {
        if (k == 0) // if no items are required to work, reliability is 100%
        {
            return () => 1D;
        }

        if (n < k) // if more items are required to work than there are systems, reliability = 0%
        {
            return () => 0D;
        }


        return () =>
        {
            var r_minus_n1 = SymmetricState(items, k, n - 1);
            var r_minus_k1 = SymmetricState(items, k - 1, n - 1);

            var t1 = r_minus_n1();
            var item = items[n - 1];
            return t1 + (item?.Invoke() ?? 0) * (r_minus_k1() - t1);
        };
    }

    private static Func<HoursModel, ChanceModel> SymmetricState(List<Func<HoursModel, ChanceModel>> items, int k, int n)
    {
        if (k == 0) // if no items are required to work, reliability is 100%
        {
            return t => 1D;
        }

        if (n < k) // if more items are required to work than there are systems, reliability = 0%
        {
            return t => 0D;
        }


        return t =>
        {
            var r_minus_n1 = SymmetricState(items, k, n - 1);
            var r_minus_k1 = SymmetricState(items, k - 1, n - 1);

            var t1 = r_minus_n1(t);
            var item = items[n - 1];
            
            return t1 + (item?.Invoke(t) ?? 0) * (r_minus_k1(t) - t1);
        };
    }
}