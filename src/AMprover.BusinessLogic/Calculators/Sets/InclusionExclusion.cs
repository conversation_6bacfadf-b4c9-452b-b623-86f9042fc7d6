using System.Collections.Generic;
using System.Linq;

namespace AMprover.BusinessLogic.Calculators.Sets
{
    public static class InclusionExclusion
    {
        public static IEnumerable<InclusionExclusionResult<T>> Combinations<T>(this IEnumerable<T> items, int k)
        {
            if (items == null || !items.Any())
            {
                return Enumerable.Empty<InclusionExclusionResult<T>>();
            }

            var itemsWithIndex = items.Select((item, index) => new {Item = item, Index = index});
            var indices = itemsWithIndex.Select(item => item.Index).ToArray();
            var n = indices.Length;

            var includedItems = Enumerable.Range(k, n)
                .SelectMany(i => Accord.Math.Combinatorics.Combinations(indices, i));

            return includedItems.Select(includedItem =>
            {
                var excludedItem = indices.Except(includedItem);
                var included = includedItem.Join(itemsWithIndex, x => x, y => y.Index, (x, y) => y.Item);
                var excluded = excludedItem.Join(itemsWithIndex, x => x, y => y.Index, (x, y) => y.Item);

                return new InclusionExclusionResult<T>(included, excluded);
            });
        }
    }

    public class InclusionExclusionResult<T>
    {
        public InclusionExclusionResult(IEnumerable<T> included, IEnumerable<T> excluded)
        {
            Included = included;
            Excluded = excluded;
        }

        public IEnumerable<T> Included { get; private set; }
        public IEnumerable<T> Excluded { get; private set; }
    }
}