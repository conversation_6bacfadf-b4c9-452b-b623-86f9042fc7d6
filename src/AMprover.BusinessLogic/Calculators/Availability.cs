using System;
using System.Collections.Generic;
using AMprover.BusinessLogic.Calculators.Sets;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Calculators
{
    public static class Availability
    {
        public static Func<ChanceModel> Get(Func<HoursModel> fMtbf, Func<HoursModel> fMttr)
        {
            ChanceModel Calc()
            {
                var mtbf = fMtbf();
                var mttr = fMttr();

                return
                    IsInfinite(mtbf) || IsZeroOrEquivalent(mttr) ? 1 :
                    IsZeroOrEquivalent(mtbf) || IsInfinite(mttr) ? 0 :
                    (mtbf - mttr) / mtbf;
            }

            return Calc;
        }


        private static bool IsInfinite(HoursModel h) => h.IsInfinite || double.IsInfinity(h);

        private static bool IsZeroOrEquivalent(HoursModel h) => h == 0 || double.IsNaN(h);


        public static Func<PeriodModel, ChanceModel> Get(Func<PeriodModel, HoursModel> mtbf, Func<HoursModel> mttr,
            Func<HoursModel> availableTime)
        {
            ChanceModel Calc(PeriodModel p)
            {
                var mtbfPeriod = mtbf(p);
                var availableTimeFraction = availableTime() / HoursModel.OneYear * mtbfPeriod;

                return Get(() => availableTimeFraction, mttr)();
            }

            return Calc;
        }


        public static Func<ChanceModel> Avail(IEnumerable<Func<ChanceModel>> avail, Func<int> k) =>
            avail.SymmetricState(k());
    }
}