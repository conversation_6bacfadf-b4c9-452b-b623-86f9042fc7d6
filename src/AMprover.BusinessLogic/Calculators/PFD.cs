using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Calculators
{
    public static class Pfd
    {
        /// <summary>
        /// PFD calculation for single node.
        /// </summary>
        /// <returns></returns>
        public static Func<double> GetPfd(Func<HoursModel> ti, Func<double> dcd, Func<HoursModel, RateModel> labdaDu,
            Func<HoursModel, RateModel> labdaDd, int h)
        {
            double Calc()
            {
                return 1 - Math.Exp(-0.5D * ti() * (labdaDd(h) * dcd() + labdaDu(h) * (1 - dcd())));
            }

            return Calc;
        }

        /// <summary>
        /// Sff calculation for single node.
        /// </summary>
        /// <returns></returns>
        public static Func<double> GetSff(Func<HoursModel, RateModel> labdaDu, Func<HoursModel, RateModel> labdaDd,
            Func<HoursModel, RateModel> labdaSu, Func<HoursModel, RateModel> labdaSd, int h)
        {
            double Calc()
            {
                if (labdaDu == null && labdaDd == null && labdaSu == null && labdaSd == null) return 0;

                var ff = labdaSd(h) + labdaSu(h) + labdaDd(h) + labdaDu(h);
                if (ff == 0) return 0;

                return (ff - labdaDu(h)) / ff;
            }

            return Calc;
        }

        /// <summary>
        ///  LabdaDu calculation for single node.
        ///  </summary>
        /// <returns></returns>
        public static Func<double> GetLDu(Func<HoursModel, RateModel> labdaDu, int h)
        {
            double Calc()
            {
                return labdaDu?.Invoke(h) ?? 0;
            }

            return Calc;
        }

        /// <summary>
        /// LabdaDd calculation for single node.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="node"></param>
        /// <returns></returns>
        public static Func<double> GetLDd(Func<HoursModel, RateModel> labdaDd, int h)
        {
            double Calc()
            {
                return labdaDd?.Invoke(h) ?? 0;
            }

            return Calc;
        }

        /// <summary>
        /// LabdaSu calculation for single node.
        /// </summary>
        /// <returns></returns>
        public static Func<double> GetLSu(Func<HoursModel, RateModel> labdaSu, int h)
        {
            double Calc()
            {
                return labdaSu?.Invoke(h) ?? 0;
            }

            return Calc;
        }

        /// <summary>
        /// LabdaSd calculation for single node.
        /// </summary>
        /// <returns></returns>
        public static Func<double> GetLSd(Func<HoursModel, RateModel> labdaSd, int h)
        {
            double Calc()
            {
                return labdaSd?.Invoke(h) ?? 0;
            }

            return Calc;
        }

        /// <summary>
        /// PFD calculation for parallel nodes.
        /// </summary>
        /// <returns></returns>
        public static Func<double> GetPfdParallel(Func<HoursModel> testInterval, Func<int> k, Func<int> n,
            Func<HoursModel, RateModel> labdaDu, Func<HoursModel, RateModel> labdaDd, Func<double> dcd,
            Func<HoursModel> mttr, Func<double> beta)
        {
            double Calc()
            {
                var kValue = k?.Invoke() ?? 0;
                var nValue = n?.Invoke() ?? 0;

                //to do: set period time
                int period = 8760;

                if (kValue > nValue || (kValue == 0 && nValue == 0) || labdaDu == null || labdaDd == null)
                {
                    return 0;
                }

                var ti = testInterval();

                // mean time to repair * dangerous detected failure rate
                var labdaMttr = labdaDd(period) * mttr();
                // test interval in hours.

                var inverseBeta = 1 - beta();

                var combinations = Combinations(nValue, nValue - kValue + 1);
                
                // copied from Leergang
                // modelleert het niet-merkbaar en onafhankelijk falen van n-k+1 componenten
                var termA = combinations * Math.Pow(inverseBeta * labdaDu(period), nValue - kValue + 1) *
                            Math.Pow(ti, nValue - kValue) * (ti / (nValue - kValue + 2) + mttr());
                //modelleert het merkbaar en onafhankelijk falen van n-k+1 componenten
                var termB = combinations * Math.Pow(inverseBeta * labdaMttr, nValue - kValue + 1);
                // modelleert het onafhankelijk falen van n-k+1 componenten, waarbij minstens één daarvan merkbaar en minstens één niet merkbaar is. 
                var count = Math.Max(0, nValue - kValue - 1);
                var termC = Enumerable.Range(1, count).Sum(i =>
                {
                    var k2 = nValue - kValue + 1 - i;
                    //  functie voor de bijdrage van de common cause failures, waarbij x het faalaantal is voor DU situaties. FDU(x)=1 voor x=1, FDU(x)=(1- ß) voor x>1. 
                    var fdu = i == 1 ? 1 : inverseBeta;
                    // functie voor de bijdrage van de common cause failures, waarbij x het faalaantal is voor DD situaties. FDD(x)=1 voor x=1, FDD(x)=(1- ßD) voor x>1. 
                    var fdd = k2 == 1 ? 1 : inverseBeta;

                    var c =
                        Combinations(nValue, i) *
                        Math.Pow(fdu * labdaDu(period), i) *
                        Math.Pow(ti, i - 1) *
                        (ti / (i + 1) + mttr()) *
                        Combinations(nValue - i, k2) *
                        Math.Pow(fdd * labdaMttr, k2);

                    return c;
                });

                //  modelleert het afhankelijk en niet-merkbaar falen van alle n componenten, volgens het zogenaamde ß-model, waarbij - als er sprake is van afhankelijk falen - alle componenten gefaald zijn.  
                var termD = beta() * labdaDu(period) * (ti / 2 + mttr());
                // modelleert het afhankelijk merkbaar falen van alle n componenten
                var termE = beta() * labdaDu(period) * mttr();

                var pfd = termA + termB + termC + termD + termE;
                return pfd;
            }

            return Calc;
        }

        public static Func<double> GetSffLabdaBased(Func<double> labdaDu, Func<double> labdaDd, Func<double> labdaSu,
            Func<double> labdaSd)
        {
            double Calc()
            {
                if (labdaDu == null && labdaDd == null && labdaSu == null && labdaSd == null) return 0;

                var ff = labdaSd() + labdaSu?.Invoke() + labdaDd?.Invoke() + labdaDu?.Invoke();
                if (ff == 0) return 0;
                var sff = (ff - labdaDu?.Invoke()) / ff;
                return (ff - labdaDu?.Invoke() ?? 0) / ff ?? 0;
            }

            return Calc;
        }

        public static Func<int> GetSilAc(Func<double> sff, int hft, AcType type)
        {
            int Calc()
            {
                {
                    return type switch
                    {
                        AcType.A when sff() < 0.6 && hft == 0 => 1,
                        AcType.A when sff() < 0.6 && hft == 1 => 2,
                        AcType.A when sff() < 0.6 && hft > 1 => 3,
                        AcType.A when sff() >= 0.6 && sff() < 0.9 && hft == 0 => 2,
                        AcType.A when sff() >= 0.6 && sff() < 0.9 && hft == 1 => 3,
                        AcType.A when sff() >= 0.6 && sff() < 0.9 && hft > 1 => 4,
                        AcType.A when sff() >= 0.9 && hft == 0 => 3,
                        AcType.A when sff() >= 0.9 && hft == 1 => 4,
                        AcType.A when sff() >= 0.9 && hft > 1 => 4,
                        AcType.B when sff() < 0.6 && hft == 1 => 1,
                        AcType.B when sff() < 0.6 && hft > 1 => 2,
                        AcType.B when sff() >= 0.6 && sff() < 0.9 && hft == 0 => 1,
                        AcType.B when sff() >= 0.6 && sff() < 0.9 && hft == 1 => 2,
                        AcType.B when sff() >= 0.6 && sff() < 0.9 && hft > 1 => 3,
                        AcType.B when sff() >= 0.9 && sff() < 0.99 && hft == 0 => 2,
                        AcType.B when sff() >= 0.9 && sff() < 0.99 && hft == 1 => 3,
                        AcType.B when sff() >= 0.9 && sff() < 0.99 && hft > 1 => 4,
                        AcType.B when sff() >= 0.99 && hft == 0 => 3,
                        AcType.B when sff() >= 0.99 && hft == 1 => 4,
                        AcType.B when sff() >= 0.99 && hft > 1 => 4,
                        _ => 0
                    };
                }
            }

            return Calc;
        }

        public static Func<double> GetPfd(IEnumerable<Func<double>> items) =>
            AggregateItems(items, 
                (p1, p2) => () => 1 - (1 - p1?.Invoke() ?? 0) * (1 - p2?.Invoke() ?? 0), 
                () => 0);

        public static Func<double> GetSff(IEnumerable<Func<double>> items) =>
            AggregateItems(items, 
                (p3, p4) => () => (p3?.Invoke() ?? 0 + p4?.Invoke() ?? 0) / 2, 
                () => 0);

        public static Func<int> GetSilAc(IEnumerable<Func<int>> items) =>
            AggregateItems(items, 
                (x1, x2) => () => Math.Min(x1?.Invoke() ?? 0, x2?.Invoke() ?? 0), 
                () => 0);

        public static Func<double> GetLabdaDu(IEnumerable<Func<double>> items) =>
            AggregateItems(items, 
                (q1, q2) => () => q1?.Invoke() ?? 0 + q2?.Invoke() ?? 0, 
                () => 0);

        public static Func<double> GetLabdaDd(IEnumerable<Func<double>> items) =>
            AggregateItems(items, 
                (q3, q4) => () => q3?.Invoke() ?? 0 + q4?.Invoke() ?? 0, 
                () => 0);

        public static Func<double> GetLabdaSd(IEnumerable<Func<double>> items) =>
            AggregateItems(items, 
                (q5, q6) => () => q5?.Invoke() ?? 0 + q6?.Invoke() ?? 0, 
                () => 0);

        public static Func<double> GetLabdaSu(IEnumerable<Func<double>> items) =>
            AggregateItems(items, 
                (q7, q8) => () => q7?.Invoke() ?? 0 + q8?.Invoke() ?? 0, 
                () => 0);

        public static Func<T> AggregateItems<T>(IEnumerable<Func<T>> items, Func<Func<T>, Func<T>, Func<T>> aggregator, Func<T> defaultFunc)
        {
            if (items == null || !items.Any())
                return defaultFunc; // Return default function when items is null or empty
    
            return items.Aggregate(aggregator);
        }

        /// <summary>
        /// performance optimization. Basically a lookup with a set of predefined values. Saves us from calculating the number of combinations.
        /// every time.
        /// </summary>
        /// <param name="n"></param>
        /// <param name="k"></param>
        /// <returns></returns>
        private static double Combinations(int n, int k)
        {
            if (n > 20)
            {
                return MathNet.Numerics.Combinatorics.Combinations(n, k);
            }

            #region autogenerated code. Do not modify.

            if (n == 1)
            {
                if (k == 1) return 1;
            }

            if (n == 2)
            {
                if (k == 1) return 2;
                if (k == 2) return 1;
            }

            if (n == 3)
            {
                if (k == 1) return 3;
                if (k == 2) return 3;
                if (k == 3) return 1;
            }

            if (n == 4)
            {
                if (k == 1) return 4;
                if (k == 2) return 6;
                if (k == 3) return 4;
                if (k == 4) return 1;
            }

            if (n == 5)
            {
                if (k == 1) return 5;
                if (k == 2) return 10;
                if (k == 3) return 10;
                if (k == 4) return 5;
                if (k == 5) return 1;
            }

            if (n == 6)
            {
                if (k == 1) return 6;
                if (k == 2) return 15;
                if (k == 3) return 20;
                if (k == 4) return 15;
                if (k == 5) return 6;
                if (k == 6) return 1;
            }

            if (n == 7)
            {
                if (k == 1) return 7;
                if (k == 2) return 21;
                if (k == 3) return 35;
                if (k == 4) return 35;
                if (k == 5) return 21;
                if (k == 6) return 7;
                if (k == 7) return 1;
            }

            if (n == 8)
            {
                if (k == 1) return 8;
                if (k == 2) return 28;
                if (k == 3) return 56;
                if (k == 4) return 70;
                if (k == 5) return 56;
                if (k == 6) return 28;
                if (k == 7) return 8;
                if (k == 8) return 1;
            }

            if (n == 9)
            {
                if (k == 1) return 9;
                if (k == 2) return 36;
                if (k == 3) return 84;
                if (k == 4) return 126;
                if (k == 5) return 126;
                if (k == 6) return 84;
                if (k == 7) return 36;
                if (k == 8) return 9;
                if (k == 9) return 1;
            }

            if (n == 10)
            {
                if (k == 1) return 10;
                if (k == 2) return 45;
                if (k == 3) return 120;
                if (k == 4) return 210;
                if (k == 5) return 252;
                if (k == 6) return 210;
                if (k == 7) return 120;
                if (k == 8) return 45;
                if (k == 9) return 10;
                if (k == 10) return 1;
            }

            if (n == 11)
            {
                if (k == 1) return 11;
                if (k == 2) return 55;
                if (k == 3) return 165;
                if (k == 4) return 330;
                if (k == 5) return 462;
                if (k == 6) return 462;
                if (k == 7) return 330;
                if (k == 8) return 165;
                if (k == 9) return 55;
                if (k == 10) return 11;
                if (k == 11) return 1;
            }

            if (n == 12)
            {
                if (k == 1) return 12;
                if (k == 2) return 66;
                if (k == 3) return 220;
                if (k == 4) return 495;
                if (k == 5) return 792;
                if (k == 6) return 924;
                if (k == 7) return 792;
                if (k == 8) return 495;
                if (k == 9) return 220;
                if (k == 10) return 66;
                if (k == 11) return 12;
                if (k == 12) return 1;
            }

            if (n == 13)
            {
                if (k == 1) return 13;
                if (k == 2) return 78;
                if (k == 3) return 286;
                if (k == 4) return 715;
                if (k == 5) return 1287;
                if (k == 6) return 1716;
                if (k == 7) return 1716;
                if (k == 8) return 1287;
                if (k == 9) return 715;
                if (k == 10) return 286;
                if (k == 11) return 78;
                if (k == 12) return 13;
                if (k == 13) return 1;
            }

            if (n == 14)
            {
                if (k == 1) return 14;
                if (k == 2) return 91;
                if (k == 3) return 364;
                if (k == 4) return 1001;
                if (k == 5) return 2002;
                if (k == 6) return 3003;
                if (k == 7) return 3432;
                if (k == 8) return 3003;
                if (k == 9) return 2002;
                if (k == 10) return 1001;
                if (k == 11) return 364;
                if (k == 12) return 91;
                if (k == 13) return 14;
                if (k == 14) return 1;
            }

            if (n == 15)
            {
                if (k == 1) return 15;
                if (k == 2) return 105;
                if (k == 3) return 455;
                if (k == 4) return 1365;
                if (k == 5) return 3003;
                if (k == 6) return 5005;
                if (k == 7) return 6435;
                if (k == 8) return 6435;
                if (k == 9) return 5005;
                if (k == 10) return 3003;
                if (k == 11) return 1365;
                if (k == 12) return 455;
                if (k == 13) return 105;
                if (k == 14) return 15;
                if (k == 15) return 1;
            }

            if (n == 16)
            {
                if (k == 1) return 16;
                if (k == 2) return 120;
                if (k == 3) return 560;
                if (k == 4) return 1820;
                if (k == 5) return 4368;
                if (k == 6) return 8008;
                if (k == 7) return 11440;
                if (k == 8) return 12870;
                if (k == 9) return 11440;
                if (k == 10) return 8008;
                if (k == 11) return 4368;
                if (k == 12) return 1820;
                if (k == 13) return 560;
                if (k == 14) return 120;
                if (k == 15) return 16;
                if (k == 16) return 1;
            }

            if (n == 17)
            {
                if (k == 1) return 17;
                if (k == 2) return 136;
                if (k == 3) return 680;
                if (k == 4) return 2380;
                if (k == 5) return 6188;
                if (k == 6) return 12376;
                if (k == 7) return 19448;
                if (k == 8) return 24310;
                if (k == 9) return 24310;
                if (k == 10) return 19448;
                if (k == 11) return 12376;
                if (k == 12) return 6188;
                if (k == 13) return 2380;
                if (k == 14) return 680;
                if (k == 15) return 136;
                if (k == 16) return 17;
                if (k == 17) return 1;
            }

            if (n == 18)
            {
                if (k == 1) return 18;
                if (k == 2) return 153;
                if (k == 3) return 816;
                if (k == 4) return 3060;
                if (k == 5) return 8568;
                if (k == 6) return 18564;
                if (k == 7) return 31824;
                if (k == 8) return 43758;
                if (k == 9) return 48620;
                if (k == 10) return 43758;
                if (k == 11) return 31824;
                if (k == 12) return 18564;
                if (k == 13) return 8568;
                if (k == 14) return 3060;
                if (k == 15) return 816;
                if (k == 16) return 153;
                if (k == 17) return 18;
                if (k == 18) return 1;
            }

            if (n == 19)
            {
                if (k == 1) return 19;
                if (k == 2) return 171;
                if (k == 3) return 969;
                if (k == 4) return 3876;
                if (k == 5) return 11628;
                if (k == 6) return 27132;
                if (k == 7) return 50388;
                if (k == 8) return 75582;
                if (k == 9) return 92378;
                if (k == 10) return 92378;
                if (k == 11) return 75582;
                if (k == 12) return 50388;
                if (k == 13) return 27132;
                if (k == 14) return 11628;
                if (k == 15) return 3876;
                if (k == 16) return 969;
                if (k == 17) return 171;
                if (k == 18) return 19;
                if (k == 19) return 1;
            }

            if (n == 20)
            {
                if (k == 1) return 20;
                if (k == 2) return 190;
                if (k == 3) return 1140;
                if (k == 4) return 4845;
                if (k == 5) return 15504;
                if (k == 6) return 38760;
                if (k == 7) return 77520;
                if (k == 8) return 125970;
                if (k == 9) return 167960;
                if (k == 10) return 184756;
                if (k == 11) return 167960;
                if (k == 12) return 125970;
                if (k == 13) return 77520;
                if (k == 14) return 38760;
                if (k == 15) return 15504;
                if (k == 16) return 4845;
                if (k == 17) return 1140;
                if (k == 18) return 190;
                if (k == 19) return 20;
                if (k == 20) return 1;
            }

            return MathNet.Numerics.Combinatorics.Combinations(n, k);

            #endregion
        }
    }
}