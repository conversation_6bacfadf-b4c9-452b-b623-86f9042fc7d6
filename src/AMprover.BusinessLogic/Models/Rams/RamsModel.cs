using System;
using AMprover.BusinessLogic.Attributes;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Models.Rams;

public class RamsModel
{
    public int DiagramId { get; set; }

    [GridReadOnly] public int Id { get; set; }

    [GridReadOnly][DownloadIgnore] public Guid NodeId { get; set; }
    public int? DiagramRefId { get; set; } // TODO: remove??
    public int? RiskObjectId { get; set; }
    public int? ObjectId { get; set; }
    public int? RiskId { get; set; }
    public int? SiId { get; set; }
    public string Name { get; set; }
    public string Descr { get; set; }
    public double? AvailabilityInput { get; set; }
    public double? AvailabilityOutput { get; set; }
    public double? Mtbftechn { get; set; } = 1;
    public double? Mtbffunct { get; set; } = 1;
    public double? Mttr { get; set; }
    public string FunctionalDemand { get; set; }
    public decimal? TotalCost { get; set; }
    public byte[] Bitmap { get; set; }
    public string Remark { get; set; }
    public double? Pfd { get; set; }
    public double? Dcd { get; set; }
    public string ClassDc { get; set; }
    public double? Beta { get; set; }
    public DateTime? DateModified { get; set; }
    public double? UtilizationTechn { get; set; }
    public double? UtilizationFunct { get; set; }
    public double? ProductivityTechn { get; set; }
    public double? ProductivityFunct { get; set; }
    public double? EcoTechn { get; set; }
    public double? EcoFunct { get; set; }
    public int PartOf { get; set; }
    public bool Container { get; set; }
    public int? Xposition { get; set; }
    public int? Yposition { get; set; }
    public int LinkedLeft { get; set; }
    public int LinkedRight { get; set; }
    public int? Year { get; set; }
    public int? XooN { get; set; }
    public int? Status { get; set; }
    public int? ParallelBlocks { get; set; }
    public int? ReadSequence { get; set; }
    public bool? Identical { get; set; }
    public bool? Completed { get; set; }
    public decimal? PreventiveCost { get; set; }
    public decimal? TechnCorrCost { get; set; }
    public decimal? CircuitDepCorrCost { get; set; }
    public decimal? FailCorrCost { get; set; }
    public int? LinkType { get; set; }
    public int? LinkMethod { get; set; }
    public bool? Collapsed { get; set; }
    public bool? WantLcc { get; set; }
    public bool? CostOwner { get; set; }
    public bool? CostLinked { get; set; }
    public string InitiatedBy { get; set; }
    public DateTime? DateInitiated { get; set; }
    public string ModifiedBy { get; set; }
    public int? FailureMode { get; set; }
    public decimal? WeibullShape { get; set; }
    public decimal? CharacteristicLife { get; set; }
    public int? DistributionType { get; set; }
    public double? ReliabilityFunctional { get; set; }
    public double? ReliabilityTechnical { get; set; }
    public double? LabdaFunctional { get; set; }
    public double? LabdaTechnical { get; set; }
    public double? MtbfFuncCalced { get; set; }
    public double? MtbfTecCalced { get; set; }
    public bool LccusePfd { get; set; }
    public double? BufferTime { get; set; }
    public int? TestInterval { get; set; }
    public string Sil { get; set; }
    public string SilAc { get; set; }
    public decimal? Sff { get; set; }
    public string Type { get; set; }
    public int? Hft { get; set; }

    #region Calculations

    public Func<double> Shape(RamsModel rams) => () =>
        rams.DistributionType == null ? 1 : (double?) WeibullShape ?? 1D;

    public Func<HoursModel> CharLife(Func<HoursModel> mtbf)
    {
        var isWeibull = DistributionType == (int) Distribution.Weibull
                        && CharacteristicLife.HasValue
                        && CharacteristicLife != 0;

        return isWeibull ? () => (double) CharacteristicLife.Value * 8760 : mtbf;
    }

    public HoursModel Mtbf { get; set; }

    #endregion

    #region Calculations container

    /// <summary>
    /// Either XooN or number of start nodes
    /// </summary>
    public Func<int> K => XooN == null ? N : () => XooN ?? 0;

    /// <summary>
    /// Number of start nodes of container
    /// </summary>
    public Func<int> N { get; set; }

    public Func<int> ContainerHFT => () => Math.Max(N() - K(), 0);

    #endregion

    #region Conversions

    public AcType ACType
    {
        get
        {
            return Type?.ToUpper() switch
            {
                "A" => AcType.A,
                "B" => AcType.B,
                _ => AcType.None
            };
        }
        set
        {
            Type = value switch
            {
                AcType.A => "A",
                AcType.B => "B",
                AcType.None => null,
                _ => null
            };
        }
    }

    #endregion
}