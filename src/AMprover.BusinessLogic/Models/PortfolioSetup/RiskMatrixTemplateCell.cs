using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Exceptions;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.XMLEntities;
using Microsoft.AspNetCore.Components;
using System;
using System.Globalization;
using System.Linq;

namespace AMprover.BusinessLogic.Models.PortfolioSetup;

public class RiskMatrixTemplateCell
{
    public RiskMatrixTemplateCell()
    {
    }

    public bool AllowDecimals = false;

    public RiskMatrixTemplateCell(FmecaCell cell, bool header)
    {
        Id = Guid.NewGuid();
        CellType = header ? CellType.Header : CellType.Row;
        Color = cell.BackColor.KnownColor.ToHtmlColor();
        _customValue = cell.Customvalue;
        Points = cell.Points;
        Description = cell.Description;
        if (cell.Description is {Length: > 35})
            ShortDescription = cell.Description.LimitLength(35) + "...";
        else
            ShortDescription = cell.Description;
        Value = cell.Value;
    }

    public Guid Id { get; set; }
    public int RowId { get; set; }
    public int ColumnId { get; set; }
    public string Description { get; set; }
    public string ShortDescription { get; set; }

    private string _value;

    public string Value
    {
        get => _value;
        set => SetValuePropertyAsInt(ref _value, value);
    }

    private string _customValue;

    public string CustomValue
    {
        get => _customValue;
        set => SetValuePropertyAsInt(ref _customValue, value);
    }

    private string _points;

    public string Points
    {
        get => _points;
        set => SetValuePropertyAsInt(ref _points, value, string.Empty);
    }

    public string Color { get; set; }
    private CellType CellType { get; set; }

    public ElementReference CellReference { get; set; }

    #region Helpers

    /// <summary>
    /// Background method used for templates
    /// </summary>
    /// <returns></returns>
    public string BackGround()
    {
        return $"background-color: {Color}";
    }

    /// <summary>
    /// Subgrid styling
    /// </summary>
    /// <param name="row"></param>
    /// <param name="column"></param>
    /// <param name="template"></param>
    /// <param name="tab"></param>
    /// <param name="isSapaView"></param>
    /// <param name="isRiskAnalysis"></param>
    /// <param name="subGrid"></param>
    /// <returns></returns>
    public string CellStyling(int row, int column, RiskMatrixDataGrid subGrid, RiskMatrixTemplateGrid template,
        string tab, bool isSapaView = true, bool isRiskAnalysis = true)
    {
        var color = string.Empty;
        bool selected;
        var currentColumn = subGrid.TableColumns.Find(x => x.ColumnIndex == column);
        if (!Enum.TryParse<GridType>(tab, out var tabType)) return string.Empty;

        if (template != null)
        {
            color = template.TableContent[row].Cells[column].Color;
        }

        var styling = string.Empty;
        switch (tabType)
        {
            case GridType.RiskWithCurrentActions:

                if (currentColumn != null && row.ToString() == currentColumn.SelectAfter)
                    styling += "color: green;";

                selected = currentColumn != null && row.ToString() == currentColumn.SelectPmo;

                if (selected)
                    styling += "background-color: lightgreen";
                else
                    styling += $"background-color: {color}";

                return styling;
            case GridType.RiskWithoutActions:

                if (currentColumn != null && row.ToString() == currentColumn.SelectAfter)
                    styling += "color:green;";
                selected = currentColumn != null && row.ToString() == currentColumn.SelectBefore;

                if (selected)
                    styling += "background-color: #F4C4AA;";
                else
                    styling += $"background-color: {color}";

                return styling;
            case GridType.RiskWithTakenActions:

                if (isRiskAnalysis || isSapaView)
                {
                    if (currentColumn != null && row.ToString() == currentColumn.SelectBefore)
                        styling += "color: red;";
                }
                else
                {
                    //After color in PMO screen
                    if (currentColumn != null && row.ToString() == currentColumn.SelectPmo)
                        styling += "color:#00008B;";
                }

                selected = currentColumn != null && row.ToString() == currentColumn.SelectAfter;

                if (selected)
                    styling += "background-color: lightgreen;";
                else
                    styling += $"background-color: {color}";

                return styling;
        }

        return string.Empty;
    }

    /// <summary>
    /// Background method used for risk analysis
    /// </summary>
    /// <returns></returns>
    public string CellStyling(int row, int column, RiskModel risk, string tab, string language,
        bool isSapaView = true, bool isRiskAnalysis = true)
    {
        if (risk == null)
            return ShortDescription;
        
        bool selected;
        var transparent = false;

        int.TryParse(risk?.Template.Data.FmecaMainGrid.EffectColumns, out var effectColumnsCount);
        var effectColumns = column < effectColumnsCount;

        if (!Enum.TryParse<GridType>(tab, out var tabType)) return string.Empty;
        var dataCell = risk.MainDataGrid.TableColumns.Find(x => x.ColumnIndex == column);

        var styling = string.Empty;
        if (!effectColumns && row == 0)
            styling += "cursor: pointer;";

        switch (tabType)
        {
            case GridType.RiskWithCurrentActions:
                var selectedMtbfWithActions = false;

                if (dataCell != null && row.ToString() == dataCell.SelectAfter)
                    styling += "color: green;";

                if (effectColumns)
                {
                    selected = dataCell != null && row.ToString() == dataCell.SelectPmo;
                }
                else if (row == 0)
                {
                    selected = column.ToString() == risk.MainDataGrid.MtbfSelectedPmo;

                    selectedMtbfWithActions = column.ToString() == risk.MainDataGrid.MtbfSelectedAfter ||
                                              row.ToString() == risk.MainDataGrid.TableColumns
                                                  .Where(x => x.ValueAfter.ToDecimal(language).HasValue)
                                                  .MaxBy(x => x.ValueAfter.ToDecimal(language))
                                                  ?.SelectAfter;
                }
                else
                {
                    selected = row.ToString() == risk.MainDataGrid.TableColumns
                        .Where(x => x.ValuePmo.ToDecimal(language).HasValue)
                        .MaxBy(x => x.ValuePmo.ToDecimal(language))
                        ?.SelectPmo;
                }

                if (!effectColumns)
                    transparent = column.ToString() != risk.MainDataGrid.MtbfSelectedPmo;

                if (selected)
                    styling += (effectColumns && row != 0) || (!effectColumns && row == 0)
                        ? "background-color: #93C9FF;"
                        : $"background-color: {Color}";
                else if (transparent && row != 0)
                    styling += $"background-color: {Color}; opacity:0.5;";
                else
                    styling += $"background-color: {Color}";

                if (!effectColumns && row == 0)
                {
                    styling += "cursor: pointer;";
                    if (selectedMtbfWithActions)
                        styling += "color: green;";
                }

                return styling;
            case GridType.RiskWithoutActions:
                var selectedMtbfWithAct = false;

                if (dataCell != null && row.ToString() == dataCell.SelectAfter)
                    styling += "color: green;";

                if (effectColumns)
                {
                    selected = dataCell != null && row.ToString() == dataCell.SelectBefore;
                }
                else if (row == 0)
                {
                    selected = column.ToString() == risk.MainDataGrid.MtbfSelectedBefore;

                    selectedMtbfWithAct = column.ToString() == risk.MainDataGrid.MtbfSelectedAfter ||
                                          row.ToString() == risk.MainDataGrid.TableColumns
                                              .Where(x => x.ValueAfter.ToDecimal(language).HasValue)
                                              .MaxBy(x => x.ValueAfter.ToDecimal(language))
                                              ?.SelectAfter;
                }
                else
                {
                    selected = row.ToString() == risk.MainDataGrid.TableColumns
                        .Where(x => x.ValueBefore.ToDecimal(language).HasValue)
                        .MaxBy(x => x.ValueBefore.ToDecimal(language))
                        ?.SelectBefore;
                }

                if (!effectColumns)
                    transparent = column.ToString() != risk.MainDataGrid.MtbfSelectedBefore;

                if (selected)
                    styling += (effectColumns && row != 0) || (!effectColumns && row == 0)
                        ? "background-color:#F4C4AA;"
                        : $"background-color: {Color};";
                else if (transparent && row != 0)
                    styling += $"background-color: {Color}; opacity:0.5;";
                else
                    styling += $"background-color: {Color};";

                if (!effectColumns && row == 0)
                {
                    styling += "cursor: pointer;";
                    if (selectedMtbfWithAct)
                        styling += "color: green;";
                }

                return styling;
            case GridType.RiskWithTakenActions:
                var selectedMtbfOtherMatrix = false;

                if (isRiskAnalysis || isSapaView)
                {
                    if (dataCell != null && row.ToString() == dataCell.SelectBefore)
                        styling += "color:red;";
                }
                else
                {
                    //After color in PMO screen
                    if (dataCell != null && row.ToString() == dataCell.SelectPmo)
                        styling += "color:#0066CC;";
                }

                if (effectColumns)
                {
                    selected = dataCell != null && row.ToString() == dataCell.SelectAfter;
                }
                else if (row == 0)
                {
                    selected = column.ToString() == risk.MainDataGrid.MtbfSelectedAfter;

                    if (isRiskAnalysis || isSapaView)
                    {
                        selectedMtbfOtherMatrix = column.ToString() == risk.MainDataGrid.MtbfSelectedBefore ||
                                                  row.ToString() == risk.MainDataGrid.TableColumns
                                                      .Where(x => x.ValueBefore.ToDecimal(language).HasValue)
                                                      .MaxBy(x => x.ValueBefore.ToDecimal(language))
                                                      ?.SelectBefore;
                    }
                    else
                    {
                        //Matrix in case of PMO:
                        selectedMtbfOtherMatrix = column.ToString() == risk.MainDataGrid.MtbfSelectedPmo ||
                                                  row.ToString() == risk.MainDataGrid.TableColumns
                                                      .Where(x => x.ValuePmo.ToDecimal(language).HasValue)
                                                      .MaxBy(x => x.ValuePmo.ToDecimal(language))
                                                      ?.SelectPmo;
                    }
                }
                else
                {
                    selected = row.ToString() == risk.MainDataGrid.TableColumns
                        .Where(x => x.ValueAfter.ToDecimal(language).HasValue)
                        .MaxBy(x => x.ValueAfter.ToDecimal(language))
                        ?.SelectAfter;
                }

                if (!effectColumns)
                    transparent = column.ToString() != risk.MainDataGrid.MtbfSelectedAfter;

                if (selected)
                    if ((effectColumns && row != 0) || (!effectColumns && row == 0))
                    {
                        styling += "background-color: #9DD58B;";
                    }
                    else
                    {
                        styling += $"background-color: {Color}";
                    }
                else if (transparent && row != 0)
                    styling += $"background-color: {Color}; opacity:0.5;";
                else
                    styling += $"background-color: {Color};";

                //mark other matrix in this one
                if (selectedMtbfOtherMatrix && row == 0)
                    styling += "color:red;";

                if (!effectColumns && row == 0)
                {
                    styling += "cursor: pointer;";
                    if (selectedMtbfOtherMatrix)
                    {
                        if (isRiskAnalysis || isSapaView)
                            styling += "color: red;";
                        else
                            styling += "color: #0066CC;";
                    }
                }

                return styling;
        }

        return string.Empty;
    }

    public string UniqueId(string tab, bool subGrid = false)
    {
        return subGrid ? $"subgrid_{tab}_{Id}" : $"{tab}_{Id}";
    }

    /// <summary>
    /// Value method used for template
    /// </summary>
    /// <param name="template"></param>
    /// <param name="tab"></param>
    /// <param name="column"></param>
    /// <param name="language"></param>
    /// <returns></returns>
    public string GetValue(RiskMatrixTemplateModel template, string tab, int column, string language = "nl-NL")
    {
        if (!Enum.TryParse<GridType>(tab, out var tabType)) return string.Empty;

        var templateColumn = template.MainGrid.TableColumns[column];
        var numberType = templateColumn.IsPercentage ? "P0" : "C0";
        double.TryParse(Value, out var cellValue);
        cellValue = templateColumn.IsPercentage ? cellValue / 100 : cellValue;

        switch (tabType)
        {
            case GridType.Description:
                return ShortDescription;
            case GridType.ImpactValue:
                return cellValue.ToString(numberType, CultureInfo.CreateSpecificCulture(language));
            case GridType.CustomValue:
                return CustomValue == 0.ToString() ? string.Empty : CustomValue;
            case GridType.Points:
                return string.IsNullOrEmpty(Points) ? string.Empty : Points;
            default:
                return string.Empty;
        }
    }

    /// <summary>
    /// Value method used for risk analysis
    /// </summary>
    /// <param name="row"></param>
    /// <param name="column"></param>
    /// <param name="risk"></param>
    /// <param name="tab"></param>
    /// <param name="displayVersion">Value, custom value or points</param>
    /// <param name="language">Locale, default to NL</param>
    /// <returns></returns>
    public string GetValue(int row, int column, RiskModel risk, string tab, int displayVersion = 1,
        string language = "nl-NL")
    {
        if (risk == null)
            return ShortDescription;
        
        if (!Enum.TryParse<GridType>(tab, out var tabType)) return string.Empty;

        switch (tabType)
        {
            case GridType.SpareParts:
            case GridType.PreventiveActions:
            case GridType.RiskWithTakenActions:
                //set value in selected colored block
                var templateColumnAfter = risk.Template.MainGrid.TableColumns[column];
                var numberType = templateColumnAfter.IsPercentage ? "P0" : "C0";

                if (column.ToString() == risk.MainDataGrid.MtbfSelectedAfter && row != 0 &&
                    row.ToString() == risk.MainDataGrid.TableColumns.MaxBy(x => x.ValueAfter.ToDecimal(language))
                        ?.SelectAfter &&
                    (risk.RiskAfter.HasValue && displayVersion == 1 ||
                     risk.CustomAfter.HasValue && displayVersion == 2 ||
                     risk.PointsAfter.HasValue && displayVersion == 3))
                {
                    switch (displayVersion)
                    {
                        case 1:
                            return risk.RiskAfter?.ToString(numberType, CultureInfo.CreateSpecificCulture(language));
                        case 2:
                            return risk.CustomAfter?.ToString("F2", CultureInfo.CreateSpecificCulture(language));
                        case 3:
                            return risk.PointsAfter?.ToString("F2", CultureInfo.CreateSpecificCulture(language));
                    }
                }

                //Set content table header
                if (row == 0 && (column.ToString() == risk.MainDataGrid.MtbfSelectedAfter ||
                                 row.ToString() == risk.MainDataGrid.TableColumns
                                     .Where(x => x.ValueAfter.ToDecimal(language).HasValue)
                                     .MaxBy(x => x.ValueAfter.ToDecimal(language))
                                     ?.SelectAfter))
                {
                    return ShortDescription.RemoveNonNumberDigitsAndCharacters()
                        .Equals(risk.MainDataGrid.CustomMtbfAfter)
                        ? ShortDescription
                        : $"{ShortDescription} ({risk.MainDataGrid.CustomMtbfAfter})";
                }

                if (templateColumnAfter is {IsEffectColumn: true} && row > 0 &&
                    column < risk.MainDataGrid.TableColumns.Count)
                {
                    var dataColumn = risk.MainDataGrid.TableColumns[column];

                    if (templateColumnAfter.IsPercentage) return ShortDescription;
                    if (dataColumn == null || row.ToString() != dataColumn.SelectAfter) return ShortDescription;

                    if (dataColumn.ValueAfter != risk.Template.MainGrid.TableContent[row].Cells[column].Value)
                    {
                        return int.TryParse(dataColumn.ValueAfter, out var valueAfter)
                            ? $"{ShortDescription} ({valueAfter.ToString(numberType, CultureInfo.CreateSpecificCulture(language))})"
                            : $"{ShortDescription} ({dataColumn.ValueAfter})";
                    }
                }

                return ShortDescription;
            case GridType.RiskWithCurrentActions:
                //set value in selected colored block
                var templateColumnPmo = risk.Template.MainGrid.TableColumns[column];
                numberType = templateColumnPmo.IsPercentage ? "P0" : "C0";

                if (column.ToString() == risk.MainDataGrid.MtbfSelectedPmo && row != 0 &&
                    row.ToString() == risk.MainDataGrid.TableColumns.MaxBy(x => x.ValuePmo.ToDecimal(language))
                        ?.SelectPmo &&
                    (risk.RiskPmo.HasValue && displayVersion == 1 ||
                     risk.CustomPmo.HasValue && displayVersion == 2 ||
                     risk.PointsPmo.HasValue && displayVersion == 3))
                {
                    switch (displayVersion)
                    {
                        case 1:
                            return risk.RiskPmo?.ToString(numberType, CultureInfo.CreateSpecificCulture(language));
                        case 2:
                            return risk.CustomPmo?.ToString("F2", CultureInfo.CreateSpecificCulture(language));
                        case 3:
                            return risk.PointsPmo?.ToString("F2", CultureInfo.CreateSpecificCulture(language));
                    }
                }

                //Set content table header
                if (row == 0 && (column.ToString() == risk.MainDataGrid?.MtbfSelectedPmo ||
                                 row.ToString() == risk.MainDataGrid?.TableColumns
                                     .Where(x => x.ValuePmo.ToDecimal(language).HasValue)
                                     .MaxBy(x => x.ValuePmo.ToDecimal(language) ?? 0)
                                     ?.SelectPmo))
                {
                    return ShortDescription.RemoveNonNumberDigitsAndCharacters()
                        .Equals(risk.MainDataGrid?.CustomMtbfPmo)
                        ? ShortDescription
                        : $"{ShortDescription} ({risk.MainDataGrid?.CustomMtbfPmo})";
                }

                if (templateColumnPmo is {IsEffectColumn: true} && row > 0 &&
                    column < risk.MainDataGrid?.TableColumns.Count)
                {
                    var dataColumn = risk.MainDataGrid.TableColumns[column];

                    if (templateColumnPmo.IsPercentage) return ShortDescription;
                    if (dataColumn == null || row.ToString() != dataColumn.SelectPmo) return ShortDescription;

                    if (dataColumn.ValuePmo != risk.Template.MainGrid.TableContent[row].Cells[column].Value)
                    {
                        return int.TryParse(dataColumn.ValuePmo, out var valuePmo)
                            ? $"{ShortDescription} ({valuePmo.ToString(numberType, CultureInfo.CreateSpecificCulture(language))})"
                            : $"{ShortDescription} ({dataColumn.ValuePmo})";
                    }
                }

                return ShortDescription;
            case GridType.RiskWithoutActions:
                //set value in selected colored block
                var templateColumnBefore = risk.Template.MainGrid.TableColumns[column];
                numberType = templateColumnBefore.IsPercentage ? "P0" : "C0";

                if (column.ToString() == risk.MainDataGrid.MtbfSelectedBefore && row != 0 &&
                    row.ToString() == risk.MainDataGrid.TableColumns
                        .Where(x => x.ValueBefore.ToDecimal(language).HasValue)
                        .MaxBy(x => x.ValueBefore.ToDecimal(language))
                        ?.SelectBefore && (risk.RiskBefore.HasValue && displayVersion == 1 ||
                                           risk.CustomBefore.HasValue && displayVersion == 2 ||
                                           risk.PointsBefore.HasValue && displayVersion == 3))
                {
                    switch (displayVersion)
                    {
                        case 1:
                            return risk.RiskBefore?.ToString(numberType, CultureInfo.CreateSpecificCulture(language));
                        case 2:
                            return risk.CustomBefore?.ToString("F2", CultureInfo.CreateSpecificCulture(language));
                        case 3:
                            return risk.PointsBefore?.ToString("F2", CultureInfo.CreateSpecificCulture(language));
                    }
                }

                //Set content table header
                if (row == 0 && (column.ToString() == risk.MainDataGrid.MtbfSelectedBefore ||
                                 row.ToString() == risk.MainDataGrid.TableColumns
                                     .Where(x => x.ValueBefore.ToDecimal(language).HasValue)
                                     .MaxBy(x => x.ValueBefore.ToDecimal(language))
                                     ?.SelectBefore))
                {
                    return ShortDescription.RemoveNonNumberDigitsAndCharacters()
                        .Equals(risk.MainDataGrid.CustomMtbfBefore)
                        ? ShortDescription
                        : $"{ShortDescription} ({risk.MainDataGrid.CustomMtbfBefore})";
                }

                if (templateColumnBefore is {IsEffectColumn: true} && row > 0 &&
                    column < risk.MainDataGrid.TableColumns.Count)
                {
                    var dataColumn = risk.MainDataGrid.TableColumns[column];

                    if (templateColumnBefore.IsPercentage) return ShortDescription;
                    if (dataColumn == null || row.ToString() != dataColumn.SelectBefore) return ShortDescription;

                    if (dataColumn.ValueBefore != risk.Template.MainGrid.TableContent[row].Cells[column].Value)
                    {
                        return int.TryParse(dataColumn.ValueBefore, out var valueBefore)
                            ? $"{ShortDescription} ({valueBefore.ToString(numberType, CultureInfo.CreateSpecificCulture(language))})"
                            : $"{ShortDescription} ({dataColumn.ValueBefore})";
                    }
                }

                return ShortDescription;
            default:
                return string.Empty;
        }
    }

    /// <summary>
    /// Subgrid Value method used for risk analysis
    /// </summary>
    /// <param name="row"></param>
    /// <param name="column"></param>
    /// <param name="dataSubgrid"></param>
    /// <param name="tab"></param>
    /// <param name="displayVersion">Value, custom value or points</param>
    /// <param name="language">Locale, default to NL</param>
    /// <param name="templateSubgrid"></param>
    /// <returns></returns>
    public string GetSubgridValue(int row, int column, RiskMatrixTemplateGrid templateSubgrid,
        RiskMatrixDataGrid dataSubgrid, string tab, int displayVersion = 1,
        string language = "nl-NL")
    {
        if (!Enum.TryParse<GridType>(tab, out var tabType)) return string.Empty;

        switch (tabType)
        {
            case GridType.RiskWithTakenActions:
                //Set content table header
                if (row == 0 && (dataSubgrid != null && column.ToString() == dataSubgrid.MtbfSelectedAfter ||
                                 dataSubgrid != null &&
                                 row.ToString() == dataSubgrid.TableColumns
                                     .Where(x => !string.IsNullOrWhiteSpace(x.ValueAfter))
                                     .Where(x => x.ValueAfter.ToDecimal(language).HasValue)
                                     ?.MaxBy(x => x.ValueAfter.ToDecimal(language))?.SelectAfter))
                {
                    return ShortDescription.RemoveNonNumberDigitsAndCharacters().Equals(dataSubgrid.CustomMtbfAfter)
                        ? ShortDescription
                        : $"{ShortDescription} ({dataSubgrid.CustomMtbfAfter})";
                }

                return templateSubgrid.TableContent[row].Cells[column].Description;
            case GridType.RiskWithCurrentActions:
                //Set content table header
                if (row == 0 && (dataSubgrid != null && column.ToString() == dataSubgrid.MtbfSelectedPmo ||
                                 dataSubgrid != null &&
                                 row.ToString() == dataSubgrid.TableColumns
                                     .Where(x => x.ValuePmo.ToDecimal(language).HasValue)
                                     .MaxBy(x => x.ValuePmo.ToDecimal(language))?.SelectPmo))
                {
                    return ShortDescription.RemoveNonNumberDigitsAndCharacters().Equals(dataSubgrid.CustomMtbfPmo)
                        ? ShortDescription
                        : $"{ShortDescription} ({dataSubgrid.CustomMtbfPmo})";
                }
                
                return templateSubgrid.TableContent[row].Cells[column].Description;
            case GridType.RiskWithoutActions:
                //Set content table header
                if (row == 0 && (dataSubgrid != null && column.ToString() == dataSubgrid.MtbfSelectedBefore ||
                                 dataSubgrid != null &&
                                 row.ToString() == dataSubgrid.TableColumns.Where(x =>
                                     x.ValueBefore.ToDecimal(language).HasValue).MaxBy(x =>
                                     x.ValueBefore.ToDecimal(language))?.SelectBefore))
                {
                    return ShortDescription.RemoveNonNumberDigitsAndCharacters().Equals(dataSubgrid.CustomMtbfBefore)
                        ? ShortDescription
                        : $"{ShortDescription} ({dataSubgrid.CustomMtbfBefore})";
                }

                return templateSubgrid.TableContent[row].Cells[column].Description;
            default:
                return string.Empty;
        }
    }

    public string GetConcatenatedSubGridValue(RiskMatrixTemplateModel template, int row, int column, string tab,
        string language = "nl-NL")
    {
        if (!Enum.TryParse<GridType>(tab, out var tabType)) return string.Empty;

        var subgrid = template.SubGrids.Find(x => x.ColumnId == column);
        if (subgrid == null) return string.Empty;

        var templateColumn = template.MainGrid.TableColumns[column];
        var numberType = templateColumn.IsPercentage ? "P0" : "C0";

        if (!templateColumn.IsPercentage)
            return tabType switch
            {
                GridType.Description => Description,
                GridType.ImpactValue => subgrid.TableContent[row]
                    .Cells.Sum(x => x.Value.ToDecimal(language))
                    ?.ToString(numberType, CultureInfo.CreateSpecificCulture(language)),
                GridType.CustomValue => subgrid.TableContent[row]
                    .Cells.Sum(x => x.CustomValue.ToDecimal(language))
                    .DecimalToString(language),
                GridType.Points => subgrid.TableContent[row]
                    .Cells.Sum(x => x.Points.ToDecimal(language))
                    .DecimalToString(language),
                _ => throw new ArgumentOutOfRangeException(nameof(tab), tab, null)
            };
        
        return tabType switch
        {
            GridType.Description => Description,
            GridType.ImpactValue => subgrid.TableContent[row]
                .Cells.Max(x => x.Value.ToDecimal(language) / 100)
                ?.ToString(numberType, CultureInfo.CreateSpecificCulture(language)),
            GridType.CustomValue => subgrid.TableContent[row]
                .Cells.Sum(x => x.CustomValue.ToDecimal(language))
                .DecimalToString(language),
            GridType.Points => subgrid.TableContent[row]
                .Cells.Sum(x => x.Points.ToDecimal(language))
                .DecimalToString(language),
            _ => throw new ArgumentOutOfRangeException(nameof(tab), tab, null)
        };
    }

    /// <summary>
    /// Build header for template functionality
    /// </summary>
    /// <param name="tab"></param>
    /// <returns></returns>
    public string GetHeader(string tab)
    {
        if (!Enum.TryParse<GridType>(tab, out var tabType)) return string.Empty;

        switch (tabType)
        {
            case GridType.Description:
            case GridType.RiskWithoutActions:
                return ShortDescription;
            case GridType.SpareParts:
            case GridType.PreventiveActions:
            case GridType.RiskWithTakenActions:
                return ShortDescription;
            case GridType.Points:
                return string.IsNullOrEmpty(Points) ? Description : $"{Description} ({Points})";
            case GridType.CustomValue:
                return string.IsNullOrEmpty(CustomValue) ? Description : $"{Description} ({CustomValue})";
            case GridType.ImpactValue:
                return string.IsNullOrEmpty(Value) ? Description : $"{Description} ({CustomValue})";
            default:
                return string.Empty;
        }
    }

    #endregion

    public FmecaCell ToFmecaCell(int rowId, int columnId)
    {
        return new FmecaCell
        {
            BackColor = new BackColor {KnownColor = Color},
            ColIndex = columnId.ToString(),
            RowIndex = rowId.ToString(),
            Customvalue = CustomValue,
            Points = Points,
            Description = Description,
            ShortDescription = ShortDescription,
            Value = Value
        };
    }

    /// <summary>
    /// Enforces that a string (which is actually supposed to be used as decimal) is stored as an integer
    /// </summary>
    private void SetValuePropertyAsInt(ref string property, string value, string defaultValue = null)
    {
        try
        {
            if (value != property)
            {
                if (decimal.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out var result))
                {
                    property = AllowDecimals
                        ? $"{result.ToString(CultureInfo.InvariantCulture)}"
                        : $"{(int) result}";
                }
                else if (defaultValue != null)
                {
                    property = defaultValue;
                }
            }
        }
        catch (Exception ex)
        {
            throw new ParsingException(
                $"Unable to Parse string to int -> property: '{property}', value: '{value}', defaultValue: '{defaultValue}'",
                ex);
        }
    }
}