using AMprover.BusinessLogic.Configuration;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Diagnostics;
using System.IO;

namespace AMprover.BusinessLogic.BackgroundServices
{
    /// <summary>
    /// This File was copied from https://github.com/andrei-m-code/net-core-html-to-image/blob/master/CoreHtmlToImage/HtmlConverter.cs and modified
    /// </summary>
    public class HtmlImageConverter
    {
        private readonly string _rootDirectory;
        private readonly string _tempDirectory;
        private readonly string _toolFilepath;
        private readonly ILogger<HtmlImageConverter> _logger;

        public HtmlImageConverter(
            ILoggerFactory loggerFactory,
            IWebHostEnvironment hostEnvironment,
            IOptions<FmecaImageSettings> settings)
        {
            try
            {
                _logger = loggerFactory.CreateLogger<HtmlImageConverter>();
                var fmecaImageSettings = settings.Value;
                _rootDirectory = hostEnvironment.ContentRootPath;
                _tempDirectory = Path.Combine(_rootDirectory, fmecaImageSettings.TempFolder);

                _logger.LogInformation("{HtmlImageConverterName} rootDirectory: \'{RootDirectory}\'", nameof(HtmlImageConverter), _rootDirectory);
                _logger.LogInformation("{HtmlImageConverterName} tempDirectory: \'{TempDirectory}\'", nameof(HtmlImageConverter), _tempDirectory);

                //Check on what platform we are
                if (System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Windows))
                {
                    _toolFilepath = Path.Combine(_rootDirectory, fmecaImageSettings.Executable);

                    if (!File.Exists(_toolFilepath))
                    {
                        throw new ApplicationException($"Unable to find ImageGenerator program at: {_toolFilepath}");
                    }
                }
                else
                {
                    // Only windows is implemented
                    throw new Exception("OSX and Linux Platform not implemented yet");
                }
            }
            catch(Exception ex)
            {
                _logger?.LogCritical(ex, $"Unable to Initialize {nameof(HtmlImageConverter)}");
            }
        }

        /// <summary>
        /// Converts HTML string to image
        /// </summary>
        /// <param name="html">HTML string</param>
        /// <param name="width">Output document width</param>
        /// <param name="format">Output image format</param>
        /// <param name="quality">Output image quality 1-100</param>
        /// <returns></returns>
        public byte[] FromHtmlString(string html, int width = 1024, ImageFormat format = ImageFormat.Jpg, int quality = 100)
        {
            try
            {
                var filename = Path.Combine(_tempDirectory, $"{Guid.NewGuid()}.html");
                _logger.LogTrace("Writing file to {Filename}", filename);

                File.WriteAllText(filename, html, System.Text.Encoding.UTF8);
                var bytes = FromUrl(filename, width, format, quality);
                File.Delete(filename);
                return bytes;
            }
            catch(Exception ex)
            {
                _logger.LogCritical(ex, "Unable to generate image from Html string");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Converts HTML page to image
        /// </summary>
        /// <param name="url">Valid http(s):// URL</param>
        /// <param name="width">Output document width</param>
        /// <param name="format">Output image format</param>
        /// <param name="quality">Output image quality 1-100</param>
        /// <returns></returns>
        public byte[] FromUrl(string url, int width = 1024, ImageFormat format = ImageFormat.Jpg, int quality = 100)
        {
            var imageFormat = format.ToString().ToLower();
            var filename = Path.Combine(_tempDirectory, $"{Guid.NewGuid()}.{imageFormat}");

            string args;

            if (IsLocalPath(url))
            {
                args = $"--quality {quality} --width {width} -f {imageFormat} \"{url}\" \"{filename}\"";
            }
            else
            {
                args = $"--quality {quality} --width {width} -f {imageFormat} {url} \"{filename}\"";
            }

            var process = Process.Start(new ProcessStartInfo(_toolFilepath, args)
            {
                WindowStyle = ProcessWindowStyle.Hidden,
                CreateNoWindow = true,
                UseShellExecute = false,
                WorkingDirectory = _rootDirectory,
                RedirectStandardError = true
            });

            if (process == null)
                throw new Exception("Something went wrong. Process appears to be empty.");

            process.ErrorDataReceived += Process_ErrorDataReceived;
            process.WaitForExit();

            if (File.Exists(filename))
            {
                var bytes = File.ReadAllBytes(filename);
                File.Delete(filename);
                return bytes;
            }

            throw new Exception("Something went wrong. Please check input parameters");
        }

        private static bool IsLocalPath(string path)
        {
            return !path.StartsWith("http") && new Uri(path).IsFile;
        }

        private static void Process_ErrorDataReceived(object sender, DataReceivedEventArgs e)
        {
            throw new Exception(e.Data);
        }
    }

    public enum ImageFormat
    {
        Jpg,
        Png
    }
}
