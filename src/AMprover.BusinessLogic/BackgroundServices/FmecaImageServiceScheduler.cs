using AMprover.BusinessLogic.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace AMprover.BusinessLogic.BackgroundServices
{
    public class FmecaImageServiceScheduler : BackgroundService
    {
        private readonly ILogger<FmecaImageServiceScheduler> _logger;
        private readonly FmecaImageSettings _settings;
        private readonly IServiceProvider _serviceProvider;

        public FmecaImageServiceScheduler(
            IServiceProvider serviceProvider,
            ILoggerFactory loggerFactory,
            IOptions<FmecaImageSettings> settings)
        {
            _serviceProvider = serviceProvider;
            _logger = loggerFactory.CreateLogger<FmecaImageServiceScheduler>();
            _settings = settings.Value;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation($"{nameof(FmecaImageServiceScheduler)} started. " +
                                   $"GenerateDuringDay: {_settings.GenerateDuringDay}, " +
                                   $"MinutesDelayBetweenRuns: {_settings.MinutesDelayBetweenRuns}");

            await DoWork(stoppingToken);
        }

        private async Task DoWork(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Start with delay to not increase startup time after deployment
                    await Task.Delay(TimeSpan.FromMinutes(_settings.MinutesDelayBetweenRuns), stoppingToken);

                    if (DateTime.Now.Hour < 5 || DateTime.Now.Hour > 20 || _settings.GenerateDuringDay)
                    {
                        using var scope = _serviceProvider.CreateScope();
                        var fmecaImageService = scope.ServiceProvider.GetRequiredService<IFmecaImageService>();

                        _logger.LogTrace($"{nameof(FmecaImageServiceScheduler)} is working at {DateTime.Now:HH:mm:ss}");
                        await ExecuteWithRetry(() => fmecaImageService.GenerateImages());
                    }
                }
                catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                {
                    _logger.LogWarning($"Shutdown Requested for {nameof(FmecaImageServiceScheduler)}");
                }
                catch (Exception ex)
                {
                    _logger.LogCritical(ex, "Error when generating Fmeca Images");
                }
            }
        }
        
        private async Task ExecuteWithRetry(Func<Task> action, int maxRetries = 3, int delaySeconds = 5)
        {
            for (var attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    await action();
                    return; // Exit on success
                }
                catch (Exception ex) when (attempt < maxRetries)
                {
                    _logger.LogWarning(ex,$"Attempt {attempt} failed. Retrying in {delaySeconds} seconds...");
                    await Task.Delay(TimeSpan.FromSeconds(delaySeconds));
                }
            }

            throw new Exception($"Operation failed after {maxRetries} retries.");
        }


        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogWarning($"{nameof(FmecaImageServiceScheduler)} is stopping.");
            await base.StopAsync(stoppingToken);
        }
    }
}