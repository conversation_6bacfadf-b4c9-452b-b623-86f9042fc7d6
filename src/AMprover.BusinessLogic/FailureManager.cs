using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.Failures;
using AMprover.Data.Entities.AM;
using AMprover.Data.Extensions;
using AMprover.Data.Infrastructure;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;

namespace AMprover.BusinessLogic;

public interface IFailureManager
{
    FailureCategories GetAllFailureCategories();
    FailureCategory SaveFailureCategory(FailureCategory failureCategory, int failureCategoryGroup);
    void DeleteFailureCategory(FailureCategory failureCategory);
    
    List<FailureModeModel> GetAllFailureModes();
    FailureModeModel SaveFailureMode(FailureModeModel failureMode);
    void DeleteFailureMode(FailureModeModel failureMode);
    bool FailureModeHasLinkedRisks(int id);
}
    
public class FailureManager: IFailureManager
{
    private readonly AssetManagementDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly string _userName;

    public FailureManager(
        AuthenticationStateProvider authenticationStateProvider
        , AssetManagementDbContext dbContext
        , IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;

        _userName = authenticationStateProvider.GetLoggedInUsername().Result;
    }

    const string FailureCategoryGroupFormatString = "Failure category {0}"; // The values "Failure category 1" and "Failure category 2" is coming from legacy AMprover 4 implementation
    readonly string _failureCategoryGroup1Name = string.Format(FailureCategoryGroupFormatString, 1);
    readonly string _failureCategoryGroup2Name = string.Format(FailureCategoryGroupFormatString, 2);

    public FailureCategories GetAllFailureCategories()
    {
        var dbFailureCategories = _dbContext.LookupFailCat.OrderBy(x => x.FailCatGroup).OrderBy(x => x.FailCatId).ToList();

        FailureCategories result = new();
        if (dbFailureCategories.Any())
        {
            result.Primary = dbFailureCategories.Where(fc => fc.FailCatGroup == _failureCategoryGroup1Name).Select(_mapper.Map<FailureCategory>).ToList();
            result.Secondary = dbFailureCategories.Where(fc => fc.FailCatGroup == _failureCategoryGroup2Name).Select(_mapper.Map<FailureCategory>).ToList();
        }
        return result;
    }

    public FailureCategory SaveFailureCategory(FailureCategory failureCategory, int failureCategoryGroup)
    {
        var dbFailureCategory = _mapper.Map<LookupFailCat>(failureCategory);

        dbFailureCategory.FailCatGroup = string.Format(FailureCategoryGroupFormatString, failureCategoryGroup);

        var dbResult = dbFailureCategory.FailCatId > 0
            ? _dbContext.LookupFailCat.Update(dbFailureCategory)
            : _dbContext.LookupFailCat.Add(dbFailureCategory);

        _dbContext.SaveChangesAndClear(_userName);
        return _mapper.Map<FailureCategory>(dbResult.Entity);
    }

    public void DeleteFailureCategory(FailureCategory failureCategory)
    {
        if (failureCategory?.Id == null) return;

        var dbFailureCategory = _dbContext.LookupFailCat.Find(failureCategory.Id);
        _dbContext.Remove(dbFailureCategory);
        _dbContext.SaveChangesAndClear(_userName);
    }

    public List<FailureModeModel> GetAllFailureModes()
    {
        var dbFailModes = _dbContext.LookupFailMode.OrderBy(x => x.FailId).ToList();

        return dbFailModes.Select(_mapper.Map<FailureModeModel>).ToList();
    }

    public FailureModeModel SaveFailureMode(FailureModeModel failureMode)
    {
        var dbFailMode = _mapper.Map<LookupFailMode>(failureMode);
        dbFailMode.FailModifiedBy = _userName;
        dbFailMode.FailDateModified = DateTime.Now;

        if (failureMode.RateDistributionCalculationMethod != FailureRateDistributionCalculationMethod.Weibull)
        {
            dbFailMode.FailShape = null;
            dbFailMode.FailIntervalUnit = null;
            dbFailMode.FailWeibullLocation = null;
        }

        var dbResult = dbFailMode.FailId > 0
            ? _dbContext.LookupFailMode.Update(dbFailMode)
            : _dbContext.LookupFailMode.Add(dbFailMode);

        _dbContext.SaveChangesAndClear(_userName);
        return _mapper.Map<FailureModeModel>(dbResult.Entity);
    }

    public void DeleteFailureMode(FailureModeModel failureMode)
    {
        var dbFailureMode = _dbContext.LookupFailMode.Find(failureMode.Id);

        _dbContext.LookupFailMode.Remove(dbFailureMode);
        _dbContext.SaveChangesAndClear(_userName);
    }

    public bool FailureModeHasLinkedRisks(int id)
    {
        // Use WHERE with SELECT instead of FIRSTORDEFAULT with INCLUDE to force an efficient sql query as described in top comment:
        // https://stackoverflow.com/questions/50471436/how-can-i-get-the-count-of-a-list-in-an-entity-framework-model-without-including
        return _dbContext.LookupFailMode.Where(x => x.FailId == id).Select(a => a.Risks.Any()).FirstOrDefault();
    }
}
