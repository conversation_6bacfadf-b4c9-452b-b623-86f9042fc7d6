using System;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Distributions
{
    /// <summary>
    /// Contains calculations for Reliability and failure rate specific to the <PERSON>bull distribution type
    /// The formulas are based on the theory found here: http://www.weibull.com/hotwire/issue14/relbasics14.htm
    /// </summary>
    public static class Weibull
    {
        private const double one = 1D;

        /// <summary>
        /// Calculates the value of reliability for the provided parameters
        /// </summary>
        /// <param name="shape">Defines the slope of the current location of the graph (β)</param>
        /// <param name="characteristicLife">The time at which 63.2% of the units will fail (η)</param>
        /// <param name="failureFreeTime">The time (in hours) we can expect the unit to keep working without failure</param>
        /// <param name="t">Time in hours (represents the specific moment in time we want to know the reliability of)</param>
        /// <returns>Reliability</returns>
        public static ChanceModel R(double shape, HoursModel characteristicLife, HoursModel t)
        {
            return Math.Exp(-Math.Pow(t / characteristicLife, shape));
        }

        /// <summary>
        /// Calculates the failure rate for the provided parameters
        /// </summary>
        /// <param name="shape">Defines the slope of the current location of the graph (β)</param>
        /// <param name="characteristicLife">The time at which 63.2% of the units will fail (η)</param>
        /// <param name="t">Time in hours (represents the specific moment in time we want to know the reliability of)</param>
        /// <returns>Failure rate</returns>
        public static RateModel FailureRate(double shape, HoursModel characteristicLife, HoursModel t)
        {
            return shape / characteristicLife * Math.Pow(t / characteristicLife, shape - one);
        }

        public static HoursModel CalculateCharacteristicLifeUsingMTTF(HoursModel mttf, double shape)
        {
            if (shape == one)
            {
                return mttf;
            }

            var gammaFunction = one / shape + one;
            var gamma = Accord.Math.Gamma.Function(gammaFunction);

            return mttf / gamma;
        }
    }
}