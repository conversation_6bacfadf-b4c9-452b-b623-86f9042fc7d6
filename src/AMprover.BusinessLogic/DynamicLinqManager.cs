using AMprover.BusinessLogic.Models.ABS;
using System.Collections.Generic;
using System.Linq.Dynamic.Core;
using System.Linq;
using AMprover.BusinessLogic.Enums;
using System;

namespace AMprover.BusinessLogic;

public interface IDynamicLinqManager
{
    List<AssetModel> GetDynamicFilteredOnList(List<AssetModel> assets, List<FilterQueryRow> filters);
}

public class DynamicLinqManager : IDynamicLinqManager
{
    public List<AssetModel> GetDynamicFilteredOnList(List<AssetModel> assets, List<FilterQueryRow> filters)
    {
        IQueryable<AssetModel> queryable = assets.AsQueryable();
        var filtersAsDynamicLinq = filters.Select(x => FormatFilterForList(x)).ToList();
        var entireFilter = string.Join(' ', filtersAsDynamicLinq).TrimStart('&').TrimStart('|');
        var result = queryable.Where(entireFilter).ToList();

        return result;
    }

    private string FormatFilterForList(FilterQueryRow filter)
    {
        var AndOr = AndOrToDynamicLinq(filter);

        if (filter.Criterium == DatabaseSelectionCriterium.DoesNotEqual)
        {
            if(filter.FieldType == DatabaseFieldType.Int)
                return $"{filter.FieldName} != {filter.Selection}";

            return $"{AndOr} {filter.FieldName} != \"{filter.Selection}\"";
        }

        if(filter.FieldType == DatabaseFieldType.String)
        {
            return $"{AndOr} {filter.FieldName} != null && {filter.FieldName}.{filter.Criterium}{GetSelection(filter)}";
        }
        else
        {
            return $"{AndOr} {filter.FieldName}.{filter.Criterium}{GetSelection(filter)}";
        }
    }

    private string AndOrToDynamicLinq(FilterQueryRow filter)
    {
        return filter?.AndOr?.ToLower() switch
        {
            "and" => "&&",
            "or" => "||",
            _ => throw new NotImplementedException($"{nameof(AndOrToDynamicLinq)} could not handle value: '{filter?.AndOr}' in {filter} Accepted values: ['and', 'or']")
        };
    }

    private string GetSelection(FilterQueryRow filter)
    {
        return filter.FieldType switch
        {
            DatabaseFieldType.Int => $"({filter.Selection})",
            DatabaseFieldType.String => $"(\"{filter.Selection}\", StringComparison.OrdinalIgnoreCase)",
            _ => throw new NotImplementedException($"{nameof(GetSelection)} does not implemented '{nameof(DatabaseFieldType)}': '{filter.FieldType}'"),
        };
    }
}