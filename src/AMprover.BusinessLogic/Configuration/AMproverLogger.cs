using System;
using System.Diagnostics;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Helpers;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

public class AMproverLogger : ILogger
{
    private readonly string _categoryName;
    private readonly IConfiguration _config;
    private readonly AuthenticationStateProvider _authenticationProvider;
    private readonly IPortfolioManager _portfolioManager;

    public AMproverLogger(string categoryName, IConfiguration config,
        AuthenticationStateProvider authenticationStateProvider, IPortfolioManager portfolioManager)
    {
        _categoryName = categoryName;
        _config = config;
        _authenticationProvider = authenticationStateProvider;
        _portfolioManager = portfolioManager;
    }

    public IDisposable BeginScope<TState>(TState state)
    {
        return null;
    }

    public bool IsEnabled(LogLevel logLevel)
    {
        var logLevelConfig = _config.GetSection("Logging:LogLevel");
        if (!Enum.TryParse(logLevelConfig["Default"], out LogLevel defaultLogLevel))
        {
            defaultLogLevel = LogLevel.Warning;
        }

        if (!Enum.TryParse(logLevelConfig[_categoryName], out LogLevel categoryLogLevel))
        {
            categoryLogLevel = defaultLogLevel;
        }

        return logLevel >= categoryLogLevel;
    }

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception,
        Func<TState, Exception, string> formatter)
    {
        if (IsEnabled(logLevel))
        {
            var user = _authenticationProvider.GetLoggedInUsername().Result;
            var currentPortfolio = _portfolioManager.GetCurrentPortfolio().Name;

            var message = formatter(state, exception);
            if (!string.IsNullOrEmpty(message))
            {
                try
                {
                    if (!EventLog.SourceExists(_categoryName))
                    {
                        EventLog.CreateEventSource(_categoryName, "Application");
                    }

                    var fullMessage = message + Environment.NewLine +
                                      "User: " + user + Environment.NewLine +
                                      "Database: " + currentPortfolio;

                    if (exception != null)
                    {
                        fullMessage += Environment.NewLine + "Stack Trace: " + exception.StackTrace;
                    }

                    EventLog.WriteEntry(_categoryName, fullMessage, EventLogEntryType.Information);
                }
                catch
                {
                    // Logging to event log failed
                }
            }
        }
    }
}