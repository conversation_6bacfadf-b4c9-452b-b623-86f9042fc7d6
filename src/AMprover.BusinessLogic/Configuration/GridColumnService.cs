using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.ABS;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.Criticality;
using AMprover.BusinessLogic.Models.Failures;
using AMprover.BusinessLogic.Models.Import;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.Reports;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.Data.Entities.Identity;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Models.Sapa;

namespace AMprover.BusinessLogic.Configuration
{
    public interface IGridColumnService
    {
        void SetDefaults<T>(List<GridColumnModel> gridColumnModels);
    }

    public class GridColumnService : IGridColumnService
    {
        private IStringLocalizer _localizer { get; }
        private Dictionary<ObjectLevel, string> _objectLevels { get; set; }

        public GridColumnService(
            LocalizationHelper localizationHelper,
            IObjectManager objectManager)
        {
            _localizer = localizationHelper.GetLocalizer<GridColumnService>();

            _objectLevels = objectManager.GetObjectLevelNames();
        }

        private string GetObjectLevel(ObjectLevel objectLevel)
        {
            return _objectLevels.TryGetValue(objectLevel, out var level) ? level : $"{objectLevel}";
        }

        public void SetDefaults<T>(List<GridColumnModel> gridColumnModels)
        {
            var relevantColumns = new List<GridColumnModel>();

            // Portfolio Setup Tables
            if (typeof(T) == typeof(ScenarioModel))
                relevantColumns = GetScenarioModelColumns(gridColumnModels);

            if (typeof(T) == typeof(FailureModeModel))
                relevantColumns = GetFailureModeModelColumns(gridColumnModels);

            if (typeof(T) == typeof(ObjectModel))
                relevantColumns = GetObjectModelColumns(gridColumnModels);

            if (typeof(T) == typeof(IntervalUnitModel))
                relevantColumns = GetIntervalUnitModelColumns(gridColumnModels);

            if (typeof(T) == typeof(WorkPackageModel))
                relevantColumns = GetWorkpackageModelColumns(gridColumnModels);

            if (typeof(T) == typeof(CommonCostModel))
                relevantColumns = GetCommonCostModelColumns(gridColumnModels);

            if (typeof(T) == typeof(RiskMatrixTemplateModel))
                relevantColumns = GetRiskMatrixTemplateModelColumns(gridColumnModels);

            if (typeof(T) == typeof(RiskObjectModel))
                relevantColumns = GetRiskObjectModelColumns(gridColumnModels);

            if (typeof(T) == typeof(AssetModel))
                relevantColumns = GetAssetModelColumns(gridColumnModels);

            if (typeof(T) == typeof(SpareModel))
                relevantColumns = GetSpareModelColumns(gridColumnModels);

            if (typeof(T) == typeof(ClusterCostModel))
                relevantColumns = GetClusterCostColumns(gridColumnModels);

            if (typeof(T) == typeof(ClusterTaskPlanModel))
                relevantColumns = GetClusterTaskPlanColumns(gridColumnModels);

            if (typeof(T) == typeof(ClusterTaskPlanModelWithExtraTaskProperties))
                relevantColumns = GetClusterTaskPlanWithExtraPropertiesColumns(gridColumnModels);

            if (typeof(T) == typeof(ClusterModel))
                relevantColumns = GetClusterColumns(gridColumnModels);

            if (typeof(T) == typeof(CriticalityRankingModelFlat))
                relevantColumns = GetCriticalityColumns(gridColumnModels);

            if (typeof(T) == typeof(LccEffectDetailModel))
                relevantColumns = GetLccEffectDetailColumns(gridColumnModels);

            if (typeof(T) == typeof(UserAccount))
                relevantColumns = GetUserAccountColumns(gridColumnModels);

            if (typeof(T) == typeof(RamsModel))
                relevantColumns = GetRamsModelColumns(gridColumnModels);

            if (typeof(T) == typeof(SapaDetailModel))
                relevantColumns = GetSapaDetailColumns(gridColumnModels);

            if (typeof(T) == typeof(AttachmentModel))
                relevantColumns = GetAttachmentColumns(gridColumnModels);

            // Object with multiple different Layouts
            if (typeof(T) == typeof(LccDetailModel))
            {
                if (IsGridType(gridColumnModels, GridNames.Lcc.RealCosts))
                    relevantColumns = GetLccDetailRealCostsColumns(gridColumnModels);

                else if (IsGridType(gridColumnModels, GridNames.Lcc.OptimalCost))
                    relevantColumns = GetLccDetailOptimalCostsColumns(gridColumnModels);
                else if (IsGridType(gridColumnModels, GridNames.Lcc.OptimalCostPmo))
                    relevantColumns = GetLccDetailOptimalCostsPmoColumns(gridColumnModels);
            }

            if (typeof(T) == typeof(RiskModel))
            {
                if (IsGridType(gridColumnModels, GridNames.RiskOrganizer.Risks))
                    relevantColumns = GetRiskOrganizerRisksColumns(gridColumnModels);

                else if (IsGridType(gridColumnModels, GridNames.Lcc.Risks))
                    relevantColumns = GetLCCRisksColumns(gridColumnModels);
            }

            if (typeof(T) == typeof(TaskModel))
            {
                if (IsGridType(gridColumnModels, GridNames.Cluster.PreventiveActions))
                    relevantColumns = GetClusterTaskModelColumns(gridColumnModels);

                else if (IsGridType(gridColumnModels, GridNames.RiskEdit.PreventiveActions))
                    relevantColumns = GetRiskEditTaskModelColumns(gridColumnModels);

                else if (IsGridType(gridColumnModels, GridNames.RiskOrganizer.PreventiveActions))
                    relevantColumns = GetRiskOrganizerTaskModelColumns(gridColumnModels);

                else if (IsGridType(gridColumnModels, GridNames.Lcc.PreventiveActions))
                    relevantColumns = GetLccTaskModelColumns(gridColumnModels);
            }

            if (typeof(T) == typeof(CommonTaskModel))
            {
                if (IsGridType(gridColumnModels, GridNames.Portfolio.CommonActions))
                    relevantColumns = GetCommonTaskModelColumns(gridColumnModels);

                else if (IsGridType(gridColumnModels, GridNames.Portfolio.CommonActionsImport))
                    relevantColumns = GetCommonTaskModelImportColumns(gridColumnModels);
            }

            // Imports/ Exports
            if (typeof(T) == typeof(TaskImportModel))
                relevantColumns = GetTaskImportModelColumns(gridColumnModels);

            if (typeof(T) == typeof(SpareImportModel))
                relevantColumns = GetSpareImportColumns(gridColumnModels);

            if (typeof(T) == typeof(RiskImportModel))
                relevantColumns = GetRiskImportModelColumns(gridColumnModels);

            if (typeof(T) == typeof(TaskPlanReportItemModel))
                relevantColumns = GetTaskPlanExportColumns(gridColumnModels);

            //null check
            relevantColumns = relevantColumns.Where(x => x != null).ToList();

            if (!relevantColumns.Any()) return;
            gridColumnModels.ForEach(x => x.Visible = false);
            relevantColumns.ForEach(x => x.Visible = true);
            ReArrangeGridColumns(relevantColumns);
        }

        private static bool IsGridType(List<GridColumnModel> gridColumnModels, string name) =>
            gridColumnModels.All(x => x.ControlName.Equals(name, StringComparison.InvariantCultureIgnoreCase));

        private List<GridColumnModel> GetScenarioModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(ScenarioModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(ScenarioModel.ShortKey), displayName: GetName("GcsShortKey*"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(ScenarioModel.Name), displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(ScenarioModel.Description),
                    displayName: GetName("GcsDescription")),
                GetGridRowByName(gridColumnModels, nameof(ScenarioModel.Prerequisites),
                    displayName: GetName("GcsPrerequisites")),
                GetGridRowByName(gridColumnModels, nameof(ScenarioModel.Status), width: 70)
            };
        }

        private List<GridColumnModel> GetFailureModeModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(FailureModeModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(FailureModeModel.Name), displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(FailureModeModel.Description),
                    displayName: GetName("GcsDescription")),
                GetGridRowByName(gridColumnModels, nameof(FailureModeModel.RateTrend),
                    displayName: GetName("GcsRateTrend"))
            };
        }

        private List<GridColumnModel> GetRiskEditTaskModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Name), displayName: GetName("GcsName*"),
                    width: 120),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.MxPolicy), displayName: GetName("GcsPolicy*"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.InitiatorId),
                    displayName: GetName("GcsInitiator*")),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.WorkpackageId),
                    displayName: GetName("GcsWorkpackage*")),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.ExecutorId), displayName: GetName("GcsExecutor*"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Interval), displayName: GetName("GcsInterval*"),
                    fieldType: FieldType.Number, width: 50),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.IntervalUnitId), displayName: GetName("GcsUnit*"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.EstCosts), displayName: GetName("GcsEstCosts"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.ClusterCosts),
                    displayName: GetName("GcsClusterCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Costs), displayName: GetName("GcsCalcCosts"),
                    fieldType: FieldType.Currency, width: 70)
            };
        }

        private List<GridColumnModel> GetRiskOrganizerTaskModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Name), displayName: GetName("GcsName"), width: 130),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Policy), displayName: GetName("GcsPolicy"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Interval), displayName: GetName("GcsInterval"),
                    fieldType: FieldType.Number, width: 50),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.IntervalUnit), displayName: GetName("GcsUnit"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.EstCosts), displayName: GetName("GcsEstCosts"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.ClusterCosts),
                    displayName: GetName("GcsClusterCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Costs), displayName: GetName("GcsCalcCosts"),
                    fieldType: FieldType.Currency, width: 70),
            };
        }

        private List<GridColumnModel> GetClusterTaskModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.SortOrder), displayName: "Sort Order", width: 30),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Name), displayName: GetName("GcsName"), width: 120),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.MxPolicy), displayName: GetName("GcsPolicy"),
                    width: 60),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.InitiatorId), displayName: GetName("GcsInitiator"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.ExecutorId), displayName: GetName("GcsExecutor"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.WorkpackageId),
                    displayName: GetName("GcsWorkpackage*"), width: 80),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Interval), displayName: GetName("GcsInterval"),
                    fieldType: FieldType.Number, width: 40),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.IntervalUnitId), displayName: GetName("GcsUnit"),
                    width: 40),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.DownTime), fieldType: FieldType.Number, displayName: GetName("GcsDownTime"),
                    width: 40),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Duration), fieldType: FieldType.Number, displayName: GetName("GcsDuration"),
                    width: 40),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.EstCosts), displayName: GetName("GcsEstCosts"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.ClusterCosts),
                    displayName: GetName("GcsClusterCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Costs), displayName: GetName("GcsCalcCosts"),
                    fieldType: FieldType.Currency, width: 70),
            };
        }

        private List<GridColumnModel> GetSpareModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(SpareModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(SpareModel.Name), displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(SpareModel.ObjectCount),
                    displayName: GetName("GcsObjectCount")),
                GetGridRowByName(gridColumnModels, nameof(SpareModel.OrderLeadTime), fieldType: FieldType.Number,
                    displayName: GetName("GcsOrderLeadTime")),
                GetGridRowByName(gridColumnModels, nameof(SpareModel.Reliability), fieldType: FieldType.Percentage,
                    displayName: GetName("GcsReliability"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(SpareModel.PurchasePrice),
                    displayName: GetName("GcsPurchasePrice"), fieldType: FieldType.DetailCurrency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(SpareModel.NoOfItems), displayName: GetName("GcsNoOfItems"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(SpareModel.Costs), displayName: GetName("GcsCosts"),
                    fieldType: FieldType.Currency, width: 70)
            };
        }

        private List<GridColumnModel> GetSpareImportColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.RiskId), width: 40, displayName: "Import Risk Id"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.Id), width: 40, displayName: "Spare Id"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.Name), displayName: "Spare"),

                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.Remarks), displayName: "Remarks"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.ObjectCount), displayName: "Object count"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.OrderLeadTime), displayName: "Lead time"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.Reliability), displayName: "Reliability",
                    fieldType: FieldType.Percentage),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.PurchasePrice),
                    displayName: "Purchase price", fieldType: FieldType.DetailCurrency),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.NoOfSpares), fieldType: FieldType.Number, displayName: "No of spares"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.SpareCosts), displayName: "Spare costs",
                    fieldType: FieldType.Currency),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.StockNumber), displayName: "Stock number"),

                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.PurchaseYear), fieldType: FieldType.IntegerNoStyling, displayName: "Purchase year"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.YearlyCost), displayName: "Yearly cost",
                    fieldType: FieldType.Currency),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.SupplierId), displayName: "Supplier id"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.VendorId), displayName: "Vendor id"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.ReferenceId), displayName: "Reference Id"),
                GetGridRowByName(gridColumnModels, nameof(SpareImportModel.Pmo), displayName: "PMO"),
            };
        }

        private List<GridColumnModel> GetTaskPlanExportColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.SiName), width: 100, displayName: "ABS Id"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.SiDescription), width: 100, displayName: "ABS Name"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.Scenario), width: 100, displayName: "Scenario"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.RiskObjName), width: 100, displayName: "Risk Assessment"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpName), width: 100, displayName: "Tp Name"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.System), width: 70, displayName: "System"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.Component), width: 70, displayName: "Component"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpInterval), fieldType: FieldType.Number, width: 50, displayName: "Tp Interval"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpIntervalUnit), width: 50, displayName: "Tp Unit"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpDescription), width: 100, displayName: "Tp Description"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpInstruction), width: 100, displayName: "Tp Instruction"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpMxPolicy), width: 100, displayName: "Tp Strategy"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpRemarks), width: 100, displayName: "Tp Remarks"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpDuration), fieldType: FieldType.Number, width: 50, displayName: "Tp Duration"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpDownTime), fieldType: FieldType.Number, width: 50, displayName: "Tp Down Time"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpEstCosts), fieldType: FieldType.Currency, width: 70, displayName: "Tp Est.Costs"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpTotalCosts), fieldType: FieldType.Currency, width: 70, displayName: "Tp Total Costs"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpExecutor), width: 80, displayName: "Tp Executor"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.CltpInitiator), width: 80, displayName: "Tp Initiator"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.Risk), width: 100, displayName: "Risk"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.FailureMode), width: 100, displayName: "Failure Mode"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.MrbFailureCause), width: 100, displayName: "Failure Cause"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.MrbFailureConsequences), width: 100, displayName: "Failure Consequences"),
                GetGridRowByName(gridColumnModels, nameof(TaskPlanReportItemModel.MrbEffect), width: 100, displayName: "Failure Effect"),
            };
        }

        private List<GridColumnModel> GetClusterCostColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(ClusterCostModel.CommonCostId),
                    displayName: GetName("GcsDescription")),
                GetGridRowByName(gridColumnModels, nameof(ClusterCostModel.TaskId), displayName: GetName("GcsTask")),
                GetGridRowByName(gridColumnModels, nameof(ClusterCostModel.Units), displayName: GetName("GcsUnits"),
                    fieldType: FieldType.Number, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterCostModel.Quantity),
                    displayName: GetName("GcsQuantity"), fieldType: FieldType.Number, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterCostModel.Price), displayName: GetName("GcsPrice"),
                    fieldType: FieldType.DetailCurrency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterCostModel.Cost), displayName: GetName("GcsCosts"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterCostModel.CalculationType),
                    displayName: GetName("GcsCalculationType")),
                GetGridRowByName(gridColumnModels, nameof(ClusterCostModel.Type), displayName: GetName("GcsType"),
                    width: 80),
            };
        }

        private List<GridColumnModel> GetClusterTaskPlanColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.RiskId),
                    displayName: GetName("GcsRiskId")),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.ClusterId), displayName: "Cluster"),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.Task), displayName: GetName("GcsTaskId"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.TaskId),
                    displayName: GetName("GcsTask")),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.AssetId), displayName: "Asset Code"),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.Asset)),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.EstCosts),
                    displayName: GetName("GcsEstCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.ClusterCosts),
                    displayName: GetName("GcsClusterCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModel.TotalCosts),
                    displayName: GetName("GcsCalcCosts"), fieldType: FieldType.Currency, width: 70)
            };
        }

        private List<GridColumnModel> GetClusterTaskPlanWithExtraPropertiesColumns(
            List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.RiskId),
                    displayName: GetName("GcsRiskId")),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.ClusterId),
                    displayName: "Cluster"),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.Task),
                    displayName: GetName("GcsTaskId"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.TaskId),
                    displayName: GetName("GcsTask")),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.Policy),
                    displayName: GetName("GcsPolicy")),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.Interval),
                    displayName: GetName("GcsInterval"), fieldType: FieldType.Number, width: 30),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.IntervalUnit),
                    displayName: GetName("GcsUnit"), width: 40),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.Executor),
                    displayName: GetName("GcsExecutor")),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.AssetId),
                    displayName: "Asset Code"),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.Asset)),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.EstCosts),
                    displayName: GetName("GcsEstCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.ClusterCosts),
                    displayName: GetName("GcsClusterCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterTaskPlanModelWithExtraTaskProperties.TotalCosts),
                    displayName: GetName("GcsCalcCosts"), fieldType: FieldType.Currency, width: 70)
            };
        }

        private List<GridColumnModel> GetClusterColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.Name), displayName: GetName("GcsName")),
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.Level), width: 40),
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.PartOf), displayName: "Part of", width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.PartOfCluster), displayName: "Part of Cluster"),
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.EstTaskCosts),
                    displayName: GetName("GcsEstCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.TaskCosts), displayName: GetName("GcsCalcCosts"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.Status), width: 50),
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.Responsible),
                    displayName: GetName("GcsResponsible")),
                GetGridRowByName(gridColumnModels, nameof(ClusterModel.Scenario), displayName: GetName("GcsScenario")),
            };
        }

        private List<GridColumnModel> GetObjectModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(ObjectModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(ObjectModel.ShortKey), displayName: GetName("GcsShortKey*"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(ObjectModel.Name), displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(ObjectModel.Description),
                    displayName: GetName("GcsDescription")),
                GetGridRowByName(gridColumnModels, nameof(ObjectModel.NewValue), displayName: GetName("GcsNewValue"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(ObjectModel.Function), displayName: GetName("GcsFunction"))
            };
        }

        private List<GridColumnModel> GetIntervalUnitModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(IntervalUnitModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(IntervalUnitModel.ShortKey),
                    displayName: GetName("GcsShortKey*"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(IntervalUnitModel.Name), displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(IntervalUnitModel.UnitsPerYear),
                    displayName: GetName("GcsUnitsPerYear*"), fieldType: FieldType.Number, width: 70),
            };
        }

        private List<GridColumnModel> GetWorkpackageModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(WorkPackageModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(WorkPackageModel.ShortDescription),
                    displayName: GetName("GcsShortKey*"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(WorkPackageModel.Name), displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(WorkPackageModel.Interval),
                    displayName: GetName("GcsInterval*"), fieldType: FieldType.Number, width: 50),
                GetGridRowByName(gridColumnModels, nameof(WorkPackageModel.IntervalUnit),
                    displayName: GetName("GcsUnit*"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(WorkPackageModel.Executor),
                    displayName: GetName("GcsExecutor*"), width: 70)
            };
        }

        private List<GridColumnModel> GetCommonTaskModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Name), displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Description),
                    displayName: GetName("GcsDescription")),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Interval),
                    displayName: GetName("GcsInterval*"), fieldType: FieldType.Number, width: 50),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.IntervalUnitId),
                    displayName: GetName("GcsUnit*"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.MxPolicyId),
                    displayName: GetName("GcsPolicy*"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.WorkPackageId),
                    displayName: GetName("GcsWorkpackage*"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Type), displayName: "Type*", width: 50),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Costs), displayName: GetName("GcsCosts"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.InitiatorId),
                    displayName: GetName("GcsInitiator"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.ExecutorId),
                    displayName: GetName("GcsExecutor*"), width: 70),
            };
        }

        private List<GridColumnModel> GetCommonTaskModelImportColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Name), displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Policy), displayName: GetName("GcsPolicy*")),

                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Interval), fieldType: FieldType.Number, displayName: GetName("GcsInterval*")),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.IntervalUnit), displayName: GetName("GcsUnit*")),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.IntervalModifiable)),
                
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Initiator), displayName: GetName("GcsInitiator")),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.InitiatorModifiable)),

                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Executor), displayName: GetName("GcsExecutor*")),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.ExecutorModifiable)),

                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.WorkPackageModel), displayName: GetName("GcsWorkpackage*")),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.WorkPackageModifiable)),

                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Type), displayName: "Type*"),

                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.Costs), fieldType: FieldType.Currency, displayName: GetName("GcsCosts")),
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.UnitType)), // TODO, make human readable
                GetGridRowByName(gridColumnModels, nameof(CommonTaskModel.CostModifiable)),
            };
        }

        private List<GridColumnModel> GetCommonCostModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(CommonCostModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(CommonCostModel.ShortKey),
                    displayName: GetName("GcsShortKey*"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(CommonCostModel.Type), displayName: "Type*", width: 50),
                GetGridRowByName(gridColumnModels, nameof(CommonCostModel.CalculationType),
                    displayName: GetName("GcsCalculationType"), width: 50),
                GetGridRowByName(gridColumnModels, nameof(CommonCostModel.Description),
                    displayName: GetName("GcsDescription*")),
                GetGridRowByName(gridColumnModels, nameof(CommonCostModel.Number), fieldType: FieldType.Number, displayName: "Number*", width: 50),
                GetGridRowByName(gridColumnModels, nameof(CommonCostModel.Units), fieldType: FieldType.Number, displayName: GetName("GcsUnits*"),
                    width: 50),
                GetGridRowByName(gridColumnModels, nameof(CommonCostModel.Price), displayName: GetName("GcsPrice*"),
                    fieldType: FieldType.DetailCurrency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(CommonCostModel.PriceIndexYear),
                    displayName: GetName("GcsPriceIndexYear*"), fieldType: FieldType.IntegerNoStyling, width: 70),
            };
        }

        private List<GridColumnModel> GetRiskMatrixTemplateModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(RiskMatrixTemplateModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(RiskMatrixTemplateModel.ShortName),
                    displayName: GetName("GcsShortKey*"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskMatrixTemplateModel.Name),
                    displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(RiskMatrixTemplateModel.Description),
                    displayName: GetName("GcsDescription")),
                GetGridRowByName(gridColumnModels, nameof(RiskMatrixTemplateModel.Version),
                    displayName: GetName("GcsVersion")),
                GetGridRowByName(gridColumnModels, nameof(RiskMatrixTemplateModel.IsDefault), displayName: "Default*")
            };
        }

        private List<GridColumnModel> GetRiskObjectModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(RiskObjectModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(RiskObjectModel.Name), displayName: GetName("GcsName")),
                GetGridRowByName(gridColumnModels, nameof(RiskObjectModel.Scenario),
                    displayName: GetName("GcsScenario"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskObjectModel.Object), displayName: "Object"),
                GetGridRowByName(gridColumnModels, nameof(RiskObjectModel.StatusNavigation), displayName: "Status",
                    width: 50),
                GetGridRowByName(gridColumnModels, nameof(RiskObjectModel.AnalysisType),
                    displayName: GetName("GcsAnalysisType"), width: 50),
                GetGridRowByName(gridColumnModels, nameof(RiskObjectModel.PreventiveCostLcc),
                    displayName: GetName("GcsPreventiveCost"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskObjectModel.CorrectiveCostLcc),
                    displayName: GetName("GcsCorrectiveCost"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskObjectModel.Aec), displayName: "AEC",
                    fieldType: FieldType.Currency, width: 70),
            };
        }

        private List<GridColumnModel> GetAssetModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(AssetModel.Code), displayName: "Code*", width: 70),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.ParentCode), displayName: "ParentCode", width: 70),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.Name), displayName: GetName("GcsName*")),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.Description), displayName: GetName("GcsDescription")),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.AssetType), displayName: "Asset Type", width: 70),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.AssetManager), displayName: "Asset Manager",
                    width: 80),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.ServiceProvider), displayName: "Service Provider",
                    width: 80),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.Location), displayName: GetName("GcsLocation"),
                    width: 80),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.WarrantyPeriod), fieldType: FieldType.IntegerNoStyling,
                    displayName: GetName("GcsWarrantyPeriod"), width: 60),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.QualityScore), fieldType: FieldType.Number,
                    displayName: GetName("GcsQualityScore"), width: 50),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.CategoryId), fieldType: FieldType.IntegerNoStyling, displayName: GetName("GcsCategoryId"),
                    width: 50),
                GetGridRowByName(gridColumnModels, nameof(AssetModel.Category), displayName: GetName("GcsCategory"),
                    width: 50),
            };
        }

        private List<GridColumnModel> GetRiskOrganizerRisksColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(RiskModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.Name), displayName: GetName("GcsName")),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.RiskObjectId), displayName: "Risk Object"),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.Installation), displayName: "Installation",
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.System), displayName: "System", width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.Component), displayName: "Component", width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.Assembly), displayName: "Assembly", width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.RiskBefore), displayName: GetName("GcsRiskBefore"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.SpareCosts), displayName: GetName("GcsSpareCosts"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.ActionCosts),
                    displayName: GetName("GcsActionCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.RiskAfter), displayName: GetName("GcsRiskAfter"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.MtbfAfter), fieldType: FieldType.Number, displayName: "MTBF", width: 50),
            };
        }

        private List<GridColumnModel> GetLccDetailRealCostsColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.Year), width: 40),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.TaskCost), displayName: "Task Costs",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.ActionCost), displayName: "Action Costs",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.ProcedureCost), displayName: "Procedure Costs",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.ModificationCost),
                    displayName: "Modification Costs", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.OpexCost), displayName: "Opex Costs",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.SpareCost), displayName: "Spare Costs",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RealPreventiveCost),
                    displayName: "Real Prev. Costs", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RealCorrectiveCost),
                    displayName: "Real Cor. Costs", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RealTotalCost),
                    displayName: "Real Total Costs", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.FailureRate), displayName: "Failure Rate",
                    fieldType: FieldType.Number, width: 50),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.TotalNpv), displayName: "Pesent Value",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.Aec), displayName: "AEC",
                    fieldType: FieldType.Currency, width: 70),
            };
        }

        private List<GridColumnModel> GetLccDetailOptimalCostsColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.Year), width: 40),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RealPreventiveCost),
                    displayName: "Real Prev. Costs", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RealCorrectiveCost),
                    displayName: "Real Cor. Costs", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RealTotalCost), displayName: "Real Tot. Costs",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.FailureRate), displayName: "Failure Rate",
                    fieldType: FieldType.Number, width: 50),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.Reliability), displayName: "Reliability",
                    fieldType: FieldType.Percentage, width: 50),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.Depreciation), displayName: "Depreciation",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.PreventiveCost),
                    displayName: "Tot. Prev. Costs", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RiskAfter), displayName: "Corrective Costs",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.TotalCost), displayName: "Tot. Mx. Costs",
                    fieldType: FieldType.Currency, width: 70),
            };
        }

        private List<GridColumnModel> GetLccDetailOptimalCostsPmoColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.Year), width: 40),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RealPreventiveCostPmo),
                    displayName: "Real Prev. Costs PMO", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RealCorrectiveCostPmo),
                    displayName: "Real Cor. Costs PMO", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RealTotalCostPmo),
                    displayName: "Real Tot. Costs PMO", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.FailureRatePmo),
                    displayName: "Failure Rate PMO", fieldType: FieldType.Number, width: 50),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.ReliabilityPmo),
                    displayName: "Reliability PMO", fieldType: FieldType.Percentage, width: 50),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.Depreciation), displayName: "Depreciation",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.TaskCostPmo), displayName: "Task Costs PMO",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.ActionCostPmo),
                    displayName: "Action Costs PMO", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.ProcedureCostPmo),
                    displayName: "Procedure Costs PMO", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.ModificationCostPmo),
                    displayName: "Modification Costs PMO", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.OpexCostPmo), displayName: "Opex Costs PMO",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.SpareCostPmo), displayName: "Spare Costs PMO",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.PreventiveCostPmo),
                    displayName: "Tot. Prev. Costs PMO", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.RiskPmo), displayName: "Corrective Costs PMO",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccDetailModel.TotalCostPmo),
                    displayName: "Tot. Mx. Costs PMO", fieldType: FieldType.Currency, width: 70),
            };
        }

        private List<GridColumnModel> GetLccEffectDetailColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(LccEffectDetailModel.Year), width: 40),
                GetGridRowByName(gridColumnModels, nameof(LccEffectDetailModel.EffectColumn),
                    displayName: "Effect Column", width: 40),
                GetGridRowByName(gridColumnModels, nameof(LccEffectDetailModel.EffectName), displayName: "Effect Name"),
                GetGridRowByName(gridColumnModels, nameof(LccEffectDetailModel.FmecaBefore),
                    displayName: "FMECA Before", fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccEffectDetailModel.FmecaAfter), displayName: "FMECA After",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccEffectDetailModel.RiskBefore), displayName: "Risk Before",
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(LccEffectDetailModel.RiskAfter), displayName: "Risk After",
                    fieldType: FieldType.Currency, width: 70),
            };
        }

        private List<GridColumnModel> GetUserAccountColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(UserAccount.Name)),
                GetGridRowByName(gridColumnModels, nameof(UserAccount.UserName)),
                GetGridRowByName(gridColumnModels, nameof(UserAccount.Email)),
                GetGridRowByName(gridColumnModels, nameof(UserAccount.Company))
            };
        }

        private List<GridColumnModel> GetLccTaskModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Name), displayName: GetName("GcsName")),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Description),
                    displayName: GetName("GcsDescription")),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.MxPolicy), displayName: GetName("GcsPolicy"),
                    width: 50),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Interval), fieldType: FieldType.Number, displayName: GetName("GcsInterval"),
                    width: 50),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.IntervalUnitId), displayName: GetName("GcsUnit"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.ExecutorId), displayName: GetName("GcsExecutor"),
                    width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.WorkpackageId),
                    displayName: GetName("GcsWorkpackage"), width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Status), displayName: "Status", width: 50),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.EstCosts), displayName: GetName("GcsEstCosts"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.ClusterCosts),
                    displayName: GetName("GcsClusterCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(TaskModel.Costs), displayName: GetName("GcsCalcCosts"),
                    fieldType: FieldType.Currency, width: 70),
            };
        }

        private List<GridColumnModel> GetTaskImportModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.RiskId), width: 40,
                    displayName: "Import Risk Id"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Id), width: 40, displayName: "Action Id"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Name), displayName: "Action"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.CommonAction), displayName: "Common Action"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.GeneralDescription),
                    displayName: "Instructions"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Description), displayName: "Description"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Type), displayName: "Type"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Policy), displayName: "Policy"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Initiator), displayName: "Initiator"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Executor), displayName: "Executor"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Duration), fieldType: FieldType.Number, displayName: "Duration"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.DownTime), fieldType: FieldType.Number, displayName: "Downtime"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.WorkPackage), displayName: "Work package"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Interval), fieldType: FieldType.Number, displayName: "Interval"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.IntervalUnit), displayName: "Interval unit"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Units), displayName: "Units"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.UnitType), displayName: "Unit Type"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.EstCostPerUnit), displayName: "cost per unit",
                    fieldType: FieldType.DetailCurrency),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Costs), displayName: "costs",
                    fieldType: FieldType.Currency),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.ValidFromYear), fieldType: FieldType.Integer, displayName: "Valid from"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.ValidUntilYear), fieldType: FieldType.Integer, displayName: "Valid until"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Norm), displayName: "Norm"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Permit), displayName: "Permit"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Remarks), displayName: "Remarks"),
                GetGridRowByName(gridColumnModels, nameof(TaskImportModel.Pmo), displayName: "Pmo task"),
            };
        }

        private List<GridColumnModel> GetLCCRisksColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(RiskModel.Id), width: 40),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.Name), displayName: GetName("GcsName")),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.FailureCause),
                    displayName: GetName("GcsFailureCause"), width: 110),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.FailureConsequences),
                    displayName: GetName("GcsFailureConsequences"), width: 110),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.Description), displayName: GetName("GcsEffect"),
                    width: 110),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.InstallationId), displayName: "Installation",
                    width: 80),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.SystemId), displayName: "System", width: 80),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.ComponentId), displayName: "Component", width: 80),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.RiskBefore), displayName: GetName("GcsRiskBefore"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.ActionCosts),
                    displayName: GetName("GcsActionCosts"), fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.SpareCosts), displayName: GetName("GcsSpareCosts"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.RiskAfter), displayName: GetName("GcsRiskAfter"),
                    fieldType: FieldType.Currency, width: 70),
                GetGridRowByName(gridColumnModels, nameof(RiskModel.DirectCostAfter),
                    displayName: GetName("GcsDirectCostAfter"), fieldType: FieldType.Currency, width: 70),
            };
        }

        private List<GridColumnModel> GetCriticalityColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.AssetCode), displayName: "Name**",
                    width: 80),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.AssetDescription),
                    displayName: GetName("GcsDescription"), width: 80),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.FailureMechanism),
                    displayName: GetName("GcsFailureMode"), width: 80),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Fmeca), displayName: "Safety",
                    width: 30),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Fmeca1Value),
                    displayName: "Safety Value", fieldType: FieldType.Currency, width: 50),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Fmeca2),
                    displayName: "Environment", width: 30),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Fmeca2Value),
                    displayName: "Environmental Value", fieldType: FieldType.Currency, width: 50),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Fmeca3), width: 30),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Fmeca3Value),
                    fieldType: FieldType.Currency, width: 50),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Fmeca4), width: 30),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Fmeca4Value),
                    fieldType: FieldType.Currency, width: 50),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Probability),
                    fieldType: FieldType.Number, displayName: "MTBF", width: 40),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Total), width: 30),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.TotalValue),
                    fieldType: FieldType.Currency, width: 50),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.Category),
                    displayName: "ABC Category*", width: 30),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.AssetCategory),
                    displayName: GetName("GcsCategory"), width: 35),
                GetGridRowByName(gridColumnModels, nameof(CriticalityRankingModelFlat.AssetCategoryId),
                    displayName: "Asset Category ID", fieldType: FieldType.IntegerNoStyling, width: 35),
            };
        }

        private List<GridColumnModel> GetRiskImportModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MRBId), width: 40, displayName: "Risk Id"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MRBImportId), width: 40,
                    displayName: "Import Id"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbName), displayName: "Name"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbDescription), displayName: "Description"),

                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.Object),
                    displayName: GetObjectLevel(ObjectLevel.Collection)),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.Object1),
                    displayName: GetObjectLevel(ObjectLevel.Installation)),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.Object2),
                    displayName: GetObjectLevel(ObjectLevel.System)),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.Object3),
                    displayName: GetObjectLevel(ObjectLevel.Component)),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.Object4),
                    displayName: GetObjectLevel(ObjectLevel.Assembly)),

                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.FailMode), displayName: "Failure Mode"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbFailureCause), displayName: "Cause"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbFailureConsequences),
                    displayName: "Consequence"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbFailureCategorie1),
                    displayName: "Fail Category 1"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbFailureCategorie2),
                    displayName: "Fail Category 2"),

                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbFunction), displayName: "Function"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbRemarks), displayName: "Remarks"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbResponsible), displayName: "Responsible"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbStatus), displayName: "Status"),
                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbState), displayName: "Criticality"),

                GetGridRowByName(gridColumnModels, nameof(RiskImportModel.MrbFmecaGrid),
                    displayName: "Fmeca Selections")
            };
        }

        private List<GridColumnModel> GetRamsModelColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(RamsModel.Id), width: 40, displayName: "Block Id"),
                GetGridRowByName(gridColumnModels, nameof(RamsModel.Name), width: 150, displayName: "Name"),
                GetGridRowByName(gridColumnModels, nameof(RamsModel.Mtbftechn), width: 60, fieldType: FieldType.Number, displayName: "Mtbf Techn."),
                GetGridRowByName(gridColumnModels, nameof(RamsModel.Mtbffunct), width: 60, fieldType: FieldType.Number, displayName: "Mtbf Funct."),
                GetGridRowByName(gridColumnModels, nameof(RamsModel.Mttr), width: 60, fieldType: FieldType.Number, displayName: "Mttr"),
                GetGridRowByName(gridColumnModels, nameof(RamsModel.AvailabilityOutput), width: 60, fieldType: FieldType.Number, displayName: "A Techn."),
                GetGridRowByName(gridColumnModels, nameof(RamsModel.AvailabilityInput), width: 60, fieldType: FieldType.Number, displayName: "A Funct."),
                //GetGridRowByName(gridColumnModels, nameof(RamsModel.Status), width: 60, displayName: "Status"),
                GetGridRowByName(gridColumnModels, nameof(RamsModel.XooN), width: 30, displayName: "XooN"),
                //GetGridRowByName(gridColumnModels, nameof(RamsModel.N), width: 30, displayName: "N")
            };
        }

        private List<GridColumnModel> GetSapaDetailColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.Installation), width: 100, displayName: "Site"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.System), width: 100, displayName: "Asset"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.Component), width: 70, displayName: "Asset No"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.TaskPolicy), width: 50, displayName: "Strategy"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.RiskMarketBefore), width: 60, displayName: "CBI ratio"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.RiskSafetyBefore), width: 60, displayName: "SHE risk"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.RiskObjectName), width: 120, displayName: "RiskObject"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.RiskName), width: 120, displayName: "Risk"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.RiskMtbfBefore), width: 60, fieldType: FieldType.Integer, displayName: "RUL before actions"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.TaskName), width: 150, displayName: "Capex"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.RiskMtbfAfter), width: 60, fieldType: FieldType.Integer, displayName: "RUL after actions"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.RiskSapaIndex), width: 50, fieldType: FieldType.Number, displayName: "Sapa Index"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.Score),  width: 70, fieldType: FieldType.Currency, displayName: "Score"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.CostYear1), width: 70, fieldType: FieldType.Currency, displayName: "Year 1"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.CostYear2), width: 70, fieldType: FieldType.Currency, displayName: "Year 2"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.CostYear3), width: 70, fieldType: FieldType.Currency, displayName: "Year 3"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.CostYear4), width: 70, fieldType: FieldType.Currency, displayName: "Year 4"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.CostYear5), width: 70, fieldType: FieldType.Currency, displayName: "Year 5"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.TotalCapexNeeded), width: 80, fieldType: FieldType.Currency, displayName: "Total Capex Needed"),
                GetGridRowByName(gridColumnModels, nameof(SapaDetailModel.Approved), width: 50, fieldType: FieldType.Boolean, displayName: "Approved"),
            };
        }

        private List<GridColumnModel> GetAttachmentColumns(List<GridColumnModel> gridColumnModels)
        {
            return new List<GridColumnModel>
            {
                GetGridRowByName(gridColumnModels, nameof(AttachmentModel.Id), displayName: "Id"),
                GetGridRowByName(gridColumnModels, nameof(AttachmentModel.Title), width: 100, displayName: "Title"),
                GetGridRowByName(gridColumnModels, nameof(AttachmentModel.Uri), width: 100, displayName: "Uri"),
                GetGridRowByName(gridColumnModels, nameof(AttachmentModel.Description), width: 100, displayName: "Description"),
                GetGridRowByName(gridColumnModels, nameof(AttachmentModel.CatgoryId), width: 100, displayName: "Category"),
                GetGridRowByName(gridColumnModels, nameof(AttachmentModel.RiskId), width: 50, displayName: "RiskId"),
                GetGridRowByName(gridColumnModels, nameof(AttachmentModel.RiskObjectId), width: 50, displayName: "RiskObjectId"),
                GetGridRowByName(gridColumnModels, nameof(AttachmentModel.SapaId), width: 50, displayName: "SapaId"),
                GetGridRowByName(gridColumnModels, nameof(AttachmentModel.TaskId), width: 50, displayName: "TaskId"),
            };
        }

        private static GridColumnModel GetGridRowByName(IEnumerable<GridColumnModel> gridColumnModels, string name,
            string displayName = null, FieldType? fieldType = null, int? width = null)
        {
            var col = gridColumnModels.FirstOrDefault(
                x => x.ColumnName.Equals(name, StringComparison.OrdinalIgnoreCase));

            if (col == null)
                return null;

            if (!string.IsNullOrWhiteSpace(displayName))
                col.ColumnHeader = displayName;

            if (fieldType != null)
                col.FieldType = fieldType.Value;

            if (width != null)
                col.ColumnWidth = width.Value;

            return col;
        }

        private static void ReArrangeGridColumns(IReadOnlyList<GridColumnModel> columnsToShow)
        {
            for (var i = 0; i < columnsToShow.Count; i++)
                columnsToShow[i].DisplayIndex = i - columnsToShow.Count;
        }

        private string GetName(string key) => _localizer[key];
    }
}