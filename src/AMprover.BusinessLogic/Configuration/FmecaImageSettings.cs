namespace AMprover.BusinessLogic.Configuration
{
    public class FmecaImageSettings
    {
        public string Executable { get; init; }
        
        public bool GenerateDuringDay { get; init; }
        
        public string ImageFolder { get; init; }

        public int MinutesDelayBetweenRuns { get; init; }

        public int MrbCountPerRun { get; init; }

        public string TempFolder { get; init; }
    }
}
