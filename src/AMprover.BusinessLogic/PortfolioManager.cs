using AMprover.Data.Infrastructure;
using AMprover.Data.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Models;
using AutoMapper;

namespace AMprover.BusinessLogic;

public interface IPortfolioManager
{
    PortfolioModel GetCurrentPortfolio();

    List<PortfolioModel> GetAll();

    List<PortfolioModel> GetPortfoliosForLoggedInUser();

    void SwitchPortfolioForLoggedInUser(int portfolioId);

    int Assign(string userId, int portfolioId, bool value);
}

public class PortfolioManager : IPortfolioManager
{
    private readonly IAssetManagementPortfolioResolver _assetManagementPortfolioResolver;
    private readonly IPortfolioRepository _portfolioRepository;
    private readonly IMapper _mapper;

    public PortfolioManager(IAssetManagementPortfolioResolver assetManagementPortfolioResolver,
        IPortfolioRepository portfolioRepository, IMapper mapper)
    {
        _assetManagementPortfolioResolver = assetManagementPortfolioResolver;
        _portfolioRepository = portfolioRepository;
        _mapper = mapper;
    }

    public List<PortfolioModel> GetAll()
    {
        return _portfolioRepository.GetAll().AsEnumerable().Select(_mapper.Map<PortfolioModel>).ToList();
    }

    public PortfolioModel GetCurrentPortfolio()
    {
        var lastUsedPortfolio = _assetManagementPortfolioResolver.GetCurrentPortfolio();

        if (lastUsedPortfolio == null)
        {
            throw new Exception($"No portfolio assigned to logged in user.");
        }

        return _mapper.Map<PortfolioModel>(lastUsedPortfolio);
    }

    public List<PortfolioModel> GetPortfoliosForLoggedInUser()
    {
        var portfolios = _assetManagementPortfolioResolver.GetPortfoliosForLoggedInUser();
        return portfolios.Select(_mapper.Map<PortfolioModel>).OrderBy(x => x.Name).ToList();
    }

    public void SwitchPortfolioForLoggedInUser(int portfolioId)
    {
        _assetManagementPortfolioResolver.SwitchPortfolioForLoggedInUser(portfolioId);
    }

    public int Assign(string userId, int portfolioId, bool value)
    {
        return _portfolioRepository.Assign(userId, portfolioId, value);
    }
}