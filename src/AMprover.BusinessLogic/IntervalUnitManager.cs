using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Entities.AM;
using AMprover.Data.Repositories;
using Microsoft.AspNetCore.Components.Authorization;
using System;
using System.Collections.Generic;
using AutoMapper;
using AMprover.Data.Infrastructure;
using System.Linq;
using AMprover.Data.Models;

namespace AMprover.BusinessLogic;

public interface IIntervalUnitManager
{
    List<IntervalUnitModel> GetIntervalUnits();

    IntervalUnitModel GetIntervalUnit(int id);

    bool SaveIntervalUnit(IntervalUnitModel model, out DbOperationResult<IntervalUnitModel> result);

    bool DeleteIntervalUnit(int id, out DbOperationResult result);
}

public class IntervalUnitManager : IIntervalUnitManager
{
    private readonly IAssetManagementBaseRepository<LookupIntervalUnit> _intervalUnitsRepository;
    private readonly IMapper _mapper;
    private readonly AssetManagementDbContext _dbContext;
    private readonly string _userName;

    public IntervalUnitManager(AuthenticationStateProvider authenticationStateProvider,
        IAssetManagementBaseRepository<LookupIntervalUnit> intervalUnitsRepository, IMapper mapper,
        AssetManagementDbContext dbContext)
    {
        _intervalUnitsRepository = intervalUnitsRepository;
        _mapper = mapper;
        _dbContext = dbContext;

        _userName = authenticationStateProvider.GetLoggedInUsername().Result;
    }

    public List<IntervalUnitModel> GetIntervalUnits()
    {
        var dbIntervalUnits = _intervalUnitsRepository.GetAll();
        return dbIntervalUnits.ConvertAll(_mapper.Map<IntervalUnitModel>);
    }

    public IntervalUnitModel GetIntervalUnit(int id)
    {
        return _mapper.Map<IntervalUnitModel>(_intervalUnitsRepository.GetById(id));
    }

    public bool SaveIntervalUnit(IntervalUnitModel model, out DbOperationResult<IntervalUnitModel> result)
    {
        var dbModel = _dbContext.LookupIntervalUnit.FirstOrDefault(x => x.IntUnitId == model.Id);
        dbModel = dbModel != null ? _mapper.Map(model, dbModel) : _mapper.Map<LookupIntervalUnit>(model);

        SetMetaData(dbModel);
        var dbResult = _intervalUnitsRepository.Update(dbModel, _userName);

        result = DbOperationResult<LookupIntervalUnit>.Convert(dbResult,
            _mapper.Map<IntervalUnitModel>(dbResult?.Item));
        return result.Success;
    }

    private void SetMetaData(LookupIntervalUnit model)
    {
        model.IntUnitModifiedBy = _userName;
        model.IntUnitDateModified = DateTime.Now;
    }

    public bool DeleteIntervalUnit(int id, out DbOperationResult result)
    {
        var itemToRemove = _intervalUnitsRepository.GetById(id);
        result = _intervalUnitsRepository.Delete(itemToRemove, _userName);
        return result.Success;
    }
}