using static AMprover.Data.Constants.AMproverIdentityConstants;

namespace AMprover.BusinessLogic
{
    public interface IGlobalDataService
    {
        public bool Initialized { get; }

        public Role? UserRole { get; set; }

        public bool CanEdit { get; }

        public string Language { get; set; }

        public string Currency { get; set; }

        public string CurrencyFormat { get; }
    }

    public class GlobalDataService : IGlobalDataService
    {
        private Role? _userRole;
        public Role? UserRole 
        { 
            get => _userRole;
            set
            {
                _userRole = value;
                Initialized = true;
            }
        }

        public bool CanEdit => _userRole is Role.Administrators or Role.Users;

        public bool Initialized { get; private set; }

        public string Language { get; set; }

        public string Currency { get; set; }

        public string CurrencyFormat 
        {
            get
            {
                return Currency.ToLower() switch
                {
                    "nl-nl" => "€ #,##0.00",
                    _ => "$ #,##0.00"
                };
            }
        }
    }
}
