using AMprover.BusinessLogic.Helpers;
using AMprover.Data.Infrastructure;
using Microsoft.AspNetCore.Components.Authorization;
using AMprover.Data.Extensions;
using System.Linq;
using AMprover.BusinessLogic.Models;
using System.Collections.Generic;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace AMprover.BusinessLogic
{
    public interface IAttachmentManager
    {
        void DeleteAttachment(int attachmentId);

        List<AttachmentModel> GetAttachments();
        List<AttachmentCategoryModel> GetAttachmentCategories();

        AttachmentModel SaveAttachment(AttachmentModel model);
        AttachmentCategoryModel SaveAttachmentCategory(AttachmentCategoryModel model);

        bool DeleteAttachmentCategory(int attachmentCategoryId, out string error);
        bool DeleteAttachment(int attachmentId, out string error);
    }

    public class AttachmentManager : IAttachmentManager
    {
        readonly AssetManagementDbContext _dbContext;
        readonly string _userName;
        readonly IMapper _mapper;

        public AttachmentManager(
            AssetManagementDbContext dbContext, 
            AuthenticationStateProvider authenticationStateProvider,
            IMapper mapper)
        {
            _dbContext = dbContext;
            _userName = authenticationStateProvider.GetLoggedInUsername().Result;
            _mapper = mapper;
        }

        public void DeleteAttachment(int attachmentId)
        {
            var attachment = _dbContext.Attachment.FirstOrDefault(x => x.AtchId == attachmentId);
            if(attachment != null)
            {
                _dbContext.Attachment.Remove(attachment);
                _dbContext.SaveChangesAndClear(_userName);
            }
        }

        public List<AttachmentModel> GetAttachments()
        {
            return _dbContext.Attachment
                .Select(x => _mapper.Map<AttachmentModel>(x))
                .ToList();
        }

        public List<AttachmentCategoryModel> GetAttachmentCategories()
        {
            return _dbContext.AttachmentCategory
                .Select(x => _mapper.Map<AttachmentCategoryModel>(x))
                .ToList();
        }

        public bool DeleteAttachmentCategory(int attachmentCategoryId, out string error)
        {
            var count = _dbContext.Attachment.Count(x => x.AtchCatgoryId == attachmentCategoryId);
            error = count > 0
                ? $"Unable to delete, category is used by {count} Attachments"
                : null;

            if(count == 0)
            {
                var model = _dbContext.AttachmentCategory.FirstOrDefault(x => x.AtchCatId == attachmentCategoryId);
                if(model != null)
                {
                    _dbContext.AttachmentCategory.Remove(model);
                    _dbContext.SaveChangesAndClear(_userName);
                    return true;
                }
            }

            return false;
        }

        public bool DeleteAttachment(int attachmentId, out string error)
        {
            error = string.Empty;
            var attachment = _dbContext.Attachment
                .FirstOrDefault(x => x.AtchId == attachmentId);

            if (attachment == null)
            {
                error = string.Empty;
                return true;
            }

            _dbContext.Attachment.Remove(attachment);
            _dbContext.SaveChangesAndClear(_userName);
            return true;
        }

        public AttachmentCategoryModel SaveAttachmentCategory(AttachmentCategoryModel model)
        {
            var dbModel = _mapper.Map<Data.Entities.AM.AttachmentCategory>(model);
            _dbContext.Update(dbModel);
            _dbContext.SaveChangesAndClear(_userName);

            var result = _mapper.Map<AttachmentCategoryModel>(dbModel);
            return result;
        }

        public AttachmentModel SaveAttachment(AttachmentModel model)
        {
            var dbModel = _mapper.Map<Data.Entities.AM.Attachment>(model);
            _dbContext.Update(dbModel);
            _dbContext.SaveChangesAndClear(_userName);

            var result = _mapper.Map<AttachmentModel>(dbModel);
            return result;
        }
    }
}
