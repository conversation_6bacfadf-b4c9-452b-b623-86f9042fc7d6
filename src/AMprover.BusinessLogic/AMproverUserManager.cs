using AMprover.BlazorApp.Services;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using System;
using System.Linq;
using System.Threading.Tasks;
using static AMprover.Data.Constants.AMproverIdentityConstants;

namespace AMprover.BusinessLogic
{
    public interface IAMproverUserManager
    {
        public Task<Role> GetUserRole();
    }

    public class AMproverUserManager : IAMproverUserManager
    {
        private readonly UserManager<UserAccount> _userManager;
        private readonly AuthenticationStateProvider _authStateProvider;
        private readonly IScopedUserService _scopedUserService;

        public AMproverUserManager(UserManager<UserAccount> userManager, AuthenticationStateProvider authStateProvider, IScopedUserService scopedUserService)
        {
            _userManager = userManager;
            _authStateProvider = authStateProvider;
            _scopedUserService = scopedUserService;
        }

        public async Task<Role> GetUserRole()
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            var loggedInUserId = _userManager.GetUserId(authState.User);

            if (loggedInUserId == null)
                return Role.LoggedOut;

            var userAccount = await _scopedUserService.FindByIdAsync(loggedInUserId).ConfigureAwait(false);

            var roles = await _scopedUserService.GetRolesAsync(userAccount).ConfigureAwait(false);
            if (roles.Any(x => Enum.Parse<Role>(x) == Role.Administrators))
            {
                return Role.Administrators;
            }

            return roles.Any(x => Enum.Parse<Role>(x) == Role.ReadOnly) ? Role.ReadOnly : Role.Users;
        }
    }
}
