using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;
using static AMprover.Data.Constants.AMproverIdentityConstants;

namespace AMprover.BusinessLogic
{
    public interface IAMproverUserManager
    {
        public Task<Role> GetUserRole();
    }

    public class AMproverUserManager : IAMproverUserManager
    {
        private readonly UserManager<UserAccount> _userManager;
        private readonly AuthenticationStateProvider _authStateProvider;
        private readonly IServiceProvider _serviceProvider;

        public AMproverUserManager(UserManager<UserAccount> userManager, AuthenticationStateProvider authStateProvider, IServiceProvider serviceProvider)
        {
            _userManager = userManager;
            _authStateProvider = authStateProvider;
            _serviceProvider = serviceProvider;
        }

        public async Task<Role> GetUserRole()
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            var loggedInUserId = _userManager.GetUserId(authState.User);

            if (loggedInUserId == null)
                return Role.LoggedOut;

            // Create a new scope to get a fresh UserManager and DbContext
            using var scope = _serviceProvider.CreateScope();
            var scopedUserManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();

            var userAccount = await scopedUserManager.FindByIdAsync(loggedInUserId).ConfigureAwait(false);

            var roles = await scopedUserManager.GetRolesAsync(userAccount).ConfigureAwait(false);
            if (roles.Any(x => Enum.Parse<Role>(x) == Role.Administrators))
            {
                return Role.Administrators;
            }

            return roles.Any(x => Enum.Parse<Role>(x) == Role.ReadOnly) ? Role.ReadOnly : Role.Users;
        }
    }
}
