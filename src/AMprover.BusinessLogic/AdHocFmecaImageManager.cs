using AMprover.BusinessLogic.BackgroundServices;
using AMprover.Data.Constants;
using AMprover.Data.Infrastructure;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace AMprover.BusinessLogic
{
    public interface IAdHocFmecaImageManager
    {
        void GenerateFmecaImages(int riskObjectId);
    }

    public class AdHocFmecaImageManager : IAdHocFmecaImageManager
    {
        ILogger<AdHocFmecaImageManager> _logger { get; init; }

        IConfiguration _config { get; init; }

        IFmecaImageService _fmecaImageService { get; init; }

        AssetManagementDbContext _dbContext { get; init; }

        public AdHocFmecaImageManager(
            ILoggerFactory loggerfactory,
            IConfiguration config,
            IFmecaImageService fmecaImageService,
            AssetManagementDbContext dbContext)
        {
            _logger = loggerfactory.CreateLogger<AdHocFmecaImageManager>();
            _config = config;
            _fmecaImageService = fmecaImageService;
            _dbContext = dbContext;
        }

        public void GenerateFmecaImages(int riskObjectId)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    // Generate Fmeca Images in a new context, in the background
                    var connectionString = GetConnectionString(_dbContext.Database.GetDbConnection().Database);
                    var portfolioOptionsBuilder = new DbContextOptionsBuilder<AssetManagementDbContext>();
                    portfolioOptionsBuilder.UseSqlServer(connectionString);
                    await using var portfolioContext = new AssetManagementDbContext(portfolioOptionsBuilder.Options);

                    // Trigger Regenerate images
                    await _fmecaImageService.GenerateImagesForRiskObject(riskObjectId, portfolioContext);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error after manual trigger to regerate Fmeca Images");
                }
            });
        }

        private string GetConnectionString(string databaseName)
        {
            var defaultConnString = _config.GetConnectionString(ConnectionstringConstants.DefaultConnectionstringName);
            var connStringObject = new SqlConnectionStringBuilder(defaultConnString)
            {
                InitialCatalog = databaseName
            };

            return connStringObject.ToString();
        }
    }
}
