using System;
using System.Collections.Generic;
using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using Microsoft.AspNetCore.Components.Authorization;
using System.Linq;
using System.Web;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Repositories;
using AutoMapper;
using Microsoft.Extensions.Logging;
using AMprover.Data.Extensions;
using AMprover.BusinessLogic.Enums;

namespace AMprover.BusinessLogic;

public interface IPageNavigationManager
{
    List<PageNavigationModel> GetPageQueryStrings();

    string GetPageUrl(string url);

    string GetRiskEditUrl(int riskObjectId);

    void SavePageQueryString(string url, string query = null);

    int? GetSelectedScenario();

    void SetSelectedScenario(int scenarioId);
}

public class PageNavigationManager : IPageNavigationManager
{
    private readonly AssetManagementDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<PageNavigationManager> _logger;
    private readonly IAssetManagementBaseRepository<PageNavigation> _pageNavigationRepository;
    private readonly string _userName;

    public PageNavigationManager(AuthenticationStateProvider authenticationStateProvider,
        AssetManagementDbContext dbContext, IMapper mapper, ILogger<PageNavigationManager> logger,
        IAssetManagementBaseRepository<PageNavigation> pageNavigationRepository)
    {
        _logger = logger;
        _dbContext = dbContext;
        _mapper = mapper;
        _pageNavigationRepository =
            pageNavigationRepository; // Needed to make sure the database is on latest migration. This is the first fired event on page load.

        _userName = authenticationStateProvider.GetLoggedInUsername().Result;
    }

    public List<PageNavigationModel> GetPageQueryStrings()
    {
        try
        {
            return _dbContext.PageNavigations.Where(x => x.UserName == _userName)
                .Select(x => _mapper.Map<PageNavigationModel>(x)).ToList();
        }
        catch (Exception)
        {
            _logger.LogError("Cannot retrieve page querystring for user.");
        }

        return new List<PageNavigationModel>();
    }

    public string GetPageUrl(string url)
    {
        PageNavigation pageNavigation = null;

        try
        {
            if (url.Split("/").Length - 1 > 1)
            {
                var urlParts = url.Split("/").Where(x => !string.IsNullOrEmpty(x) && !x.Any(char.IsDigit));
                pageNavigation = _dbContext.PageNavigations.Where(x => x.UserName == _userName).AsEnumerable()
                    .FirstOrDefault(x => urlParts.All(y => x.PagePath.Contains(y)));
            }
            else
            {
                pageNavigation = _dbContext.PageNavigations.FirstOrDefault(x =>
                    x.UserName == _userName && x.PagePath.Contains(url));
            }
        }
        catch (Exception)
        {
            _logger.LogError("Cannot retrieve page navigation item so returning requested URL");
        }

        return pageNavigation != null
            ? $"{pageNavigation.PagePath}{HttpUtility.UrlDecode(pageNavigation.PageQuery)}"
            : url;
    }

    public string GetRiskEditUrl(int riskObjectId)
    {
        var pageInfo = _dbContext.PageNavigations.FirstOrDefault(x => x.PagePath.StartsWith($"/value-risk-analysis/{riskObjectId}/risks/"));

        return pageInfo != null
            ? $"{pageInfo.PagePath}{pageInfo.PageQuery}"
            : null;
    }

    public int? GetSelectedScenario()
    {
        var setting = GetSetting(UserSettingTypes.SelectedScenarioId);

        if (setting == null)
            return null;

        if (int.TryParse(setting.UsrSetValue, out var selectedScenarioId))
            return selectedScenarioId;

        return null;
    }

    public void SetSelectedScenario(int scenarioId)
    {
        var setting = GetSetting(UserSettingTypes.SelectedScenarioId);

        if (setting == null)
        {
            setting = new UserSetting
            {
                UsrSetSetting = UserSettingTypes.SelectedScenarioId.ToString(),
                UsrSetUsername = _userName,
                UsrSetValue = scenarioId.ToString()
            };

            _dbContext.Add(setting);
        }
        else
        {
            setting.UsrSetValue = scenarioId.ToString();
            _dbContext.Update(setting);
        }

        _dbContext.SaveChangesAndClear(_userName);
    }

    private UserSetting GetSetting(UserSettingTypes type) =>
        _dbContext.Set<UserSetting>().FirstOrDefault(
            x => x.UsrSetUsername == _userName
                 && x.UsrSetSetting == type.ToString());

    public void SavePageQueryString(string url, string query = null)
    {
        PageNavigation dbEntry;
        //If url contains several objects first split for searching correct page navigation
        if (url.Split("/").Length - 1 > 1)
        {
            var urlParts = url.Split("/").Where(x => !string.IsNullOrEmpty(x) && !x.Any(char.IsDigit));
            dbEntry = _dbContext.PageNavigations.Where(x => x.UserName == _userName).AsEnumerable()
                .FirstOrDefault(x => urlParts.All(y => x.PagePath.Contains(y)));
        }
        else
        {
            dbEntry = _dbContext.PageNavigations.FirstOrDefault(x =>
                x.UserName == _userName && x.PagePath.Contains(url));
        }

        if (query != null && !query.StartsWith("?") && query.Length > 0)
            query = $"?{query}";

        if (dbEntry != null)
        {
            if (url.Equals(dbEntry.PagePath) && (string.IsNullOrEmpty(query) || query.Equals(dbEntry.PageQuery)) &&
                _userName.Equals(dbEntry.UserName))
                return;

            dbEntry.PagePath = url;
            dbEntry.PageQuery = query;
            dbEntry.UserName = _userName;
            _dbContext.Update(dbEntry);
        }
        else
        {
            _dbContext.PageNavigations.Add(new PageNavigation
                {PagePath = url, PageQuery = query, UserName = _userName});
        }

        _dbContext.SaveChangesAndClear(_userName);
    }
}