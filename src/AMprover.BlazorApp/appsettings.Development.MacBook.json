{
  "ConnectionStrings": {
    "DefaultConnection": "Server=ds-main-pd1.hosts.darestep.nl;Database=AMprover5;User Id=AMprover5_Prod;Password=********************************;",
    //"DefaultConnection": "Server=amprovert.darestep.nl;Database=AMprover5;User Id=AMprover5_Test;Password=*************$v5wop@FeaQsza9",
    //"DefaultConnection": "Server=127.0.0.1;Database=Mainnovation_AMprover_Dev;User Id=sa;Password=********",
    "EFCoreMigrationsConnection": "Server=127.0.0.1;Database=AMDB40_Training01_RFC;User Id=sa;Password=********"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.AspNetCore.SignalR": "Trace",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "ApplicationInsights": {
      "LogLevel": {
        "Default": "Information"
      }
    }
  },
  "DetailedErrors": true,
  "OutboundEmail": {
    "SmtpHost": "smtp.ziggo.nl",
    "SmtpPort": 25,
    "SmtpSsl": false
  },
  "Features": {
    "Rams": true
  }
}