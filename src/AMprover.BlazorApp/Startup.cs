using System;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Components.Tree;
using AMprover.BlazorApp.Configuration;
using AMprover.BlazorApp.Infrastructure.Bindings;
using AMprover.BlazorApp.Infrastructure.Events;
using AMprover.BlazorApp.Infrastructure.Parameters;
using AMprover.BlazorApp.Pages.LCC;
using AMprover.BlazorApp.Pages.RiskOrganize;
using AMprover.BlazorApp.Pages.RiskOrganize.Risks;
using AMprover.BlazorApp.Services;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.ABS;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.Criticality;
using AMprover.BusinessLogic.Models.Failures;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.Data;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using AMprover.Data.Infrastructure;
using AMprover.Data.Repositories;
using BlazorDownloadFile;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Radzen;
using System.Linq;
using AMprover.BlazorApp.Areas.Identity;
using AMprover.BlazorApp.Helpers;
using AMprover.BlazorApp.Pages.Report;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Configuration;
using Microsoft.Extensions.Logging;
using AMprover.BusinessLogic.Models.Reports;
using AMprover.BusinessLogic.Models.Import;
using AMprover.BusinessLogic.Models.RiskOnAbs;
using AMprover.BusinessLogic.BackgroundServices;
using AMprover.BusinessLogic.Models.Sapa;
using AMprover.BlazorApp.Pages.SAPA;

namespace AMprover.BlazorApp;

public class Startup
{
    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
    }

    private IConfiguration Configuration { get; }

    private string CurrentEnvironment { get; set; } = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

    // This method gets called by the runtime. Use this method to add services to the container.
    // For more information on how to configure your application, visit https://go.microsoft.com/fwlink/?LinkID=398940
    public void ConfigureServices(IServiceCollection services)
    {
        //Added localization configuration
        services.AddLocalization(options => options.ResourcesPath = "Resources");

        // The following line enables Application Insights telemetry collection.
        // But for Blazor (and the way it uses circuits instead of classic request>response round trips, it requires some additional
        // configuration. This till Microsoft team delivered native support: https://github.com/dotnet/aspnetcore/issues/30972 and https://github.com/dotnet/aspnetcore/issues/5461
        // Inspiration taken from: https://stackoverflow.com/a/66687175
        services.AddApplicationInsightsTelemetry();

        // First piece of data(base)handling is regarding the Identities, which comes with their own IdentityDbContext implementation.
        // Probably we can combine this IdentityDbContext derived class with our own future dbContext (by base classing IdentityDbContext)
        services.AddDbContext<MainDbContext>(options =>
        {
            if (CurrentEnvironment.Equals("development", StringComparison.InvariantCultureIgnoreCase))
            {
                options.EnableSensitiveDataLogging();
            }

            options.UseSqlServer(
                Configuration.GetConnectionString(ConnectionstringConstants.DefaultConnectionstringName),
                builder => { builder.EnableRetryOnFailure(5, TimeSpan.FromSeconds(10), null); });
        });

        // Ensure all migrations are being executed on startup. 
        services.AddTransient<IStartupFilter, AMproverDataContextAutomaticMigrationStartupFilter<MainDbContext>>();

        // Default is 32 kb and this means you also can not upload files larger than that. Required for Asset Import
        services.AddSignalR(hubOptions =>
        {
            hubOptions.MaximumReceiveMessageSize = 4 * 1024 * 1024; // 2 MB
            //TODO: look into timeout intervals
            // hubOptions.ClientTimeoutInterval = TimeSpan.FromSeconds(60);
            // hubOptions.KeepAliveInterval = TimeSpan.FromSeconds(30);
            // hubOptions.HandshakeTimeout = TimeSpan.FromSeconds(30);
        });
        
        // .Net Core Identity configuration, heavily dependent on the above AMproverIdentityDbContext
        services.AddDefaultIdentity<UserAccount>(
                options => // extension to out-of-the-box IdentityUser for additional properties
                {
                    options.SignIn.RequireConfirmedAccount = true; // enforce existing e-mail addresses
                    options.User.RequireUniqueEmail = true;
                })
            .AddRoles<IdentityRole>()
            .AddEntityFrameworkStores<MainDbContext>();

        //Mvvm functionality
        services.AddSingleton<IEventManagerFactory, EventManagerFactory>();
        services.AddSingleton<IBindingFactory, BindingFactory>();
        services.AddSingleton<IParameterResolver, ParameterResolver>();
        services.AddSingleton<IParameterCache, ParameterCache>();
        services.AddSingleton<IViewModelParameterSetter, ViewModelParameterSetter>();
        services.AddSingleton<LocalizationHelper>();
        services.AddScoped<JavaScriptHelper>();

        // EmailSender is used by the Identity framework UI
        services.Configure<OutboundEmailSettings>(Configuration.GetSection("OutboundEmail"));
        services.AddSingleton<IEmailSender, IdentityEmailSender>();

        //AuthenticationStateProvider will be needed for other services below, so order does matter
        services.AddScoped<AuthenticationStateProvider, RevalidatingIdentityAuthenticationStateProvider<IdentityUser>>();

        // "Cache" data like language, currency, and user Role
        services.AddScoped<IGlobalDataService, GlobalDataService>();

        //Add the repository before AMContext because its used there
        services.AddScoped<IPortfolioRepository, PortfolioRepository>();

        //Portfolio manager depends on identity and AuthSate
        services.AddScoped<IPortfolioManager, PortfolioManager>();

        // Cache
        services.AddScoped<ICachingService, CachingService>();

        // Dropdown functionality
        services.AddScoped<DropdownHelper>();

        //Add customized logging (combining data with user and database info)
        services.AddScoped(provider => new AMproverLogger("AMprover",
            provider.GetRequiredService<IConfiguration>(),
            provider.GetRequiredService<AuthenticationStateProvider>(),
            provider.GetRequiredService<IPortfolioManager>()
        ));

        // Grid Column Service is injected separate because it is the only service that uses localization for now
        services.AddScoped<IGridColumnService, GridColumnService>();

        //Add the db context for the (last)selected portfolio database of the logged in user
        services.AddScoped<IAssetManagementPortfolioResolver, AssetManagementPortfolioResolver>();
        services.AddDbContext<AssetManagementDbContext>(ServiceLifetime.Scoped);

        //Add repository to selected db context
        services.AddScoped(typeof(IAssetManagementBaseRepository<>), typeof(AssetManagementBaseRepository<>));

        //Repositories (except base repository)
        services.Scan(scan => scan.FromAssemblyOf<ISpareRepository>()
            .AddClasses(classes =>
                classes.Where(type => type.Name.EndsWith("Repository") && !type.Name.EndsWith("BaseRepository")))
            .AsImplementedInterfaces()
            .WithScopedLifetime());
        
        //Managers
        services.Scan(scan => scan.FromAssemblyOf<RiskAnalysisManager>()
            .AddClasses(classes => classes.Where(type => type.Name.EndsWith("Manager")))
            .AsImplementedInterfaces()
            .WithScopedLifetime());

        //ViewModels
        services.Scan(scan => scan.FromAssemblyOf<LCCViewModel>()
            .AddClasses(classes => classes.Where(type => type.Name.EndsWith("ViewModel")))
            .AsSelf()
            .WithScopedLifetime());

        // Radzen functionality
        services.Scan(scan => scan.FromAssemblyOf<DialogService>()
            .AddClasses(classes => classes.Where(type => type.Name.EndsWith("Service")))
            .AsSelf()
            .WithScopedLifetime());

        services.AddRadzenComponents();

        // Scoped User Service to handle DbContext disposal issues in Blazor Server
        services.AddScoped<IScopedUserService, ScopedUserService>();

        // Auto Mapper Configurations
        services.RegisterMappings();

        // Register Models that are used as T in the tree Component
        RegisterTreeComponent<RiskTreeObject>(services);
        RegisterTreeComponent<LccTreeObject>(services);
        RegisterTreeComponent<ClusterTreeObject>(services);
        RegisterTreeComponent<RamsTreeObject>(services);
        RegisterTreeComponent<AssetModel>(services);
        RegisterTreeComponent<RiskOnAbsTreeModel>(services);
        RegisterTreeComponent<SapaTreeObject>(services);

        // Register Models that are used as T in UtilityGrid
        RegisterUtilityGridComponent<AdditionalDataModel>(services);
        RegisterUtilityGridComponent<AttachmentModel>(services);
        RegisterUtilityGridComponent<AttachmentCategoryModel>(services);
        RegisterUtilityGridComponent<CriticalityRankingModelFlat>(services);
        RegisterUtilityGridComponent<SpareModel>(services);
        RegisterUtilityGridComponent<TaskModel>(services);
        RegisterUtilityGridComponent<RiskModel>(services);
        RegisterUtilityGridComponent<RiskWithPlainObjectsModel>(services);
        RegisterUtilityGridComponent<TaskWithPlainObjectsModel>(services);
        RegisterUtilityGridComponent<RiskImportModel>(services);
        RegisterUtilityGridComponent<TaskImportModel>(services);
        RegisterUtilityGridComponent<SpareImportModel>(services);
        RegisterUtilityGridComponent<PmoRiskReportItemModel>(services);
        RegisterUtilityGridComponent<CommonTaskModel>(services);
        RegisterUtilityGridComponent<CommonCostModel>(services);
        RegisterUtilityGridComponent<ClusterTaskModel>(services);
        RegisterUtilityGridComponent<ClusterTaskPlanModel>(services);
        RegisterUtilityGridComponent<ClusterTaskPlanModelWithExtraTaskProperties>(services);
        RegisterUtilityGridComponent<ClusterCostModel>(services);
        RegisterUtilityGridComponent<ClusterModel>(services);
        RegisterUtilityGridComponent<LccEffectDetailModel>(services);
        RegisterUtilityGridComponent<LccDetailModel>(services);
        RegisterUtilityGridComponent<RiskMatrixTemplateModel>(services);
        RegisterUtilityGridComponent<ScenarioModel>(services);
        RegisterUtilityGridComponent<FailureModeModel>(services);
        RegisterUtilityGridComponent<FailureCategory>(services);
        RegisterUtilityGridComponent<GenericObjectLevel>(services);
        RegisterUtilityGridComponent<ObjectModel>(services);
        RegisterUtilityGridComponent<IntervalUnitModel>(services);
        RegisterUtilityGridComponent<InitiatorModel>(services);
        RegisterUtilityGridComponent<ExecutorModel>(services);
        RegisterUtilityGridComponent<WorkPackageModel>(services);
        RegisterUtilityGridComponent<PolicyModel>(services);
        RegisterUtilityGridComponent<AssetModel>(services);
        RegisterUtilityGridComponent<LookupUserDefinedModel>(services);
        RegisterUtilityGridComponent<RiskObjectModel>(services);
        RegisterUtilityGridComponent<RamsModel>(services);
        RegisterUtilityGridComponent<TaskPlanReportItemModel>(services);
        RegisterUtilityGridComponent<UserAccount>(services);
        RegisterUtilityGridComponent<CommonActionImportModel>(services);
        RegisterUtilityGridComponent<SapaDetailModel>(services);
        RegisterUtilityGridComponent<SapaWorkpackageModel>(services);

        // Query params
        services.AddScoped<RiskOrganizerQueryParams>();
        services.AddScoped<RiskEditQueryParams>();
        services.AddScoped<ReportViewQueryParams>();
        services.AddScoped<SapaOverviewQueryParams>();

        //Register download functionality
        services.AddBlazorDownloadFile();

        //Register service for RAMS
        services.AddScoped(typeof(DragAndDropService<>));

        // Register background Services
        services.Configure<FmecaImageSettings>(Configuration.GetSection("FmecaImageSettings"));
        services.AddScoped<IFmecaImageService, FmecaImageService>();
        services.AddScoped<HtmlImageConverter>();
        services.AddHostedService<FmecaImageServiceScheduler>();

        services.AddRazorPages();
        services.AddServerSideBlazor();
    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
    {
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }
        else
        {
            app.UseExceptionHandler("/Error");
            // TODO: The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            app.UseHsts();
        }

        app.UseHttpsRedirection();
        app.UseStaticFiles();

        app.UseRequestLocalization(GetLocalizationOptions());

        app.UseRouting();

        app.UseAuthentication();
        app.UseAuthorization(); // must appear between app.UseRouting() and app.UseEndpoints(...)

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
            endpoints.MapBlazorHub(opts => opts.WebSockets.CloseTimeout = new TimeSpan(1,1,1));
            endpoints.MapFallbackToPage("/_Host");
        });
    }

    private RequestLocalizationOptions GetLocalizationOptions()
    {
        var cultures = Configuration.GetSection("Languages")
            .GetChildren()
            .ToDictionary(x => x.Key, x => x.Value);

        var supportedCultures = cultures.Keys.ToArray();

        var localizationOptions = new RequestLocalizationOptions()
            .AddSupportedCultures(supportedCultures)
            .AddSupportedUICultures(supportedCultures);

        return localizationOptions;
    }

    private static void RegisterTreeComponent<T>(IServiceCollection services)
    {
        services.AddTransient<TreeComponentViewModel<T>>();
        services.AddTransient<TreeNodeComponentViewModel<T>>();
    }

    private static void RegisterUtilityGridComponent<T>(IServiceCollection services)
    {
        services.AddTransient<UtilityGridViewModel<T>>();
    }
}