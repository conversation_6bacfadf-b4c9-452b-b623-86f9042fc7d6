using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using System;

namespace AMprover.BlazorApp;

public class Program
{
    public static void Main(string[] args)
    {
        CreateHostBuilder(args).Build().Run();
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureWebHostDefaults(webBuilder => { webBuilder.UseStartup<Startup>(); })
            .ConfigureLogging((ctx, builder) =>
            {
                //Add additional logging output to file, using the Karambolo Nuget package
                //Outputs to file(s) as per the Logging sections within the appSettings config
                builder.AddFile(o => o.RootPath = ctx.HostingEnvironment.ContentRootPath);
            })
            .ConfigureAppConfiguration((context, builder) =>
            {
                if (context.HostingEnvironment.IsDevelopment())
                {
                    var devboxHostname = Environment.MachineName;
                    builder.AddJsonFile($"appsettings.{Environments.Development}.{devboxHostname}.json", true, true);
                }
            });
}