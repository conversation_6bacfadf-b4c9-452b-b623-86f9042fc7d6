@using System.Net.Http
@using Microsoft.ApplicationInsights
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using AMprover.BlazorApp
@using AMprover.Data
@using AMprover.BlazorApp.Components
@using AMprover.BlazorApp.Components.ABS
@using AMprover.BlazorApp.Components.AreYouSure;
@using AMprover.BlazorApp.Components.PortfolioSetup
@using AMprover.BlazorApp.Components.PortfolioSetup.RiskAnalysis
@using AMprover.BlazorApp.Components.ContextMenu
@using AMprover.BlazorApp.Shared
@using AMprover.BlazorApp.Shared.Components
@using AMprover.BlazorApp.Shared.Layouts
@using Ra<PERSON>zen
@using Radzen.Blazor
@using AMprover.BusinessLogic.Constants
@using AMprover.BusinessLogic.Comparers;
@using AMprover.BusinessLogic.Models.ABS
@using AMprover.BusinessLogic.Models.Tree
@using AMprover.BusinessLogic.Models.ContextMenu
@using AMprover.BlazorApp.Components.Tree
@using AMprover.BusinessLogic.Models.RiskAnalysis
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using AMprover.BusinessLogic.Enums
@using AMprover.BlazorApp.Pages.RiskOrganize.Risks
@using AMprover.BlazorApp.Pages.RiskOrganize.Spares
@using AMprover.BusinessLogic
@using AMprover.BusinessLogic.Models.Cluster
@using AMprover.BusinessLogic.Models
@using AMprover.BusinessLogic.Models.Criticality
@using AMprover.BusinessLogic.Models.RiskOnAbs;
@using AMprover.BusinessLogic.Extensions
@using AMprover.BlazorApp.Components.GridTypes
@using AMprover.BlazorApp.Components.GridTypes.Utility
@using AMprover.BlazorApp.Components.Reports
@using AMprover.BlazorApp.Components.RiskAnalysis
@using AMprover.BlazorApp.Components.Pagination
@using Microsoft.Extensions.Configuration
@using Microsoft.Extensions.Localization
@using AMprover.BlazorApp.Components.SplitButton
@using AMprover.BlazorApp.Services
@using AMprover.BlazorApp.Components.Rams
@using AMprover.BusinessLogic.Models.Rams

@*To enforce login globally: exclude specific components, such as login by applying the Anonymous attribute within the component*@
@attribute [Authorize] 