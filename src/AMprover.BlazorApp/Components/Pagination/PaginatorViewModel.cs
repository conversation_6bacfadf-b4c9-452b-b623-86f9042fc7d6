using Microsoft.AspNetCore.Components;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Components.Pagination;

public class PaginatorViewModel : BaseViewModel
{
    [Parameter] public int Count { get; set; }

    [Parameter] public int Initial { get; set; }

    [Parameter] public EventCallback<int> CallBack { get; set; }

    public int Current { get; set; } = 0;

    public override void OnInitialized()
    {
        Current = Initial;
        base.OnInitialized();
    }

    public int GetCurrentDisplayIndex()
    {
        return Current + 1;
    }

    public void SetCurrentExternally(int index)
    {
        Current = index;
    }

    public async Task First()
    {
        await SetCurrent(0);
    }

    public async Task Previous()
    {
        var prev = Current - 1;

        if (prev < 0)
            prev = Count - 1;

        await SetCurrent(prev);
    }

    public async Task<int> SetFromInput(int input)
    {
        await SetCurrent(input - 1);
        return GetCurrentDisplayIndex();
    }

    public async Task Next()
    {
        var next = Current + 1;

        if (next >= Count)
            next = 0;

        await SetCurrent(next);
    }

    public async Task Last()
    {
        await SetCurrent(Count - 1);
    }

    public async Task SetCurrent(int index)
    {
        Current = index;
        if (CallBack.HasDelegate)
            await CallBack.InvokeAsync(index);
    }
}