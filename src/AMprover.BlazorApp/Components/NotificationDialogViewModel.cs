using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;

namespace AMprover.BlazorApp.Components;

public class NotificationDialogViewModel : BaseViewModel
{
    [Parameter] public bool ReloadPageOnClose { get; set; }

    private readonly NavigationManager _navigationManager;

    public NotificationDialogViewModel(
        ILoggerFactory loggerFactory,
        NavigationManager navigationManager,
        DialogService dialogService) : base(loggerFactory)
    {
        _navigationManager = navigationManager;
        _dialogService = dialogService;
    }

    public void CloseNotification()
    {
        _dialogService.Close();

        if (ReloadPageOnClose)
            _navigationManager.NavigateTo(_navigationManager.Uri, true);
    }
}