using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using Newtonsoft.Json;
using System.Linq;
using AutoMapper;

namespace AMprover.BlazorApp.Components.RiskAnalysis;

public class PreventiveActionEditViewModel : BaseViewModel, IEntityEditorViewModel
{
    private readonly IRiskAnalysisManager _riskAnalysisManager;
    private readonly IPortfolioSetupManager _portfolioSetupManager;
    private readonly ILookupManager _lookupManager;
    private readonly IMapper _mapper;
    private readonly IDropdownManager _dropdownManager;
    private readonly IObjectManager _objectManager;

    public PreventiveActionEditViewModel(ILoggerFactory loggerFactory, IRiskAnalysisManager riskAnalysisManager,
        DialogService dialogService, IMapper mapper,
        ILookupManager lookupManager, IPortfolioSetupManager portfolioSetupManager,
        IDropdownManager dropdownManager, IObjectManager objectManager) : base(loggerFactory, lookupManager)
    {
        _riskAnalysisManager = riskAnalysisManager;
        _portfolioSetupManager = portfolioSetupManager;
        _dialogService = dialogService;
        _lookupManager = lookupManager;
        _dropdownManager = dropdownManager;
        _objectManager = objectManager;
        _mapper = mapper;
    }

    [Parameter] public int PreventiveActionId { get; set; }
    [Parameter] public int? RiskId { get; set; }
    [Parameter] public bool IsClusterTask { get; set; }
    [Parameter] public bool Pmo { get; set; }
    [Parameter] public EventCallback<TaskModel> Callback { get; set; }

    public TaskModel PreventiveAction { get; set; }
    public RiskModel Risk { get; set; }
    public ObjectModel ObjectModel { get; set; }

    public bool ShowFailedValidations { get; set; }
    public bool IsSapaView { get; set; }

    // Dropdowns
    public Dictionary<int, string> ExecutorsDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> InitiatorsDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> WorkPackageDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> SapaWorkPackageDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> IntervalUnitsDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> ClusterDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> PolicyDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> UnitTypeDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<string, string> TaskDict { get; set; } = new Dictionary<string, string>();
    public Dictionary<int, string> TaskOnRiskDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> RiskDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> CommonActionDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int?, string> PartOfDict { get; set; } = new Dictionary<int?, string>();
    public Dictionary<int, string> StatusDict { get; private set; } = new();

    public EntityEditorMode EditorMode
    {
        get
        {
            return PreventiveActionId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    public bool DisableReferenceId { get; set; }
    public bool DisableExecutor { get; set; }
    public bool DisableInitiator { get; set; }
    public bool DisableWorkPackage { get; set; }
    public bool DisableIntervalUnit { get; set; }
    public bool DisableCosts { get; set; }
    public bool DisablePolicy { get; set; }
    public bool DisableActionType { get; set; }
    public bool DisableName { get; set; }
    public bool IsCsirView { get; set; }

    public override void OnInitialized()
    {
        ExecutorsDict = _dropdownManager.GetExecutorDict();
        InitiatorsDict = _dropdownManager.GetInitiatorDict();
        WorkPackageDict = _dropdownManager.GetWorkpackageDict();
        SapaWorkPackageDict = _dropdownManager.GetSapaWorkpackageDict();
        IntervalUnitsDict = _dropdownManager.GetIntervalUnitDict();
        ClusterDict = _dropdownManager.GetClusterDict();
        PolicyDict = _dropdownManager.GetPolicyDict();
        RiskDict = _dropdownManager.GetRisksDict();
        StatusDict = _dropdownManager.GetStatusDict();
        UnitTypeDict = _dropdownManager.GetLookupUserDefinedByFilterDict("UnitTypes");
        TaskDict = _lookupManager.GetLookupByFilterDict("MeasureType", true);
        TaskOnRiskDict = _dropdownManager.GetTasksDict((int)RiskId, Pmo);

        Risk = _riskAnalysisManager.GetRisk((int)RiskId);
        var task = _riskAnalysisManager.GetTaskById(PreventiveActionId);

        if (Risk != null && task != null)
        {
            task.CalculateCosts(Risk);
        }
        
        PreventiveAction = EditorMode switch
        {
            EntityEditorMode.Create => new TaskModel { Id = PreventiveActionId, MrbId = RiskId, Type = "tsk", Pmo = Pmo },
            _ => task
        };
        
        PartOfDict = Risk.Tasks.Where(x => x.PartOf == null && x.Id != PreventiveAction.Id).ToDictionary(x => (int?)x.Id, y => y.Name);
        
        var analysisType = Risk.RiskObject.AnalysisType.ToLower();
        IsCsirView = analysisType.Contains("csir");
        IsSapaView = analysisType.Contains("sapa");

        if (!IsCsirView)
            CommonActionDict = _dropdownManager.GetCommonTasksDict();
        else if (Risk.SystemId == null)
            CommonActionDict = _dropdownManager.GetCommonTasksPrioDict();
        else if (Risk.ComponentId == null)
        {
            ObjectModel = _objectManager.GetObject((int)Risk.SystemId);
            CommonActionDict = _dropdownManager.GetCommonTasksPrioDefinedByFilterDict(ObjectModel.ShortKey);
        }
        else
        {
            ObjectModel = _objectManager.GetObject((int)Risk.ComponentId);
            CommonActionDict = _dropdownManager.GetCommonTasksPrioDefinedByFilterDict(ObjectModel.ShortKey);
        }

        // Set disabled state of common action related fields
        ProcessCommonActionContent();

        base.OnInitialized();
    }

    public void ProcessCommonActionContent(bool setFields = false)
    {
        var selectedCommonAction = PreventiveAction.CommonActionId;
        if (!selectedCommonAction.HasValue)
        {
            DisableInitiator = false;
            DisableExecutor = false;
            DisableWorkPackage = false;
            DisableIntervalUnit = false;
            DisableCosts = false;
            DisablePolicy = false;
            DisableName = false;
            DisableActionType = false;

            return;
        }

        var commonAction = _portfolioSetupManager.GetCommonAction(selectedCommonAction.Value);

        if (setFields)
        {
            if (!string.IsNullOrEmpty(commonAction.Name))
                PreventiveAction.Name = commonAction.Name;

            if(!string.IsNullOrEmpty(commonAction.ReferenceId))
                PreventiveAction.ReferenceId = commonAction.ReferenceId;

            if (!string.IsNullOrEmpty(commonAction.Description))
                PreventiveAction.Description = commonAction.Description;

            if (!string.IsNullOrEmpty(commonAction.GeneralDescription))
                PreventiveAction.GeneralDescription = commonAction.GeneralDescription;

            if (!string.IsNullOrEmpty(commonAction.Remark))
                PreventiveAction.Remark = commonAction.Remark;

            if (!string.IsNullOrEmpty(commonAction.Permit))
                PreventiveAction.Permit = commonAction.Permit;

            if (!string.IsNullOrEmpty(commonAction.Responsible))
                PreventiveAction.Responsible = commonAction.Responsible;

            if (!string.IsNullOrEmpty(commonAction.Type))
                PreventiveAction.Type = commonAction.Type;

            if (commonAction.MxPolicyId.HasValue)
                PreventiveAction.MxPolicy = commonAction.MxPolicyId;

            if (commonAction.InitiatorId.HasValue)
                PreventiveAction.InitiatorId = commonAction.InitiatorId.Value;

            if (commonAction.ExecutorId.HasValue)
                PreventiveAction.ExecutorId = commonAction.ExecutorId.Value;

            if (commonAction.WorkPackageId.HasValue)
                PreventiveAction.WorkpackageId = commonAction.WorkPackageId.Value;

            if (commonAction.IntervalUnitId.HasValue)
                PreventiveAction.IntervalUnitId = commonAction.IntervalUnitId.Value;

            if (commonAction.Interval.HasValue)
                PreventiveAction.Interval = commonAction.Interval.Value;

            if (commonAction.Costs.HasValue)
                PreventiveAction.EstCosts = commonAction.Costs.Value;

            if (commonAction.DownTime.HasValue)
                PreventiveAction.DownTime = commonAction.DownTime.Value;

            if (commonAction.Duration.HasValue)
                PreventiveAction.Duration = commonAction.Duration.Value;

            if (commonAction.PriorityCode.HasValue)
                PreventiveAction.PriorityCode = commonAction.PriorityCode.Value;

            if (commonAction.SortOrder.HasValue)
                PreventiveAction.SortOrder = commonAction.SortOrder.Value;

            PreventiveAction.UnitType = commonAction.UnitType;
        }

        //Determine which fields need to be readonly
        DisableInitiator = commonAction.InitiatorModifiable == false;
        DisableExecutor = commonAction.ExecutorModifiable == false;
        DisableWorkPackage = commonAction.WorkPackageModifiable == false;
        DisableIntervalUnit = commonAction.IntervalModifiable == false;
        DisableCosts = commonAction.CostModifiable == false;

        //always readonly when common action is selected
        DisableReferenceId = !string.IsNullOrWhiteSpace(commonAction.ReferenceId);
        DisablePolicy = true;
        DisableName = true;
        DisableActionType = true;

        // Fix UI bug where all fields required fields give an error message
        PreventiveAction = JsonConvert.DeserializeObject<TaskModel>(JsonConvert.SerializeObject(PreventiveAction));
    }

    public void ValidTaskSubmitted(TaskModel task)
    {
        RemoveYearCostsFromNonExistantYears(task);
        ShowFailedValidations = false;
        _dialogService.Close();
        Callback.InvokeAsync(task);
    }

    private void RemoveYearCostsFromNonExistantYears(TaskModel task)
    {
        if (task.ValidFromYear > 0)
        {
            var years = (task.ValidUntilYear ?? task.ValidFromYear) - task.ValidFromYear + 1;

            if (years <= 1)
            {
                task.CostY2 = 0;
            }
            if (years <= 2)
            {
                task.CostY3 = 0;
            }
            if (years <= 3)
            {
                task.CostY4 = 0;
            }
            if (years <= 4)
            {
                task.CostY5 = 0;
            }

            UpdateYearCosts();
        }
    }

    public void ReCalculateTaskCosts()
    {
        PreventiveAction.CalculateCosts();
    }

    public void ReCalculateEstimatedCosts()
    { 
        PreventiveAction.RecalculateEstimatedCosts();
        PreventiveAction.CalculateCosts();
    }

    public void CalculateDowntimeCosts()
    {
        Risk = _riskAnalysisManager.GetRisk((int)RiskId);

        var DtCost = Risk.RiskObject.DownTimeCost;
        PreventiveAction.CalculateDownTimeCost(DtCost);
    }

    public int GetYears()
    {
        if (PreventiveAction.ValidFromYear == null || PreventiveAction.ValidUntilYear == null)
            return 0;

        return PreventiveAction.ValidUntilYear.Value - PreventiveAction.ValidFromYear.Value;
    }

    public void UpdateYearCosts()
    {
        PreventiveAction.EstCosts =
            (PreventiveAction.CostY1 ?? 0)
            + (PreventiveAction.CostY2 ?? 0)
            + (PreventiveAction.CostY3 ?? 0)
            + (PreventiveAction.CostY4 ?? 0)
            + (PreventiveAction.CostY5 ?? 0);
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ShowFailedValidations = true;

        _logger.LogWarning(
            $"Invalid {EditorMode} form submitted for {nameof(TaskModel)} with Id {PreventiveActionId}.");

        _logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }
}