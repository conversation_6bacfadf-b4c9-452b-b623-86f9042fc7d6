using System;
using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Ra<PERSON>zen;

namespace AMprover.BlazorApp.Components.RiskAnalysis;

public class RiskMatrixContainerViewModel : BaseViewModel
{
    private readonly IRiskAnalysisManager _riskAnalysisManager;

    public RiskMatrixContainerViewModel(ILoggerFactory loggerFactory, IRiskAnalysisManager riskAnalysisManager,
        ILookupManager lookupManager, IGlobalDataService globalDataService, TooltipService tooltipService,
        IJSRuntime jsRuntime) : base(loggerFactory, lookupManager, tooltipService, jsRuntime)
    {
        _riskAnalysisManager = riskAnalysisManager;
        _canEdit = globalDataService.CanEdit;
    }

    [Parameter] public RiskModel Risk { get; set; }
    [Parameter] public EventCallback<RiskModel> OnChangeCallback { get; set; }
    [Parameter] public MatrixTypes MatrixType { get; set; }
    [Parameter] public bool IsRiskAnalysis { get; set; }
    [Parameter] public bool IsSapaView { get; set; }
    [Parameter] public bool TypesSetToColumn { get; set; }
    [Parameter] public bool IsCsirView { get; set; }
    [Parameter] public bool IsReadOnly { get; set; }

    public int DisplayVersion { get; set; } = 1;

    private bool _canEdit { get; set; }
    private bool CanEdit()
    {
        return _canEdit && !IsReadOnly;
    }

    public List<int> ActivatedSubgrids { get; set; } = new();

    public override void OnInitialized()
    {
        if (IsCsirView) DisplayVersion = 3;
    }

    /// <summary>
    /// Called when custom MTBF is being filled in
    /// </summary>
    public void UpdateRiskWithCustomValues()
    {
        if (CanEdit())
        {
            _riskAnalysisManager.UpdateRiskWithSelectedEffect(Risk, MatrixType);

            if (OnChangeCallback.HasDelegate)
                OnChangeCallback.InvokeAsync(Risk);
        }
    }

    /// <summary>
    /// Click event for effect columns (left part of the grid)
    /// </summary>
    /// <param name="column"></param>
    /// <param name="row"></param>
    public void ProcessEffectChanges(int column, int row)
    {
        if (CanEdit())
        {
            _riskAnalysisManager.UpdateRiskWithSelectedEffect(Risk, row, column, MatrixType);

            if (OnChangeCallback.HasDelegate)
                OnChangeCallback.InvokeAsync(Risk);
        }
    }

    /// <summary>
    /// Click event for effect columns in subgrid (left part of the grid)
    /// </summary>
    /// <param name="column"></param>
    /// <param name="row"></param>
    /// <param name="parent"></param>
    /// <param name="isPercentage"></param>
    public void ProcessEffectChanges(int column, int row, int parent, bool isPercentage)
    {
        if (CanEdit())
        {
            _riskAnalysisManager.UpdateRiskWithSelectedEffect(Risk, row, column, parent, MatrixType, isPercentage);

            if (OnChangeCallback.HasDelegate)
                OnChangeCallback.InvokeAsync(Risk);
        }
    }

    /// <summary>
    /// Custom MTBF change method
    /// </summary>
    public void ChangeCustomMtbfValue()
    {
        if (!CanEdit())
            return;

        _riskAnalysisManager.UpdateRiskWithCustomMtbf(Risk, MatrixType);

        if (OnChangeCallback.HasDelegate)
            OnChangeCallback.InvokeAsync(Risk);
    }

    /// <summary>
    /// Click column in color columns
    /// </summary>
    /// <param name="column"></param>
    /// <param name="row"></param>
    public void ProcessDataChanges(int column, int row)
    {
        if (!CanEdit())
            return;

        _riskAnalysisManager.UpdateRiskWithSelectedColorData(Risk, row, column, MatrixType);

        if (OnChangeCallback.HasDelegate)
            OnChangeCallback.InvokeAsync(Risk);
    }

    public GridType MatrixTypeToGridType()
    {
        switch (MatrixType)
        {
            case MatrixTypes.After:
                return GridType.RiskWithTakenActions;
            case MatrixTypes.Before:
                return GridType.RiskWithoutActions;
            case MatrixTypes.Pmo:
                return GridType.RiskWithCurrentActions;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    public void AddActiveSubgrid(int column)
    {
        if (!ActivatedSubgrids.Contains(column))
            ActivatedSubgrids.Add(column);
    }

    public void RemoveActiveSubgrid(int column)
    {
        if (ActivatedSubgrids.Contains(column))
            ActivatedSubgrids.Remove(column);
    }

    public RiskMatrixTemplateColumn GetColumn(int column)
    {
        return Risk.Template.MainGrid.TableColumns[column];
    }
}