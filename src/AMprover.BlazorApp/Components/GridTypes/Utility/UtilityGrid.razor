@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<UtilityGridViewModel<TItem>>
@using AMprover.BusinessLogic.Helpers
@typeparam TItem
@inject LocalizationHelper _localizationHelper
@inject IGlobalDataService _globalDataService
@inject IJSRuntime JsRuntime

<div class=@GetHeaderDivClass()>
<div class="row spacer">
    <div class="col-5 pl-4">
        @if(Interactive)
        {
            @if (AddRowCallBack.HasDelegate)
            {
                <RadzenButton Icon=add_circle_outline Text=@GetLocalizedText("UgAddBtn") ButtonStyle=ButtonStyle.Success Size=ButtonSize.Medium Click=@AddRowCallBack
                                Disabled=@(!BindingContext.UserCanEdit) @ref=BindingContext.NewButton
                                @onmouseover="@(() => BindingContext.ShowTooltip(BindingContext.NewButton.Element, GetLocalizedText("UgAddBtnTxt")))"
                                @onmouseleave="@(() => BindingContext.HideTooltip(BindingContext.NewButton.Element))" />
            }
            else if (AddNewButton || AddNewOverride != null)
            {
                <RadzenButton Icon="add_circle_outline" Text=@GetLocalizedText("UgAddBtn") ButtonStyle=ButtonStyle.Success Size=ButtonSize.Medium Click=@BindingContext.AddNew
                                Disabled=@(!BindingContext.UserCanEdit || BindingContext.CurrentItem != null)
                                @ref=BindingContext.NewButton
                                @onmouseover="@(_ => BindingContext.ShowTooltip(BindingContext.NewButton.Element, @GetLocalizedText("UgAddBtnTxt2")))"
                                @onmouseleave="@(() => BindingContext.HideTooltip(BindingContext.NewButton.Element))" />
            }
        }
        @if (FileUploadCallBack.HasDelegate)
        {
            <RadzenFileInput ChooseText=Import @ref=@BindingContext.UploadRef @bind-Value=@BindingContext.FileUpload Accept=".xls,.xlsx" TValue=string
                             Disabled=@(!BindingContext.UserCanEdit) Change=@(async args => await BindingContext.OnChange(args)) Error=@(args => BindingContext.OnError(args))/>
        }
        @if (@BindingContext.Title != "")
        {
            <div class="utility-title">
                @BindingContext.Title
            </div>
        }
    </div>

    <div class="col-2 text-center">
        @if (CustomAction.HasDelegate)
        {
            <RadzenButton ButtonStyle=ButtonStyle.Success Text=@CustomButtonText Size=ButtonSize.Medium
                          Click=@(async () => await CustomAction.InvokeAsync())
                          Disabled=@(!BindingContext.UserCanEdit)
                          @ref=BindingContext.CustomButton
                          @onmouseover="@(() => BindingContext.ShowTooltip(BindingContext.CustomButton.Element, CustomToolTipText))"
                          @onmouseleave="@(() => BindingContext.HideTooltip(BindingContext.CustomButton.Element))"/>
        }
    </div>

    <div class="col-5">
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" Gap="0.4rem" Wrap="FlexWrap.Wrap" class="btn-float-right">
                <RadzenButton Icon=@OrderedListCallbackBtnIcon Text=@OrderedListCallbackBtnTxt Size=ButtonSize.Medium Click=@BindingContext.CallbackOrderedList
                              Visible="OrderedListCallback.HasDelegate"
                              Disabled=@(!BindingContext.UserCanEdit || !OrderedListCallback.HasDelegate) ButtonStyle=ButtonStyle.Secondary />
        
                <RadzenButton Text=Excel Icon=grid_on Click=@BindingContext.DownloadExcel Gap="1rem" Size=ButtonSize.Medium
                              Visible="AllowXlsExport"
                              Disabled="!AllowXlsExport" 
                              @ref=BindingContext.ExportExcelButton
                              @onmouseover="@(() => BindingContext.ShowTooltip(BindingContext.ExportExcelButton.Element, GetLocalizedText("UgExceltnTxt")))"
                              @onmouseleave="@(() => BindingContext.HideTooltip(BindingContext.ExportExcelButton.Element))"/>

                <RadzenButton Icon="edit" Text=@GetLocalizedText("UgChangeColumnsBtn") Size=ButtonSize.Medium Click=@BindingContext.OpenColumnEditor
                              Visible="AllowChangeColumns"
                              Disabled=@(!BindingContext.UserCanEdit || !AllowChangeColumns)
                              @ref=BindingContext.ChangeColumnsButton
                              @onmouseover="@(() => BindingContext.ShowTooltip(BindingContext.ChangeColumnsButton.Element, GetLocalizedText("UgChangeColumnsBtnTxt")))"
                              @onmouseleave="@(() => BindingContext.HideTooltip(BindingContext.ChangeColumnsButton.Element))"/>
        </RadzenStack>
    </div>
</div>

<div class=@GetOuterDivClass()>
    <div id=@BindingContext.ContainerInnerDivId >
        <RadzenDataGrid Data=@Data TItem=TItem @ref=@BindingContext.Grid
                        EditMode=DataGridEditMode.Multiple
                        FilterMode=FilterMode.Simple FilterCaseSensitivity=FilterCaseSensitivity.CaseInsensitive
                        CellContextMenu=@(args => ShowContextMenuWithItems(args)) AllowColumnResize="true" 
                        RowSelect=@RowSelectCallBack RowDoubleClick=@BindingContext.RowDoubleClick Filter=@OnFilter FilterCleared=@OnFilter
                        AllowFiltering=@AllowFiltering AllowSorting=@AllowSorting AllowPaging=@(MaxRows != null) PageSize=@(MaxRows ?? 25)>
            <Columns>

                <!-- Actions -->
                @if (Interactive && !DisableEditButton)
                {
                        <RadzenDataGridColumn Width="80px" TItem="TItem" Title="Actions" TextAlign=TextAlign.Center
                                        Filterable=false Sortable=false CssClass="m-0 p-1">
                            <Template Context="item">
                                @if (ContextMenuCondition == null || ContextMenuCondition?.Invoke(item) == true)
                                {
                                    @if (BindingContext.UserCanEdit && (BindingContext.CurrentItem == null || BindingContext.CurrentItem.Equals(item)))
                                    {
                                        <AMproverSplitButton Text=@GetEditButtonText() class="float-right m-0 p-0" Click=@(args => BindingContext.SplitButtonItemClicked(args, item))>
                                            <ChildContent>
                                                @foreach (var item in ContextMenuItems)
                                                {
                                                    <AMproverSplitButtonItem Text=@item.Text Value=@(((int) item.Type).ToString()) Image=@GetFa6SvgPath(item.Icon)/>
                                                }
                                            </ChildContent>
                                        </AMproverSplitButton>
                                    }
                                    else
                                    {
                                        <AMproverSplitButton Text=@GetEditButtonText() class="float-right m-0 p-0" Disabled=!EditCallBackOverride.HasDelegate Click=@(args => EditCallBackOverride.InvokeAsync(item))>
                                            <ChildContent>
                                                @foreach (var item in ContextMenuItems.Where(x => ReadOnlyAllowedCommands.Contains(x.Type)))
                                                {
                                                    <AMproverSplitButtonItem Text=@item.Text Value=@(((int) item.Type).ToString()) Image=@GetFa6SvgPath(item.Icon)/>
                                                }
                                            </ChildContent>
                                        </AMproverSplitButton>
                                    }
                                }
                            </Template>
                        <EditTemplate Context="item">
                            <div class="rz-splitbutton rz-buttonset float-right m-0 p-0">

                                <!-- Save Inline Changes-->
                                <RadzenButton Click=@(() => BindingContext.SaveRow(item))
                                        class="amprover-splitbutton ampover-combined-button"
                                        Text=@GetLocalizedText("UgSaveBtn") type="button"
                                        @ref=BindingContext.SaveButton
                                        @onmouseover="@(() => BindingContext.ShowTooltip(BindingContext.SaveButton.Element, GetLocalizedText("UgSaveBtnTxt")))"
                                        @onmouseleave="@(() => BindingContext.HideTooltip(BindingContext.SaveButton.Element))" />

                                <!-- Cancel Inline Changes-->
                                <RadzenButton Click=@(() => BindingContext.CancelEditRow(item)) type="button"
                                        class="rz-splitbutton-menubutton rz-button-icon-only ampover-combined-button"
                                        @ref=BindingContext.CancelButton
                                        @onmouseover="@(() => BindingContext.ShowTooltip(BindingContext.CancelButton.Element, GetLocalizedText("UgUndoBtnTxt")))"
                                        @onmouseleave="@(() => BindingContext.HideTooltip(BindingContext.CancelButton.Element))">
                                        <i class="fas fa-undo amprover-split-button-right-content"></i>
                                </RadzenButton>

                            </div>
                        </EditTemplate>
                    </RadzenDataGridColumn>
                }

                @foreach (var prop in BindingContext.PropertiesToDisplay)
                {
                        <RadzenDataGridColumn Filterable=@BindingContext.IsFilterable(prop) FilterProperty=@(prop.GetFilterProperty()) 
                                Sortable=@(prop.GridColumn.FieldType != FieldType.Complex) 
                                TItem=TItem Property=@prop.GridColumn.FieldName Title=@(prop.GridColumn.ColumnHeader ?? prop.Header ?? prop.Name)
                                Width=@BindingContext.GetColumnWidth(prop)>

                        <Template Context="item">
                            @BindingContext.GetDisplayValueHtml(item, prop)
                        </Template>
                        
                        <EditTemplate Context="item">
                            @if (prop.IsEditable())
                            {
                                    <div class=@(prop.Valid ? "" : "invalid-cell")>
                                    @if (prop.DropdownOverride != null)
                                    {
                                        @if (prop.Nullable)
                                        {
                                            <AMDropdown 
                                                AllowClear=!prop.Required
                                                TKeyType=int?
                                                Value=@(BindingContext.GetValue<int?>(item, prop))
                                                ValueChanged=@(args => BindingContext.ValueChanged(item, prop.Name, args, !prop.Required))
                                                Data=@prop.DropdownOverride.ToNullableDictionary() 
                                                ContainerClass=@string.Empty/>
                                        }
                                        else
                                        {
                                            <AMDropdown 
                                                AllowClear=!prop.Required
                                                TKeyType=int?
                                                Value=@(BindingContext.GetValue<int>(item, prop))
                                                ValueChanged=@(args => BindingContext.ValueChanged(item, prop.Name, args, !prop.Required))
                                                Data=@prop.DropdownOverride.ToNullableDictionary()
                                                ContainerClass=@string.Empty />
                                        }
                                    }
                                    else if (prop.OptionOverride != null)
                                    {
                                        <AMDropdown 
                                            AllowClear=!prop.Required
                                            Value=@(BindingContext.GetValue<string>(item, prop))
                                            Change=@(args => BindingContext.ValueChanged(item, prop.Name, args, !prop.Required))
                                            Data=@prop.OptionOverride
                                            ContainerClass=@string.Empty />
                                    }
                                    else if (prop.GridColumn.FieldType is FieldType.Text)
                                    {
                                        if (prop.TextArea)
                                        {
                                            <RadzenTextArea Rows=@prop.TextAreaRows Name=@prop.Name class="form-control"
                                                Value=@(BindingContext.GetValue<string>(item, prop))
                                                ValueChanged=@(args => BindingContext.ValueChanged(item, prop.Name, args, !prop.Required))
                                                MaxLength=@prop.MaxLength />
                                        }
                                        else
                                        {
                                            <RadzenTextBox Name=@prop.Name class="form-control"
                                                Value=@(BindingContext.GetValue<string>(item, prop))
                                                ValueChanged=@(args => BindingContext.ValueChanged(item, prop.Name, args, !prop.Required))
                                                MaxLength=@prop.MaxLength />
                                        }
                                    }
                                    else if (prop.GridColumn.FieldType is FieldType.Currency or FieldType.DetailCurrency or FieldType.Percentage or FieldType.Number)
                                    {
                                        <AMproverNumberInput Name=@prop.Name TValue=decimal? Format=@prop.GetFormat()
                                            Value=@(BindingContext.GetValue<decimal?>(item, prop)) UsedInGrid=true
                                            ValueChanged=@(args => BindingContext.ValueChanged(item, prop.Name, args, !prop.Required))
                                            Min=@prop.MinValue Max=@prop.MaxValue class="form-control" />
                                    }
                                    else if (prop.GridColumn.FieldType is FieldType.Integer or FieldType.IntegerNoStyling)
                                    {
                                        <RadzenNumeric Name=@prop.Name TValue=int? class="form-control" Format=@prop.GetFormat()
                                            Value=@(BindingContext.GetValue<int?>(item, prop))
                                            ValueChanged=@(args => BindingContext.ValueChanged(item, prop.Name, args, !prop.Required))
                                            Min=@prop.MinValue Max=@prop.MaxValue />
                                    }

                                    @if (!prop.Valid)
                                    {
                                        <small>Required</small>
                                    }
                                </div>
                            }
                            else
                            {
                                @BindingContext.GetDisplayValue(item, prop)
                            }
                        </EditTemplate>
                        
                    </RadzenDataGridColumn>
                }
            </Columns>
        </RadzenDataGrid>

        @if (Data?.Any() != true)
        {
            <div class="datagrid-context-header" @oncontextmenu=@(args => ShowContextMenuWithoutItems(args)) @oncontextmenu:preventDefault="true"></div>
        }
        <br />
    </div>
</div>


</div>

@code {

    [Parameter]
    public List<TItem> Data { get; set; }

    [Parameter]
    public EventCallback<FileUpload> FileUploadCallBack { get; set; }

    [Parameter]
    public EventCallback<TItem> EditRowCallback { get; set; }

    [Parameter]
    public EventCallback<TItem> OpenLccCallback { get; set; }

    [Parameter]
    public EventCallback<TItem> OpenPopup { get; set; }

    [Parameter]
    public EventCallback<TItem> OpenInfo { get; set; }

    [Parameter]
    public EventCallback AddRowCallBack { get; set; }

    [Parameter]
    public EventCallback<TItem> DeriveRowCallBack { get; set; }

    [Parameter]
    public EventCallback<TItem> CopyFromPmoCallback { get; set; }

    [Parameter]
    public EventCallback<TItem> CopyToPmoRowCallBack { get; set; }

    [Parameter]
    public EventCallback RowSelectCallBack { get; set; }

    [Parameter]
    public EventCallback CustomAction { get; set; }

    [Parameter]
    public EventCallback<TItem> SaveCallback { get; set; }

    [Parameter]
    public EventCallback<TItem> DeleteCallback { get; set; }

    [Parameter]
    public EventCallback<(TItem, CopyType)> PasteCallBack { get; set; }
    
    [Parameter]
    public Func<TItem, bool> ContextMenuCondition { get; set; }

    [Parameter]
    public bool AllowCut { get; set; }

    [Parameter]
    public EventCallback<TItem> CloneCallBack { get; set; }

    [Parameter]
    public EventCallback<TItem> EditCallBackOverride { get; set; }

    [Parameter]
    public EventCallback<TItem> OpenPageCallback { get; set; }

    [Parameter]
    public EventCallback<TItem> AcceptCallback { get; set; }

    [Parameter]
    public EventCallback<TItem> DeclineCallback { get; set; }

    [Parameter]
    public EventCallback<List<TItem>> OrderedListCallback { get; set; }

    [Parameter]
    public string OrderedListCallbackBtnTxt { get; set; }

    [Parameter]
    public string OrderedListCallbackBtnIcon { get; set; }

    [Parameter]
    public string OrderedListCallbackExplanation { get; set; }

    [Parameter]
    public bool DisableEditButton { get; set; }

    [Parameter]
    public string OpenPageTextOverride { get; set; }

    [Parameter]
    public EventCallback<DataGridColumnFilterEventArgs<TItem>> OnFilter { get; set; }

    [Parameter]
    public bool UseOpenTextInsteadOfEdit { get; set; }

    [Parameter]
    public bool Interactive { get; set; }

    [Parameter]
    public bool AllowFiltering { get; set; }

    [Parameter]
    public bool AllowXlsExport { get; set; }

    [Parameter]
    public bool DeleteAreYouSurePopup { get; set; } = true;

    [Parameter]
    public bool AllowChangeColumns { get; set; } = true;

    [Parameter]
    public string CustomButtonText { get; set; }

    [Parameter]
    public string CustomToolTipText { get; set; }

    [Parameter]
    public string Title { get; set; }

    [Parameter]
    public int? MaxRows { get; set; }

    [Parameter]
    public string FileName { get; set; }

    [Parameter]
    public bool AddNewButton { get; set; }

    [Parameter]
    public Action AddNewOverride { get; set; }

    [Parameter]
    public Dictionary<string, Dictionary<int, string>> DropDownOverrides { get; set; }

    [Parameter]
    public Dictionary<string, Dictionary<string, string>> OptionOverrides { get; set; }

    [Parameter]
    public bool AllowSorting { get; set; } = true;

    [Parameter]
    public int? PageSize { get; set; }

    [Parameter]
    public TItem NewItemTemplate { get; set; }

    [Parameter]
    public Func<TItem, bool> ExternalSaveValidation { get; set; }

    [Parameter]
    public EventCallback<TItem> ExternalSaveValidationFailed { get; set; }

    [Parameter]
    public bool DynamicWidth { get; set; }

    [Parameter]
    public string CssClass { get; set; }

    [Parameter]
    public List<string> EditableFields { get; set; }

    [Parameter] public ContextMenuItemType DefaultContextMenuAction { get; set; } = ContextMenuItemType.Edit;

    public List<AMproverContextMenuItem> ContextMenuItems { get; set; } = new List<AMproverContextMenuItem>();

    public List<AMproverContextMenuItem> ContextMenuItemsEditMode { get; set; } = new List<AMproverContextMenuItem>();

    public List<AMproverContextMenuItem> ContextMenuItemsForEmptyRow { get; set; } = new List<AMproverContextMenuItem>();

    private ContextMenuItemType[] ReadOnlyAllowedCommands { get; set; } = new ContextMenuItemType[] { ContextMenuItemType.Open, ContextMenuItemType.EditOverride };

    protected override void OnInitialized()
    {
        GenerateContextMenuListEditMode();
        GenerateContextMenuList();
        GenerateContextMenuListForEmptyRow();
        base.OnInitialized();
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if(firstRender)
        {
            BindingContext.SetDynamicContainerWidth();
        }
    }

    private string GetHeaderDivClass() => DynamicWidth ? $"{CssClass} utilityGrid-dynamic-header" : CssClass;

    private string GetOuterDivClass() => DynamicWidth ? "utilityGrid-dynamic-container" : string.Empty;

    public void ShowContextMenuWithItems(DataGridCellMouseEventArgs<TItem> args)
    {
        if (Interactive)
        {
            if (BindingContext.Grid.IsRowInEditMode(args.Data))
            {
                BindingContext.ShowContextMenuWithItems(args);

                BindingContext._contextMenuService.Open(args, ds =>
                    @<RadzenMenu Click=BindingContext.OnContextMenuItemClicked>
                        @foreach (var contextItem in ContextMenuItemsEditMode)
                        {
                            <RadzenMenuItem Value=@((int) contextItem.Type) Text=@contextItem.Text Image=@GetFa6SvgPath(contextItem.Icon)></RadzenMenuItem>
                        }
                    </RadzenMenu>);
            }
            else
            {
                BindingContext.ShowContextMenuWithItems(args);

                BindingContext._contextMenuService.Open(args, ds =>
                    @<RadzenMenu Click=BindingContext.OnContextMenuItemClicked>
                        @foreach (var contextItem in ContextMenuItems)
                        {
                            if (BindingContext.UserCanEdit || ReadOnlyAllowedCommands.Contains(contextItem.Type))
                            {
                                <RadzenMenuItem Value=@((int) contextItem.Type) Text=@contextItem.Text Image=@GetFa6SvgPath(contextItem.Icon)></RadzenMenuItem>
                            }
                        }
                    </RadzenMenu>);
            }
        }
    }

    void ShowContextMenuWithoutItems(MouseEventArgs args)
    {
        if (Interactive)
        {
            BindingContext.ShowContextMenuWithoutItem(args);

            BindingContext._contextMenuService.Open(args, ds =>
                @<RadzenMenu Click=BindingContext.OnContextMenuItemClicked>
                    @foreach (var contextItem in ContextMenuItemsForEmptyRow)
                    {
                        if (BindingContext.UserCanEdit || ReadOnlyAllowedCommands.Contains(contextItem.Type))
                        {
                            <RadzenMenuItem Value=@((int) contextItem.Type) Text=@contextItem.Text Image=@GetFa6SvgPath(contextItem.Icon)></RadzenMenuItem>
                        }
                    }
            </RadzenMenu>
    );
        }
    }

    public void GenerateContextMenuList()
    {
        if (ContextMenuItems.Any()) return;

        if (!DisableEditButton)
        {
            if (!EditCallBackOverride.HasDelegate)
            {
                ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Edit, Text = GetLocalizedText("UgCtxEdit"), Icon = "pencil" });
            }
            if (EditCallBackOverride.HasDelegate)
            {
                ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.EditOverride, Text = GetLocalizedText("UgCtxOpen"), Icon = "arrow-up-right-from-square" });
            }
        }

        if (OpenPageCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { 
                Type = ContextMenuItemType.OpenPage, 
                Text = OpenPageTextOverride ?? GetLocalizedText("UgCtxOpen"), 
                Icon = "arrow-up-right-from-square" });
        }

        if (OpenPopup.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Open, Text = GetLocalizedText("UgCtxOpenPopup"), Icon = "arrow-up-right-from-square" });
        }

        if (OpenInfo.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Info, Text = GetLocalizedText("UgCtxOpenInfoPopup"), Icon = "arrow-up-left-from-square" });
        }

        if(OpenLccCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.OpenLcc, Text = GetLocalizedText("UgCtxOpenLcc"), Icon = "chart-line-solid"});
        }

        if (CloneCallBack.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.Clone, Text = GetLocalizedText("UgCtxCopy"), Icon = "copy"});
        }

        if (PasteCallBack.HasDelegate)
        {
            if (AllowCut)
            {
                ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Cut, Text = GetLocalizedText("UgCtxCut"), Icon = "scissors" });
            }

            ContextMenuItems.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.Copy, Text = GetLocalizedText("UgCtxCopy"), Icon = "copy"});
            ContextMenuItems.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.Paste, Text = GetLocalizedText("UgCtxPaste"), Icon = "paste"});
        }

        if (DeleteCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.Delete, Text = GetLocalizedText("UgCtxDelete"), Icon = "trash-can"});
        }

        if (DeriveRowCallBack.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.Derive, Text = GetLocalizedText("UgCtxDerive"), Icon = "copy"});
        }

        if (CopyFromPmoCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.CopyFromPmo, Text = GetLocalizedText("UgCtxCopyFromPmo"), Icon = "copy"});
        }

        if (CopyToPmoRowCallBack.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.CopyToPmo, Text = GetLocalizedText("UgCtxCopyToPmo"), Icon = "copy"});
        }

        if(AcceptCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Accept, Text = GetLocalizedText("UgCtxAccept"), Icon = "check" });
        }

        if (DeclineCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Decline, Text = GetLocalizedText("UgCtxDecline"), Icon = "circle-xmark" });
        }

        var defaultItem = ContextMenuItems.Find(x => x.Type == DefaultContextMenuAction);

        if (defaultItem != null)
        {
            ContextMenuItems.Remove(defaultItem);
            ContextMenuItems.Insert(0, defaultItem);
        }
    }

    public void GenerateContextMenuListEditMode()
    {
        if (ContextMenuItemsEditMode.Any()) return;
        ContextMenuItemsEditMode.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.Save, Text = GetLocalizedText("UgCtxSave"), Icon = "check"});
        ContextMenuItemsEditMode.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.Cancel, Text = GetLocalizedText("UgCtxCancel"), Icon = "rotate-left"});
    }

    public void GenerateContextMenuListForEmptyRow()
    {
        if (ContextMenuItemsForEmptyRow.Any()) return;
        if (PasteCallBack.HasDelegate)
        {
            ContextMenuItemsForEmptyRow.Add(new AMproverContextMenuItem {Type = ContextMenuItemType.Paste, Text = GetLocalizedText("UgCtxPaste"), Icon = "paste"});
        }
    }

    public void SelectRow(TItem item)
    {
        BindingContext.Grid.SelectRow(item);
    }

    public void Reload() => BindingContext.Grid.Reload();

    private string GetFa6SvgPath(string name) => $"/svg/{name}.svg";

    private string GetLocalizedText(string key) => $"{_localizationHelper.GetLocalizer<UtilityGrid<TItem>>()[key]}";

    private string GetEditButtonText()
    {
        switch(DefaultContextMenuAction)
        {
            case ContextMenuItemType.Edit:
                return EditCallBackOverride.HasDelegate ? GetLocalizedText("UgCtxOpen") : GetLocalizedText("UgCtxEdit");

            case ContextMenuItemType.OpenPage:
                return GetLocalizedText("UgCtxOpen");

            default: 
            throw new NotImplementedException($"{DefaultContextMenuAction} has not been implemented");
        }
    }
}