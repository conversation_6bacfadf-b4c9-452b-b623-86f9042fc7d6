using AMprover.BlazorApp.Components.SplitButton;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.ContextMenu;
using BlazorDownloadFile;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Radzen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Constants;
using Microsoft.Extensions.Localization;
using AMprover.BusinessLogic.Helpers;
using AMprover.BlazorApp.Pages;

namespace AMprover.BlazorApp.Components.GridTypes.Utility;

public class UtilityGridViewModel<TItem> : BaseGridViewModel<TItem>
{
    [Parameter] public bool Interactive { get; set; }

    [Parameter] public bool DeleteAreYouSurePopup { get; set; } = true;

    [Parameter] public bool DisableEditButton { get; set; }

    [Parameter] public EventCallback<FileUpload> FileUploadCallBack { get; set; }

    [Parameter] public EventCallback<TItem> EditRowCallback { get; set; }

    [Parameter] public EventCallback<TItem> OpenLccCallback { get; set; }

    [Parameter] public EventCallback<(TItem, CopyType)> PasteCallBack { get; set; }

    [Parameter] public EventCallback<TItem> CloneCallBack { get; set; }

    [Parameter] public EventCallback<TItem> OpenPopup { get; set; }
    
    [Parameter] public EventCallback<TItem> OpenInfo { get; set; }

    [Parameter] public EventCallback<TItem> EditCallBackOverride { get; set; }

    [Parameter] public EventCallback<TItem> DeriveRowCallBack { get; set; }

    [Parameter] public EventCallback<TItem> CopyFromPmoCallback { get; set; }

    [Parameter] public EventCallback<TItem> CopyToPmoRowCallBack { get; set; }

    [Parameter] public EventCallback<TItem> OpenPageCallback { get; set; }

    [Parameter] public EventCallback<TItem> AcceptCallback { get; set; }

    [Parameter] public EventCallback<TItem> DeclineCallback { get; set; }

    [Parameter] public EventCallback<List<TItem>> OrderedListCallback { get; set; }

    [Parameter] public string OrderedListCallbackExplanation { get; set; }

    [Parameter] public string Title { get; set; }

    [Parameter] public ContextMenuItemType DefaultContextMenuAction { get; set; } = ContextMenuItemType.Edit;

    [Parameter] public bool DynamicWidth { get; set; }

    // Used in javascript to dynamically scale container width
    public string ContainerInnerDivId { get; set; } = $"UtilityGridInnerDiv-{Guid.NewGuid()}";

    public bool UserCanEdit => _globalDataService.CanEdit;

    private TItem _contextMenuOpenFor { get; set; }

    private readonly IJSRuntime _jsRuntime;
    private readonly IExportManager _exportManager;
    private readonly IBlazorDownloadFileService _downloadFileService;
    public readonly ContextMenuService _contextMenuService;
    private readonly IClipboardManager _clipboardManager;
    private readonly IGlobalDataService _globalDataService;
    private readonly IStringLocalizer _localizer;

    public UtilityGridViewModel(
        ILoggerFactory loggerFactory,
        ILookupManager lookupManager,
        TooltipService toolTipService,
        IJSRuntime jsRuntime,
        IExportManager exportManager,
        IBlazorDownloadFileService downloadFileService,
        DialogService dialogService,
        NavigationManager navigationManager,
        ContextMenuService contextMenuService,
        IClipboardManager clipboardManager,
        IGlobalDataService globalDataService,
        LocalizationHelper localizationHelper) : base(loggerFactory, lookupManager, toolTipService, navigationManager,
        dialogService)
    {
        _jsRuntime = jsRuntime;
        _exportManager = exportManager;
        _downloadFileService = downloadFileService;
        _contextMenuService = contextMenuService;
        _clipboardManager = clipboardManager;
        _globalDataService = globalDataService;
        _localizer = localizationHelper.GetLocalizer<UtilityGrid<TItem>>();
    }

    // Upload Requirements
    public string FileUpload { get; set; }

    public RadzenFileInput<string> UploadRef { get; set; }

    public string UploadError { get; set; }

    public RadzenButton CancelButton { get; set; }
    public RadzenButton CustomButton { get; set; }
    public RadzenButton ExportCsvButton { get; set; }
    public RadzenButton ExportExcelButton { get; set; }
    public RadzenButton NewButton { get; set; }
    public RadzenButton SaveButton { get; set; }

    public async Task OnChange(string value)
    {
        // clear ref, the component wants to display an image preview which does not apply
        UploadError = FileUpload = null;

        var fileUpload = new FileUpload
        {
            FileType = value?.Split(',').FirstOrDefault()?.Trim(),
            Base64Content = value?.Split(',').LastOrDefault()?.Trim()
        };

        await FileUploadCallBack.InvokeAsync(fileUpload).ConfigureAwait(true);
        await _jsRuntime.InvokeAsync<bool>("ClearFileInputValue", UploadRef.Element).ConfigureAwait(true);
    }

    public void OnError(UploadErrorEventArgs args)
    {
        UploadError = args.Message;
    }

    public async Task RowDoubleClick(DataGridRowMouseEventArgs<TItem> args)
    {
        if (Interactive && !DisableEditButton)
        {
            switch (DefaultContextMenuAction)
            {
                case ContextMenuItemType.Edit:
                    if (EditCallBackOverride.HasDelegate)
                    {
                        await EditCallBackOverride.InvokeAsync(args.Data);
                    }
                    else if (_globalDataService.CanEdit)
                    {
                        if (CurrentItem == null)
                        {
                            await EditRow(args.Data);
                        }
                        else
                        {
                            await SaveRow(args.Data);
                        }
                    }
                    break;

                case ContextMenuItemType.OpenPage:
                    if (OpenPageCallback.HasDelegate)
                    {
                        await OpenPageCallback.InvokeAsync(args.Data);
                    }
                    break;

                default:
                    throw new NotImplementedException($"{DefaultContextMenuAction} has not been implemented");
            }
        }
    }

    // Context Menu
    public void ShowContextMenuWithItems(DataGridCellMouseEventArgs<TItem> args)
    {
        if (Interactive)
            _contextMenuOpenFor = args == default ? default : args.Data;
    }

    public void ShowContextMenuWithoutItem(MouseEventArgs args)
    {
        if (Interactive)
            _contextMenuOpenFor = default;
    }

    public async Task SplitButtonItemClicked(AMproverSplitButtonItem args, TItem item)
    {
        _contextMenuOpenFor = item;
        await ContextMenuItemCLickedInternal(
            (ContextMenuItemType) int.Parse(args?.Value ?? $"{(int)DefaultContextMenuAction}"));
    }

    public async Task OnContextMenuItemClicked(MenuItemEventArgs args)
    {
        await ContextMenuItemCLickedInternal((ContextMenuItemType) args.Value);
    }

    private async Task ContextMenuItemCLickedInternal(ContextMenuItemType itemType)
    {
        switch (itemType)
        {
            case ContextMenuItemType.Edit:
                if (EditCallBackOverride.HasDelegate)
                    await EditCallBackOverride.InvokeAsync(_contextMenuOpenFor);
                else
                    await EditRow(_contextMenuOpenFor);
                break;

            case ContextMenuItemType.EditOverride:
                await EditCallBackOverride.InvokeAsync(_contextMenuOpenFor);
                break;

            case ContextMenuItemType.Copy:
                _clipboardManager.CopyItem(_contextMenuOpenFor);
                break;

            case ContextMenuItemType.Cut:
                _clipboardManager.CutItem(_contextMenuOpenFor);
                break;

            case ContextMenuItemType.Paste:
                if (PasteCallBack.HasDelegate)
                {
                    var (item, copyType) = _clipboardManager.GetCopiedItem<TItem>();
                    if (item != null)
                    {
                        await PasteCallBack.InvokeAsync((item, copyType));
                    }
                    else
                    {
                        _dialogService.Open<InformationDialog>(GetLocalizedText("UgNoItemCopied"),
                            new Dictionary<string, object>
                            {
                                {"DialogContent", GetLocalizedText("UgCopyFirst")}
                            });
                    }
                }

                break;

            case ContextMenuItemType.Delete:
                if (DeleteCallback.HasDelegate)
                {
                    if (DeleteAreYouSurePopup)
                    {
                        _dialogService.Open<AreYouSureDialog<TItem>>(GetLocalizedText("UgAreYouSure"),
                            new Dictionary<string, object>
                            {
                                {nameof(AreYouSureDialog<TItem>.Item), _contextMenuOpenFor},
                                {
                                    nameof(AreYouSureDialog<TItem>.YesCallback),
                                    EventCallback.Factory.Create<TItem>(this, ConfirmDelete)
                                }
                            });
                    }
                    else
                    {
                        ConfirmDelete(_contextMenuOpenFor);
                    }
                }

                break;

            case ContextMenuItemType.Open:
                if (OpenPopup.HasDelegate)
                    await OpenPopup.InvokeAsync(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.Info:
                if (OpenInfo.HasDelegate)
                    await OpenInfo.InvokeAsync(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.OpenPage:
                await OpenPageCallback.InvokeAsync(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.Save:
                await SaveRow(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.Cancel:
                CancelEditRow(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.Derive:
                await DeriveRowCallBack.InvokeAsync(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.CopyFromPmo:
                await CopyFromPmoCallback.InvokeAsync(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.CopyToPmo:
                await CopyToPmoRowCallBack.InvokeAsync(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.Clone:
                await CloneCallBack.InvokeAsync(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.OpenLcc:
                await OpenLccCallback.InvokeAsync(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.Accept:
                await AcceptCallback.InvokeAsync(_contextMenuOpenFor);
                break;
            case ContextMenuItemType.Decline:
                await DeclineCallback.InvokeAsync(_contextMenuOpenFor);
                break;
            default:
                throw new NotImplementedException();
        }

        _contextMenuOpenFor = default;
        _contextMenuService.Close();
    }

    private void ConfirmDelete(TItem item) => DeleteCallback.InvokeAsync(item);

    // Downloads
    public async Task DownloadExcel()
    {
        var stream = _exportManager.ExportExcel(Data, TableColumns);

        if (stream != null)
            _ = await _downloadFileService.DownloadFile($"{FileName}_{DateTime.Now.ToDateString(Language)}.xlsx",
                stream, FileContentTypes.Excel);
    }

    // Settings
    public bool IsFilterable(ColumnData column)
    {
        switch (column?.GridColumn?.FieldType)
        {
            case BusinessLogic.Enums.FieldType.Text:
            case BusinessLogic.Enums.FieldType.Number:
            case BusinessLogic.Enums.FieldType.Currency:
            case BusinessLogic.Enums.FieldType.IntegerNoStyling:
            case BusinessLogic.Enums.FieldType.Integer:
            case BusinessLogic.Enums.FieldType.Enum:
                return true;

            default:
                return false;
        }
    }

    // Column Editor
    public void OpenColumnEditor()
    {
        _dialogService.Open<TableColumns>("Table columns",
            new Dictionary<string, object>
            {
                {"ControlName", FileName},
                {"ReseedFunction", EventCallback.Factory.Create<object>(this, ReSeedColumns)},
                {"SaveCallback", EventCallback.Factory.Create<object>(this, SaveGridColumnsCallback)}
            },
            new DialogOptions {Width = "880px", Resizable = true, Draggable = true});
    }

    public async Task CallbackOrderedList()
    {
        _dialogService.Open<AreYouSureDialog<int>>(GetLocalizedText("UgAreYouSure"),
            new Dictionary<string, object>
            {
                {
                    nameof(AreYouSureDialog<int>.Text),
                    OrderedListCallbackExplanation
                },
                {
                    nameof(AreYouSureDialog<int>.YesCallback),
                    EventCallback.Factory.Create<int>(this, PerformOrderListCallBack)
                }
            });
    }

    public async Task PerformOrderListCallBack(int i)
    {
        if (OrderedListCallback.HasDelegate)
        {
            var ordered = Grid.PagedView.ToList();
            await OrderedListCallback.InvokeAsync(ordered);
        }
    }

    private void ReSeedColumns(object _)
    {
        _dialogService.Close();
        _lookupManager.SeedGridColumns<TItem>(FileName);
        Grid.Reload();
        Initialize();
        SetDynamicContainerWidth();
    }

    private void SaveGridColumnsCallback(object _)
    {
        Grid.Reload();
        Initialize();
        SetDynamicContainerWidth();
    }

    public void SetDynamicContainerWidth()
    {
        if (DynamicWidth)
        {
            var widthFromColumns = PropertiesToDisplay.Sum(x => x?.GridColumn?.ColumnWidth ?? 0);
            Task.Run(async () =>
                await _jsRuntime.InvokeVoidAsync("SetUtilityGridInnerContainerWidth", widthFromColumns,
                    ContainerInnerDivId));
        }
    }

    // Localization
    private string GetLocalizedText(string key) => $"{_localizer[key]}";

    public void RefreshDropdownOverrides(Dictionary<string, Dictionary<int, string>> dropdownOverrides)
    {
        DropDownOverrides = dropdownOverrides;
        Initialize();
    }
}