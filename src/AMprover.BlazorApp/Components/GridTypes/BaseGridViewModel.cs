using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Attributes;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using Microsoft.AspNetCore.Components;
using <PERSON><PERSON>zen;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using Microsoft.Extensions.Logging;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Components.GridTypes;

public class BaseGridViewModel<TItem> : BaseViewModel
{
    protected ILookupManager _lookupManager;

    protected NavigationManager _navigationManager;

    [Parameter] public Dictionary<string, Dictionary<int, string>> DropDownOverrides { get; set; }

    [Parameter] public Dictionary<string, Dictionary<string, string>> OptionOverrides { get; set; }

    [Parameter] public string FileName { get; set; }

    [Parameter] public List<TItem> Data { get; set; }

    [Parameter] public Action AddNewOverride { get; set; }

    [Parameter] public EventCallback<TItem> SaveCallback { get; set; }

    [Parameter] public EventCallback<TItem> DeleteCallback { get; set; }

    [Parameter] public TItem NewItemTemplate { get; set; }

    [Parameter] public Func<TItem, bool> ExternalSaveValidation { get; set; }

    [Parameter] public EventCallback<TItem> ExternalSaveValidationFailed { get; set; }

    [Parameter]
    public List<string> EditableFields { get; set; }

    public List<ColumnData> Properties { get; set; }

    public List<ColumnData> PropertiesToDisplay { get; set; }

    public List<GridColumnModel> TableColumns { get; set; }

    public RadzenButton ChangeColumnsButton { get; set; }

    public BaseGridViewModel(
        ILoggerFactory loggerFactory, 
        ILookupManager lookupManager, 
        TooltipService toolTipService,
        NavigationManager navigationManager,
        DialogService dialogService) : base(loggerFactory, toolTipService, lookupManager)
    {
        _lookupManager = lookupManager;
        _navigationManager = navigationManager;
        _dialogService = dialogService;
    }

    public override void OnInitialized()
    {
        BackupItem = (TItem)Activator.CreateInstance(typeof(TItem));
        Initialize();
    }

    protected void Initialize()
    {
        Properties = GetProperties();
        TableColumns = ForceGetTableColumns();

        foreach (var prop in Properties)
            prop.GridColumn = TableColumns.FirstOrDefault(x => x.ColumnName == prop.Name);

        PropertiesToDisplay = Properties
            .Where(x => x.GridColumn?.Visible == true)
            .OrderBy(x => x.GridColumn.DisplayIndex).ThenBy(x => x.Name)
            .ToList();
    }

    public string GetColumnWidth(ColumnData columnData)
    {
        if (columnData.Width > 0)
            return $"{columnData.Width}px";

        return $"{columnData.GridColumn.ColumnWidth}%";
    }

    protected List<GridColumnModel> ForceGetTableColumns()
    {
        var result = _lookupManager.GetColumns(FileName, false).ToList();
        return result.Any() ? result : _lookupManager.SeedGridColumns<TItem>(FileName);
    }

    private List<ColumnData> GetProperties()
    {
        var properties = new List<ColumnData>();
        foreach (var prop in typeof(TItem).GetProperties())
        {
            // Attributes
            Attribute[] attrs = Attribute.GetCustomAttributes(prop);

            if (attrs.Any(x => x is GridIgnoreAttribute))
                continue;

            // result
            var columnData = new ColumnData
            {
                Name = prop.Name,
                Nullable = Nullable.GetUnderlyingType(prop.PropertyType) != null
            };

            foreach (var attr in attrs)
            {
                if (attr is GridWidthPixelsAttribute w)
                    columnData.Width = w.Width;

                if (attr is GridHeaderAttribute h)
                    columnData.Header = h.Header;

                if (attr is RequiredAttribute r)
                    columnData.Required = true;

                if (attr is MaxLengthAttribute m)
                    columnData.MaxLength = m.Length;

                if (attr is GridNumericFormatAttribute nf)
                    columnData.NumericFormat = nf.Format;

                if (attr is GridStringEnumAttribute ea)
                {
                    if (ea?.ValuesAsEnum == null || !ea.ValuesAsEnum.IsEnum)
                        throw new ArgumentException($"GridStringEnumAttribute can only have an Enum as parameter. Currenlty it is `{ea?.ValuesAsEnum?.GetType()}`");

                    var values = Enum.GetValues(ea.ValuesAsEnum)
                        .Cast<object>()
                        .Select(x => x.ToString())
                        .ToList();

                    columnData.OptionOverride = values.ToDictionary(x => x);
                }
                if (attr is RangeAttribute ra)
                {
                    columnData.MinValue = Convert.ToDecimal(ra.Minimum);
                    columnData.MaxValue = Convert.ToDecimal(ra.Maximum);
                }

                if (attr is GridTextAreaAttribute t)
                {
                    columnData.TextArea = true;
                    columnData.TextAreaRows = t.Lines;
                }

                if(attr is GridFilterProperty f)
                {
                    columnData.FilterProperty = f.FilterProperty;
                }
            }

            // Auto gen enum dropdown values
            var type = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
            if (type.IsEnum)
            {
                columnData.DropdownOverride = new Dictionary<int, string>();

                foreach (var val in Enum.GetValues(type))
                    columnData.DropdownOverride.Add(Convert.ToInt32(val), val.ToString());
            }

            columnData.Editable = !attrs.Any(x => x is GridReadOnlyAttribute);
            properties.Add(columnData);
        }

        if (DropDownOverrides != null)
        {
            foreach (var pair in DropDownOverrides)
            {
                var column = properties.FirstOrDefault(x => x.Name == pair.Key);

                if (column != null)
                {
                    column.DropdownOverride = pair.Value;
                }
            }
        }

        if (OptionOverrides != null)
        {
            foreach (var pair in OptionOverrides)
            {
                var column = properties.FirstOrDefault(x => x.Name == pair.Key);

                if (column != null)
                {
                    column.OptionOverride = pair.Value;
                }
            }
        }

        if(EditableFields?.Any() == true)
        {
            properties.ForEach(x => x.Editable = false);

            foreach(var prop in properties.Where(x => EditableFields.Contains(x.Name)))
                prop.Editable = true;
        }

        return properties;
    }

    public TItem CurrentItem { get; set; }

    public TItem BackupItem { get; set; }

    public RadzenDataGrid<TItem> Grid { get; set; }

    public MarkupString GetDisplayValueHtml(TItem item, ColumnData columnData)
    {
        var (val, displayValue) = GetDisplayValue(item, columnData);
        var cssClass = GetCssClassDisplayValue(val, displayValue, columnData);
        var html = string.Format("<div class=\"{0}\">{1}</div>", cssClass, displayValue);
        return new MarkupString(html);
    }

    private string GetCssClassDisplayValue(object val, string displayValue, ColumnData columnData)
    {
        return columnData.GetFieldType() switch
        {
            FieldType.Currency => "text-right",
            FieldType.Text => GetTextCss(displayValue, columnData),
            _ => string.Empty
        };
    }

    private readonly Dictionary<string, string> CssDict = new()
    {
        { "A", "red-cell" },
        { "B", "orange-cell" },
        { "C", "green-cell" }
    };

    private string GetTextCss(string displayValue, ColumnData columnData)
    {
        if(columnData?.GridColumn?.ControlName == "Criticality_Rankings" && columnData.Name == "Category")
        {
            CssDict.TryGetValue(displayValue, out string css);
            return css;
        }

        return string.Empty;
    }

    /// <summary>
    /// Will be used for more coloring of cells
    /// </summary>
    private static bool IsGreaterThan(object value, double threshold)
    {
        return value switch
        {
            int i => i > threshold,
            long l => l > threshold,
            float f => f > threshold,
            double d => d > threshold,
            decimal m => m > (decimal)threshold,
            _ => false
        };
    } 

    public (object, string) GetDisplayValue(TItem item, ColumnData columnData)
    {
        // Dropdown
        if (columnData.DropdownOverride != null)
        {
            var index = GetValue<int?>(item, columnData);
            if (index != null)
            {
                return columnData.DropdownOverride.ContainsKey(index.Value)
                    ? (null, columnData.DropdownOverride[index.Value])
                    : (null, string.Empty);
            }
        }

        // String Dropdown
        if (columnData.OptionOverride != null)
        {
            var val = GetValue<string>(item, columnData);
            if (val != null)
            {
                return columnData.OptionOverride.ContainsKey(val)
                    ? (null, columnData.OptionOverride[val])
                    : (null, string.Empty);
            }
        }

        // Value as object
        var fields = GetFieldsRecursive(item);
        var field = fields.FirstOrDefault(x => x.IsField(columnData.Name));
        var value = field?.GetValue(item) ?? string.Empty;

        var stringResult = columnData.GetFieldType() switch
        {
            FieldType.Complex => value.ToString(),
            FieldType.None => value.ToString(),
            FieldType.Text => value.ToString(),
            FieldType.Number => value.ToNumericDisplayValue(Language, GetFormatString(columnData, "n2")),
            FieldType.Currency => value.ToNumericDisplayValue(Currency, GetFormatString(columnData, "c0")),
            FieldType.DetailCurrency => value.ToNumericDisplayValue(Currency, GetFormatString(columnData, "c2")),
            FieldType.Percentage => value.ToNumericDisplayValue(Language, GetFormatString(columnData, "p2")),
            FieldType.Date => DateTime.TryParse(value.ToString(), out var date) ? date.ToDateString(Language) : string.Empty,
            FieldType.Integer => value.ToNumericDisplayValue(Language, GetFormatString(columnData, "n0")),
            FieldType.IntegerNoStyling => $"{value}",
            FieldType.Boolean => GetBooleanFormat(item, field),
            _ => value.ToString(),
        };

        return (value, stringResult);
    }

    private string GetBooleanFormat(TItem item, FieldInfo field)
    {
        var value = field?.GetValue(item);
        if(value is bool b)
        {
            return b ? "✅" : "❌";
        }
        return "❌";
    }

    private string GetFormatString(ColumnData columnData, string defaultFormat)
    {
        return string.IsNullOrWhiteSpace(columnData?.NumericFormat)
            ? defaultFormat
            : columnData.NumericFormat;
    }

    private FieldInfo[] GetFieldsRecursive(TItem item)
    {
        var fields = item.GetType().GetFields(BindingFlags.NonPublic | BindingFlags.Instance);
        var parentType = item.GetType().BaseType;

        while (parentType != typeof(object))
        {
            fields = fields.AsEnumerable().Concat(parentType.GetFields(BindingFlags.NonPublic | BindingFlags.Instance)).ToArray();
            parentType = parentType.BaseType;
        }

        return fields;
    }

    public T GetValue<T>(TItem item, ColumnData columnData)
    {
        var fields = GetFieldsRecursive(item);

        var field = fields.FirstOrDefault(x => x.IsField(columnData.Name));
        var value = field?.GetValue(item);

        var t = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);

        return value != null
            ? (T)Convert.ChangeType(value, t)
            : default;
    }

    public void ValueChanged(TItem item, string prop, object input, bool allowClear)
    {
        var p = item.GetType().GetProperty(prop);
        if (p == null) return;
        var type = Nullable.GetUnderlyingType(p.PropertyType) ?? p.PropertyType;

        if (input == null && !allowClear && (type == typeof(int) || type.IsEnum))
            return;

        if (input == null)
        {
            p.SetValue(item, null);
        }
        else if (p.PropertyType == typeof(int) || p.PropertyType == typeof(int?))
        {
            var intValue = Convert.ToInt32(input);
            p.SetValue(item, intValue);
        }
        else if (p.PropertyType.IsEnum)
        {
            p.SetValue(item, Enum.Parse(p.PropertyType, input.ToString()));
        }
        else if (Nullable.GetUnderlyingType(p.PropertyType)?.IsEnum == true)
        {
            p.SetValue(item, Enum.Parse(Nullable.GetUnderlyingType(p.PropertyType), input.ToString()));
        }
        else if (p.PropertyType == typeof(decimal) || p.PropertyType == typeof(decimal?))
        {
            var decimalValue = Convert.ToDecimal(input);
            p.SetValue(item, decimalValue);
        }
        else if (p.PropertyType == typeof(double) || p.PropertyType == typeof(double?))
        {
            var doubleValue = Convert.ToDouble(input);
            p.SetValue(item, doubleValue);
        }
        else if (p.PropertyType == typeof(string))
        {
            p.SetValue(item, input);
        }
    }

    public async Task AddNew()
    {
        if (AddNewOverride != null)
            AddNewOverride.Invoke();

        else
        {
            CurrentItem = (TItem)Activator.CreateInstance(typeof(TItem));

            if (NewItemTemplate != null)
                CopyValues(NewItemTemplate, CurrentItem);

            await Grid.InsertRow(CurrentItem).ConfigureAwait(false);

            // It is impossible to edit details on a new entry when the original collection was empty. This 'fixes' that
            if (!Data.Any())
                Data.Add(CurrentItem);
        }
    }

    public async Task EditRow(TItem item)
    {
        // When trying to edit a row, while another row is already in edit mode, this only works if the row currently being edited is valid
        // Otherwise trying to save the row will fail, and therefore no new row should be allowed to go in edit mode.
        var canEdit = CurrentItem == null || await SaveRow(CurrentItem);

        if (canEdit)
        {
            CurrentItem = item;
            Properties.ForEach(x => x.Valid = true);
            CopyValues(item, BackupItem);
            await Grid.EditRow(item).ConfigureAwait(false);
        }
    }

    public async Task<bool> SaveRow(TItem item)
    {
        Properties.ForEach(x => x.Valid = true);
        var type = item.GetType();
        
        // Get both fields and properties
        var fields = type.GetFields(BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Public);
        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        var parentType = type.BaseType;

        // Include parent class fields and properties if in the same namespace
        if (parentType != null && parentType.Namespace == type.Namespace)
        {
            var parentFields = parentType.GetFields(BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Public);
            var parentProperties = parentType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            fields = fields.Concat(parentFields).ToArray();
            properties = properties.Concat(parentProperties).ToArray();
        }

        foreach (var prop in Properties.Where(x => x.Editable && x.Required))
        {
            try
            {
                // Try to find field or property with matching name (case-insensitive)
                var field = fields.FirstOrDefault(f => 
                    string.Equals(f.Name, $"<{prop.Name}>k__BackingField", StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(f.Name, prop.Name, StringComparison.OrdinalIgnoreCase));
                    
                var property = properties.FirstOrDefault(p => 
                    string.Equals(p.Name, prop.Name, StringComparison.OrdinalIgnoreCase));

                object value = null;
                if (field != null)
                {
                    value = field.GetValue(item);
                }
                else if (property != null && property.CanRead)
                {
                    value = property.GetValue(item);
                }

                // Handle different types of values
                if (value == null)
                {
                    prop.Valid = false;
                }
                else if (value is string strValue)
                {
                    prop.Valid = !string.IsNullOrWhiteSpace(strValue);
                }
                else if (value is int intValue)
                {
                    prop.Valid = intValue != 0; // Assuming 0 is not a valid value for required int fields
                }
                else if (value is decimal decimalValue)
                {
                    prop.Valid = decimalValue != 0; // Assuming 0 is not a valid value for required decimal fields
                }
                else
                {
                    // For other types (like enums, objects, etc.), consider them valid if not null
                    prop.Valid = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating property {PropName}", prop.Name);
                prop.Valid = false;
            }
        }

        if (Properties.All(x => x.Valid))
        {
            if(ExternalSaveValidation != null)
            {
                var passedExtraValidation = ExternalSaveValidation.Invoke(item);
                if (!passedExtraValidation)
                {
                    if (ExternalSaveValidationFailed.HasDelegate)
                        await ExternalSaveValidationFailed.InvokeAsync(item);

                    return false;
                }
            };

            await SaveCallback.InvokeAsync(item);
            Grid.CancelEditRow(item);
            CurrentItem = default;
            return true;
        }

        return false;
    }

    public void CancelEditRow(TItem item)
    {
        CopyValues(BackupItem, item);
        Grid.CancelEditRow(item);
        Properties.ForEach(x => x.Valid = true);
        CurrentItem = default;
    }

    private void CopyValues(TItem source, TItem target)
    {
        foreach (PropertyInfo property in typeof(TItem).GetProperties().Where(p => p.CanWrite))
        {
            property.SetValue(target, property.GetValue(source, null), null);
        }
    }

    public async Task DeleteRow(TItem item)
    {
        Properties.ForEach(x => x.Valid = true);
        await DeleteCallback.InvokeAsync(item).ConfigureAwait(false);

        CopyValues(BackupItem, item);
        Grid.CancelEditRow(item);
        CurrentItem = default;
    }

    public void EditItem(TItem item)
    {
        CurrentItem = item;
    }
}
