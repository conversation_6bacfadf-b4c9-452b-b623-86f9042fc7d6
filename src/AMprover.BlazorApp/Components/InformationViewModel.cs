using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;
using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;

namespace AMprover.BlazorApp.Components;

public class InformationViewModel : BaseViewModel
{
    public InformationViewModel(ILoggerFactory loggerFactory, DialogService dialogService, TooltipService tooltipService, ILookupManager lookupManager) : base(loggerFactory, tooltipService, lookupManager)
    {
        _dialogService = dialogService;
    }

    [Parameter] public string DialogTitle { get; set; }

    [Parameter] public string DialogContent { get; set; }

    public void OpenInformationDialog()
    {
        _dialogService.Open<InformationDialog>(DialogTitle, new Dictionary<string, object> { { "DialogContent", DialogContent } },
        new DialogOptions() { Width = "800px", Resizable = false, Draggable = true });
    }
}