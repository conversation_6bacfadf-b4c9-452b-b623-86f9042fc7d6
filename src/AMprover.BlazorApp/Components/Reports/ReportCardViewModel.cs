using AMprover.BlazorApp.Helpers;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BlazorApp.Pages.Report;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums.Reports;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;

namespace AMprover.BlazorApp.Components.Reports;

public class ReportCardViewModel : BaseViewModel
{
    private readonly NavigationManager _navigationManager;

    public ReportCardViewModel(
        ILoggerFactory loggerFactory,
        DialogService dialogService, 
        ILookupManager lookupManager, 
        NavigationManager navigationManager) : base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _navigationManager = navigationManager;
    }

    [Parameter]
    public ReportViewQueryParams QueryParams { get; set; }

    public void ViewReport(ReportType reportType)
    {
        var url = $"/reports/{reportType}";

        if (QueryParams.CollectionFilter.HasValue)
            url = url.AddQueryString("CollectionFilter", QueryParams.CollectionFilter.ToString());
        
        if (QueryParams.InstallationFilter.HasValue)
            url = url.AddQueryString("InstallationFilter", QueryParams.InstallationFilter.ToString());
        
        if (QueryParams.SystemFilter.HasValue)
            url = url.AddQueryString("SystemFilter", QueryParams.SystemFilter.ToString());
        
        if (QueryParams.ScenarioFilter.HasValue)
            url = url.AddQueryString("ScenarioFilter", QueryParams.ScenarioFilter.ToString());
        
        if (QueryParams.SiCategoryFilter.HasValue)
            url = url.AddQueryString("SiCategoryFilter", QueryParams.SiCategoryFilter.ToString());
        
        _navigationManager.NavigateTo(url, true);
    }
}