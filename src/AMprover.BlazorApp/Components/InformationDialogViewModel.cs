using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;

namespace AMprover.BlazorApp.Components;

public class InformationDialogViewModel : BaseViewModel
{
    public InformationDialogViewModel(ILoggerFactory loggerFactory, DialogService dialogService, TooltipService tooltipService, ILookupManager lookupManager) : base(loggerFactory, tooltipService, lookupManager)
    {
        _dialogService = dialogService;
    }

    [Parameter] public string DialogTitle { get; set; }
    [Parameter] public string DialogContent { get; set; }
}