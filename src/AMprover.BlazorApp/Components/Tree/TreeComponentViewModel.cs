using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Components.Tree;

public class TreeComponentViewModel<T> : BaseViewModel
{
    private TreeGeneric<T> _treeview;
    [Parameter] public TreeGeneric<T> Treeview 
    { 
        get => _treeview;
        set
        {
            _treeview = value;
            _treeview.DeleteCallback = DeleteCallback;
            _treeview.NewCallback = NewCallback;
            _treeview.PasteCallback = PasteCallback;
            _treeview.ChangeOrderCallback = ChangeOrderCallback;
            _treeview.NodeClickCallback = NodeClickCallback;
            _treeview.NodeCollapseCallback = NodeCollapseCallback;
            _treeview.NodeExpandCallback = NodeExpandCallback;
            _treeview.OpenCallback = OpenCallback;
            _treeview.SetSelectedNode = EventCallback.Factory.Create<object>(this, SetSelectedNode);
            _treeview.HandleCutPasteInternally = HandleCutPasteInternally;
            _treeview.CascadeDeleteParentsWithNoOtherChildren = CascadeDeleteParentsWithNoOtherChildren;
            _treeview.ExpandNodeWhenSelected = ExpandNodeWhenSelected;
            _treeview.GenerateContextMenuList();
        }
    }

    [Parameter] public EventCallback<TreeNodeGeneric<T>> NodeClickCallback { get; set; }

    [Parameter] public EventCallback<TreeNodeGeneric<T>> NodeExpandCallback { get; set; }

    [Parameter] public EventCallback<TreeNodeGeneric<T>> NodeCollapseCallback { get; set; }

    [Parameter] public EventCallback<T> DeleteCallback { get; set; }

    [Parameter] public EventCallback<T> NewCallback { get; set; }

    [Parameter] public EventCallback<T> OpenCallback { get; set; }

    [Parameter] public EventCallback<(List<T>, T)> PasteCallback { get; set; }

    [Parameter] public EventCallback<List<TreeNodeGeneric<T>>> ChangeOrderCallback { get; set; }

    [Parameter] public bool HandleCutPasteInternally { get; set; }

    [Parameter] public bool CascadeDeleteParentsWithNoOtherChildren { get; set; }

    [Parameter] public bool ExpandNodeWhenSelected { get; set; } = true;

    private void SetSelectedNode(object newSelected)
    {
        Treeview.SelectNode((TreeNodeGeneric<T>)newSelected);
    }
}