using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.ContextMenu;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Ra<PERSON>zen;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models.RiskAnalysis;

namespace AMprover.BlazorApp.Components.Tree;

public class TreeNodeComponentViewModel<T> : BaseViewModel
{
    [Parameter] public int Depth { get; set; }

    [Parameter] public TreeNodeGeneric<T> Node { get; set; }

    [Parameter] public TreeGeneric<T> Tree { get; set; }

    [Parameter] public TreeNodeGeneric<T> ParentNode { get; set; }

    protected readonly ContextMenuService _contextMenuService;

    protected readonly IClipboardManager _clipboardManager;

    public TreeNodeComponentViewModel(
        ContextMenuService contextMenuService,
        DialogService dialogService,
        IClipboardManager clipboardManager)
    {
        _contextMenuService = contextMenuService;
        _dialogService = dialogService;
        _clipboardManager = clipboardManager;
    }

    public override void OnInitialized()
    {
        Node.Parent = ParentNode;
        base.OnInitialized();
    }

    public void OnContextMenuItemClicked(MenuItemEventArgs args)
    {
        ContextMenuItemCLickedInternal((ContextMenuItemType)args.Value);
        _contextMenuService.Close();
    }

    public void ContextMenuItemCLickedInternal(ContextMenuItemType itemType)
    {
        switch (itemType)
        {
            case ContextMenuItemType.Delete:
                if(Tree.DeleteCallback.HasDelegate)
                {
                    var flattendNodes = Node.Flatten(x => x.Nodes).Select(x => x.Source).ToList();

                    if(flattendNodes.Count > 1)
                    {
                        _dialogService.Open<AreYouSureDialog<T>>($"WARNING! deleting multiple items.",
                            new Dictionary<string, object>
                            {
                                { nameof(AreYouSureDialog<T>.Items), flattendNodes.Select(x => x.ToString()).ToList() },
                                { nameof(AreYouSureDialog<T>.YesCallback), EventCallback.Factory.Create<T>(this, ConfirmDelete) }
                            });
                    }
                    else
                    {
                        _dialogService.Open<AreYouSureDialog<T>>($"Are you sure?",
                            new Dictionary<string, object>
                            {
                                { nameof(AreYouSureDialog<T>.Item), Node.Source },
                                { nameof(AreYouSureDialog<T>.YesCallback), EventCallback.Factory.Create<T>(this, ConfirmDelete) }
                            });
                    }
                }
                break;

            case ContextMenuItemType.Copy or 
                 ContextMenuItemType.Cut:
                _clipboardManager.CopyItem(Node);
                break;

            case ContextMenuItemType.Paste:
                var (item, copyType) = _clipboardManager.GetCopiedItem<TreeNodeGeneric<T>>();
                if (item != null)
                {
                    if (Tree.HandleCutPasteInternally)
                    {
                        var parent = Node.Parent;
                        while (parent != null)
                        {
                            if(parent == item)
                            {
                                _dialogService.Open<InformationDialog>("Not possible", new Dictionary<string, object> {
                                    { "DialogContent", "You can not paste a Tree Node onto one of it's descendants." }
                                });
                                return;
                            }
                            parent = parent.Parent;
                        }
                        item.Parent.Nodes.Remove(item);
                        Node.Nodes.Add(item);
                        _clipboardManager.ClearCopiedItem<TreeNodeGeneric<T>>();
                    }

                    var nodesAsList = item.Flatten(n => n.Nodes).Select(n => n.Source).ToList();
                    if (Tree.PasteCallback.HasDelegate)
                        Tree.PasteCallback.InvokeAsync((nodesAsList, Node.Source));
                }
                else
                {
                    _dialogService.Open<InformationDialog>("No item copied", new Dictionary<string, object> {
                        { "DialogContent", "You should first copy an Item of the same type before you can paste it." }
                    });
                }
                break;

            case ContextMenuItemType.New:
                Tree.NewCallback.InvokeAsync(Node.Source);
                break;

            case ContextMenuItemType.Open:
                Tree.OpenCallback.InvokeAsync(Node.Source);
                break;

            case ContextMenuItemType.MoveDown:
                MoveDown();
                break;

            case ContextMenuItemType.MoveUp:
                MoveUp();
                break;

            default:
                break;
        }
    }

    private void MoveDown()
    {
        var index = Node.Parent.Nodes.IndexOf(Node);
        MoveNodeInternal(index, index + 1);
    }

    private void MoveUp()
    {
        var index = Node.Parent.Nodes.IndexOf(Node);
        MoveNodeInternal(index - 1, index);
    }

    private void MoveNodeInternal(int firstIndex, int nextIndex)
    {
        var nodes = Node.Parent.Nodes;
        if(firstIndex >= 0 && nextIndex < nodes.Count)
        {
            (nodes[firstIndex], nodes[nextIndex]) = (nodes[nextIndex], nodes[firstIndex]);

            for (int i = 0; i < nodes.Count; i++)
            {
                nodes[i].SortOrder = i;
            }

            Tree.ChangeOrderCallback.InvokeAsync(nodes);
        }
    }

    public void ConfirmDelete(T node)
    {
        ParentNode.Nodes.Remove(Node);

        if(Tree.CascadeDeleteParentsWithNoOtherChildren)
        {
            var nodeToCheck = Node.Parent;
            while (true)
            {
                if (!nodeToCheck.Nodes.Any())
                {
                    if (nodeToCheck.Parent == null)
                        break;

                    nodeToCheck.Parent.Nodes.Remove(nodeToCheck);
                    nodeToCheck = nodeToCheck.Parent;
                    continue;
                }

                break;
            }
        }

        var flattendNodes = Node.Flatten(x => x.Nodes).Select(x => x.Source).Reverse().ToList();
        foreach (var n in flattendNodes)
        {
            Tree.DeleteCallback.InvokeAsync(n);
        }
    }

    public void ExpandButtonClick()
    {
        if (!Node.Nodes.Any())
            return;

        Node.Open = !Node.Open;

        if (Node.Open)
            Tree.NodeExpandCallback.InvokeAsync(Node);
        else
            Tree.NodeCollapseCallback.InvokeAsync(Node);
    }

    public async Task ClickItem()
    {
        Node.IsSelected = true;
        await Tree.SetSelectedNode.InvokeAsync(Node).ConfigureAwait(false);
        await Tree.NodeClickCallback.InvokeAsync(Node).ConfigureAwait(false);
    }

    public string GetRowClass() => Node.IsSelected
        ? "treeview-row-selected noselect"
        : "treeview-row noselect";

    public string GetRowStyle() => $"margin-left:{10 * Depth}px;";

    public string GetFilteredStyle()
    {
        return Node.FilteredState switch
        {
            FilteredState.Self =>       "color:green;",
            FilteredState.Inherited =>  "color:blue;",
            _ =>                        "display:none;",
        };
    }
}