@page "/libraries/{MatrixId:int}/rows/{Row:int}/columns/{Column:int}/rows/{SubRow:int}/columns/{SubColumn:int}"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<RiskMatrixTemplateSubCellViewModel>
@inject IGlobalDataService GlobalDataService;
@inject IStringLocalizer<RiskMatrixTemplateCellComponent> _localizer

<EditForm Model="@BindingContext.RiskMatrixTemplate" OnValidSubmit=@BindingContext.ValidFormSubmitted OnInvalidSubmit=@BindingContext.InvalidFormSubmitted>
    @if (Row > 0)
    {
        <h2>@_localizer["RmtCcCellProperties"]</h2>
    }
    else
    {
        <h4>@_localizer["RmtCcColProperties"]</h4>
    }
    <br/>
    <div class="row">
        <div class="col-sm-12">
            <div class="form-group">
                <label>@_localizer["RmtCcCellColor"]</label>
                <RadzenColorPicker @bind-Value=@BindingContext.SelectedCell.Color ShowHSV="false" ShowRGBA="false" Disabled=!GlobalDataService.CanEdit>

                    <!-- Grays -->
                    <RadzenColorPickerItem Value="FFFFFF" />
                    <RadzenColorPickerItem Value="EEEEEE" />
                    <RadzenColorPickerItem Value="EAEAEA" />
                    <RadzenColorPickerItem Value="E6E6E6" />
                    <RadzenColorPickerItem Value="DDDDDD" />
                    <RadzenColorPickerItem Value="C0C0C0" />
                    <RadzenColorPickerItem Value="B2B2B2" />
                    <RadzenColorPickerItem Value="808080" />
                    <RadzenColorPickerItem Value="606060" />
                    <RadzenColorPickerItem Value="404040" />
                    <RadzenColorPickerItem Value="000000" />

                    <!-- Yellow/Orange/Red -->
                    <RadzenColorPickerItem Value="FFFFCC" />
                    <RadzenColorPickerItem Value="FFFF99" />
                    <RadzenColorPickerItem Value="FFFF00" />
                    <RadzenColorPickerItem Value="FFCC99" />
                    <RadzenColorPickerItem Value="FFCC00" />
                    <RadzenColorPickerItem Value="FFA500" />
                    <RadzenColorPickerItem Value="FF9900" />
                    <RadzenColorPickerItem Value="FF9966" />
                    <RadzenColorPickerItem Value="FFCCCC" />
                    <RadzenColorPickerItem Value="FF3300" />
                    <RadzenColorPickerItem Value="CC0000" />
                    <RadzenColorPickerItem Value="800000" />

                    <!-- Purple -->

                    <RadzenColorPickerItem Value="FFCCFF" />
                    <RadzenColorPickerItem Value="FF66FF" />
                    <RadzenColorPickerItem Value="FF00FF" />
                    <RadzenColorPickerItem Value="CC00CC" />
                    <RadzenColorPickerItem Value="800080" />

                    <!-- Blue -->
                    <RadzenColorPickerItem Value="CCFFFF" />
                    <RadzenColorPickerItem Value="CCECFF" />
                    <RadzenColorPickerItem Value="99CCFF" />
                    <RadzenColorPickerItem Value="00FFFF" />
                    <RadzenColorPickerItem Value="008080" />
                    <RadzenColorPickerItem Value="3366FF" />
                    <RadzenColorPickerItem Value="0066FF" />
                    <RadzenColorPickerItem Value="0000FF" />
                    <RadzenColorPickerItem Value="000080" />

                    <!-- Green -->
                    <RadzenColorPickerItem Value="CCFFCC" />
                    <RadzenColorPickerItem Value="00FF00" />
                    <RadzenColorPickerItem Value="33CC33" />
                    <RadzenColorPickerItem Value="00CC00" />
                    <RadzenColorPickerItem Value="339933" />
                    <RadzenColorPickerItem Value="008000" />
                    <RadzenColorPickerItem Value="808000" />

                </RadzenColorPicker>
            </div>
        </div>
    </div>

    @if (BindingContext.IsEffectColumn || Row == 0)
    {
        <div class="row">
            <div class="col-sm-12">
                <div class="form-group">
                    <label>@_localizer["RmtCcDescription"]</label>
                    <AMproverTextBox class="form-control" @bind-Value=@BindingContext.SelectedCell.Description />
                </div>
            </div>
        </div>
    }

    @if (BindingContext.IsEffectColumn && Row > 0)
    {
        var column = BindingContext.RiskMatrixTemplate.MainGrid.TableColumns[BindingContext.Column];
        string numbertype = column.IsPercentage ? "P0" : "C0";
        
        <div class="row">
            <div class="col-sm-12">
                <AMproverNumberInput Min="0" Label=@_localizer["RmtCcImpactValue"] Format=@numbertype @bind-Value=@BindingContext.SelectedCell.Value />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <AMproverNumberInput Min="0" Label=@_localizer["RmtCcCustomValue"] Format="N4" @bind-Value=@BindingContext.SelectedCell.CustomValue />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <AMproverNumberInput Min="0" Label=@_localizer["RmtCcPoints"] Format="N0" class="form-control" @bind-Value=@BindingContext.SelectedCell.Points />
            </div>
        </div>
    }

    @if (!BindingContext.IsEffectColumn && Row == 0)
    {
        <div class="row">
            <div class="col-sm-12">
                <AMproverNumberInput Min="0" Label=@_localizer["RmtCcMtbf"] Format="N4" @bind-Value=@BindingContext.SelectedCell.CustomValue />
            </div>
        </div>
    }

    @if (BindingContext.IsEffectColumn && Row == 0)
    {
        <div class="row">
            <div class="col-sm-12">
                <AMproverTextBox Label=@_localizer["RmtCcSecValueEntity"] @bind-Value=@BindingContext.SelectedColumn.Entity />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <AMDropdown Label=@_localizer["RmtCcCorCostCalc"]
                    @bind-Value=@BindingContext.SelectedColumn.Setting
                    Data=BindingContext.CorrectiveCostOptions
                    AllowClear=true />
            </div>
        </div>
    }
    <br />
    @if (BindingContext.SetTypesToColumn && Row == 0)
    {
        <div class="row mt-35">
            <div class="col-sm-6">
                <AMDropdown @bind-Value=@BindingContext.SelectedColumn.Type
                        Data=BindingContext.ColumnType
                        AllowClear=true />
            </div>
            <div class="col-sm-5">
                @_localizer["RmtCcSetSheToColumn"]
            </div>
        </div>
    }

    <br/>
    <RadzenButton type="submit" Disabled=!GlobalDataService.CanEdit class="btn btn-primary" Text="Save"/>
</EditForm>


@code{

    [Parameter]
    public int MatrixId { get; set; }

    [Parameter]
    public int Row { get; set; }

    [Parameter]
    public int Column { get; set; }

    [Parameter]
    public int SubRow { get; set; }

    [Parameter]
    public int SubColumn { get; set; }

    [Parameter]
    public Action CallBack { get; set; }

}