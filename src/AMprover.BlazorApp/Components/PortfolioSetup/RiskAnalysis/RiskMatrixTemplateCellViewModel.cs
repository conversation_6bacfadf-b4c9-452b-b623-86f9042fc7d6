using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using Radzen;
using System;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.Data.Entities.AM;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Constants;
using System.Linq;
using Blazor.Extensions.Canvas.WebGL;

namespace AMprover.BlazorApp.Components.PortfolioSetup.RiskAnalysis;

public class RiskMatrixTemplateCellViewModel : BaseViewModel
{
    private readonly IRiskAnalysisSetupManager _riskAnalysisSetupManager;

    public RiskMatrixTemplateCellViewModel(
        ILoggerFactory loggerFactory,
        DialogService dialogService,
        IRiskAnalysisSetupManager riskMatrixManager, ILookupManager lookupManager) : base(loggerFactory, lookupManager)
    {
        _riskAnalysisSetupManager = riskMatrixManager;
        _dialogService = dialogService;
    }

    [Parameter] public int MatrixId { get; set; }
    [Parameter] public int Row { get; set; }
    [Parameter] public int Column { get; set; }
    [Parameter] public Action CallBack { get; set; }

    public List<LookupSettingModel> LookupSettings { get; set; }
    public LookupSettingModel EnableColumnsForTypes { get; set; }

    public bool IsEffectColumn { get; set; }
    public bool SetTypesToColumn { get; set; }

    public RiskMatrixTemplateModel RiskMatrixTemplate { get; private set; }
    public RiskMatrixTemplateCell SelectedCell { get; private set; }
    public RiskMatrixTemplateColumn SelectedColumn { get; private set; }

    public Dictionary<int, string> CorrectiveCostOptions { get; set; }
        = new()
        {
            {1, "Technical cost"},
            {2, "Circuit affected cost"},
            {3, "Normal"}
        };

    public Dictionary<int, string> ColumnType { get; set; }
        = new()
        {
            {1, "Percentage - Sub Column Max."}, //Only Main Column
            {2, "Percentage - Sub Column Prod."}, //Only Main Column
            {3, "Labda (%) - Sub Column Max."}, //Only Main Column
            //{4, "SHE Column"}, // NB. Only available for sub columns
            {5, "CBI (%) - Sub Column Prod."} //Only Main Column
        };

    public override void OnInitialized()
    {
        RiskMatrixTemplate = _riskAnalysisSetupManager.GetTemplate(MatrixId);
        SelectedCell = RiskMatrixTemplate.MainGrid.TableContent[Row].Cells[Column];

        // Decimals are not allowed in the matrices. 1 Exception is the MTBF
        // allow setting to decimals only for header rows to enable this
        if(Row == 0)
            SelectedCell.AllowDecimals = true;

        var column = RiskMatrixTemplate.MainGrid.TableColumns[Column];
        IsEffectColumn = column.IsEffectColumn;

        //To Enable the SAPA module it's necessary to use percentages in the Matrix. Herfore the boolean is used
        LookupSettings = _lookupManager.GetLookupSettings();
        EnableColumnsForTypes = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.EnableColumnsForTypes, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.EnableColumnsForTypes };
        SetTypesToColumn = EnableColumnsForTypes.IntValue == 1;
        SelectedColumn = column;
    }

    public void ValidFormSubmitted(EditContext editContext)
    {
        var model = (RiskMatrixTemplateModel) editContext.Model;

        //Remove subgrids
        if (!SelectedColumn.HasSubColumns)
        {
            model.SubGrids.RemoveAll(x => x.ColumnId == Column);
        }

        _riskAnalysisSetupManager.UpdateTemplate(model);
        CallBack?.Invoke();
        _dialogService.Close();
    }

    public void InvalidFormSubmitted(EditContext editContext)
    {
        //Throw error message.
    }
}