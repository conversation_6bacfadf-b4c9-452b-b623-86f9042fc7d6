using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models.Rams;
using Microsoft.AspNetCore.Components;

namespace AMprover.BlazorApp.Components.Rams;

public class RamsDiagramComponentViewModel : BaseViewModel
{
    #region parameters

    [Parameter] public RamsDiagramContentModel Diagram { get; set; }

    [Parameter] public string Title { get; set; }

    [Parameter] public EventCallback<RamsComponentModel> ComponentSelectCallback { get; set; }

    [Parameter] public EventCallback<RamsComponentModel> ComponentReOrderCallback { get; set; }

    [Parameter] public EventCallback ComponentCollapseCallback { get; set; }

    [Parameter] public EventCallback ComponentParallelCallback { get; set; }

    [Parameter] public EventCallback<NewContainerModel> AddItemsToNewContainerCallback { get; set; }
    
    [Parameter] public EventCallback DrawLines { get; set; }
    
    [Parameter] public EventCallback RemoveLines { get; set; }
    
    [Parameter] public EventCallback RecalculateDiagramSize { get; set; }

    public ElementReference RecursiveTable { get; set; }

    #endregion
}