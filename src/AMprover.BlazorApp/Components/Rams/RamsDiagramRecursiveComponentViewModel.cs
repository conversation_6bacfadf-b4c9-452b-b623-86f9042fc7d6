using System;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models.Rams;
using Microsoft.AspNetCore.Components;
using Ra<PERSON>zen;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Components.Rams;

public class RamsDiagramRecursiveComponentViewModel : BaseViewModel
{
    #region parameters

    [Parameter] public RamsComponentModel Item { get; set; }

    [Parameter] public RamsDiagramContentModel Diagram { get; set; }

    [Parameter] public RamsDisplayModel DisplayMode { get; set; }

    [Parameter] public EventCallback<RamsComponentModel> ComponentSelectCallback { get; set; }

    [Parameter] public EventCallback<RamsComponentModel> ComponentReOrderCallback { get; set; }

    [Parameter] public EventCallback ComponentCollapseCallback { get; set; }

    [Parameter] public EventCallback ComponentParallelCallback { get; set; }

    [Parameter] public EventCallback<NewContainerModel> AddItemsToNewContainerCallback { get; set; }

    #endregion

    #region properties

    public Guid? SelectedId { get; set; }

    public ElementReference RecursiveTable { get; set; }

    public ElementReference ToolTip { get; set; }

    #endregion

    public void SelectItem(RamsComponentModel component)
    {
        SelectedId = SelectedId == component.Id ? null : component.Id;
        StateHasChanged();
        ComponentSelectCallback.InvokeAsync(component);
    }

    public void CollapseItem(RamsComponentModel component)
    {
        component.Collapsed = !component.Collapsed;
        StateHasChanged();
        ComponentCollapseCallback.InvokeAsync();
    }
}