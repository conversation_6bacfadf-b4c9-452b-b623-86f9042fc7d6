using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Components;

public class PortfolioSwitcherViewModel : BaseViewModel
{
    private readonly NavigationManager _navigationManager;
    private readonly IPortfolioManager _portfolioManager;
    public PortfolioSwitcherViewModel(ILoggerFactory loggerFactory, NavigationManager navigationManager, IPortfolioManager portfolioManager) : base(loggerFactory)
    {
        _navigationManager = navigationManager;
        _portfolioManager = portfolioManager;
    }

    public List<SwitchItem> PortfoliosForUser { get; private set; }

    public int SelectedPortfolioId { get; set; }

    public override void OnInitialized()
    {
        var userPortfolios = _portfolioManager.GetPortfoliosForLoggedInUser();

        // Avoid null references
        PortfoliosForUser ??= new List<SwitchItem>();

        if (PortfoliosForUser.Count <= 0)
        {
            foreach (PortfolioModel port in userPortfolios)
            {
                PortfoliosForUser.Add(new SwitchItem { Id = port.Id, Name = port.Name });
            }
            var currentPortfolio = _portfolioManager.GetCurrentPortfolio();
            SelectedPortfolioId = currentPortfolio.Id;
        }

        base.OnInitialized();
    }

    public void Switch()
    {
        _portfolioManager.SwitchPortfolioForLoggedInUser(SelectedPortfolioId);
        // Database contexts are being connected via the SignalR circuit, and scoped
        // to such circuit. Only change the underlying data in the database will not
        // be reflecting while navigating to other pages. force a reload will get
        // a fresh circuit and thus new database contexts. 
        _navigationManager.NavigateTo("/", true);
    }

    public class SwitchItem
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }
}