using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Sapa;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AutoMapper;
using AMprover.BusinessLogic.Helpers;
using System.Globalization;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.Data.Constants;
using AMprover.Data.Entities.AM;
using System.Linq;
using System.Collections.Generic;

namespace AMprover.BlazorApp.Components.Sapa;

public class SapaDetailPrintViewModel : BaseViewModel, IEntityEditorViewModel
{
    private readonly ISapaOverviewManager _sapaOverviewManager;
    private readonly IRiskAnalysisManager _riskAnalysisManager;
    private readonly IStringLocalizer _localizer;
    private readonly ILookupManager _lookupManager;
    private IDropdownManager _dropdownManager { get; }
    private readonly IMapper _mapper;
    private readonly IObjectManager _objectManager;

    public SapaDetailPrintViewModel(ILoggerFactory loggerFactory, ISapaOverviewManager sapaOverviewManager, IRiskAnalysisManager riskAnalysisManager,
        DialogService dialogService, IMapper mapper, ILookupManager lookupManager, LocalizationHelper localizationHelper,
        IObjectManager objectManager, IDropdownManager dropdownManager) : base(loggerFactory, lookupManager)
    {
        _sapaOverviewManager = sapaOverviewManager;
        _riskAnalysisManager = riskAnalysisManager;
        _dialogService = dialogService;
        _lookupManager = lookupManager;
        _dropdownManager = dropdownManager;
        _objectManager = objectManager;
        _mapper = mapper;
        _localizer = localizationHelper.GetLocalizer<SapaDetailPrint>();
    }

    [Parameter] public int SapaDetailId { get; set; }
    [Parameter] public EventCallback<SapaDetailModel> Callback { get; set; }

    public SapaDetailModel SapaDetail { get; set; }
    public RiskModel Risk { get; set; }
    public UtilityGrid<TaskModel> PreventiveActionsGrid { get; set; }
    public UtilityGrid<SpareModel> SparesGrid { get; set; }
    public Dictionary<string, Dictionary<int, string>> TaskModelDropDownOverrides { get; set; }

    public EntityEditorMode EditorMode
    {
        get
        {
            return SapaDetailId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    public override void OnInitialized()
    {
        SapaDetail = _sapaOverviewManager.GetSapaDetail(SapaDetailId);
        Risk = _riskAnalysisManager.GetRisk(SapaDetail.RiskId);
        TaskModelDropDownOverrides = _dropdownManager.GetTaskModelDropDownOverrides();
    }

    public string FormatAsSelectedCurrency(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
    }

    public string FormatAsNumber(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("N0", CultureInfo.CreateSpecificCulture(Currency));
    }

    public void UpdateCurrentRisk()
    {
        //read only, no updates
    }
}