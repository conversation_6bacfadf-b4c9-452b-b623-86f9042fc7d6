using System;
using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BlazorApp.Pages.RiskOrganize;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Ra<PERSON>zen;

namespace AMprover.BlazorApp.Pages.Rams;

public class NewRamsWidgetViewModel : BaseViewModel
{
    private readonly IScenarioManager _scenarioManager;
    private readonly IRamsManager _ramsManager;

    public NewRamsWidgetViewModel(ILoggerFactory loggerFactory, IScenarioManager scenarioManager,
        IRamsManager ramsManager,
        ILookupManager lookupManager, DialogService dialogService) : base(loggerFactory,
        lookupManager, dialogService)
    {
        _scenarioManager = scenarioManager;
        _ramsManager = ramsManager;
    }

    [Parameter] public Action<int?> CallBack { get; set; }

    public List<ScenarioModel> Scenarios { get; set; }

    public NewRamsModel NewRams { get; } = new();

    public bool IsBusy { get; set; }

    public override void OnInitialized()
    {
        Scenarios = _scenarioManager.GetAllScenarios();
    }

    public void ValidTaskSubmitted(NewRamsModel rams)
    {
        var generatedGuid = Guid.NewGuid();

        //Generate content model and add initial container
        var content = new RamsDiagramContentModel
        {
            ParallelTracks = 1, Parts = new List<RamsComponentModel>
            {
                new()
                {
                    Id = generatedGuid,
                    Type = RamsComponentType.Container,
                    Title = "[New container]",
                    Changed = DateTime.Now,
                    Dept = 1
                }
            }
        };

        var rootContainer = new RamsModel
        {
            NodeId = generatedGuid,
            Container = true,
            Descr = "[New container]",
            Name = "[New container]",
            Mtbftechn = 1,
            Mtbffunct = 1,
            LabdaFunctional = 1,
            LabdaTechnical = 1,
            WeibullShape = 1,
            CharacteristicLife = 1
        };

        var diagram = _ramsManager.CreateOrEditRamsDiagram(new RamsDiagramModel
        {
            Name = NewRams.Name,
            ScenId = NewRams.ScenarioId,
            AvailableTime = 8760D,
            TestInterval = 1000,
            Horizon = 1,
            CalculateAvailability = true,
            PeriodFrom = 0,
            PeriodTo = 1,
            Serialized = JsonConvert.SerializeObject(content),
            Rams = new List<RamsModel>
            {
                rootContainer
            }
        });

        rootContainer.DiagramId = diagram.Id;
        _ramsManager.CreateOrEditRams(rootContainer);

        CallBack?.Invoke(diagram.Id);
        _dialogService.Close();
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        _logger.LogError($"Invalid form submitted in {nameof(NewRiskObjectWidgetViewModel)}");
        _logger.LogError(JsonConvert.SerializeObject(args));
    }
}