@page "/rams"
@using AMprover.BusinessLogic.Enums.Rams
@using AMprover.BlazorApp.Helpers
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<RamsEditPageViewModel>
@inject IStringLocalizer<RamsEditPage> _localizer
@inject IGlobalDataService GlobalDataService

<style>
    .content {
        margin-bottom: 0;
    }
    .main{
        overflow: hidden;
    }
</style>

<h2>@_localizer["RamsHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-3 text-center">
    </div>
    <div class="col-5 text-right">
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Start" Gap="0.4rem" Wrap="FlexWrap.Wrap" class="btn-mainbar">
                    <RadzenButton Icon="add_circle_outline" Text=@_localizer["RamsNewBtnTxt"] Click=@BindingContext.OpenNewRamsWidget
                                  ButtonStyle=ButtonStyle.Secondary/>
        
                    <RadzenButton Icon="delete" Text=@_localizer["RamsDeleteBtnTxt"] Click=BindingContext.DeleteDiagram ButtonStyle=ButtonStyle.Primary
                                  Visible="BindingContext.CurrentDiagram != null"
                                  Disabled="((BindingContext.CurrentDiagram == null) || !GlobalDataService.CanEdit)"/>
        </RadzenStack>
    </div>
</div>

<div class="row rams">
<!-- Rams diagram picker -->
@if (!BindingContext.ShowHidePanelMode)
{
    <div
        class="col-sm-3 @(!BindingContext.ShowHidePanelMode ? "diagram-container-width-left" : "diagram-container-width-full-left")">
    <div class="row">
        <div class="col-sm-12 treeview risk-assessment">
            <RadzenTabs @ref=@BindingContext.TreeTab class="hide-tabs-nav">
                <Tabs>
                    <RadzenTabsItem>
                        <TreeComponent TItem=RamsTreeObject
                                       Treeview=BindingContext.RamsTree
                                       NodeClickCallback=BindingContext.ClickTreeNode />
                    </RadzenTabsItem>
                </Tabs>
            </RadzenTabs>
        </div>
    </div>
    <!-- Selected rams block and container editor -->
    <div class="row">
    <div class="col-sm-12">
    <RadzenTabs @ref=@BindingContext.EditorTab @bind-SelectedIndex=@BindingContext.SelectedTabIndex>
    <Tabs>
    <RadzenTabsItem Text="Diagram">
        @if (BindingContext.CurrentDiagram != null)
        {
            <div class="col-sm-12">
                <AMproverTextBox Label=@_localizer["RamsNameTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.CurrentDiagram.Name Placeholder="Name"
                                 Change=@BindingContext.SaveRamsDiagram/>
                <AMproverDictionaryDropdown Disabled=@BindingContext.DisabledDropdowns Label="Scenario"
                                            Data=@BindingContext.ScenarioDict.ToNullableDictionary() @bind-Value=@BindingContext.CurrentDiagram.ScenId
                                            Change=@BindingContext.SaveRamsDiagram AllowClear="true" />
                <div class="row">
                    <div class="col-sm-8">
                        <AMproverDictionaryDropdown Label=@_localizer["RamsRiskObjectTxt"]
                                                    Data=@BindingContext.RiskObjectDict.ToNullableDictionary() @bind-Value=@BindingContext.CurrentDiagram.RiskObject
                                                    Change=@BindingContext.SaveRamsDiagram
                                                    AllowClear="true" />
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <RadzenButton Icon="download" class="my-2 mx-1"
                                          Text=@_localizer["RamsLoadDiagramTxt"]
                                          Click=@BindingContext.GenerateDiagramFromRiskObject
                                          Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary
                                          Disabled=!GlobalDataService.CanEdit />
                        </div>
                    </div>
                </div>

                <!-- Reliability (from to) year(s) -->
                <div class="row">
                    <div class="col-sm-6">
                        <AMproverNumberInput TValue="decimal" Label="Reliability"
                                             @bind-Value=@BindingContext.CurrentDiagram.PeriodFrom/>
                    </div>
                    <div class="col-sm-6">
                        <AMproverNumberInput TValue="decimal" Label=@_localizer["RamsToYearsTxt"]
                                             Format="N4"
                                             @bind-Value=@BindingContext.CurrentDiagram.PeriodTo/>
                    </div>
                </div>

                <!-- Available (hours/year)-->
                <AMproverNumberInput TValue="double" Label=@_localizer["RamsAvailHoursTxt"] Format="N0"
                                     @bind-Value=@BindingContext.CurrentDiagram.AvailableTime
                                     Change=@BindingContext.SaveRamsDiagram/>

                <!-- Tabs: Properties, Descriptions, remarks -->
                <AMproverTextArea TValue="string" Label=@_localizer["RamsPrereqTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.CurrentDiagram.Prerequisites
                                  Change=@BindingContext.SaveRamsDiagram/>
                <AMproverTextArea TValue="string" Label=@_localizer["RamsDescrTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.CurrentDiagram.Descr
                                  Change=@BindingContext.SaveRamsDiagram/>
                <AMproverTextArea TValue="string" Label=@_localizer["RamsRemarksTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.CurrentDiagram.Remark
                                  Change=@BindingContext.SaveRamsDiagram/>

                <br/>
                <!-- create lcc calculation of this diagram -->
                <Radzen.Blazor.RadzenCheckBox @bind-Value=@BindingContext.CurrentDiagram.WantLcc
                                              TValue="bool?"
                                              Change=@BindingContext.CreateLcvCalculation/> @_localizer["RamsLccCalcTxt"]
                <br/>
                <!-- compatibility mode (checkbox) -->
                @*<Radzen.Blazor.RadzenCheckBox @bind-Value=@BindingContext.CurrentDiagram.CalculationCompatibilityMode TValue="bool" Change=@(() => BindingContext.SaveRamsDiagram())/> @_localizer["RamsCompModeTxt"]<br/>*@
                <!-- automatically calculate availability -->
                <Radzen.Blazor.RadzenCheckBox
                    @bind-Value=@BindingContext.CurrentDiagram.CalculateAvailability TValue="bool"
                    Change=@(() => BindingContext.SaveRamsDiagram())/> @_localizer["RamsAutoCalcTxt"]
                <br/><br/>

                <!-- test interval (hrs), horizon (int) -->
                <div class="row">
                    <div class="col-sm-6">
                        <AMproverNumberInput TValue="int?" Label=@_localizer["RamsTestIntervalTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.CurrentDiagram.TestInterval
                                             Change=@BindingContext.SaveRamsDiagram/>
                    </div>
                    <div class="col-sm-6">
                        <AMproverNumberInput TValue="int?" Label=@_localizer["RamsHorizonTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.CurrentDiagram.Horizon
                                             Change=@BindingContext.SaveRamsDiagram/>
                    </div>
                </div>

                <AMproverNumberInput TValue="int" Label="Id"
                                     @bind-Value=@BindingContext.CurrentDiagram.Id Disabled="true"/>
            </div>
        }
    </RadzenTabsItem>
    <RadzenTabsItem Text="Group" @ref=@BindingContext.EditorTabGroup
                    Disabled=@BindingContext.DisableGroupTab>
    @if (BindingContext.SelectedGroup != null)
    {
        <div class="col-sm-12">
        <AMproverNumberInput Label="Id" @bind-Value=@BindingContext.SelectedGroup.Id
                             Disabled="true"/>

        @if (BindingContext.XooNData.Count > 1)
        {
            <div class="form-group">
                <div class="neg-margin-small">
                    <label>Container:</label>
                </div>
                <div class="neg-margin-small">
                    <RadzenDropDown TValue="int?" TextProperty="Value" ValueProperty="Key"
                                    Data=@BindingContext.XooNData @bind-Value=@BindingContext.SelectedGroup.XooN
                                    Change=@BindingContext.SaveRamsComponentAndRecalculate
                                    AllowClear="true"/>
                    <AMproverNumberInput TValue="string"
                                         Placeholder=@($"out of {BindingContext.XooNData.Count}")
                                         Disabled="true" Class="small" ShowOnlyInput="true"/>
                </div>
            </div>
        }

        <AMproverTextBox Label=@_localizer["RamsNameTxt"]   
                         @bind-Value=@BindingContext.SelectedGroup.Name Placeholder="Name"
                         Change=@BindingContext.SaveRamsComponent/>
        <AMproverTextArea Label=@_localizer["RamsDescrTxt"] Placeholder="Description"
                          @bind-Value=@BindingContext.SelectedGroup.Descr
                          Change=@BindingContext.SaveRamsComponent/>

        <div class="form-group">
            <div class="neg-margin-small">
                <label>Status:</label>
            </div>
            <div class="neg-margin-small">
                <RadzenDropDown TValue="int?" TextProperty="Value" ValueProperty="Key"
                                Data=@BindingContext.BlockStatusDict @bind-Value=@BindingContext.SelectedGroup.Status
                                Change=@BindingContext.SaveRamsComponent AllowClear="true"
                                Class="form-control"/>
            </div>
        </div>

        <RadzenTabs @ref=@BindingContext.ContainerContentTab>
            <Tabs>
                <RadzenTabsItem Text="Data">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>@_localizer["RamsTechnicalTxt"]:</strong>
                        </div>
                        <div class="col-sm-6">
                            <strong>@_localizer["RamsFunctionalTxt"]:</strong>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <AMproverNumberInput TValue="double" Format="F2" Label=@BindingContext.MtbfLabel
                                                 @bind-Value=@BindingContext.SelectedItem.Results.MtbfTechnical
                                                 Multiplier=@(BindingContext.DisplayMode.ShowYears ? 1 : BindingContext.CurrentDiagram?.AvailableTime ?? 1)
                                                 Disabled="true"/>
                        </div>
                        <div class="col-sm-6">
                            <AMproverNumberInput TValue="double" Format="F2" Label=@BindingContext.MtbfLabel
                                                 @bind-Value=@BindingContext.SelectedItem.Results.MtbfFunctional
                                                 Multiplier=@(BindingContext.DisplayMode.ShowYears ? 1 : BindingContext.CurrentDiagram?.AvailableTime ?? 1)
                                                 Disabled="true"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <AMproverTextBox Label=@_localizer["RamsAvailablityTxt"]
                                                 Value=@BindingContext.SelectedItem.Results.AvailTechnical.FormatRoundedValue(BindingContext.Language, 2)
                                                 Disabled="true"/>
                        </div>
                        <div class="col-sm-6">
                            <AMproverTextBox Label=@_localizer["RamsAvailablityTxt"]
                                                 Value=@BindingContext.SelectedItem.Results.AvailFunctional.FormatRoundedValue(BindingContext.Language, 2)
                                                 Disabled="true"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            @if (BindingContext.DisplayMode.ShowFunctional)
                            {
                                <AMproverNumberInput TValue="double" Format="F2"
                                                     Label=@(BindingContext.SelectedItem.Results.AffectedBufferTime > 0 ? $"MTTR (FRT) (Affected by a buffer time of {BindingContext.SelectedItem.Results.AffectedBufferTime})" : "MTTR (FRT)")
                                                     @bind-Value=@BindingContext.SelectedItem.Results.MttrFunctional
                                                     Disabled="true"/>
                            }
                            else
                            {
                                <AMproverNumberInput TValue="double" Format="F2"
                                                     Label=@(BindingContext.SelectedItem.Results.AffectedBufferTime > 0 ? $"MTTR (FRT) (Affected by a buffer time of {BindingContext.SelectedItem.Results.AffectedBufferTime})" : "MTTR (FRT)")
                                                     @bind-Value=@BindingContext.SelectedItem.Results.MttrTechnical
                                                     Disabled="true"/>
                            }
                        </div>
                        <br/>
                        <div class="col-sm-6">
                            <Radzen.Blazor.RadzenCheckBox
                                @bind-Value=@BindingContext.SelectedGroup.Completed
                                TValue="bool?"
                                Change=@BindingContext.SaveRamsComponent/> @_localizer["RamsCompletedTxt"]
                            <br/>
                            <Radzen.Blazor.RadzenCheckBox
                                @bind-Value=@BindingContext.SelectedGroup.Identical
                                TValue="bool?"
                                Change=@(() => BindingContext.SaveRamsComponent(true))/> @_localizer["RamsIdenticalToFirstBlockTxt"]
                            <br/><br/>
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["RamsABSTxt"]>
                    <div class="form-group">
                        <div class="neg-margin-small">
                            <label>Asset:</label>
                        </div>
                        <div class="neg-margin-small">
                            <RadzenDropDown TValue="int?" TextProperty="Value"
                                            ValueProperty="Key" Data=@BindingContext.AssetDict @bind-Value=@BindingContext.SelectedGroup.SiId
                                            Change=@BindingContext.SaveRamsComponent
                                            AllowClear="true" Class="form-control"/>
                        </div>
                    </div>
                    <AMproverNumberInput Label="Year" TValue="int?" Placeholder="Year"
                                         @bind-Value=@BindingContext.SelectedGroup.Year
                                         Change=@BindingContext.SaveRamsComponent/>
                </RadzenTabsItem>
                <RadzenTabsItem Text="Cost">
                    <Radzen.Blazor.RadzenCheckBox
                        @bind-Value=@BindingContext.SelectedGroup.WantLcc TValue="bool?"
                        Change=@BindingContext.SaveRamsComponent/> @_localizer["RamsLccCalcTxt"]
                    <br/>
                    <div class="row">
                        <div class="col-sm-6">
                            <Radzen.Blazor.RadzenCheckBox
                                @bind-Value=@BindingContext.SelectedGroup.CostOwner
                                TValue="bool?"
                                Change=@BindingContext.SaveRamsComponent/> @_localizer["RamsCostOwnerTxt"]
                            <br/>
                        </div>
                        <div class="col-sm-6">
                            <Radzen.Blazor.RadzenCheckBox
                                @bind-Value=@BindingContext.SelectedGroup.CostLinked
                                TValue="bool?"
                                Change=@BindingContext.SaveRamsComponent/> @_localizer["RamsCalcInFmecaTxt"]
                            <br/><br/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4">
                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                 Label=@_localizer["RamsPrevCostTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedGroup.PreventiveCost
                                                 Change=@BindingContext.SaveRamsComponent
                                                 Disabled=@(BindingContext.SelectedGroup.CostOwner != true)/>
                        </div>
                        <div class="col-sm-8" style="background-color:lightgrey">
                            @_localizer["RamsCorrectiveCostTxt"]
                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                 Label=@_localizer["RamsTechnicalTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedGroup.TechnCorrCost
                                                 Change=@BindingContext.SaveRamsComponent
                                                 Disabled=@(BindingContext.SelectedGroup.CostOwner != true)/>
                            <br/>
                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                 Label=@_localizer["RamsNotEffByCircuitTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedGroup.CircuitDepCorrCost
                                                 Change=@BindingContext.SaveRamsComponent
                                                 Disabled=@(BindingContext.SelectedGroup.CostOwner != true)/>
                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                 Label=@_localizer["RamsEffByCircuitTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedGroup.CircuitDepCorrCost
                                                 Change=@BindingContext.SaveRamsComponent
                                                 Disabled=@(BindingContext.SelectedGroup.CostOwner != true)/>
                        </div>
                    </div>
                    <AMproverNumberInput TValue="decimal?" Format="F2"
                                         Label=@_localizer["RamsTotalCostTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedGroup.TotalCost
                                         Change=@BindingContext.SaveRamsComponent
                                         Disabled=@(BindingContext.SelectedGroup.CostOwner != true)/>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["RamsRemarksTxt"]>
                    <AMproverTextArea Label=@_localizer["RamsRemarksTxt"] Placeholder="Remarks"
                                      Change=@BindingContext.SaveRamsComponent>
                    </AMproverTextArea>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["RamsHierObjectTxt"]>
                    <div class="form-group">
                        <div class="neg-margin-small">
                            <label>@_localizer["RamsHierObjectTxt"]</label>
                        </div>
                        <div class="neg-margin-small">
                            @if (BindingContext.SelectedGroup.ObjectId.HasValue)
                            {
                                <TreeComponent TItem=RiskTreeObject
                                               Treeview=BindingContext.RiskTree
                                               NodeClickCallback=BindingContext.ClickRiskTreeNode/>
                            }
                        </div>
                    </div>
                </RadzenTabsItem>
                @* <RadzenTabsItem Text="Connections"> *@
                @*     <p>Add links to non sequential blocks or containers</p> *@
                @*     <AMproverDropdown AllowFiltering=true TextProperty="Title" ValueProperty="Id" Change=@(args => BindingContext.AddCustomConnection()) *@
                @*                       Data=@BindingContext.RamsComponentModels @bind-value=@BindingContext.SelectedConnectionDestination Label="Destination"/> *@
                @* *@
                @*     <p>List of custom connections</p> *@
                @* *@
                @*     @if (BindingContext.SelectedItem != null) *@
                @*     { *@
                @*         @foreach (var customConnection in BindingContext.SelectedItem.CustomConnectors) *@
                @*         { *@
                @*             <p>@customConnection.Destination</p> *@
                @*         } *@
                @*     } *@
                @* </RadzenTabsItem> *@
            </Tabs>
        </RadzenTabs>
        </div>
    }
    else
    {
        <p>@((MarkupString) _localizer["RamsNoGroupSelectedTxt"].Value)</p>
    }
    </RadzenTabsItem>
    <RadzenTabsItem Text="Block" @ref=@BindingContext.EditorTabBlock
                    Disabled=@BindingContext.DisableBlockTab>
    @if (BindingContext.SelectedNode != null)
    {
        <div class="col-sm-12">
        <AMproverNumberInput Label="Id" @bind-Value=@BindingContext.SelectedNode.Id
                             Disabled="true"/>
        <AMproverTextBox Label=@_localizer["RamsNameTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.Name Placeholder="Name"
                         Change=@BindingContext.SaveRamsComponent MaxLength="40"
                         Disabled=@(BindingContext.SelectedNode.DiagramRefId != null)/>
        <AMproverTextArea Label=@_localizer["RamsDescrTxt"] Placeholder="Description"
                          @bind-Value=@BindingContext.SelectedNode.Descr
                          Change=@BindingContext.SaveRamsComponent
                          Disabled=@(BindingContext.SelectedNode.DiagramRefId != null)/>

        <div class="form-group">
            <div class="neg-margin-small">
                <label>Status:</label>
            </div>
            <div class="neg-margin-small">
                <RadzenDropDown TValue="int?" TextProperty="Value" ValueProperty="Key"
                                Data=@BindingContext.BlockStatusDict @bind-Value=@BindingContext.SelectedNode.Status
                                Change=@(() => BindingContext.SaveRamsComponent(true))
                                AllowClear="true" Class="form-control"
                                Disabled=@(BindingContext.SelectedNode.DiagramRefId != null)/>
            </div>
        </div>

        <RadzenTabs @ref=@BindingContext.BlockContentTab>
        <Tabs>
        <RadzenTabsItem Text="Data">
            <div class="row">
                <div class="col-sm-6">
                    <strong>@_localizer["RamsTechnicalTxt"]:</strong>
                </div>
                <div class="col-sm-6">
                    <strong>@_localizer["RamsFunctionalTxt"]:</strong>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <AMproverNumberInput TValue="double?" Format="F2"
                                         Label=@BindingContext.MtbfLabel
                                         @bind-Value=@BindingContext.SelectedNode.Mtbftechn
                                         Multiplier=@(BindingContext.DisplayMode.ShowYears ? 1 : (double?)BindingContext.CurrentDiagram?.AvailableTime ?? 1)
                                         Change=@(() => BindingContext.SaveRamsComponent(true))
                                         Disabled=@(BindingContext.DisableBlockEditing || BindingContext.SelectedNode.DiagramRefId != null || BindingContext.SelectedNode.LinkType == 4)/>
                </div>
                <div class="col-sm-6">
                    <AMproverNumberInput TValue="double?" Format="F2"
                                         Label=@BindingContext.MtbfLabel
                                         @bind-Value=@BindingContext.SelectedNode.Mtbffunct
                                         Multiplier=@(BindingContext.DisplayMode.ShowYears ? 1 : (double?)BindingContext.CurrentDiagram?.AvailableTime ?? 1)
                                         Change=@(() => BindingContext.SaveRamsComponent(true))
                                         Disabled=@(BindingContext.DisableBlockEditing || BindingContext.SelectedNode.DiagramRefId != null || BindingContext.SelectedNode.LinkType == 4)/>
                </div>

            </div>
            <div class="row">
                <div class="col-sm-6">
                    <AMproverTextBox Label=@_localizer["RamsAvailablityTxt"]
                                         Value=@BindingContext.SelectedNode.AvailabilityOutput?.FormatRoundedValue(BindingContext.Language,2)
                                         Disabled="true"/>
                </div>
                <div class="col-sm-6">
                    <AMproverTextBox Label=@_localizer["RamsAvailablityTxt"]
                                         Value=@BindingContext.SelectedNode.AvailabilityInput?.FormatRoundedValue(BindingContext.Language, 2)
                                         Disabled="true"/>
                </div>

            </div>
            <div class="row">
                <div class="col-sm-6">
                    <AMproverNumberInput TValue="double?" Format="F2"
                                         Label=@(BindingContext.SelectedItem.Results.AffectedBufferTime > 0 ? $"MTTR (FRT) (Affected by a buffer time of {BindingContext.SelectedItem.Results.AffectedBufferTime})" : "MTTR (FRT) - hrs ")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.Mttr
                                         Change=@(() => BindingContext.SaveRamsComponent(true))
                                         Disabled=@(BindingContext.DisableBlockEditing || BindingContext.SelectedNode.DiagramRefId != null)/>
                </div>
                <br/>
                <div class="col-sm-6">
                    <Radzen.Blazor.RadzenCheckBox
                        @bind-Value=@BindingContext.SelectedNode.Completed
                        TValue="bool?"
                        Change=@BindingContext.SaveRamsComponent/> @_localizer["RamsCompletedTxt"]
                    <br/>
                </div>
            </div>
            @if (BindingContext.SelectedNode?.Status == (int?) RamsBlockStatus.Buffer)
            {
                <div class="row">
                    <div class="col-sm-6">
                        <AMproverNumberInput TValue="double?" Format="F2"
                                             Label=@_localizer["RamsBufferTimeTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.BufferTime
                                             Change=@(() => BindingContext.SaveRamsComponent(true))/>
                    </div>
                </div>
            }
        </RadzenTabsItem>
        <RadzenTabsItem Text="SIL/PFD">
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <div class="neg-margin-small">
                            <label>Classification:</label>
                        </div>
                        <div class="neg-margin-small">
                            <RadzenDropDown TValue="string"
                                            Data=@(new List<string> {"DD", "DU", "SD", "SU"}) @bind-Value=@BindingContext.SelectedNode.ClassDc
                                            Change=@BindingContext.ProcessClassificationChangesAndSaveRamsComponent
                                            AllowClear="true" Class="form-control"
                                            Disabled=@BindingContext.DisableBlockEditing/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <AMproverNumberInput TValue="double?" Format="F2"
                                         Label=@_localizer["RamsDCdTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.Dcd
                                         Placeholder="@_localizer["RamsDCdTxt"]"
                                         Change=@(() => BindingContext.SaveRamsComponent(true))
                                         Disabled=@BindingContext.DisableBlockEditing/>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <AMproverNumberInput TValue="int?"
                                         Label=@_localizer["RamsTestIntervalTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.TestInterval
                                         Placeholder="@_localizer["RamsTestIntervalTxt"]"
                                         Change=@(() => BindingContext.SaveRamsComponent(true))
                                         Disabled=@BindingContext.DisableBlockEditing/>
                </div>
                <div class="col-sm-6">
                    <AMproverNumberInput TValue="double?" Format="F2"
                                         Label=@_localizer["RamsBetaTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.Beta
                                         Placeholder="@_localizer["RamsBetaTxt"]"
                                         Change=@(() => BindingContext.SaveRamsComponent(true))
                                         Disabled=@BindingContext.DisableBlockEditing/>
                </div>
            </div>
        </RadzenTabsItem>
        <RadzenTabsItem Text="Cost">
            <Radzen.Blazor.RadzenCheckBox
                @bind-Value=@BindingContext.SelectedNode.WantLcc TValue="bool?"
                Change=@BindingContext.SaveRamsComponent/> @_localizer["RamsLccCalcTxt"]
            <br/>
            <div class="row">
                <div class="col-sm-6">
                    <Radzen.Blazor.RadzenCheckBox
                        @bind-Value=@BindingContext.SelectedNode.CostOwner
                        TValue="bool?"
                        Change=@BindingContext.SaveRamsComponent/> @_localizer["RamsCostOwnerTxt"]
                    <br/>
                </div>
                <div class="col-sm-6">
                    <Radzen.Blazor.RadzenCheckBox
                        @bind-Value=@BindingContext.SelectedNode.CostLinked
                        TValue="bool?"
                        Change=@BindingContext.SaveRamsComponent/> @_localizer["RamsCalcInFmecaTxt"]
                    <br/><br/>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-4">
                    <AMproverNumberInput TValue="decimal?" Format="F2"
                                         Label=@_localizer["RamsPrevCostTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.PreventiveCost
                                         Change=@BindingContext.SaveRamsComponent
                                         Disabled=@(BindingContext.SelectedNode.CostOwner != true)/>
                </div>
                <div class="col-sm-8" style="background-color:lightgrey">
                    @_localizer["RamsCorrectiveCostTxt"]
                    <AMproverNumberInput TValue="decimal?" Format="F2"
                                         Label=@_localizer["RamsTechnicalTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.TechnCorrCost
                                         Change=@BindingContext.SaveRamsComponent
                                         Disabled=@(BindingContext.SelectedNode.CostOwner != true)/>
                    <br/>
                    <AMproverNumberInput TValue="decimal?" Format="F2"
                                         Label=@_localizer["RamsNotEffByCircuitTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.CircuitDepCorrCost
                                         Change=@BindingContext.SaveRamsComponent
                                         Disabled=@(BindingContext.SelectedNode.CostOwner != true)/>
                    <AMproverNumberInput TValue="decimal?" Format="F2"
                                         Label=@_localizer["RamsEffByCircuitTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.CircuitDepCorrCost
                                         Change=@BindingContext.SaveRamsComponent
                                         Disabled=@(BindingContext.SelectedNode.CostOwner != true)/>
                </div>
            </div>
            <AMproverNumberInput TValue="decimal?" Format="F2"
                                 Label=@_localizer["RamsTotalCostTxt"]   
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @bind-Value=@BindingContext.SelectedNode.TotalCost
                                 Change=@BindingContext.SaveRamsComponent
                                 Disabled=@(BindingContext.SelectedNode.CostOwner != true)/>
        </RadzenTabsItem>
        <RadzenTabsItem Text=@_localizer["RamsABSTxt"]>
            <div class="form-group">
                <div class="neg-margin-small">
                    <label>Asset:</label>
                </div>
                <div class="neg-margin-small">
                    <RadzenDropDown TValue="int?" TextProperty="Value"
                                    ValueProperty="Key" Data=@BindingContext.AssetDict @bind-Value=@BindingContext.SelectedNode.SiId
                                    Change=@BindingContext.SaveRamsComponent
                                    AllowClear="true" Class="form-control"/>
                </div>
            </div>
            <AMproverNumberInput TValue="int?" Placeholder="Year"
                                 @bind-Value=@BindingContext.SelectedNode.Year
                                 Change=@BindingContext.SaveRamsComponent/>
        </RadzenTabsItem>
        <RadzenTabsItem Text=@_localizer["RamsRemarksTxt"]>
            <AMproverTextArea Label=@_localizer["RamsRemarksTxt"] Placeholder="Remarks"
                              @bind-Value=@BindingContext.SelectedNode.Remark
                              Change=@BindingContext.SaveRamsComponent>
            </AMproverTextArea>
        </RadzenTabsItem>
        <RadzenTabsItem Text=@_localizer["RamsHierObjectTxt"]>
            <div class="form-group">
                <div class="neg-margin-small">
                    <label>@_localizer["RamsHierObjectTxt"]</label>
                </div>
                <div class="neg-margin-small">
                    @if (BindingContext.SelectedNode.RiskId.HasValue)
                    {
                        <div class="row">
                            <div class="col-sm-6">
                                <RadzenDropDown TValue="int?"
                                                Data=@BindingContext.LinkTypeDict
                                                TextProperty="Value" ValueProperty="Key"
                                                @bind-Value=@BindingContext.SelectedNode.LinkType
                                                Change=@BindingContext.SaveRamsComponent
                                                AllowClear="true" Class="form-control"
                                                Disabled=@BindingContext.DisableBlockEditing/>
                            </div>
                            <div class="col-sm-6">
                                <RadzenDropDown TValue="int?"
                                                Data=@BindingContext.LinkMethodDict
                                                TextProperty="Value" ValueProperty="Key"
                                                @bind-Value=@BindingContext.SelectedNode.LinkMethod
                                                Change=@BindingContext.SaveRamsComponent
                                                AllowClear="true" Class="form-control"
                                                Disabled=@BindingContext.DisableBlockEditing/>
                            </div>
                        </div>
                        <TreeComponent TItem=RiskTreeObject
                                       Treeview=BindingContext.RiskTree
                                       NodeClickCallback=BindingContext.ClickRiskTreeNode/>
                    }
                </div>
            </div>
        </RadzenTabsItem>
        </Tabs>
        </RadzenTabs>
        </div>
    }
    else
    {
        <p>@((MarkupString) _localizer["RamsNoBlockSelectedTxt"].Value)</p>
    }
    </RadzenTabsItem>
    </Tabs>
    </RadzenTabs>
    </div>
    </div>
    </div>
}
<!-- Rams diagram editor -->
<div
    class="col-md-@(!BindingContext.ShowHidePanelMode ? '9' : "12") @(!BindingContext.ShowHidePanelMode ? "diagram-container-width" : "diagram-container-width-full")">
    <!-- menu bar -->
    @if (BindingContext.CurrentDiagram != null)
    {
        <div class="row">
            <div class="col-6">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Start" Gap="0.4rem" Wrap="FlexWrap.Wrap" class="btn-bar-left">
       
                    <RadzenSplitButton Text="Add block" Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary
                                       Click=@(args => BindingContext.AddBlock(args))
                                       Disabled=@BindingContext.DisableAddButton>
                        <ChildContent>
                            <RadzenSplitButtonItem Text="Before" Value="0" Icon="navigate_before" />
                            <RadzenSplitButtonItem Text="After" Value="1" Icon="navigate_next" />
                        </ChildContent>
                    </RadzenSplitButton>

                    <RadzenButton Icon="delete" Text="Remove selected" Click=@BindingContext.RemoveComponent
                                  Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary
                                  Visible="BindingContext.SelectedItem != null"
                                  Disabled="BindingContext.SelectedItem == null || !GlobalDataService.CanEdit"/>

                    <!-- TODO: turn on when custom connectors should be made available -->
                    @* <RadzenButton Icon="compare_arrows" class="my-2 mx-1" Text="Select destination" Click=@BindingContext.LinkSelected Size=ButtonSize.Small *@
                    @*               ButtonStyle=ButtonStyle.Secondary Disabled=!GlobalDataService.CanEdit/> *@

                    <RadzenButton Icon="content_copy" Text="Copy" Click=@BindingContext.CopyComponent
                                  Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary 
                                  Visible="BindingContext.SelectedItem != null"
                                  Disabled="BindingContext.SelectedItem == null || !GlobalDataService.CanEdit"/>
                
                    <RadzenButton Icon="content_paste" Text="Paste" Click=@BindingContext.PasteComponent
                                  Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary
                                  Visible="BindingContext.CopiedItem != null"
                                  Disabled="BindingContext.CopiedItem == null || !GlobalDataService.CanEdit" />

                    <RadzenSplitButton @ref=@BindingContext.DiagramButton Text="Nest diagram"
                                       Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary
                                       Click=@(args => BindingContext.AddDiagram(args))
                                       Visible="(BindingContext.CurrentDiagram != null && BindingContext.Diagrams.Any(x => x.Id != BindingContext.CurrentDiagram.Id))"
                                       Disabled="BindingContext.DisableAddButton || BindingContext.CurrentDiagram == null || !BindingContext.Diagrams.Any(x => x.Id != BindingContext.CurrentDiagram.Id)">
                                            <ChildContent>
                                                @foreach (var diagram in BindingContext.Diagrams.Where(x => x.Id > 0 && x.Id != BindingContext.CurrentDiagram?.Id))
                                                {
                                                    <RadzenSplitButtonItem Text=@diagram.Name Value=@diagram.Id.ToString() />
                                                }
                                            </ChildContent>
                    </RadzenSplitButton>
                
                    <RadzenButton Icon="undo" Text="Undo" Click=@BindingContext.UndoChanges Size=ButtonSize.Small
                                  ButtonStyle=ButtonStyle.Secondary
                                  Visible="BindingContext.UndoList.Count > 0"
                                  Disabled="BindingContext.UndoList.Count == 0 || !GlobalDataService.CanEdit " />
                
                </RadzenStack>
            </div>
            <div class="col-6">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" Gap="0.4rem" Wrap="FlexWrap.Wrap" class="btn-bar-right">
       
                            <RadzenButton Icon="camera_enhance" Text=@_localizer["RamsDownloadDiagramBtnTxt"]
                                          Click=BindingContext.DownloadImage Size=ButtonSize.Small
                                          ButtonStyle=ButtonStyle.Secondary />
                            <RadzenButton Icon="chrome_reader_mode" Text=@_localizer["RamsPanelHideBtnTxt"]
                                          Click=BindingContext.HidePanelMode Size=ButtonSize.Small
                                          ButtonStyle=ButtonStyle.Secondary />
                            <RadzenButton Icon="settings_applications" Text=@_localizer["RamsDisplayModeBtnTxt"]
                                          Click=BindingContext.SwitchDisplayMode Size=ButtonSize.Small
                                          ButtonStyle=ButtonStyle.Secondary />
                            <RadzenButton Icon="developer_mode" Text=@(BindingContext.ShowDiagram ? _localizer["RamsModeBtnTxt"] : _localizer["RamsModeBtnTxtDefault"])
                                          Click=BindingContext.SwitchMode Size=ButtonSize.Small 
                                          ButtonStyle=ButtonStyle.Secondary />
                            <RadzenButton Icon="save" Text=@_localizer["RamsSaveBtnTxt"]
                                          Click=BindingContext.SaveRamsDiagram Size=ButtonSize.Small
                                          ButtonStyle=ButtonStyle.Secondary 
                                          Disabled=!GlobalDataService.CanEdit />
                </RadzenStack>
            </div>
        </div>
    }

    @if (BindingContext.ShowDisplayMode)
    {
        <div class="row" style="border:1px solid #e9ecef; padding: 10px; margin-bottom: 10px;margin-top: 10px">
            <div class="col-md-2">
                <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@BindingContext.ShownValues
                                       TValue="int" Change=@BindingContext.UpdateDisplayMode>
                    <Items>
                        <RadzenRadioButtonListItem Text=@_localizer["RamsTechValTxt"] Value="1"/>
                        <RadzenRadioButtonListItem Text=@_localizer["RamsFunctValTxt"] Value="2"/>
                    </Items>
                </RadzenRadioButtonList>
            </div>
            <div class="col-md-2">
                <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@BindingContext.ShownNotation
                                       TValue="int" Change=@BindingContext.UpdateDisplayMode>
                    <Items>
                        <RadzenRadioButtonListItem Text="Decimal notation" Value="1"/>
                        <RadzenRadioButtonListItem Text="Scientific notation" Value="2"/>
                    </Items>
                </RadzenRadioButtonList>
            </div>
            <div class="col-md-2">
                <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@BindingContext.ShownFunctions
                                       TValue="int" Change=@BindingContext.UpdateDisplayMode>
                    <Items>
                        <RadzenRadioButtonListItem Text="Show MTBF" Value="1"/>
                        <RadzenRadioButtonListItem Text="Show Lambda" Value="2"/>
                    </Items>
                </RadzenRadioButtonList>
            </div>
            <div class="col-md-2">
                <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@BindingContext.ShownCalculation
                                       TValue="int" Change=@BindingContext.UpdateDisplayMode>
                    <Items>
                        <RadzenRadioButtonListItem Text="Show PFD" Value="1"/>
                        <RadzenRadioButtonListItem Text="Show SIL" Value="2"/>
                        <RadzenRadioButtonListItem Text="Show None" Value="4"/>
                    </Items>
                </RadzenRadioButtonList>
            </div>

            <div class="col-md-2">
                <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@BindingContext.ShownReliability
                                       TValue="int" Change=@BindingContext.UpdateDisplayMode>
                    <Items>
                        <RadzenRadioButtonListItem Text="Hours" Value="1"/>
                        <RadzenRadioButtonListItem Text="Years" Value="2"/>
                    </Items>
                </RadzenRadioButtonList>
            </div>
        </div>
    }

    <!-- diagram -->
    <div class="row @(BindingContext.ShowDiagram ? "diagram-border" : "")">
        @if (BindingContext.ShowDiagram)
        {
            <RamsDiagramComponent @ref=@BindingContext.DiagramComponent
                                  AvailableTime=@(BindingContext.CurrentDiagram?.AvailableTime ?? 0)
                                  Language=@BindingContext.Language
                                  Title=@BindingContext.CurrentDiagram?.Name
                                  Diagram=@BindingContext.DiagramContent
                                  DisplayMode=@BindingContext.DisplayMode
                                  ComponentSelectCallback=@(x => BindingContext.SelectComponent(x))
                                  ComponentReOrderCallback=@(response => BindingContext.SaveAndReorderRamsDiagram(response))
                                  ComponentCollapseCallback=@BindingContext.CollapseContainer
                                  ComponentParallelCallback=@BindingContext.SaveRamsDiagram
                                  AddItemsToNewContainerCallback=@BindingContext.AddItemsToNewContainer
                                  DrawLines=@BindingContext.DrawConnectionLines
                                  RemoveLines=@BindingContext.RemoveLines
                                  RecalculateDiagramSize=@BindingContext.RecalculateDiagramSize
                                  RamsAllowedDept=@BindingContext.RamsAllowedDeptSetting?.IntValue>
            </RamsDiagramComponent>
        }
        else if (!BindingContext.ShowDiagram)
        {
            <UtilityGrid TItem=RamsModel
                         @ref=BindingContext.RamsGrid
                         Data=@BindingContext.RamsComponents.ToList()
                         FileName=@GridNames.Rams.RamsComponents
                         SaveCallback=@BindingContext.SaveRamsModel
                         FileUploadCallBack=@BindingContext.ProcessUpload
                         Interactive=true
                         AllowXlsExport=true
                         AllowFiltering=true />
        }
        else
        {
            <p class="selection-border text-center">@((MarkupString) _localizer["RamsSelectDiagramTxt"].Value)</p>
        }
    </div>
</div>
</div>