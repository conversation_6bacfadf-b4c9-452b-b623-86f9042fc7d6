using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Spares;

public class SpareEditViewModel : BaseViewModel, IEntityEditorViewModel
{
    private readonly IRiskAnalysisManager _riskAnalysisManager;
    private readonly ISparePartManager _sparePartManager;
    private readonly ILookupManager _lookupManager;

    public SpareEditViewModel(
        ILoggerFactory loggerFactory,
        IRiskAnalysisManager riskAnalysisManager,
        DialogService dialogService,
        ISparePartManager sparePartManager,
        ILookupManager lookupManager) : base(loggerFactory, lookupManager)
    {
        _riskAnalysisManager = riskAnalysisManager;
        _sparePartManager = sparePartManager;
        _dialogService = dialogService;
        _lookupManager = lookupManager;
    }

    [Parameter] public int SpareId { get; set; }

    [Parameter] public int RiskId { get; set; }
    
    [Parameter] public bool Pmo { get; set; }

    [Parameter] public EventCallback<SpareModel> Callback { get; set; }

    public SpareModel Spare { get; set; }

    public string ErrorText { get; set; }

    public bool ShowError { get; set; }

    private LookupSettingModel DefaultDepreciationPct { get; set; }

    private LookupSettingModel DefaultSpareManagementPct { get; set; }
    
    private LookupSettingModel DefaultModificationPct { get; set; }

    public EntityEditorMode EditorMode
    {
        get
        {
            return SpareId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    public override void OnInitialized()
    {
        switch (EditorMode)
        {
            case EntityEditorMode.Create:
                Spare = new SpareModel { Id = SpareId , Pmo = Pmo};
                break;
            default:
                Spare = _sparePartManager.GetSpareById(SpareId);
                break;
        }

        DefaultDepreciationPct = _lookupManager.GetLookupSettingByPropertyName("DepreciationPct");
        DefaultSpareManagementPct = _lookupManager.GetLookupSettingByPropertyName("SpareManPct");
        DefaultModificationPct = _lookupManager.GetLookupSettingByPropertyName("ModificationPct");
    }

    public void CalculateSpareParts()
    {
        var risk = _riskAnalysisManager.GetRisk(Spare.MrbId > 0 ? Spare.MrbId : RiskId);
       
        Spare.CalculateSparePartCount(risk);
        CalculateSpareCosts();
        
       
        //Does task and spare parts calculation
        risk.CalculatePreventiveCosts(DefaultDepreciationPct.DecimalValue, DefaultSpareManagementPct.DecimalValue,
            DefaultModificationPct.DecimalValue, true);
        
        _riskAnalysisManager.UpdateRisk(risk);
    }

    public void CalculateSpareCosts()
    {
        Spare.CalculateSparePartCost();
        Spare = _sparePartManager.UpdateSpare(Spare);
    }

    public bool CalculateSparePartsBtnDisabled => Spare.ObjectCount == null || Spare.OrderLeadTime == null || Spare.Reliability == null;

    public async Task ValidTaskSubmitted(SpareModel spare)
    {
        await Callback.InvokeAsync(spare);
        _dialogService.Close();
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ErrorText = "Update NOT executed, invalid form submitted";
        _logger.LogWarning($"Invalid {EditorMode} form submitted for {nameof(Spare)} with Id {SpareId}.");
        _logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }
}