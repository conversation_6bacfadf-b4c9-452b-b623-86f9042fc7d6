@page "/value-risk-organizer/spares/{SpareId:int}"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<SpareEditViewModel>
@inject IStringLocalizer<SpareEdit> _localizer
@inject IGlobalDataService GlobalDataService;

@if (BindingContext.Spare != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem=SpareModel Data=@BindingContext.Spare
                                      Submit=@BindingContext.ValidTaskSubmitted OnInvalidSubmit=@BindingContext.InvalidTaskSubmitted>
        <div class="row">
            <div class="col-sm-2">
                <div class="form-group neg-margin-small">
                    <label>Id:</label>
                    <label class="form-control neg-margin-small">@BindingContext.Spare.Id</label>
                </div>
            </div>
            <div class="col-sm-10">
                <AMproverTextBox @bind-Value="BindingContext.Spare.Name" Label=@_localizer["SeSpareLbl"] Required=true MaxLength="60"/>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <AMproverTextArea @bind-Value=@BindingContext.Spare.Remarks Cols="30" Rows="3" Label=@_localizer["SeRemarksLbl"] />
            </div>
        </div>
        <RadzenTabs>
            <Tabs>
                <RadzenTabsItem Text=@_localizer["SeDescriptionTab"]> 
                    <div class="row"> 
                        <div class="col-sm-4">
                            <AMproverNumberInput Name="ObjectsInstalled"
                                Label=@_localizer["SeObjectsInstalledLbl"] TValue="int?"
                                @bind-Value="BindingContext.Spare.ObjectCount" />
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput Name="OrderLeadTime"
                                Label=@_localizer["SeOrderLeadTimeLbl"] TValue="decimal?" 
                                @bind-Value="BindingContext.Spare.OrderLeadTime" />
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput Name="Reliability" Min=10 Max=99.99999m 
                                Label=@_localizer["SeReliabilityLbl"] Format="p5" TValue="decimal?" 
                                @bind-Value="BindingContext.Spare.Reliability" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <RadzenButton Text=@_localizer["SeCalculateBtn"] Click=@BindingContext.CalculateSpareParts Disabled=@(BindingContext.CalculateSparePartsBtnDisabled || !GlobalDataService.CanEdit) />
                            @if(BindingContext.CalculateSparePartsBtnDisabled || !GlobalDataService.CanEdit)
                            {
                                <small>@_localizer["SeCalcExplanationTxt"]</small>
                            }
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-1"></div>
                        <div class="col-sm-10 bordered-container">
                            <div class="row">
                                <div class="col-sm-4">
                                    <AMproverNumberInput Name="PurchasePrice" Change=@BindingContext.CalculateSpareCosts Min=0 Max=int.MaxValue
                                        Label=@_localizer["SePurchasePriceLbl"]  Format="c0" TValue="decimal?" 
                                        @bind-Value="BindingContext.Spare.PurchasePrice" Required=true />
                                </div>
                                <div class="col-sm-4">
                                    <AMproverNumberInput Name="NoOfItems" Change=@BindingContext.CalculateSpareCosts TValue="int?" Min=0 Max=int.MaxValue
                                        Label=@_localizer["SeNoOfSparesLbl"]  
                                        @bind-Value="BindingContext.Spare.NoOfItems" Required=true/>
                                </div>
                                <div class="col-sm-4">
                                    <AMproverNumberInput ReadOnly="true"  
                                        Label=@_localizer["SeTotalCostLbl"] Format="c0" TValue="decimal?" 
                                        @bind-Value="BindingContext.Spare.Costs" class="form-control"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>
                    <RadzenTabsItem Text=@_localizer["SeDetailsTab"]> 
                        <div class="row">
                            <div class="col-sm-4">
                                <AMproverNumberInput TValue="int?" @bind-Value="BindingContext.Spare.PurchaseYear" Label=@_localizer["SeFirstYearLbl"] />
                            </div>
                            <div class="col-sm-4">
                                <AMproverNumberInput Label=@_localizer["SeYearlyCostLbl"]  
                                    Format="c0" TValue="decimal?" @bind-Value="BindingContext.Spare.YearlyCost" class="form-control"/>
                            </div>
                            <div class="col-sm-4">
                                <AMproverNumberInput Label=@_localizer["SeDepreciationPctLbl"] TValue="decimal?" 
                                     Format="P2" @bind-Value="BindingContext.Spare.DepreciationPct" class="form-control" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <AMproverNumberInput Label=@_localizer["SeStockNumberLbl"] TValue="int?" 
                                   @bind-Value="BindingContext.Spare.StockNumber" class="form-control" />
                            </div>
                            <div class="col-sm-4">
                            <AMproverTextBox @bind-Value="BindingContext.Spare.Category" Label=@_localizer["SeCategoryLbl"] MaxLength="50" />
                            </div>
                            <div class="col-sm-4">
                            <AMproverTextBox @bind-Value="BindingContext.Spare.SupplierId" Label=@_localizer["SeSupplierLbl"] MaxLength="50" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                            <AMproverTextBox @bind-Value="BindingContext.Spare.VendorId" Label=@_localizer["SeVendorLbl"] MaxLength="50" />
                            </div>
                            <div class="col-sm-6">
                            <AMproverTextBox @bind-Value="BindingContext.Spare.ReferenceId" Label=@_localizer["SeReferenceIdLbl"] MaxLength="50" />
                            </div>
                        </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>

        <br/>
        <RadzenButton type="submit" class="btn btn-primary" Text=@_localizer["SeSaveBtn"] Disabled=!GlobalDataService.CanEdit />
    </Radzen.Blazor.RadzenTemplateForm> 
}

@code{

    [Parameter] public int SpareId { get; set; }

    [Parameter] public int RiskId { get; set; }
    
    [Parameter] public bool Pmo { get; set; }

    [Parameter] public EventCallback<SpareModel> Callback { get; set; }
}