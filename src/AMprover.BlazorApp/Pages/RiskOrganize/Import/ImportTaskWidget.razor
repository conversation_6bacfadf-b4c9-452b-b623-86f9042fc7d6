@using AMprover.BusinessLogic.Models.Import;
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<ImportTaskWidgetViewModel>
@inject IStringLocalizer<RiskOrganizer> _localizer


<div class="row">
    <div class="col-12">

        @if (FileAnalyzed)
        {
            <div class=@BindingContext.TaskImportAnalysis.GetAnalyzeStatus() >
                Importing: '@BindingContext.TaskImportAnalysis.FileName' <br/>
                onto RiskObject '@BindingContext.TaskImportAnalysis.RiskObjectName' <br/>

                <br/>
                @(FoundTaskInImport() ? "✔" : "❌") Tasks in Import: @BindingContext.TaskImportAnalysis.TasksInImport <br />
                @(FoundRiskInImport() ? "✔" : "❌") Risk Id's in Import: @BindingContext.TaskImportAnalysis.RisksInImport <br />

                @if (FoundTaskInImport() && FoundRiskInImport())
                {
                    <span>
                        @(RisksInDbMatchImport() ? "✔" : "❌") Risks found in Database: @BindingContext.TaskImportAnalysis.RisksFoundInDB
                        @if (RisksInDbMatchImport())
                        {
                            <br />
                            <span>
                                @(AddPlusUpdateMatchesTasksInImport() ? "✔" : "❌") Tasks that will update: @BindingContext.TaskImportAnalysis.TasksToUpdate <br />
                                @(AddPlusUpdateMatchesTasksInImport() ? "✔" : "❌") Tasks that will be added: @BindingContext.TaskImportAnalysis.TasksToAdd <br />
                            </span>
                        }
                    </span>
                }
            </div>
        }

        <div class="form-group py-2">
            @GetText()
        </div>

        @if (Status == ImportTaskStatus.SelectFile || !BindingContext.TaskImportAnalysis.ImportFileAllowed())
        {
            <div class="form-group">
                <AMDropdown @bind-Value=@BindingContext.RiskObjectId
                            Data=@BindingContext.RiskObjects
                            Required=true />
            </div>

            <div class="form-group pb-2">
                <RadzenFileInput @ref=@BindingContext.UploadRef
                            @bind-Value=@BindingContext.FileUpload
                            Accept=".xls, .xlsx"
                            TValue="string"
                            ChooseText=@_localizer["RoImportRiskSelectFile"]
                            Change=@BindingContext.ChangeFile />
            </div>

            <div class="form-group pb-2">
                <AMproverCheckbox @bind-Value=@BindingContext.UseImportId Label="Use Import Id" />
                <div class="alert alert-info" role="alert">@_localizer["RoImportActionUseImportId"]</div>
            </div>

            <div class="form-group">
                <RadzenButton Click=@BindingContext.AnalyzeImportFile
                        Text=@_localizer["RoImportAnalyzeFile"]
                        Disabled=@(string.IsNullOrWhiteSpace(BindingContext.FileUpload)) />
            </div>
        }
        else if (Status == ImportTaskStatus.ImportTasks)
        {
            <RadzenButton Click=@BindingContext.PreviousStatus
                      Text=@_localizer["RoImportPrevious"] />

            <RadzenButton Click=@BindingContext.ImportTasks
                      Text=@_localizer["RoImportRiskStartImport"] />
        }
        else if (Status == ImportTaskStatus.Finished)
        {

        }
        else
        {
            foreach (var replace in BindingContext.ReplaceOptions[Status].ReplaceValues)
            {
                <AMDropdown 
                    Label=@replace.Original
                    Value=@replace.Replaced
                    Data=@BindingContext.ReplaceOptions[Status].Options 
                    Change=@(args => ImportTaskWidgetViewModel.UpdateValue(replace, (string)args))/>
            }

            <RadzenButton Click=@BindingContext.PreviousStatus
                      Text=@_localizer["RoImportPrevious"] />

            <RadzenButton Click=@BindingContext.NextStatus
                      Text=@_localizer["RoImportNext"]
                      Disabled=@(BindingContext.ReplaceOptions[Status].ReplaceValues.Any(x => string.IsNullOrWhiteSpace(x.Replaced))) />
        }

        @if(BindingContext.ShowError())
        {
            <div class="form-group pt-3">
                <div class="alert alert-danger" role="alert">
                    @BindingContext.ImportResult.ErrorMessage
                </div>
            </div>
        }

        @if (BindingContext.ShowSuccess())
        {
            <div class="form-group pt-3">
                <div class="alert alert-success" role="alert">
                    Succesfully Added @(BindingContext.ImportResult.ItemsAdded) items
                    @if (BindingContext.ImportResult.ItemsUpdated > 0)
                    {
                        <br/>
                        <span>Succesfully Updated @(BindingContext.ImportResult.ItemsUpdated) items</span>
                    }
                </div>
            </div>
        }

    </div>
</div>

@code{
    ImportTaskStatus Status => BindingContext.Status;

    bool FileAnalyzed => BindingContext.Status != ImportTaskStatus.SelectFile && BindingContext.Status != ImportTaskStatus.Finished;

    string GetText()
    {
        if (FileAnalyzed && !BindingContext.TaskImportAnalysis.ImportFileAllowed())
            return _localizer["RoImportTaskNotAllowed"].Value;

        var text = _localizer[GetKey()].Value;
        text = text.Replace("[type]", _localizer[$"{Status}"]);

        return text;
    }

    string GetKey() =>
        Status switch
        {
            ImportTaskStatus.SelectFile => "RoImportTaskTxt",
            ImportTaskStatus.ImportTasks => "RoImportReadyForImport",
            ImportTaskStatus.Finished => "RoImportFinished",
            _ => "RoImportUpdateReference"
        };

    private bool FoundTaskInImport()
    {
        return BindingContext.TaskImportAnalysis?.TasksInImport > 0 == true;
    }

    private bool FoundRiskInImport()
    {
        return BindingContext.TaskImportAnalysis?.RisksInImport > 0 == true;
    }

    private bool RisksInDbMatchImport()
    {
        return BindingContext.TaskImportAnalysis.RisksFoundInDB == BindingContext.TaskImportAnalysis.RisksInImport;
    }

    private bool AddPlusUpdateMatchesTasksInImport()
    {
        return BindingContext.TaskImportAnalysis.TasksToUpdate + BindingContext.TaskImportAnalysis.TasksToAdd == BindingContext.TaskImportAnalysis.TasksInImport;
    }
}