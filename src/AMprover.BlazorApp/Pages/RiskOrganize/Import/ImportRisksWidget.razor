@using AMprover.BusinessLogic.Models.Import
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<ImportRisksWidgetViewModel>
@inject IStringLocalizer<RiskOrganizer> _localizer


<div class="row">
    <div class="col-12">

        <div class="form-group">
            @_localizer["RoImportRiskTxt"]
        </div>

        @if(Status == ImportRiskStatus.SelectFile)
        {
            <div class="form-group">
                <AMDropdown @bind-Value=@BindingContext.RiskObjectId
                            Data=@BindingContext.RiskObjects
                            Required=true />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@BindingContext.GenerateObjects /> @_localizer["RoImportRiskGenerateObjLbl"] <br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@BindingContext.GenerateFailureModes /> @_localizer["RoImportRiskGenerateFailModeLbl"] <br /><br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@BindingContext.UseImportId /> Use Import Id
                <div class="alert alert-info mt-2" role="alert">@_localizer["RoImportRiskUseImportId"]</div>
            </div>

            <div class="form-group pb-2">
                <RadzenFileInput @ref=@BindingContext.UploadRef
                                 @bind-Value=@BindingContext.FileUpload
                                 Accept=".xls, .xlsx"
                                 TValue="string"
                                 ChooseText=@_localizer["RoImportRiskSelectFile"]
                                 Change=@BindingContext.ChangeFile />
            </div>

            <div class="form-group">
                <RadzenButton Click=@BindingContext.AnalyzeFile
                              Text=@_localizer["RoImportRiskAnalyzeFile"]
                              Disabled=@(string.IsNullOrWhiteSpace(BindingContext.FileUpload)) />
            </div>
        }
        else if (Status == ImportRiskStatus.ImportRisks)
        {
            <!-- Show status details -->
            <div class=@(AnalyzeSucces() ? "alert alert-success" : "alert alert-danger")>
                Importing: '@Result.FileName' <br />
                onto RiskObject '@Result.RiskObjectName' <br />

                <br />
                @(FoundRiskInImport() ? "✔" : "❌") Risks in Import @Result.RisksInFile <br />

                @if(!FoundRiskInImport())
                {
                    <div class="m-2">
                        There were no Risks found in your import file. Please check that you uploaded the correct file.
                    </div>
                }
                else
                {
                    <div>
                        ✔ Risks already on RiskObject: @Result.RisksInDatabase  <br />
                        ✔ Risks that will be added: @Result.RisksToAdd <br />
                        ✔ Risks that will be updated: @Result.RisksToUpdate <br />

                        <!-- Objects Handling -->
                        @if (ObjectsSucces() && Result.MissingObjects > 0)
                        {
                            <span>✔ Objects that will be added to the database: @Result.MissingObjects <br /></span>
                        }
                        else if (Result.MissingObjects > 0)
                        {
                            <span>❌ Objects missing from the database: @Result.MissingObjects <br /></span>
                        }

                        <!-- FailureMode Handling -->
                        @if (FailureModeSuccess() && Result.MissingFailureModes > 0)
                        {
                            <span>✔ FailureModes that will be added to the database: @Result.MissingFailureModes <br /></span>
                        }
                        else if (Result.MissingFailureModes > 0)
                        {
                            <span>❌ FailureModes Missing from database: @Result.MissingFailureModes <br /></span>
                        }

                        @if(!ObjectsSucces())
                        {
                            <hr/>

                            <div class="m-2">
                                There are @Result.MissingObjects Object(s) in the import file that do not exist in the Database.
                                Please go back and Check <b>@_localizer["RoImportRiskGenerateObjLbl"]</b> if you want to generate these objects.
                            </div>
                        }

                        @if(!FailureModeSuccess())
                        {
                            <hr/>

                            <div class="m-2">
                                There are @Result.MissingFailureModes FailureMode(s) in the import file that do not exist in the Database.
                                Please go back and Check <b>@_localizer["RoImportRiskGenerateFailModeLbl"]</b> if you want to generate these FailureModes.
                            </div>
                        }
                    </div>
                }
            </div>

            <div class="row mt-5">
                <div class="col-6 center">
                    <RadzenButton Click=@BindingContext.PreviousStatus
                                    Text=@_localizer["RoImportPrevious"] />
                </div>
                <div class="col-6 center">
                    <RadzenButton Click=@BindingContext.UploadRisks
                                    Text=@_localizer["RoImportRiskStartImport"]
                                    Disabled=@(string.IsNullOrWhiteSpace(BindingContext.FileUpload)) />
                </div>
            </div>

        }
        else if(Status == ImportRiskStatus.Finished)
        {
            @if (BindingContext.ShowError())
            {
                <div class="form-group pt-3">
                    <div class="alert alert-danger" role="alert">
                        @BindingContext.ImportResult.ErrorMessage
                    </div>
                </div>
            }

            @if (BindingContext.ShowSuccess())
            {
                <div class="form-group pt-3">
                    <div class="alert alert-success" role="alert">
                        Succesfully imported @(BindingContext.ImportResult.ItemsAdded) items
                    </div>
                </div>
            }
        }

    </div>
</div>

@code {
    AnalyzeRiskImportResult Result => BindingContext.ImportAnalyzeResult;

    ImportRiskStatus Status => Result.Status;

    bool FoundRiskInImport() => Result.RisksInFile > 0;

    bool ObjectsSucces() => Result.MissingObjects == 0 || BindingContext.GenerateObjects;

    bool FailureModeSuccess() => Result.MissingFailureModes == 0 || BindingContext.GenerateFailureModes;

    bool AnalyzeSucces() => FoundRiskInImport() && ObjectsSucces() && FailureModeSuccess();
}