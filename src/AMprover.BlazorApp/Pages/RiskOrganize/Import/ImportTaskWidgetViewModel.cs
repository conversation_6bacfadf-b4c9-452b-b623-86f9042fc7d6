using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Import;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Import;

public class ImportTaskWidgetViewModel : BaseViewModel
{
    private IDropdownManager _dropdownManager { get; }
    private ITaskImportManager _taskImportManager { get; }

    public ImportTaskWidgetViewModel(
        ILoggerFactory loggerFactory,
        IDropdownManager dropdownManager,
        ITaskImportManager taskImportManager,
        DialogService dialogService) : base(loggerFactory, dialogService)
    {
        _dropdownManager = dropdownManager;
        _taskImportManager = taskImportManager;
        _dialogService = dialogService;
    }

    public RadzenFileInput<string> UploadRef { get; set; }

    public int RiskObjectId { get; set; }

    public Dictionary<int, string> RiskObjects { get; set; }

    public ImportResult ImportResult { get; set; }

    public List<TaskImportModel> ImportedTasks { get; set; }

    public ImportTaskStatus Status { get; set; }

    public AnalyzeTaskImportResult TaskImportAnalysis { get; set; }

    public Dictionary<ImportTaskStatus, ImportEditStatus> ReplaceOptions { get; set; }

    public string FileUpload { get; set; }

    public bool UseImportId { get; set; }

    public override void OnInitialized()
    {
        RiskObjects = _dropdownManager.GetRiskObjectDict();
        RiskObjectId = RiskObjects.Keys.FirstOrDefault();
        ImportResult = new();
        Status = ImportTaskStatus.SelectFile;

        ReplaceOptions = new Dictionary<ImportTaskStatus, ImportEditStatus>
        {
            { ImportTaskStatus.UpdateInitiator, new ImportEditStatus { Options = _dropdownManager.GetInitiatorDict().ToStringDict() } },
            { ImportTaskStatus.UpdateExecutor, new ImportEditStatus { Options = _dropdownManager.GetExecutorDict().ToStringDict() } },
            { ImportTaskStatus.UpdateIntervalUnit, new ImportEditStatus { Options = _dropdownManager.GetIntervalUnitDict().ToStringDict() } },
            { ImportTaskStatus.UpdateWorkPackage, new ImportEditStatus { Options = _dropdownManager.GetWorkpackageDict().ToStringDict() } },
            { ImportTaskStatus.UpdatePolicy, new ImportEditStatus { Options = _dropdownManager.GetPolicyDict().ToStringDict() } }
        };
    }

    /// <summary>
    /// Blazor's Bind-Value clears the property once you stop rendering the component, therefore we update the values like this
    /// </summary>
    public static void UpdateValue(ReplaceTextValue replaceObj, string text) => replaceObj.Replaced = text;

    public async Task AnalyzeImportFile()
    {
        Status = ImportTaskStatus.SelectFile;
        var base64Input = FileUpload?.Split(',').LastOrDefault()?.Trim();

        if (string.IsNullOrWhiteSpace(base64Input))
            return;

        ImportedTasks = base64Input.GetExcelData<TaskImportModel>(1)
                .Select(x => x.Value.TrimStringProperties())
                .Where(x => !string.IsNullOrWhiteSpace(x.Name))
                .ToList();

        ShowLoadingDialog();
        TaskImportAnalysis = await _taskImportManager.AnalyzeImportFile(ImportedTasks, RiskObjectId, UseImportId);
        TaskImportAnalysis.FileName = UploadRef.FileName;
        TaskImportAnalysis.RiskObjectName = RiskObjects[RiskObjectId];
        _dialogService.Close();

        SetReplaceOptionsValues(ImportTaskStatus.UpdateInitiator);
        SetReplaceOptionsValues(ImportTaskStatus.UpdateExecutor);
        SetReplaceOptionsValues(ImportTaskStatus.UpdateIntervalUnit);
        SetReplaceOptionsValues(ImportTaskStatus.UpdatePolicy);
        SetReplaceOptionsValues(ImportTaskStatus.UpdateWorkPackage);

        NextStatus();
    }

    private void SetReplaceOptionsValues(ImportTaskStatus status) =>
        ReplaceOptions[status].ReplaceValues = TaskImportAnalysis.MissingItems(status).Select(x => new ReplaceTextValue(x)).ToList();

    public void NextStatus() => NextStatus(Status, 1);

    public void PreviousStatus() => NextStatus(Status, -1);

    public void NextStatus(ImportTaskStatus status, int step)
    {
        status = (ImportTaskStatus)((int)status + step);
        ImportResult = new();

        switch (status)
        {
            case ImportTaskStatus.Finished or ImportTaskStatus.SelectFile or ImportTaskStatus.ImportTasks:
                Status = status;
                return;

            default:
                if (TaskImportAnalysis.MissingItems(status).Any())
                {
                    Status = status;
                    return;
                }
                break;
        }

        NextStatus(status, step);
    }

    public async Task ImportTasks()
    {
        foreach(var replaceText in ReplaceOptions[ImportTaskStatus.UpdateInitiator].ReplaceValues)
            ImportedTasks.Where(x => x.Initiator == replaceText.Original).ToList().ForEach(x => x.Initiator = replaceText.Replaced);

        foreach (var replaceText in ReplaceOptions[ImportTaskStatus.UpdateExecutor].ReplaceValues)
            ImportedTasks.Where(x => x.Executor == replaceText.Original).ToList().ForEach(x => x.Executor = replaceText.Replaced);

        foreach (var replaceText in ReplaceOptions[ImportTaskStatus.UpdateIntervalUnit].ReplaceValues)
            ImportedTasks.Where(x => x.IntervalUnit == replaceText.Original).ToList().ForEach(x => x.IntervalUnit = replaceText.Replaced);

        foreach (var replaceText in ReplaceOptions[ImportTaskStatus.UpdateWorkPackage].ReplaceValues)
            ImportedTasks.Where(x => x.WorkPackage == replaceText.Original).ToList().ForEach(x => x.WorkPackage = replaceText.Replaced);

        foreach (var replaceText in ReplaceOptions[ImportTaskStatus.UpdatePolicy].ReplaceValues)
            ImportedTasks.Where(x => x.Policy == replaceText.Original).ToList().ForEach(x => x.Policy = replaceText.Replaced);

        await ImportVerifiedTasks();
    }

    public async Task ImportVerifiedTasks()
    {
        NextStatus();
        ShowLoadingDialog();
        ImportResult = await _taskImportManager.ImportTasks(ImportedTasks, RiskObjectId, UseImportId).ConfigureAwait(false);

        // this only closes the loading Dialog, not the current dialog
        _dialogService.Close();

        if (ImportResult.Success)
            FileUpload = null;
    }

    private void ShowLoadingDialog()
    {
        _dialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    public void ChangeFile() => ImportResult = new();

    public bool ShowError() => !string.IsNullOrWhiteSpace(ImportResult.ErrorMessage);

    public bool ShowSuccess() => ImportResult.Success;
}