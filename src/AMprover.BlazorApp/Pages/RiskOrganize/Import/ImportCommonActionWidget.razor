@using AMprover.BlazorApp.Pages.PortfolioSetup
@using AMprover.BusinessLogic.Models.Import
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<ImportCommonActionWidgetViewModel>
@inject IStringLocalizer<CommonActionEdit> _localizer


<div class="row">
    <div class="col-12">

        @if (Status == ImportCommonActionStatus.SelectFile)
        {
            <div class="form-group">
                @_localizer["CaImportPopupText"]
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@BindingContext.ImportAnalyzeResult.GenerateStrategies /> @_localizer["CaImportGenerateStrategy"] <br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@BindingContext.ImportAnalyzeResult.GenerateInitiators /> @_localizer["CaImportGenerateInitiator"] <br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@BindingContext.ImportAnalyzeResult.GenerateExecutors /> @_localizer["CaImportGenerateExecutor"] <br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@BindingContext.ImportAnalyzeResult.GenerateUnitTypes /> @_localizer["CaImportGenerateUnitType"] <br /><br />
            </div>

            <div class="form-group pb-2">
                <RadzenFileInput @ref=@BindingContext.UploadRef
                                 @bind-Value=@BindingContext.FileUpload
                                 Accept=".xls, .xlsx"
                                 TValue="string"
                                 ChooseText=@_localizer["CaImportSelectFile"]
                                 Change=@BindingContext.ChangeFile />
            </div>

            <div class="form-group">
                <RadzenButton Click=@BindingContext.AnalyzeFile
                              Text=@_localizer["CaImportAnalyzeFile"]
                              Disabled=@(string.IsNullOrWhiteSpace(BindingContext.FileUpload)) />
            </div>
        }
        else if (Status == ImportCommonActionStatus.StartImport)
        {
            <!-- Show status details -->
            <div class=@(AnalyzeSucces() ? "alert alert-success" : "alert alert-danger")>
                Importing: '@Result.FileName' <br />

                @if (!CanProcessFile())
                {
                    @if (!foundItemsInImport())
                    {
                        <div class="m-2">
                            There were no Risks found in your import file. Please check that you uploaded the correct file.
                        </div>
                    }
                    @if(!intervalUnitsSuccess())
                    {
                        <div class="m-2">
                            Interval units are missing and can not be automatically generated. Please create the following Interval Units manually:
                            <ul>
                                @foreach(var unit in Result.MissingIntervalUnits)
                                {
                                    <li>@unit</li>
                                }
                            </ul>
                        </div>
                    }
                    @if(!WorkPackageSuccess())
                    {
                        <div class="m-2">
                            Workpackages are missing and can not be automatically generated. Please create the following Workpackages manually:
                            <ul>
                                @foreach (var wp in Result.MissingWorkpackages)
                                {
                                    <li>@wp</li>
                                }
                            </ul>
                        </div>
                    }
                }
                else
                {
                    <div>

                        @(foundItemsInImport() ? "✔" : "❌") Common actions in Import: @Result.ImportItems.Count <br />
                        ✔ Common Actions that will be added: @Result.ItemsToAdd <br />
                        ✔ Common Actions that will be updated: @Result.ItemsToUpdate <br /><br />

                        <!-- Strategies Handling -->
                        @if (Result.GenerateStrategies && Result.MissingStrategies.Count > 0)
                        {
                            <span>✔ Strategies that will be added to the database: @Result.MissingStrategies.Count <br /></span>
                        }
                        else if (Result.MissingStrategies.Count > 0)
                        {
                            <span>❌ Strategies missing from the database: @Result.MissingStrategies.Count <br /></span>
                        }

                        <!-- Initators Handling -->
                        @if (Result.GenerateInitiators && Result.MissingInitiators.Count > 0)
                        {
                            <span>✔ Initiators that will be added to the database: @Result.MissingInitiators.Count <br /></span>
                        }
                        else if (Result.MissingInitiators.Count > 0)
                        {
                            <span>❌ Initiators missing from the database: @Result.MissingInitiators.Count <br /></span>
                        }

                        <!-- Executors Handling -->
                        @if (Result.GenerateExecutors && Result.MissingExecutors.Count > 0)
                        {
                            <span>✔ Executors that will be added to the database: @Result.MissingExecutors.Count <br /></span>
                        }
                        else if (Result.MissingExecutors.Count > 0)
                        {
                            <span>❌ Executors missing from the database: @Result.MissingExecutors.Count <br /></span>
                        }

                        <!-- UnitType Handling -->
                        @if (Result.GenerateUnitTypes && Result.MissingUnitTypes.Count > 0)
                        {
                            <span>✔ UnitTypes that will be added to the database: @Result.MissingUnitTypes.Count <br /></span>
                        }
                        else if (Result.MissingUnitTypes.Count > 0)
                        {
                            <span>❌ UnitTypes missing from the database: @Result.MissingUnitTypes.Count <br /></span>
                        }

                    </div>
                }
            </div>

            <div class="row mt-5">
                <div class="col-6 center">
                    <RadzenButton Click=@BindingContext.PreviousStatus
                                  Text=@_localizer["CaImportPrevious"] />
                </div>
                <div class="col-6 center">
                    <RadzenButton Click=@BindingContext.UploadRisks
                                  Text=@_localizer["CaImportStartImport"]
                                  Disabled=!AnalyzeSucces() />
                </div>
            </div>

        }
        else if(Status == ImportCommonActionStatus.Finished)
        {
            @if (BindingContext.ShowError())
            {
                <div class="form-group pt-3">
                    <div class="alert alert-danger" role="alert">
                        @BindingContext.ImportResult.ErrorMessage
                    </div>
                </div>
            }

            @if (BindingContext.ShowSuccess())
            {
                <div class="form-group pt-3">
                    <div class="alert alert-success" role="alert">
                        Succesfully added @(BindingContext.ImportResult.ItemsAdded) items <br/>
                        Succesfully updated @(BindingContext.ImportResult.ItemsUpdated) items
                    </div>
                </div>
            }
        }

    </div>
</div>

@code {
    [Parameter] public EventCallback RefreshCommonActions { get; set; }

    AnalyzeCommonActionImportResult Result => BindingContext.ImportAnalyzeResult;

    ImportCommonActionStatus Status => Result.Status;

    bool foundItemsInImport() => Result.ImportItems.Count > 0;

    bool WorkPackageSuccess() => Result.MissingWorkpackages.Count == 0;

    bool intervalUnitsSuccess() => Result.MissingIntervalUnits.Count == 0;

    bool StrategySuccess() => Result.GenerateStrategies || Result.MissingStrategies.Count == 0;

    bool InitiatorsSucces() => Result.GenerateInitiators || Result.MissingInitiators.Count == 0;

    bool ExecutorsSucces() => Result.GenerateExecutors || Result.MissingExecutors.Count == 0;

    bool UnitTypesSuccess() => Result.GenerateUnitTypes || Result.MissingUnitTypes.Count == 0;

    bool AnalyzeSucces() => foundItemsInImport() 
                         && WorkPackageSuccess() 
                         && intervalUnitsSuccess() 
                         && StrategySuccess() 
                         && InitiatorsSucces() 
                         && ExecutorsSucces() 
                         && UnitTypesSuccess();

    bool CanProcessFile() => foundItemsInImport() && WorkPackageSuccess() && intervalUnitsSuccess();
}