using AMprover.BusinessLogic;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Models;
using Radzen.Blazor;
using AMprover.BlazorApp.Components;
using AMprover.BusinessLogic.Models.Import;
using Microsoft.AspNetCore.Components;

namespace AMprover.BlazorApp.Pages.RiskOrganize;

public class ImportCommonActionWidgetViewModel : BaseViewModel
{
    private IDropdownManager _dropdownManager { get; }
    private ICommonActionImportManager _importManager { get; }

    public ImportCommonActionWidgetViewModel(
        ILoggerFactory loggerFactory,
        IDropdownManager dropdownManager,
        ICommonActionImportManager importManager,
        DialogService dialogService) : base(loggerFactory, dialogService)
    {
        _dropdownManager = dropdownManager;
        _importManager = importManager;
        _dialogService = dialogService;
    }

    [Parameter] public EventCallback RefreshCommonActions { get; set; }

    public RadzenFileInput<string> UploadRef { get; set; }

    public int RiskObjectId { get; set; }

    public Dictionary<int, string> RiskObjects { get; set; }

    public AnalyzeCommonActionImportResult ImportAnalyzeResult { get; set; } = new();

    public ImportResult ImportResult { get; set; }

    public override void OnInitialized()
    {
        RiskObjects = _dropdownManager.GetRiskObjectDict();
        RiskObjectId = RiskObjects.Keys.FirstOrDefault();
        ImportResult = new();
        ImportAnalyzeResult = new();
        FileUpload = null;
    }

    public string FileUpload { get; set; }

    public async Task AnalyzeFile()
    {
        await AnalyzeImport(FileUpload?.Split(',').LastOrDefault()?.Trim()).ConfigureAwait(false);
    }

    private async Task AnalyzeImport(string base64Input)
    {
        ShowLoadingDialog();
        await Task.Delay(1);

        ImportAnalyzeResult.Excel = base64Input;
        ImportAnalyzeResult.FileName = !string.IsNullOrWhiteSpace(UploadRef.FileName)
            ? UploadRef.FileName
            : ImportAnalyzeResult.FileName;

        ImportAnalyzeResult = _importManager.AnalyzeImportFile(ImportAnalyzeResult);

        // this only closes the loading Dialog, not the current dialog
        _dialogService.Close();
    }

    public async Task UploadRisks()
    {
        ShowLoadingDialog();
        await Task.Delay(1);
        ImportResult = _importManager.ImportCommonActions(ImportAnalyzeResult);

        // this only closes the loading Dialog, not the current dialog
        _dialogService.Close();

        if(ImportResult.Success)
        {
            FileUpload = null;

            if (RefreshCommonActions.HasDelegate)
                await RefreshCommonActions.InvokeAsync();
        }

        ImportAnalyzeResult.Status = ImportCommonActionStatus.Finished;
    }

    private void ShowLoadingDialog()
    {
        _dialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    public void ChangeFile()
    {
        ImportResult = new();
    }

    public bool ShowError() => !string.IsNullOrWhiteSpace(ImportResult.ErrorMessage);

    public bool ShowSuccess() => ImportResult.Success;

    public void PreviousStatus()
    {
        ImportAnalyzeResult.Status = ImportCommonActionStatus.SelectFile;
    }
}