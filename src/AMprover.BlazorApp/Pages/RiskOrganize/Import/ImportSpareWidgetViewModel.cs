using AMprover.BusinessLogic;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Models;
using Radzen.Blazor;
using AMprover.BlazorApp.Components;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.Import;

namespace AMprover.BlazorApp.Pages.RiskOrganize;

public class ImportSpareWidgetViewModel : BaseViewModel
{
    private IDropdownManager _dropdownManager { get; }
    private ISpareImportManager _spareImportManager { get; }

    public ImportSpareWidgetViewModel(
        ILoggerFactory loggerFactory,
        IDropdownManager dropdownManager,
        ISpareImportManager spareImportManager,
        DialogService dialogService) : base(loggerFactory, dialogService)
    {
        _dropdownManager = dropdownManager;
        _spareImportManager = spareImportManager;
        _dialogService = dialogService;
    }

    public RadzenFileInput<string> UploadRef { get; set; }

    public int RiskObjectId { get; set; }

    public Dictionary<int, string> RiskObjects { get; set; }

    public ImportResult ImportResult { get; set; }

    public List<SpareImportModel> ImportedSpares { get; set; }

    public ImportSpareStatus Status { get; set; }

    public AnalyzeSpareImportResult SpareImportAnalysis { get; set; }

    public Dictionary<ImportSpareStatus, ImportEditStatus> ReplaceOptions { get; set; }

    public string FileUpload { get; set; }

    public bool UseImportId { get; set; }

    public override void OnInitialized()
    {
        RiskObjects = _dropdownManager.GetRiskObjectDict();
        RiskObjectId = RiskObjects.Keys.FirstOrDefault();
        ImportResult = new();
        Status = ImportSpareStatus.SelectFile;
    }

    public async Task AnalyzeImportFile()
    {
        Status = ImportSpareStatus.SelectFile;
        var base64Input = FileUpload?.Split(',').LastOrDefault()?.Trim();

        if (string.IsNullOrWhiteSpace(base64Input))
            return;

        ImportedSpares = base64Input.GetExcelData<SpareImportModel>(1)
                .Select(x => x.Value.TrimStringProperties())
                .Where(x => !string.IsNullOrWhiteSpace(x.Name))
                .ToList();

        ShowLoadingDialog();
        SpareImportAnalysis = await _spareImportManager.AnalyzeImportFile(ImportedSpares, RiskObjectId, UseImportId);
        SpareImportAnalysis.FileName = UploadRef.FileName;
        SpareImportAnalysis.RiskObjectName = RiskObjects[RiskObjectId];
        _dialogService.Close();

        NextStatus();
    }

    public void NextStatus() => NextStatus(Status, 1);

    public void PreviousStatus() => NextStatus(Status, -1);

    public void NextStatus(ImportSpareStatus status, int step)
    {
        status = (ImportSpareStatus)((int)status + step);
        ImportResult = new();

        switch (status)
        {
            case ImportSpareStatus.Finished or ImportSpareStatus.SelectFile or ImportSpareStatus.ImportSpares:
                Status = status;
                return;
        }

        NextStatus(status, step);
    }

    public async Task ImportSpares()
    {
       await ImportVerifiedSpares();
    }

    public async Task ImportVerifiedSpares()
    {
        NextStatus();
        ShowLoadingDialog();
        ImportResult = await _spareImportManager.ImportSpares(ImportedSpares, RiskObjectId, UseImportId).ConfigureAwait(false);

        // this only closes the loading Dialog, not the current dialog
        _dialogService.Close();

        if (ImportResult.Success)
            FileUpload = null;
    }

    private void ShowLoadingDialog()
    {
        _dialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    public void ChangeFile() => ImportResult = new();

    public bool ShowError() => !string.IsNullOrWhiteSpace(ImportResult.ErrorMessage);

    public bool ShowSuccess() => ImportResult.Success;
}