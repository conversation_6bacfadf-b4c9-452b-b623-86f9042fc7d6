using AMprover.BusinessLogic;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Models;
using Radzen.Blazor;
using AMprover.BlazorApp.Components;
using Microsoft.JSInterop;
using AMprover.BusinessLogic.Models.Import;

namespace AMprover.BlazorApp.Pages.RiskOrganize;

public class ImportRisksWidgetViewModel : BaseViewModel
{
    private IDropdownManager _dropdownManager { get; }
    private IRiskImportManager _riskImportManager { get; }
    private IJSRuntime _jsRuntime { get; }

    public ImportRisksWidgetViewModel(
        ILoggerFactory loggerFactory,
        IDropdownManager dropdownManager,
        IRiskImportManager riskImportManager,
        DialogService dialogService,
        IJSRuntime jsRuntime) : base(loggerFactory, dialogService)
    {
        _dropdownManager = dropdownManager;
        _riskImportManager = riskImportManager;
        _dialogService = dialogService;
        _jsRuntime = jsRuntime;
    }

    public RadzenFileInput<string> UploadRef { get; set; }

    public int RiskObjectId { get; set; }

    public Dictionary<int, string> RiskObjects { get; set; }

    public bool GenerateObjects { get; set; }

    public bool GenerateFailureModes { get; set; }

    public AnalyzeRiskImportResult ImportAnalyzeResult { get; set; } = new();

    public ImportResult ImportResult { get; set; }

    public bool UseImportId { get; set; }

    public override void OnInitialized()
    {
        RiskObjects = _dropdownManager.GetRiskObjectDict();
        RiskObjectId = RiskObjects.Keys.FirstOrDefault();
        ImportResult = new();
        ImportAnalyzeResult = new();
        FileUpload = null;
    }

    public string FileUpload { get; set; }

    public async Task AnalyzeFile()
    {
        await AnalyzeImport(FileUpload?.Split(',').LastOrDefault()?.Trim()).ConfigureAwait(false);
    }

    private async Task AnalyzeImport(string base64Input)
    {
        ShowLoadingDialog();
        await Task.Delay(1);

        var fileName = !string.IsNullOrWhiteSpace(UploadRef.FileName)
            ? UploadRef.FileName
            : ImportAnalyzeResult.FileName;

        ImportAnalyzeResult = _riskImportManager.AnalyzeImportFile(base64Input, fileName, RiskObjectId, UseImportId);

        // this only closes the loading Dialog, not the current dialog
        _dialogService.Close();
    }

    public async Task UploadRisks()
    {
        ShowLoadingDialog();
        await Task.Delay(1);
        ImportResult = _riskImportManager.ImportRisks(ImportAnalyzeResult.Risks, RiskObjectId, GenerateObjects, GenerateFailureModes, UseImportId);

        // this only closes the loading Dialog, not the current dialog
        _dialogService.Close();

        if(ImportResult.Success)
            FileUpload = null;

        ImportAnalyzeResult.Status = ImportRiskStatus.Finished;
    }

    private void ShowLoadingDialog()
    {
        _dialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    public void ChangeFile()
    {
        ImportResult = new();
    }

    public bool ShowError() => !string.IsNullOrWhiteSpace(ImportResult.ErrorMessage);

    public bool ShowSuccess() => ImportResult.Success;

    public void PreviousStatus()
    {
        ImportAnalyzeResult.Status = ImportRiskStatus.SelectFile;
    }
}