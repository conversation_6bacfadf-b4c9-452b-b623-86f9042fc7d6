@using AMprover.BusinessLogic.Models.Import;
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<ImportSpareWidgetViewModel>
@inject IStringLocalizer<RiskOrganizer> _localizer


<div class="row">
    <div class="col-12">

        @if (FileAnalyzed)
        {
            <div class=@BindingContext.SpareImportAnalysis.GetAnalyzeStatus()>
                Importing: '@BindingContext.SpareImportAnalysis.FileName' <br />
                onto RiskObject '@BindingContext.SpareImportAnalysis.RiskObjectName' <br />

                <br />
                @(FoundSparesInImport() ? "✔" : "❌") Spares in Import: @BindingContext.SpareImportAnalysis.SparesInImport <br />
                @(FoundRiskInImport() ? "✔" : "❌") Risk Id's in Import: @BindingContext.SpareImportAnalysis.RisksInImport <br />

                @if (FoundSparesInImport() && FoundRiskInImport())
                {
                    <span>
                        @(RisksInDbMatchImport() ? "✔" : "❌") Risks found in Database: @BindingContext.SpareImportAnalysis.RisksFoundInDB
                        @if (RisksInDbMatchImport())
                        {
                            <br />
                            <span>
                                @(AddPlusUpdateMatchesTasksInImport() ? "✔" : "❌") Spares that will update: @BindingContext.SpareImportAnalysis.SparesToUpdate <br />
                                @(AddPlusUpdateMatchesTasksInImport() ? "✔" : "❌") Spares that will be added: @BindingContext.SpareImportAnalysis.SparesToAdd <br />
                            </span>
                        }
                    </span>
                }
            </div>
        }

        <div class="form-group py-2">
            @GetText()
        </div>

        @if (Status == ImportSpareStatus.SelectFile || !BindingContext.SpareImportAnalysis.ImportFileAllowed())
        {
            <div class="form-group">
                <AMDropdown @bind-Value=@BindingContext.RiskObjectId
                            Data=@BindingContext.RiskObjects
                            Required=true />
            </div>

            <div class="form-group pb-2">
                <RadzenFileInput @ref=@BindingContext.UploadRef
                            @bind-Value=@BindingContext.FileUpload
                            Accept=".xls, .xlsx"
                            TValue="string"
                            ChooseText=@_localizer["RoImportRiskSelectFile"]
                            Change=@BindingContext.ChangeFile />
            </div>

            <div class="form-group pb-2">
                <AMproverCheckbox @bind-Value=@BindingContext.UseImportId Label="Use Import Id" />
                <div class="alert alert-info" role="alert">@_localizer["RoImportSparesUseImportId"]</div>
            </div>

            <div class="form-group">
                <RadzenButton Click=@BindingContext.AnalyzeImportFile
                        Text=@_localizer["RoImportAnalyzeFile"]
                        Disabled=@(string.IsNullOrWhiteSpace(BindingContext.FileUpload)) />
            </div>
        }
        else if (Status == ImportSpareStatus.ImportSpares)
        {
            <RadzenButton Click=@BindingContext.PreviousStatus
                      Text=@_localizer["RoImportPrevious"] />

            <RadzenButton Click=@BindingContext.ImportSpares
                      Text=@_localizer["RoImportRiskStartImport"] />
        }

        @if(BindingContext.ShowError())
        {
            <div class="form-group pt-3">
                <div class="alert alert-danger" role="alert">
                    @BindingContext.ImportResult.ErrorMessage
                </div>
            </div>
        }

        @if (BindingContext.ShowSuccess())
        {
            <div class="form-group pt-3">
                <div class="alert alert-success" role="alert">
                    Succesfully added @(BindingContext.ImportResult.ItemsAdded) items
                    @if (BindingContext.ImportResult.ItemsUpdated > 0)
                    {
                        <br />
                        <span>Succesfully Updated @(BindingContext.ImportResult.ItemsUpdated) items</span>
                    }
                </div>
            </div>
        }

    </div>
</div>

@code{
    ImportSpareStatus Status => BindingContext.Status;

    bool FileAnalyzed => BindingContext.Status != ImportSpareStatus.SelectFile && BindingContext.Status != ImportSpareStatus.Finished;

    string GetText()
    {
        if (FileAnalyzed && !BindingContext.SpareImportAnalysis.ImportFileAllowed())
            return _localizer["RoImportSpareNotAllowed"].Value;

        var text = _localizer[GetKey()].Value;
        text = text.Replace("[type]", _localizer[$"{Status}"]);

        return text;
    }

    string GetKey() =>
        Status switch
        {
            ImportSpareStatus.SelectFile => "RoImportSpareBtn",
            ImportSpareStatus.ImportSpares => "RoImportReadyForImport",
            ImportSpareStatus.Finished => "RoImportFinished",
            _ => "RoImportUpdateReference"
        };

    private bool FoundSparesInImport()
    {
        return BindingContext.SpareImportAnalysis?.SparesInImport > 0 == true;
    }

    private bool FoundRiskInImport()
    {
        return BindingContext.SpareImportAnalysis?.RisksInImport > 0 == true;
    }

    private bool RisksInDbMatchImport()
    {
        return BindingContext.SpareImportAnalysis.RisksFoundInDB == BindingContext.SpareImportAnalysis.RisksInImport;
    }

    private bool AddPlusUpdateMatchesTasksInImport()
    {
        return BindingContext.SpareImportAnalysis.SparesToAdd + BindingContext.SpareImportAnalysis.SparesToUpdate == BindingContext.SpareImportAnalysis.SparesInImport;
    }
}