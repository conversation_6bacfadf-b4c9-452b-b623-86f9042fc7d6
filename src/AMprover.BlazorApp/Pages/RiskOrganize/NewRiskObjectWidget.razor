@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<NewRiskObjectWidgetViewModel>
@inject IStringLocalizer<NewRiskObjectWidget> _localizer

<Radzen.Blazor.RadzenTemplateForm TItem=NewRiskObjectSettings
                                  Data=@BindingContext.NewRiskObjectSettings
                                  Submit=@BindingContext.ValidTaskSubmitted
                                  OnInvalidSubmit=@BindingContext.InvalidTaskSubmitted>

    <div class="form-group">@_localizer["NRoHeaderTxt"]</div>

    <div class="form-group">
        <label>@_localizer["NRoNameTxt"]:</label>
        <RadzenTextBox @bind-Value=@BindingContext.NewRiskObjectSettings.Name Name="Name" Disabled=true MaxLength="50" class="form-control"/>
        <RadzenRequiredValidator Component="Name" />
    </div>

    <div class="form-group">
        <label>@_localizer["NRoScenarioTxt"]:</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        TextProperty="Name" ValueProperty="Id" Name="ScenarioId" class="form-control"
                        Data="@BindingContext.Scenarios"
                        @bind-Value="@BindingContext.NewRiskObjectSettings.ScenarioId" />
        <RadzenNumericRangeValidator Component="ScenarioId" Min="1" Text="Must Choose Scenario" />
    </div>

    <div class="form-group">
        <label>@BindingContext.GetObjectLevel(ObjectLevel.Collection):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        TextProperty="Name" ValueProperty="Id" Name="CollectionId" class="form-control"
                        Data="@BindingContext.Objects.Where(x => x.Level == (int)ObjectLevel.Collection)"
                        @bind-Value="@BindingContext.NewRiskObjectSettings.CollectionId" />
    </div>

    <div class="form-group">
        <label>@BindingContext.GetObjectLevel(ObjectLevel.Installation):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        TextProperty="Name" ValueProperty="Id" Name="InstallationId" class="form-control"
                        Data="@BindingContext.Objects.Where(x => x.Level == (int)ObjectLevel.Installation)"
                        @bind-Value="@BindingContext.NewRiskObjectSettings.InstallationId" 
                        Change=@BindingContext.GenerateRiskObjectName />
        <RadzenNumericRangeValidator Component="InstallationId" Min="1" Text=@BindingContext.GetValidatorMessage(ObjectLevel.Installation) />
    </div>

    <div class="form-group">
        <label>@_localizer["NRoRiskMatrixTxt"]:</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        TextProperty="Name" ValueProperty="Id" Name="FmecaId" class="form-control"
                        Data="@BindingContext.Templates"
                        @bind-Value="@BindingContext.NewRiskObjectSettings.FmecaId" 
                        Change=@BindingContext.GenerateRiskObjectName />
        <RadzenNumericRangeValidator Component="FmecaId" Min="1" Text="Must Choose Fmeca" />
    </div>

    <div class="form-group">
        <label>@_localizer["NRoAnalyseTypeTxt"]:</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        Name="AnalysisType" class="form-control"
                        Data="@BindingContext.AnalysisTypes"
                        @bind-Value="@BindingContext.NewRiskObjectSettings.AnalyseType" 
                        Change=@BindingContext.GenerateRiskObjectName />
        <RadzenLengthValidator Component="AnalysisType" Min="1" Text="Must Choose AnalysisType" />
    </div>

    <input type="submit" class="btn btn-primary mt-4" value=@_localizer["NRoCreateBtn"] />
</Radzen.Blazor.RadzenTemplateForm>

@code
{
    [Parameter] public int? SelectedScenario { get; set; }

    [Parameter] public EventCallback<RiskObjectModel> SaveCallBack { get; set; }
}
