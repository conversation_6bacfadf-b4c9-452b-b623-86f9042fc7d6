using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Enums;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Pages.RiskOrganize;

public class NewRiskObjectWidgetViewModel : BaseViewModel
{
    private readonly IScenarioManager _scenarioManager;
    private readonly IRiskAnalysisManager _riskAnalysisManager;
    private readonly IRiskOrganizerManager _riskOrganizerManager;
    private readonly IObjectManager _objectManager;

    public NewRiskObjectWidgetViewModel(
        ILoggerFactory loggerFactory,
        IScenarioManager scenarioManager,
        IRiskAnalysisManager riskAnalysisManager,
        IObjectManager objectManager,
        IRiskOrganizerManager riskOrganizerManager) : base(loggerFactory)
    {
        _scenarioManager = scenarioManager;
        _riskAnalysisManager = riskAnalysisManager;
        _riskOrganizerManager = riskOrganizerManager;
        _objectManager = objectManager;
    }

    [Parameter] public int? SelectedScenario { get; set; }

    [Parameter] public EventCallback<RiskObjectModel> SaveCallBack { get; set; }

    public List<ScenarioModel> Scenarios { get; set; }

    public List<RiskMatrixTemplateModel> Templates { get; set; } = new();

    public List<ObjectModel> Objects { get; set; } = new List<ObjectModel>();

    public List<string> AnalysisTypes { get; private set; } = new();

    public NewRiskObjectSettings NewRiskObjectSettings { get; set; } = new NewRiskObjectSettings();

    public override void OnInitialized()
    {
        Scenarios = _scenarioManager.GetAllScenarios();
        Objects = _riskAnalysisManager.GetAllObjects();
        Templates = _riskAnalysisManager.GetAllTemplates();
        AnalysisTypes = _riskOrganizerManager.GetAnalysisTypes();
        ObjectLevels = _objectManager.GetObjectLevelNames();

        NewRiskObjectSettings.ScenarioId = SelectedScenario ?? 0;
        GenerateRiskObjectName();
    }

    public void ValidTaskSubmitted(NewRiskObjectSettings settings)
    {
        var newRiskObject = _riskOrganizerManager.CreateRiskObject(settings);

        if (SaveCallBack.HasDelegate)
            SaveCallBack.InvokeAsync(newRiskObject);
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        _logger.LogError("Invalid form submitted in {NewRiskObjectWidgetViewModelName} with model: {JsonModel}", nameof(NewRiskObjectWidgetViewModel), Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }

    public string GetValidatorMessage(ObjectLevel objectLevel)
    {
        var name = ObjectLevels.TryGetValue(objectLevel, out var level) ? level : objectLevel.ToString();
        return $"Must Choose {name}";
    }

    public void GenerateRiskObjectName()
    {
        var analysisType = string.IsNullOrWhiteSpace(NewRiskObjectSettings.AnalyseType) ? "_" : NewRiskObjectSettings.AnalyseType;
        var fmecaShortKey = Templates.FirstOrDefault(x => x.Id == NewRiskObjectSettings.FmecaId)?.ShortName ?? "_";
        var installationName = Objects.FirstOrDefault(x => x.Id == NewRiskObjectSettings.InstallationId)?.Name ?? "_";

        NewRiskObjectSettings.Name = $"{analysisType} - {fmecaShortKey} - {installationName}";
    }
}