using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Helpers;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BlazorApp.Pages.RiskOrganize.Import;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BlazorApp.Pages.RiskOrganize;

public class RiskOrganizerViewModel : BaseViewModel
{
    public RiskOrganizerViewModel(
        ILoggerFactory loggerFactory,
        IRiskOrganizerManager riskOrganizerManager,
        IRiskAnalysisManager riskAnalysisManager,
        ILookupManager lookupManager,
        IObjectManager objectManager,
        NavigationManager navigationManager,
        RiskOrganizerQueryParams queryParams,
        IPageNavigationManager pageNavigationManager,
        IScenarioManager scenarioManager,
        IAdHocFmecaImageManager fmecaImageService,
        IStringLocalizer<RiskOrganizer> localizer,
        AuthenticationStateProvider authenticationStateProvider,
        IRiskAnalysisSetupManager riskAnalysisSetupManager,
        UserManager<UserAccount> userManager,
        DialogService dialogService,
        IDropdownManager dropdownManager) : base(loggerFactory, lookupManager, authenticationStateProvider, userManager,
        dialogService)
    {
        RiskOrganizerManager = riskOrganizerManager;
        RiskAnalysisManager = riskAnalysisManager;
        LookupManager = lookupManager;
        ObjectManager = objectManager;
        NavigationManager = navigationManager;
        PageNavigationManager = pageNavigationManager;
        ScenarioManager = scenarioManager;
        FmecaImageService = fmecaImageService;
        Localizer = localizer;
        DropdownManager = dropdownManager;
        RiskAnalysisSetupManager = riskAnalysisSetupManager;

        QueryParams = queryParams;
    }

    #region Properties

    // Managers and Services
    private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; }
    private IRiskOrganizerManager RiskOrganizerManager { get; }
    private IRiskAnalysisManager RiskAnalysisManager { get; }
    private ILookupManager LookupManager { get; }
    private IObjectManager ObjectManager { get; }
    private NavigationManager NavigationManager { get; }
    private IPageNavigationManager PageNavigationManager { get; }
    private IScenarioManager ScenarioManager { get; }
    private IAdHocFmecaImageManager FmecaImageService { get; }
    private IStringLocalizer<RiskOrganizer> Localizer { get; }
    private IDropdownManager DropdownManager { get; }

    // Query Params
    public RiskOrganizerQueryParams QueryParams { get; set; }

    // UI Elements
    public UtilityGrid<RiskObjectModel> RiskObjectGrid { get; set; }
    public RadzenTabs TabsComponent { get; set; }
    public RadzenTabs GridTabsComponent { get; set; }

    // Database Objects
    public List<ScenarioModel> Scenarios { get; private set; } = new();
    public List<ScenarioModel> SelectedScenarios { get; set; } = new();
    public List<RiskObjectModel> RiskObjects { get; private set; } = new();
    public List<RiskObjectModel> SelectedRiskObjects { get; private set; } = new();
    public List<ObjectModel> Objects { get; set; } = new();

    // Dropdown values
    public List<RiskMatrixTemplateModel> Templates { get; set; } = new();
    public List<string> AnalysisTypes { get; private set; } = new();
    public Dictionary<int, string> StatusDict { get; private set; } = new();
    public Dictionary<int, string> DepartmentDict { get; private set; } = new();

    // State tracking
    private ScenarioModel _selectedScenario;

    public ScenarioModel SelectedScenario
    {
        get => _selectedScenario;
        set
        {
            _selectedScenario = value;
            QueryParams.Scenario = _selectedScenario?.Id;
        }
    }

    private RiskObjectModel _selectedRiskObject;

    public RiskObjectModel SelectedRiskObject
    {
        get => _selectedRiskObject;
        set
        {
            _selectedRiskObject = value;
            QueryParams.RiskObject = _selectedRiskObject?.Id;
        }
    }

    public List<DepartmentModel> UserDepartments { get; set; }
    public UtilityGrid<RiskModel> RisksGrid { get; set; }
    public UtilityGrid<TaskModel> PreventiveActionsGrid { get; set; }
    public List<ObjectModel> Collections { get; set; } = new();

    public Dictionary<string, Dictionary<int, string>> TaskDropDownOverrides { get; set; }
    public Dictionary<string, Dictionary<int, string>> RiskDropDownOverrides { get; set; }
    public Dictionary<string, Dictionary<int, string>> RiskObjectDropdownOverrides { get; set; }
    public string ValRiskTabText { get; set; }
    public string PrevActTabText { get; set; }
    public string PmoActTabText { get; set; }

    public bool ShowGauge { get; set; }
    public bool ShowRegenerateImagesBtn { get; set; }

    public IEnumerable<string> UserRoles { get; set; }
    public bool IsAdminUser { get; set; }
    public bool IsAssetManagementUser => UserRoles?.Contains(RoleConstants.AssetManagement) == true;

    public int SelectedTabIndex { get; set; }
    public int SelectedBottomTabIndex { get; set; }

    public List<int?> Categories
    {
        get => QueryParams.Categories;
        set
        {
            if (value == QueryParams.Categories)
                return;

            QueryParams.Categories = value;
            Task.Run(async () => await UpdateSelectedCollections(value, true));
        }
    }

    #endregion

    #region Chart Data

    public class DataItem
    {
        public string RiskType { get; set; }
        public double Cost { get; set; }
        public string CostString { get; set; }
    }

    public DataItem[] ChartDataActions { get; set; }
    public DataItem[] ChartDataDirectRiskCost { get; set; }
    public DataItem[] ChartDataValueRisk { get; set; }

    #endregion

    #region Initialization

    public override async Task OnInitializedAsync()
    {
        QueryParams.AnalysisType =
            !NavigationManager.Uri.CheckQueryStringValue("AnalysisType", "pmo") ? string.Empty : "pmo";
        ShowRegenerateImagesBtn = LookupManager.GetShowRegenerateImages();

        await LoadInitialData();
        await SetupUserAccess();
        SetupDropdowns();

        SelectedScenario = Scenarios.FirstOrDefault(x => x.Id == QueryParams.Scenario);
        await UpdateSelectedScenarios(QueryParams.Scenarios, false);

        // Make sure the correct GridRow shows as selected on opening the page
        await BottomTabChange(0);

        await base.OnInitializedAsync();
    }

    private async Task LoadInitialData()
    {
        Scenarios = ScenarioManager.GetAllScenariosSorted();
        Templates = RiskAnalysisManager.GetAllTemplates();
        Objects = RiskAnalysisManager.GetAllObjects();
        AnalysisTypes = RiskOrganizerManager.GetAnalysisTypes();
        StatusDict = DropdownManager.GetStatusDict();
        DepartmentDict = DropdownManager.GetDepartmentDict();
        ObjectLevels = ObjectManager.GetObjectLevelNames();
        ShowGauge = LookupManager.GetRiskAnalysisShowGauge();

        ValRiskTabText = Localizer["RoValueRiskTabTxt"];
        PrevActTabText = Localizer["RoPrevActionTabTxt"];
        PmoActTabText = Localizer["RoPmoPrevActionTabTxt"];
    }

    private async Task SetupUserAccess()
    {
        UserRoles = (await GetCurrentUserRoles()).ToList();
        IsAdminUser = UserRoles?.Contains(RoleConstants.Administrators) == true ||
                      UserRoles?.Contains(RoleConstants.PortfolioAdministrators) == true;

        if (!IsAdminUser)
        {
            var user = await GetCurrentUserId();
            UserDepartments = RiskAnalysisSetupManager.GetUserDepartments(user).ToList();
        }
    }

    private void SetupDropdowns()
    {
        TaskDropDownOverrides = DropdownManager.GetTaskModelDropDownOverrides();
        RiskDropDownOverrides = DropdownManager.GetRiskModelDropDownOverrides();
        SetRiskDropdownOverride();
    }

    #endregion

    #region Data Methods

    public List<RiskModel> GetRisks() => SelectedRiskObject?.Risks?.ToList() ?? new List<RiskModel>();

    public List<TaskModel> GetTasks() =>
        SelectedRiskObject?.Risks?.SelectMany(x => x.Tasks).Where(x => !x.Pmo).ToList() ?? new List<TaskModel>();

    public List<TaskModel> GetPmoTasks() =>
        SelectedRiskObject?.Risks?.SelectMany(x => x.Tasks).Where(x => x.Pmo).ToList() ?? new List<TaskModel>();

    private void SetRiskDropdownOverride()
    {
        RiskObjectDropdownOverrides = DropdownManager.GetRiskObjectDropDownOverrides();
        RiskObjectDropdownOverrides.Add(nameof(RiskModel.RiskObject), RiskObjects.ToDictionary(x => x.Id, x => x.Name));
    }

    // Fill Collections ListBox based on SelectedRiskObjects
    private void SetCollectionsFromRiskObjects()
    {
        Collections = RiskObjects.Where(x => x.ParentObject != null)
            .Select(x => new ObjectModel
            {
                Id = x.ParentObject.Id,
                Name = x.ParentObject.Name.Trim()
            })
            .GroupBy(x => x.Id)
            .Select(g => g.First())
            .OrderBy(x => x.Name)
            .ToList();

        Collections.Insert(0, new ObjectModel {Id = 0, Name = "None"});
        QueryParams.Categories = Collections.Select(x => (int?) x.Id).ToList();
    }

    private void SetChartData()
    {
        decimal? prevCostPmo = SelectedRiskObject.PreventiveCostPmo ?? 0;
        decimal? prevCost = SelectedRiskObject.PreventiveCostAfter ?? 0;
        decimal? directCostPmo = SelectedRiskObject.DirectCorrectiveCostPmo ?? 0;
        decimal? directCost = SelectedRiskObject.DirectCorrectiveCostAfter ?? 0;
        decimal? riskPmo = (SelectedRiskObject.CorrectiveCostPmo ?? 0) - (decimal) directCostPmo;
        decimal? riskAfter = (SelectedRiskObject.CorrectiveCostAfter ?? 0) - (decimal) directCost;

        ChartDataActions = new[]
        {
            new DataItem
                {RiskType = "Current", Cost = (double) prevCostPmo, CostString = FormatAsSelectedCurrency(prevCostPmo)},
            new DataItem
                {RiskType = "Analysed", Cost = (double) prevCost, CostString = FormatAsSelectedCurrency(prevCost)}
        };

        ChartDataDirectRiskCost = new[]
        {
            new DataItem
            {
                RiskType = "Current", Cost = (double) directCostPmo,
                CostString = FormatAsSelectedCurrency(directCostPmo)
            },
            new DataItem
                {RiskType = "Analysed", Cost = (double) directCost, CostString = FormatAsSelectedCurrency(directCost)}
        };

        ChartDataValueRisk = new[]
        {
            new DataItem
                {RiskType = "Current", Cost = (double) riskPmo, CostString = FormatAsSelectedCurrency(riskPmo)},
            new DataItem
                {RiskType = "Analysed", Cost = (double) riskAfter, CostString = FormatAsSelectedCurrency(riskAfter)}
        };
    }

    #endregion

    #region Update Methods

    // Change Selected Scenarios
    public async Task UpdateSelectedScenarios(IEnumerable<int> selectedScenariosIds, bool setQueryString)
    {
        SelectedScenarios = Scenarios
            .Where(x => selectedScenariosIds.Contains(x.Id))
            .ToList();

        RiskObjects = RiskOrganizerManager.GetRiskObjectsFromScenariosV2(SelectedScenarios);

        if (!SelectedScenarios.Contains(SelectedScenario))
            SelectedScenario = SelectedScenarios.FirstOrDefault();

        SetCollectionsFromRiskObjects();
        await UpdateSelectedCollections(QueryParams.Categories ?? new(), false);

        if (!string.IsNullOrWhiteSpace(QueryParams.AnalysisType))
        {
            var analysisType = AnalysisTypes.FirstOrDefault(x =>
                x.Contains(QueryParams.AnalysisType.ToLower(), StringComparison.InvariantCultureIgnoreCase));

            RiskObjects = RiskObjects.Where(x => x.AnalysisType == analysisType).ToList();
        }

        if (setQueryString)
            SetQueryString();
    }

    // Change Selected Collections
    public async Task UpdateSelectedCollections(List<int?> selectParentObjects, bool setQueryString)
    {
        if (selectParentObjects.FirstOrDefault(x => x == 0) != null)
            selectParentObjects.Add(null);

        SelectedRiskObjects = RiskObjects
            .Where(x => selectParentObjects.Contains(x.ParentObjectId)).ToList();

        SelectedRiskObject = SelectedRiskObjects.FirstOrDefault(x => x.Id == QueryParams.RiskObject);

        if (!SelectedRiskObjects.Contains(SelectedRiskObject))
            SelectedRiskObject = null;

        SelectedTabIndex = SelectedRiskObject == null ? 0 : 1;

        // Make sure the correct GridRow shows as selected on opening the page
        if (SelectedBottomTabIndex == 0)
            await BottomTabChange(0);

        if (setQueryString)
            SetQueryString();
    }

    /// <summary>
    /// Update selected RiskObject
    /// </summary>
    public async Task UpdateCurrentRiskObject(bool updateRisks = false)
    {
        if (SelectedRiskObject == null)
            return;

        SelectedRiskObject = RiskOrganizerManager.UpdateRiskObject(SelectedRiskObject);

        if (!QueryParams.Categories.Contains(SelectedRiskObject.ParentObjectId ?? 0))
            QueryParams.Categories.Add(SelectedRiskObject.ParentObjectId ?? 0);

        if (!QueryParams.Scenarios.Contains(SelectedRiskObject.ScenarioId))
            QueryParams.Scenarios.Add(SelectedRiskObject.ScenarioId);

        SetQueryString();

        if (updateRisks)
        {
            RiskOrganizerManager.UpdateRisksObjectStructure(
                SelectedRiskObject.Risks.ToList(),
                SelectedRiskObject.ParentObjectId,
                SelectedRiskObject.ObjectId);
        }

        await UpdateSelectedScenarios(QueryParams.Scenarios, true);
    }

    /// <summary>
    /// Update Selected Scenario
    /// </summary>
    public void UpdateCurrentScenario()
    {
        if (SelectedScenario != null)
            ScenarioManager.SaveScenario(SelectedScenario);
    }

    /// <summary>
    /// Update The asset linked to the current RiskObject
    /// </summary>
    public void UpdateCurrentAsset()
    {
        var asset = SelectedRiskObject.Object;
        SelectedRiskObject.Object = ObjectManager.SaveObject(asset);
    }

    public async Task UpdateScenarioOnRiskObject()
    {
        RiskOrganizerManager.UpdateScenarioOnLcc(SelectedRiskObject);
        await UpdateCurrentRiskObject(false);
    }

    public async Task UpdateCollectionOrIntallationOnRiskObject()
    {
        RiskOrganizerManager.UpdateCollectionAndInstallationOnLccs(SelectedRiskObject);
        await UpdateCurrentRiskObject(true);
    }

    public void UpdateRisk(RiskModel risk)
    {
        RiskOrganizerManager.UpdateRisk(risk);

        if (risk.RiskObjectId != SelectedRiskObject.Id)
        {
            var riskFromRiskObject = SelectedRiskObject.Risks.FirstOrDefault(x => x.Id == risk.Id);
            if (riskFromRiskObject != null)
            {
                var risks = SelectedRiskObject.Risks.ToList();
                risks.Remove(riskFromRiskObject);
                SelectedRiskObject.Risks = risks;
            }

            var newRiskObject = RiskObjects.FirstOrDefault(x => x.Id == risk.RiskObjectId);
            if (newRiskObject?.Risks != null)
            {
                newRiskObject.Risks = newRiskObject.Risks.ToList().Append(risk);
            }
        }
    }

    #endregion

    #region Navigation Methods

    /// <summary>
    /// Cycle through Selected Scenarios
    /// </summary>
    public void PreviousScenario()
    {
        var index = SelectedScenarios.IndexOf(SelectedScenario);
        SelectedScenario = index == 0 ? SelectedScenarios.Last() : SelectedScenarios.Skip(index - 1).First();
        SetQueryString();
    }

    /// <summary>
    /// Cycle through Selected Scenarios
    /// </summary>
    public void NextScenario()
    {
        var index = SelectedScenarios.IndexOf(SelectedScenario);
        SelectedScenario = index == SelectedScenarios.Count - 1
            ? SelectedScenarios.First()
            : SelectedScenarios.Skip(index + 1).First();
        SetQueryString();
    }

    // Callback on Manually changing the Top Tabs Component
    public void TopTabChange(int index) => SetQueryString();

    /// <summary>
    /// Changing bottom TabsComponent
    /// </summary>
    public async Task BottomTabChange(int index)
    {
        if (index == 0)
        {
            // When you switch tabs the selected row of the GridComponent is lost.
            // Wait a brief moment to allow tab change to complete
            await Task.Delay(10);

            if (SelectedRiskObject != null)
            {
                RiskObjectGrid.SelectRow(SelectedRiskObject);
            }
        }

        SetQueryString();
    }

    // Set Query string based on Selected Scenarios, RiskObject and Collections
    private void SetQueryString() =>
        SetQueryStringInternal(QueryParams.ToQueryString());

    private void SetQueryStringInternal(string queryString, bool forceReload = false)
    {
        var url = string.IsNullOrWhiteSpace(queryString)
            ? NavigationManager.Uri.Split('?')[0]
            : $"{NavigationManager.Uri.Split('?')[0]}{queryString}";

        if (NavigationManager.Uri == url) return;
        var uri = new Uri(url);

        PageNavigationManager.SavePageQueryString(uri.LocalPath, uri.RemoveQueryStringByKey("AnalysisType").Query);
        NavigationManager.NavigateTo(url, forceReload);
    }

    #endregion

    #region UI Interaction Methods

    /// <summary>
    /// Select a risk object, jump to risk object tab and show RiskObject Details
    /// </summary>
    public void ClickRiskObjectRow(RiskObjectModel riskObject)
    {
        if (riskObject == null)
            return;

        SelectedRiskObject = RiskOrganizerManager.GetRiskObjectWithRisks(riskObject.Id);
        SelectedTabIndex = 1;

        TabsComponent?.Reload();

        SetQueryString();

        if (GetPmoStatus()) SetChartData();
    }

    public void DeleteRiskObject(RiskObjectModel riskObject)
    {
        _dialogService.Open<DeleteRiskObjectWidget>(
            Localizer["RoDeleteStatementTxt"],
            new Dictionary<string, object>
            {
                {"RiskObject", riskObject ?? SelectedRiskObject},
                {"Risks", riskObject?.Risks.Count() ?? SelectedRiskObject.Risks.Count()}
            }
        );
    }

    public async Task PasteRiskObject(RiskObjectModel model)
    {
        RiskOrganizerManager.CopyRiskObject(model);
        await UpdateSelectedScenarios(QueryParams.Scenarios, true);
    }

    public void OpenNewRiskObjectWidget()
    {
        _dialogService.Open<NewRiskObjectWidget>(
            Localizer["RoNewBtnTxt"],
            new Dictionary<string, object>
            {
                {nameof(NewRiskObjectWidget.SelectedScenario), SelectedScenario?.Id},
                {
                    nameof(NewRiskObjectWidget.SaveCallBack),
                    EventCallback.Factory.Create<RiskObjectModel>(this, CreateNewRiskObjectCallback)
                }
            });
    }

    private void CreateNewRiskObjectCallback(RiskObjectModel ro)
    {
        if (!QueryParams.Scenarios.Contains(ro.ScenarioId))
            QueryParams.Scenarios.Add(ro.ScenarioId);

        if (ro.ParentObjectId != null && !QueryParams.Categories.Contains(ro.ParentObjectId))
            QueryParams.Categories.Add(ro.ScenarioId);

        RiskObjects.Add(ro);
        var scenarios = QueryParams.Scenarios.Append(ro.ScenarioId).Distinct().ToList();
        var categories = RiskObjects.Where(x => x.ParentObjectId != null)
            .Select(x => x.ParentObjectId)
            .Distinct()
            .ToList();

        if (ro.ParentObjectId == null && !categories.Contains(0))
            categories.Add(0);

        NavigationManager.NavigateTo(
            $"/value-risk-organizer?scenario={ro.ScenarioId}&riskobject={ro.Id}&scenarios={string.Join(',', scenarios)}&categories={string.Join(',', categories)}"
            , true);
    }

    #endregion

    #region Navigation and Page Actions

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    public void OpenRisksPage(RiskObjectModel model)
    {
        if (!ShouldShowOpenPage(model))
            return;

        var previousUrl = PageNavigationManager.GetRiskEditUrl(model.Id);
        if (previousUrl != null)
        {
            NavigationManager.NavigateTo(previousUrl);
        }
        else
        {
            OpenRisksPage(model.Id, RiskOrganizerManager.GetFirstRiskId(model));
        }
    }

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    public void OpenRisksPage(RiskModel model) => OpenRisksPage(model.RiskObjectId, model.Id);

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    public void OpenRisksPage(TaskModel model) => OpenRisksPage(model.Risk?.RiskObjectId ?? 0, model.MrbId ?? 0);

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    private void OpenRisksPage(int id) =>
        NavigationManager.NavigateTo($"/value-risk-analysis/{SelectedRiskObject.Id}/risks/{id}", false);

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    private void OpenRisksPage(int riskObjectId, int riskId) =>
        NavigationManager.NavigateTo($"/value-risk-analysis/{riskObjectId}/risks/{riskId}", false);

    /// <summary>
    /// Open Lcc Page
    /// </summary>
    public void OpenLccPage(RiskObjectModel riskObject) =>
        NavigationManager.NavigateTo($"/lcc/{riskObject.Lcc?.Id}");

    public void ExportRiskObject() =>
        NavigationManager.NavigateTo("/value-risk-exports");

    #endregion

    #region UI Helper Methods

    /// <summary>
    /// Get Text for the Risks Tab label
    /// </summary>
    public string GetRiskTabText() => $"{ValRiskTabText} ({GetRisks().Count})";

    /// <summary>
    /// Get Text for the Tasks/Preventive actions Tab label
    /// </summary>
    public string GetPreventiveActionsTabText() => $"{PrevActTabText} ({GetTasks().Count})";

    public string GetPmoActionsTabText() => $"{PmoActTabText} ({GetPmoTasks().Count})";

    public bool GetPmoStatus() => SelectedRiskObject?.IsPmoType() ?? false;

    public bool ShouldShowOpenPage(RiskObjectModel rowData) => rowData?.Department == null || IsAdminUser ||
                                                               UserDepartments.Any(x => x.Id == rowData.Department.Id);

    public bool ShouldShowOpenPage(RiskModel rowData) =>
        rowData?.RiskObject?.Department == null || IsAdminUser ||
        UserDepartments.Any(x => x.Id == rowData.RiskObject.Department.Id);

    public bool ShouldShowOpenPage(TaskModel rowData) =>
        rowData?.Risk?.RiskObject?.Department == null || IsAdminUser ||
        UserDepartments.Any(x => x.Id == rowData.Risk.RiskObject.Department.Id);

    private string FormatAsSelectedCurrency(decimal? value) =>
        value == null ? string.Empty : value.Value.ToString("C0", CultureInfo.CreateSpecificCulture(Currency));

    public string FormatAsEur(object value) =>
        ((double) value).ToString("C0", CultureInfo.CreateSpecificCulture("nl-NL"));

    #endregion

    #region Image Generation

    public void GenerateFmecaImagePopup()
    {
        if (SelectedRiskObject == null) return;

        _dialogService.Open<AreYouSureDialog<RiskObjectModel>>(Localizer["RoRegenerateImagesLbl"],
            new Dictionary<string, object>
            {
                {
                    nameof(AreYouSureDialog<RiskObjectModel>.Text),
                    Localizer["RoRegenerateImagesTxt"].Value.Replace("[RISKOBJECT]", SelectedRiskObject.Name)
                },
                {nameof(AreYouSureDialog<RiskObjectModel>.Item), SelectedRiskObject},
                {
                    nameof(AreYouSureDialog<RiskObjectModel>.YesCallback),
                    EventCallback.Factory.Create<RiskObjectModel>(this, GenerateRiskObjectImages)
                }
            });
    }

    public void GenerateRiskObjectImages(RiskObjectModel riskObject) =>
        FmecaImageService.GenerateFmecaImages(riskObject.Id);

    #endregion

    #region Dialog Methods

    public void OnRiskObjectImportClick(RadzenSplitButtonItem item)
    {
        if (item == null) return;

        switch (item.Value)
        {
            case "1":
                _dialogService.Open<ImportRisksWidget>(
                    Localizer["RoImportRisksBtn"],
                    new Dictionary<string, object>(),
                    new DialogOptions {Draggable = true});
                break;
            case "2":
                _dialogService.Open<ImportTaskWidget>(
                    Localizer["RoImportTasksBtn"],
                    new Dictionary<string, object>(),
                    new DialogOptions {Draggable = true});
                break;
            case "3":
                _dialogService.Open<ImportSpareWidget>(
                    Localizer["RoImportSpareBtn"],
                    new Dictionary<string, object>(),
                    new DialogOptions {Draggable = true});
                break;
        }
    }

    public void MultiLineInfoClick(RadzenSplitButtonItem item)
    {
        if (item == null) return;

        var dialogOptions = new DialogOptions {Width = "800px", Resizable = false, Draggable = true};

        switch (item.Value)
        {
            case "1":
                _dialogService.Open<InformationDialog>(
                    Localizer["RoMenuTitle"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoMenuTxt"].ToString()}},
                    dialogOptions);
                break;
            case "2":
                _dialogService.Open<InformationDialog>(
                    Localizer["RoExportBtnTxt"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoExportInfoTxt"].ToString()}},
                    dialogOptions);
                break;
            case "3":
                _dialogService.Open<InformationDialog>(
                    Localizer["RoImportRiskBtn"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoImportRisksInfoTxt"].ToString()}},
                    dialogOptions);
                break;
            case "4":
                _dialogService.Open<InformationDialog>(
                    Localizer["RoImportTaskBtn"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoImportTasksTxt"].ToString()}},
                    dialogOptions);
                break;
            case "5":
                _dialogService.Open<InformationDialog>(
                    Localizer["RoImportSpareBtn"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoImportSpareTxt"].ToString()}},
                    dialogOptions);
                break;
        }
    }

    #endregion
}