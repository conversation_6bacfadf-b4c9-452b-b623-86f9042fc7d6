using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using AMprover.BusinessLogic.Models.RiskAnalysis;

namespace AMprover.BlazorApp.Pages.RiskOrganize;

public class DeleteRiskObjectWidgetViewModel : BaseViewModel
{
    private readonly NavigationManager _navigationManager;
    private readonly IRiskOrganizerManager _riskOrganizerManager;

    public DeleteRiskObjectWidgetViewModel(
        ILoggerFactory loggerFactory,
        NavigationManager navigationManager,
        IRiskOrganizerManager riskOrganizerManager) : base(loggerFactory)
    {
        _navigationManager = navigationManager;
        _riskOrganizerManager = riskOrganizerManager;
    }

    [Parameter] public RiskObjectModel RiskObject { get; set; }

    [Parameter] public int Risks { get; set; }

    public string ConfirmText { get; set; } = string.Empty;

    public string ErrorMessage { get; set; }

    public void ConfirmDelete()
    {
        // Html output turns multiple spaces into a single space, Just remove all spaces before the compare to prevent a mismatch
        if (ConfirmText.Replace(" ",  "") != RiskObject.Name.Replace(" ", ""))
            ErrorMessage = "The name is incorrect";

        else
        {
            _riskOrganizerManager.DeleteRiskObject(RiskObject.Id);
            _navigationManager.NavigateTo(_navigationManager.Uri.Replace($"riskobject={RiskObject.Id}", string.Empty), true);
        }
    }
}