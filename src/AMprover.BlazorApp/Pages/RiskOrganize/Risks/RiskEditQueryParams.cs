using AMprover.BusinessLogic.Navigation;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Risks;

public class RiskEditQueryParams : QueryParamsBase
{
    public RiskEditQueryParams(ILoggerFactory loggerFactory, NavigationManager navManager) 
        : base(loggerFactory, navManager)
    {
    }

    public int TopTabs { get; set; }

    public int BottomTabs { get; set; }
}