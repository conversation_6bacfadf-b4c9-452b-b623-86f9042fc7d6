@page "/value-risk-organizer/{RiskObjectId:int}/risks/{RiskId:int}/copy-settings"

@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<RiskCopyWidgetViewModel>
@inject IStringLocalizer<RiskEdit> _localizer

<Radzen.Blazor.RadzenTemplateForm TItem=RiskCopySettings
                                  Data=@BindingContext.RiskCopySettings
                                  Submit=@BindingContext.ValidTaskSubmitted
                                  OnInvalidSubmit=@BindingContext.InvalidTaskSubmitted>

    <div class="form-group">@(RisksToCopy?.Count() ?? 0) @_localizer["RcWRiskCountTxt"]
    </div>

    <div class="form-group">
        <label>@BindingContext.GetObjectLevel(ObjectLevel.System):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" TextProperty="Name" ValueProperty="Id" class="form-control"
                        Data="@BindingContext.Objects.Where(x => x.Level == (int)ObjectLevel.System)"
                        @bind-Value="@BindingContext.RiskCopySettings.SystemId" />
    </div>
    <div class="form-group">
        <label>@BindingContext.GetObjectLevel(ObjectLevel.Component):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" TextProperty="Name" ValueProperty="Id" class="form-control"
                        Data="@BindingContext.Objects.Where(x => x.Level == (int)ObjectLevel.Component)"
                        @bind-Value="@BindingContext.RiskCopySettings.ComponentId" />
    </div>
    <div class="form-group">
        <label>@BindingContext.GetObjectLevel(ObjectLevel.Assembly):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" TextProperty="Name" ValueProperty="Id" class="form-control"
                        Data="@BindingContext.Objects.Where(x => x.Level == (int)ObjectLevel.Assembly)"
                        @bind-Value="@BindingContext.RiskCopySettings.AssemblyId" />
    </div>

    <input type="submit" class="btn btn-primary" value=@_localizer["RcWCopyAllBtn"] />
</Radzen.Blazor.RadzenTemplateForm>

@code
{
    [Parameter] public List<TreeNodeGeneric<RiskTreeObject>> RisksToCopy { get; set; }
}
