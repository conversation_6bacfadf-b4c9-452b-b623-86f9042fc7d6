using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using AMprover.BusinessLogic.Models.Tree;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Risks;

public class RiskCopyWidgetViewModel : BaseViewModel
{
    private readonly IRiskAnalysisManager _riskAnalysisManager;
    private readonly NavigationManager _navigationManager;
    private readonly IObjectManager _objectManager;

    public RiskCopyWidgetViewModel(ILoggerFactory loggerFactory, IRiskAnalysisManager riskAnalysisManager, NavigationManager navigationManager,
        IObjectManager objectManager) : base(loggerFactory)
    {
        _riskAnalysisManager = riskAnalysisManager;
        _navigationManager = navigationManager;
        _objectManager = objectManager;
    }

    [Parameter] public List<TreeNodeGeneric<RiskTreeObject>> RisksToCopy { get; set; }

    public List<ObjectModel> Objects { get; set; } = new List<ObjectModel>();

    public RiskCopySettings RiskCopySettings { get; set; } = new RiskCopySettings();

    public override void OnInitialized()
    {
        Objects = _riskAnalysisManager.GetAllObjects();
        ObjectLevels = _objectManager.GetObjectLevelNames();

        RiskCopySettings.SystemId = RisksToCopy.FirstOrDefault()?.Source.SystemId;
        RiskCopySettings.ComponentId = RisksToCopy.FirstOrDefault()?.Source.ComponentId;
        RiskCopySettings.AssemblyId = RisksToCopy.FirstOrDefault()?.Source.AssemblyId;

        if (RisksToCopy?.Any() != true)
            _logger.LogError($"{nameof(RiskCopyWidgetViewModel)} has been initalized without any {nameof(RisksToCopy)}");
    }

    public void ValidTaskSubmitted(RiskCopySettings copySettings)
    {
        var risks = _riskAnalysisManager.CopyRisksTo(
            RisksToCopy.Where(x => x.Source.RiskId != null)
                .Select(x => x.Source.RiskId.Value), 
            copySettings);

        _navigationManager.NavigateTo($"/value-risk-analysis/{RisksToCopy.FirstOrDefault()?.Source.RiskObjectId}/risks/{risks.FirstOrDefault()?.Id}", true);
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        _logger.LogError($"Invalid form submitted in {nameof(RiskCopyWidgetViewModel)}");
        _logger.LogError(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }
}