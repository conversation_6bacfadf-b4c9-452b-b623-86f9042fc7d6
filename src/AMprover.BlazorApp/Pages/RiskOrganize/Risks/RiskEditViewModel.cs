using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Components.Pagination;
using AMprover.BlazorApp.Pages.RiskOrganize.Spares;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Failures;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Constants;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using ObjectModel = AMprover.BusinessLogic.Models.RiskAnalysis.ObjectModel;
using RiskObjectModel = AMprover.BusinessLogic.Models.RiskAnalysis.RiskObjectModel;
using AMprover.BlazorApp.Components.RiskAnalysis;
using System.Globalization;
using System.Text.RegularExpressions;
using AMprover.BlazorApp.Helpers;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Status = AMprover.BusinessLogic.Enums.Status;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Risks;

public class RiskEditViewModel : BaseViewModel, IEntityEditorViewModel
{
    private IRiskAnalysisManager _riskAnalysisManager { get; }
    private IRiskAnalysisSetupManager _riskAnalysisSetupManager { get; }
    private IScenarioManager _scenarioManager { get; }
    private IRiskOrganizerManager _riskOrganizerManager { get; }
    private ILookupManager _lookupManager { get; }
    private ISparePartManager _sparePartManager { get; }
    private IPortfolioSetupManager _portfolioSetupManager { get; }
    private IFailureManager _failureManager { get; }
    private NavigationManager _navigationManager { get; }
    private IPageNavigationManager _pageNavigationManager { get; }
    private IDropdownManager _dropdownManager { get; }
    private IJSRuntime _jsRuntime { get; }
    private IStringLocalizer<RiskEdit> _localizer { get; }
    private IObjectManager _objectManager { get; }
    private JavaScriptHelper _javaScriptHelper { get; }

    public RiskEditViewModel(
        ILoggerFactory loggerFactory,
        IRiskAnalysisManager riskAnalysisManager,
        IRiskAnalysisSetupManager riskAnalysisSetupManager,
        IRiskOrganizerManager riskOrganizerManager,
        NavigationManager navigationManager,
        IPortfolioSetupManager portfolioSetupManager,
        IFailureManager failureManager,
        IScenarioManager scenarioManager,
        ILookupManager lookupManager,
        ISparePartManager sparePartManager,
        DialogService dialogService,
        IObjectManager objectManager,
        IPageNavigationManager pageNavigationManager,
        RiskEditQueryParams queryParams,
        IJSRuntime jsRuntime,
        IStringLocalizer<RiskEdit> localizer,
        AuthenticationStateProvider authenticationStateProvider,
        UserManager<UserAccount> userManager,
        JavaScriptHelper javaScriptHelper,
        IDropdownManager dropdownManager) : base(loggerFactory, lookupManager, authenticationStateProvider, userManager)
    {
        _riskAnalysisManager = riskAnalysisManager;
        _riskAnalysisSetupManager = riskAnalysisSetupManager;
        _riskOrganizerManager = riskOrganizerManager;
        _portfolioSetupManager = portfolioSetupManager;
        _failureManager = failureManager;
        _dialogService = dialogService;
        _navigationManager = navigationManager;
        _lookupManager = lookupManager;
        _sparePartManager = sparePartManager;
        _scenarioManager = scenarioManager;
        QueryParams = queryParams;
        _pageNavigationManager = pageNavigationManager;
        _dropdownManager = dropdownManager;
        _jsRuntime = jsRuntime;
        _localizer = localizer;
        _javaScriptHelper = javaScriptHelper;
        _objectManager = objectManager;
    }

    #region Titles

    public string PageTitle { get; set; }
    public string TabBeforeTitle { get; set; }
    public string TabPmoTitle { get; set; }
    public string TabSparesTitle { get; set; }
    public string TabTasksTitle { get; set; }
    public string TabSparesPmoTitle { get; set; }
    public string TabTasksPmoTitle { get; set; }
    public string PrevActionDetail { get; set; }
    public string RaWithActionsTxt { get; set; }

    public class DataItem
    {
        public string RiskType { get; set; }
        public double Cost { get; set; }
        public string CostString { get; set; }
    }

    public string FormatAxis(object value)
    {
        return FormatAsSelectedCurrency((decimal) value);
    }

    public DataItem[] ChartDataActions { get; set; }
    public DataItem[] ChartDataDirectRiskCost { get; set; }
    public DataItem[] ChartDataValueRisk { get; set; }

    #endregion

    public bool IsRiskAnalysisView { get; set; }
    public bool IsCsirView { get; set; }
    public bool IsSapaView { get; set; }
    public bool IsPmoView { get; set; }
    public bool TypesSetToColumn { get; set; }

    public List<DepartmentModel> UserDepartments { get; set; }
    public IEnumerable<string> UserRoles { get; set; }
    public bool IsAdminUser { get; set; }
    public bool IsMaintenanceEngineer => UserRoles?.Contains(RoleConstants.MaintenanceEngineering) == true;
    public bool IsFinancialControl => UserRoles?.Contains(RoleConstants.FinancialControl) == true;
    public bool IsNormalUser => UserRoles?.Contains(RoleConstants.Default) == true;

    private RiskObjectModel _riskObject;

    public RiskObjectModel RiskObject
    {
        get => _riskObject;
        set
        {
            if (value == null)
                _navigationManager.NavigateTo("/");

            _riskObject = value;

            if (_riskObject == null) return;
            RiskObjectId = _riskObject.Id;
            RiskTree.Initialize(_riskAnalysisManager.GetRisksTreeNodeByRiskObject(RiskObjectId));
            SelectRiskNode(RiskId);
        }
    }

    public RiskEditQueryParams QueryParams { get; set; }

    [Parameter] public int RiskObjectId { get; set; }
    [Parameter] public int RiskId { get; set; }

    public Paginator Paginator { get; set; }

    public UtilityGrid<SpareModel> SparesGrid { get; set; }
    public UtilityGrid<TaskModel> PreventiveActionsGrid { get; set; }

    public RiskModel Risk { get; set; }

    public TreeGeneric<RiskTreeObject> RiskTree { get; } = new();

    public List<LookupModel> Statuses { get; set; } = new();

    public Status? StatusFilter { get; set; }

    public List<string> CriticalityTypes { get; private set; } = new();

    public List<ScenarioModel> Scenarios { get; set; } = new();
    public List<ObjectModel> Objects { get; set; } = new();

    public List<FailureCategory> FailureCategories1 { get; set; } = new();
    public List<FailureCategory> FailureCategories2 { get; set; } = new();

    public string ErrorText { get; set; }
    public int DisplayVersion { get; set; } = 1;

    public RadzenTabs RiskTabs { get; set; }
    public RadzenTabs TotalTabs { get; set; }

    private LookupSettingModel DefaultDepreciationPct { get; set; }
    private LookupSettingModel DefaultSpareManagementPct { get; set; }
    private LookupSettingModel DefaultModificationPct { get; set; }

    public string FileUpload { get; set; }
    private string UploadError { get; set; }

    private RadzenFileInput<string> UploadRef { get; set; }

    public Dictionary<BusinessLogic.Enums.Status?, string> ItemStatusDict { get; set; }
    public Dictionary<int?, string> FailureModeDict { get; set; }
    public Dictionary<int?, string> AdditionalDataDict { get; set; }
    public Dictionary<string, Dictionary<int, string>> TaskModelDropDownOverrides { get; set; }

    public EntityEditorMode EditorMode
    {
        get
        {
            return RiskId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    public bool ShowGauge { get; set; }

    public override async Task OnInitializedAsync()
    {
        await SetupUserAccess();

        FailureModeDict = _dropdownManager.GetNullableFailureModesDict();
        TaskModelDropDownOverrides = _dropdownManager.GetTaskModelDropDownOverrides();
        RiskObject = _riskOrganizerManager.TryGetRiskObject(RiskObjectId);
        ItemStatusDict = StatusExtensions.GetStatusDictionaryNullable();
        AdditionalDataDict = _dropdownManager.GetAdditionalDataDict();

        if (RiskObject == null)
            _navigationManager.NavigateTo("/value-risk-organizer");

        ShowGauge = _lookupManager.GetRiskAnalysisShowGauge();
        Scenarios = _scenarioManager.GetAllScenarios();
        Objects = _riskAnalysisManager.GetAllObjects();
        ObjectLevels = _objectManager.GetObjectLevelNames();
        CriticalityTypes = _riskAnalysisManager.GetCriticalityTypes();

        var failureCategories = _failureManager.GetAllFailureCategories();
        FailureCategories1 = failureCategories.Primary;
        FailureCategories2 = failureCategories.Secondary;

        Statuses = _lookupManager.GetLookupByFilter("StatusTypes");
        _pageNavigationManager.SetSelectedScenario(RiskObject.ScenarioId);

        DefaultDepreciationPct = _lookupManager.GetLookupSettingByPropertyName("DepreciationPct");
        DefaultSpareManagementPct = _lookupManager.GetLookupSettingByPropertyName("SpareManPct");
        DefaultModificationPct = _lookupManager.GetLookupSettingByPropertyName("ModificationPct");

        LoadExtraData(QueryParams.BottomTabs);

        var analysisType = RiskObject.AnalysisType.ToLower();
        IsRiskAnalysisView = !analysisType.Contains("pmo") && !analysisType.Contains("csir") &&
                             !analysisType.Contains("sapa");
        IsCsirView = analysisType.Contains("csir");
        IsSapaView =
            analysisType
                .Contains("sapa"); //sapa calculation enables to calculate with percentages (also enable sapa on settings page (administrator))
        IsPmoView = analysisType.Contains("pmo");
        TypesSetToColumn = _lookupManager.GetSetTypesToColumn() && IsSapaView;
        if (!IsPmoView) ShowGauge = false;

        ProcessTaskConfigurationEffectOnRisk(); //PMO acties worden op een of andere manier gereset. Op deze wijze dwing ik een correctie visualisatie af, maar is onjuist****
        SetTitlesBasedOnStatus();

        if (IsRiskAnalysisView || IsSapaView)
        {
            OnChangeBottomTabsComponent(0); //Set the Tab on the first page
        }
        else
        {
            SetChartData();
            OnChangeBottomTabsComponent(2); //Set the Tab on the PMO risk page
        }
    }

    private void SetTitlesBasedOnStatus()
    {
        if (IsRiskAnalysisView)
        {
            PageTitle = _localizer["RaHeaderTxt"];
            TabTasksTitle = _localizer["RaActionsTabTxt"];
            PrevActionDetail = _localizer["RaPrevActDetailsTxt"];
            RaWithActionsTxt = _localizer["RaWithActionsTxt"];
        }
        else if (IsSapaView)
        {
            PageTitle = "Strategic Asset Portfolio Analysis";
            TabTasksTitle = _localizer["RaSapaActionsTabTxt"];
            PrevActionDetail = _localizer["RaSapaDetailsTxt"];
            RaWithActionsTxt = _localizer["RaWithSapaActionsTxt"];
        }
        else
        {
            PageTitle = _localizer["RaPMOHeaderTxt"];
            TabTasksTitle = _localizer["RaActionsTabTxt"];
            PrevActionDetail = _localizer["RaPrevActDetailsTxt"];
            RaWithActionsTxt = _localizer["RaWithActionsTxt"];
        }

        TabPmoTitle = _localizer["RaRiskPmoTabTxt"];
        TabBeforeTitle = _localizer["RaRiskBeforeTabTxt"];
        TabSparesTitle = _localizer["RaSparesTabTxt"];
        TabSparesPmoTitle = _localizer["RaSparesPMOTabTxt"];
        TabTasksPmoTitle = _localizer["RaActionsPMOTabTxt"];
    }

    private void SetChartData()
    {
        if (Risk == null)
            return;

        decimal? prevCostPmo = Risk.PreventiveCostsPmo ?? 0;
        decimal? prevCost = Risk.PreventiveCosts ?? 0;
        decimal? directCostPmo = (Risk.DirectCostPmo ?? 0) / (Risk.MtbfPmo ?? 1000000000000);
        decimal? directCost = (Risk.DirectCostAfter ?? 0) / (Risk.MtbfAfter ?? 1000000000000);
        decimal? riskPmo = (Risk.RiskPmo ?? 0) - (directCostPmo ?? 0);
        decimal? riskAfter = (Risk.RiskAfter ?? 0) - (directCost ?? 0);

        ChartDataActions = new DataItem[]
        {
            new()
            {
                RiskType = "Current", Cost = (double) prevCostPmo, CostString = FormatAsSelectedCurrency(prevCostPmo)
            },
            new() {RiskType = "Analysed", Cost = (double) prevCost, CostString = FormatAsSelectedCurrency(prevCost)}
        };
        ChartDataDirectRiskCost = new DataItem[]
        {
            new()
            {
                RiskType = "Current", Cost = (double) directCostPmo,
                CostString = FormatAsSelectedCurrency(directCostPmo)
            },
            new() {RiskType = "Analysed", Cost = (double) directCost, CostString = FormatAsSelectedCurrency(directCost)}
        };
        ChartDataValueRisk = new DataItem[]
        {
            new() {RiskType = "Current", Cost = (double) riskPmo, CostString = FormatAsSelectedCurrency(riskPmo)},
            new() {RiskType = "Analysed", Cost = (double) riskAfter, CostString = FormatAsSelectedCurrency(riskAfter)}
        };
    }

    public override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            UpdatePaginator();
        }

        base.OnAfterRender(firstRender);
    }

    public int GetInitialPaginatorValue()
    {
        var node = RiskTree.GetRiskTreeNode(RiskId);
        return GetRiskTreeNodes().IndexOf(node);
    }

    public void ToggleGauge(bool? value)
    {
        if (value == null) return;
        var setting = new LookupSettingModel
        {
            Property = PropertyNames.RiskAnalysisShowGauge,
            TextValue = value.ToString(),
            SettingType = SettingType.Text
        };

        _lookupManager.SaveLookupSettings(setting);
    }

    public int GetRiskCount()
    {
        return GetRiskTreeNodes().Count;
    }

    public void PaginatorCallback(int riskIndex)
    {
        var riskId = GetRiskTreeNodes().Skip(riskIndex).FirstOrDefault()?.Source?.RiskId;

        if (riskId != null)
        {
            SelectRiskNode(riskId.Value);
            Risk = _riskAnalysisManager.GetRisk(RiskId);
            NavigateToRisk(RiskObjectId, RiskId);
        }
    }

    public void UpdateStatusFilter()
    {
        RiskTree.Initialize(_riskAnalysisManager.GetRisksTreeNodeByRiskObject(RiskObjectId, (int?) StatusFilter));
        SelectRiskNode(RiskId);
    }

    private void SelectRiskNode(int riskId)
    {
        var node = RiskTree.GetRiskTreeNode(riskId);

        if (node == null)
            SelectFirstRiskNode();
        else
            SelectTreeNode(node);

        //Try to correct configuration
        if (RiskObjectId <= 0) RiskObjectId = Risk.RiskObjectId;

        //If RiskObjectId is bigger than 0 then save querystring
        if (RiskObjectId > 0)
            _pageNavigationManager.SavePageQueryString($"/value-risk-analysis/{RiskObjectId}/risks/{RiskId}",
                string.Empty);
    }

    private void SelectFirstRiskNode()
    {
        var riskNode = RiskTree.GetFirstRiskNode();

        if (riskNode != null)
            SelectTreeNode(riskNode);
        else
        {
            Risk = null;
        }
    }

    private void SelectTreeNode(TreeNodeGeneric<RiskTreeObject> node)
    {
        if (node.Source.RiskId == null) return;
        RiskId = node.Source.RiskId.Value;
        Risk = _riskAnalysisManager.GetRisk(RiskId);
        NavigateToRisk(RiskObjectId, RiskId);

        if (!node.IsSelected)
            RiskTree.SelectNode(node);

        var parent = node.Parent;
        while (parent != null)
        {
            parent.Open = true;
            parent = parent.Parent;
        }
    }

    public void ChangeTreeNodeOrder(List<TreeNodeGeneric<RiskTreeObject>> nodes)
    {
        foreach(var node in nodes)
        {
            node.Source.SortOrder = node.SortOrder;
        }

        _riskAnalysisManager.UpdateRiskOrder(nodes.Select(x => x.Source).ToList());
    }

    private void UpdateTaskGridDropdowns()
    {
        TaskModelDropDownOverrides = _dropdownManager.GetTaskModelDropDownOverrides();

        if (Risk != null)
        {
            TaskModelDropDownOverrides.Add(nameof(TaskModel.PartOf), Risk.Tasks.ToDictionary(x => x.Id, x => x.Name));
        }

        PreventiveActionsGrid?.BindingContext.RefreshDropdownOverrides(TaskModelDropDownOverrides);
    }

    private List<TreeNodeGeneric<RiskTreeObject>> GetRiskTreeNodes()
    {
        return RiskTree.GetFlattenedNodes(x => x.Source.NodeType == RiskTreeNodeType.Risk);
    }

    private void UpdatePaginator()
    {
        if (Paginator != null)
        {
            var node = RiskTree.GetRiskTreeNode(RiskId);
            var riskNodes = GetRiskTreeNodes();
            Paginator.BindingContext.SetCurrentExternally(riskNodes.IndexOf(node));
        }
    }

    public void OnChangeTopTabsComponent(int index)
    {
        QueryParams.TopTabs = index;
        SetQueryString();
    }

    public void OnChangeBottomTabsComponent(int index)
    {
        QueryParams.BottomTabs = index;
        SetQueryString();
        LoadExtraData(2);
    }

    private void LoadExtraData(int bottomTabIndex)
    {
        if (Risk != null && bottomTabIndex == 2) // Preventive Actions Tab
            Risk.Tasks = _riskAnalysisManager.GetTasksWithDetailsByRisk(Risk.Id);
    }

    private void SetQueryString()
    {
        var url = $"{_navigationManager.Uri.Split('?')[0]}{QueryParams.ToQueryString()}";

        if (_navigationManager.Uri != url)
            _navigationManager.NavigateTo(url);
    }

    private void CloseModal()
    {
        RiskTabs?.Reload();
        SparesGrid?.BindingContext?.Grid?.Reload();
        PreventiveActionsGrid?.BindingContext?.Grid?.Reload();
        TotalTabs?.Reload();
    }

    public void UpdateCurrentRisk()
    {
        if (Risk.FailureModeId != Risk.FailureMode?.Id)
            ChangeNameBasedOnMode();

        Risk = _riskAnalysisManager.UpdateRisk(Risk);
    }

    public void CreateNewRisk()
    {
        CreateAndSelectRisk(GetNewRisk());
        if (IsRiskAnalysisView)
        {
            OnChangeBottomTabsComponent(0);
        }
        else
        {
            OnChangeBottomTabsComponent(2);
        }
    }

    public async Task ImportCurrentTaskPlan(string value)
    {
        // clear ref, the component wants to display an image preview which does not apply
        UploadError = FileUpload = null;
        await UploadTaskPlans(value?.Split(',').LastOrDefault()?.Trim()).ConfigureAwait(false);
        await _jsRuntime.InvokeAsync<bool>("ClearFileInputValue", UploadRef.Element).ConfigureAwait(false);
    }

    private async Task UploadTaskPlans(string base64Input)
    {
        if (string.IsNullOrWhiteSpace(base64Input))
            return;

        ShowLoadingDialog();

        var result = await _riskAnalysisManager.ProcessImportedTaskPlans(Risk, base64Input).ConfigureAwait(false);

        if (result.Success)
        {
            Risk = _riskAnalysisManager.GetRisk(RiskId);
        }

        _dialogService.Close();
        // TODO: Reload tree??
        ShowImportResultDialog(result);
    }

    private void ShowLoadingDialog()
    {
        _dialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                {nameof(ImportResultDialog.ImportResult), new ImportResult {Status = ImportStatus.Loading}}
            });
    }

    private void ShowImportResultDialog(ImportResult importResult)
    {
        _dialogService.Open<ImportResultDialog>(importResult.Success ? "Success" : "Error importing items",
            new Dictionary<string, object>
            {
                {nameof(ImportResultDialog.ImportResult), importResult}
            });
    }

    public void DeleteSelectedRisk()
    {
        ShowAreYouSurePopup(Risk, _localizer["RaDeleteRiskWarning"],
            EventCallback.Factory.Create<RiskModel>(this, ConfirmedDeleteRisk));
    }

    private void ConfirmedDeleteRisk(RiskModel risk)
    {
        var location = RiskTree.GetNodeLocation(RiskTree.SelectedNode);
        RiskTree.Initialize(_riskAnalysisManager.RemoveRiskNodeFromTree(RiskTree.Node, location), false);
        _riskAnalysisManager.DeleteRisk(Risk.Id);
        SelectNearestNode();
    }

    private void SelectNearestNode()
    {
        var location = RiskTree.GetNodeLocation(RiskTree.SelectedNode);
        var parentNode = location.Count > 1
            ? RiskTree.GetNodeFromLocation(location.Take(location.Count - 1).ToList())
            : null;

        if (parentNode?.Nodes.Any() == true)
        {
            var siblingNode = parentNode.Nodes[Math.Max(0, location.Last() - 1)];
            SelectTreeNode(siblingNode);
        }
        else
        {
            SelectFirstRiskNode();
        }

        UpdatePaginator();
    }

    public void DeleteRiskFromTree(object riskTreeObject)
    {
        if (riskTreeObject is RiskTreeObject risk)
        {
            if (!(risk.RiskId > 0)) return;
            _riskAnalysisManager.DeleteRisk(risk.RiskId.Value);

            if (risk.RiskId == Risk.Id)
                SelectNearestNode();
            else
                UpdatePaginator();
        }
        else
        {
            throw new ArgumentException(
                $"Incorrect configuration, function should only receive object of Type {typeof(RiskTreeObject)}");
        }
    }

    public void PasteInRiskTree((List<RiskTreeObject> source, RiskTreeObject destination) args)
    {
        var copiedNodeType = args.source.FirstOrDefault()?.NodeType ?? RiskTreeNodeType.Risk;
        var destinationType = args.destination.NodeType;

        foreach (var s in args.source)
        {
            if (s.RiskId == null)
                continue;

            var riskToCopy = _riskAnalysisManager.GetRisk(s.RiskId.Value);
            riskToCopy.Id = 0;

            // handle copying risk to a different RiskObject
            riskToCopy.RiskObjectId = Risk.RiskObjectId;
            riskToCopy.CollectionId = Risk.CollectionId;
            riskToCopy.InstallationId = Risk.InstallationId;

            // You can only paste by clicking on a Node, therefore it's currently not possible to set the SystemId to anything other that the destination Node SystemId
            riskToCopy.SystemId = args.destination.SystemId;

            // We default to pasting a Risk onto a System, in this case both Component and Assembly should be set to null
            riskToCopy.ComponentId = null;
            riskToCopy.AssemblyId = null;

            // If either the source or destination has a relevant value we need to copy, do that
            if ((int) copiedNodeType <= (int) RiskTreeNodeType.Component ||
                (int) destinationType >= (int) RiskTreeNodeType.Component)
                riskToCopy.ComponentId = args.destination.ComponentId ?? s.ComponentId;

            // Same for Assembly
            if ((int) copiedNodeType <= (int) RiskTreeNodeType.Assembly ||
                (int) destinationType >= (int) RiskTreeNodeType.Assembly)
                riskToCopy.AssemblyId = args.destination.AssemblyId ?? s.AssemblyId;

            CreateAndSelectRisk(riskToCopy);

            Risk.CalculateSpareCost(DefaultDepreciationPct.DecimalValue, DefaultSpareManagementPct.DecimalValue);
        }

        NavigateToRisk(RiskObjectId, RiskId);
        UpdatePaginator();
    }

    public void NewRiskInTree(RiskTreeObject treeObject)
    {
        if (RiskObject == null) return;

        var risk = GetNewRisk();
        risk.SystemId = treeObject?.SystemId;
        risk.ComponentId = treeObject?.ComponentId;
        risk.AssemblyId = treeObject?.AssemblyId;

        CreateAndSelectRisk(risk);
        UpdatePaginator();
    }

    private RiskModel GetNewRisk()
    {
        var failureMode = FailureModeDict.FirstOrDefault();
        return new RiskModel
        {
            RiskObjectId = RiskObjectId,
            RiskObject = RiskObject,
            CollectionId = RiskObject.ParentObjectId,
            InstallationId = RiskObject.ObjectId,
            Name = $"{failureMode.Value} New Risk".Trim(),
            FailureModeId = failureMode.Key,
            SystemId = Risk?.SystemId,
            ComponentId = Risk?.ComponentId,
            AssemblyId = Risk?.AssemblyId
        };
    }

    private void CreateAndSelectRisk(RiskModel risk)
    {
        Risk = _riskAnalysisManager.UpdateRisk(risk);
        RiskId = Risk.Id;
        RiskTree.Initialize(_riskAnalysisManager.AddRiskToTree(RiskTree.Node, Risk), false);
        SelectRiskNode(RiskId);
        _logger.LogInformation($"Creating a {nameof(Risk)} succeeded. Name = {Risk.Name}, ID = {Risk.Id}.");
    }

    private void UpdateObjectAndNavigate(RiskModel risk)
    {
        Risk = _riskAnalysisManager.UpdateRisk(risk);

        if (RiskTree.SelectedNode != null)
            RiskTree.SelectedNode.Name = Risk.Name;

        _logger.LogInformation($"Update of {nameof(Risk)} with Id {Risk.Id} succeeded.");
        NavigateToRisk(Risk.RiskObjectId, Risk.Id);
    }

    private void NavigateToRisk(int riskObjectId, int riskId)
    {
        if (Risk == null || Risk.Id != riskId)
            Risk = _riskAnalysisManager.GetRisk(riskId);

        if (riskObjectId <= 0) riskObjectId = Risk.RiskObjectId;

        LoadExtraData(QueryParams.BottomTabs);

        UpdateTaskGridDropdowns();

        // prevent navigation on opening the page
        if (!_navigationManager.Uri.Contains($"/value-risk-analysis/{riskObjectId}/risks/{riskId}"))
            _navigationManager.NavigateTo($"/value-risk-analysis/{riskObjectId}/risks/{riskId}{GetQueryString()}",
                false);

        SelectCurrentRiskInTree(RiskId);

        if (IsPmoView) SetChartData();
    }

    private string GetQueryString()
    {
        return _navigationManager.Uri.Contains('?')
            ? $"?{_navigationManager.Uri.Split('?').LastOrDefault()}"
            : string.Empty;
    }

    private void SelectCurrentRiskInTree(int riskId)
    {
        var currentRiskTreeObject = RiskTree.GetRiskTreeNode(riskId);

        if (currentRiskTreeObject != null)
            RiskTree.SelectNode(currentRiskTreeObject);
    }

    public async Task DownloadImage()
    {
        // Invoke JavaScript function to convert HTML to image
        await _javaScriptHelper.InvokeAsync<object>("convertToImage", "content", Risk.Name);
    }

    public void ClickTreeNode(TreeNodeGeneric<RiskTreeObject> node)
    {
        if (RiskTree.MultiSelect)
        {
            if (node.Source.RiskId.HasValue) return;
            RiskTree.SelectNode(node);

            while (node.Source.RiskId == null)
            {
                node.Open = true;
                node = node.Nodes.First();
            }

            RiskTree.SelectNode(node);
        }
        else
        {
            if (node.Source.RiskId.HasValue)
            {
                RiskId = node.Source.RiskId.Value;
                Risk = _riskAnalysisManager.GetRisk(RiskId);
                NavigateToRisk(RiskObjectId, RiskId);
            }
            else
            {
                while (node.Source.RiskId == null)
                    node = node.Nodes.First();

                RiskTree.SelectNode(node);
                ClickTreeNode(node);
            }

            _pageNavigationManager.SavePageQueryString($"/value-risk-analysis/{RiskObjectId}/risks/{RiskId}");
            ProcessTaskConfigurationEffectOnRisk();
            UpdatePaginator();
        }
    }

    public void ChangeObjectStructure(ObjectLevel level, ObjectModel objectModel)
    {
        // There is no foolproof way to find the previous item without significant refactoring,
        // however since we're only changing some texts, It's fine to just loop over all the possible previous objects
        // the only duplicates will be when multiple Objects (possibly on different levels) have the exact same name
        var previousPossibleObjects = Objects.Where(x => x.Name == RiskTree.SelectedNode.Parent.Name);

        switch (level)
        {
            case ObjectLevel.System:
                Risk.SystemId = objectModel?.Id;

                if (Risk.ComponentId == null && Risk.AssemblyId == null &&
                    (string.IsNullOrWhiteSpace(Risk.Function) ||
                     previousPossibleObjects.Any(x => x.Function == Risk.Function)))
                {
                    Risk.Function = objectModel?.Function;
                }

                break;
            case ObjectLevel.Component:
                Risk.ComponentId = objectModel?.Id;

                if (Risk.AssemblyId == null &&
                    (string.IsNullOrWhiteSpace(Risk.Function) ||
                     previousPossibleObjects.Any(x => x.Function == Risk.Function)))
                {
                    Risk.Function = objectModel?.Function;
                }

                break;
            case ObjectLevel.Assembly:
                Risk.AssemblyId = objectModel?.Id;

                if (string.IsNullOrWhiteSpace(Risk.Function) ||
                    previousPossibleObjects.Any(x => x.Function == Risk.Function))
                {
                    Risk.Function = objectModel?.Function;
                }

                break;
        }

        var location = RiskTree.GetNodeLocation(RiskTree.SelectedNode);
        RiskTree.Initialize(_riskAnalysisManager.RemoveRiskNodeFromTree(RiskTree.Node, location), false);
        RiskTree.Initialize(_riskAnalysisManager.AddRiskToTree(RiskTree.Node, Risk), false);
        UpdateObjectAndNavigate(Risk);
        SelectRiskNode(RiskId);
        UpdatePaginator();
    }

    private void ChangeNameBasedOnMode()
    {
        // Check previous failure mode, used for changing the name
        var previousFailureModeName = Risk.FailureMode?.Name;
        var failureModeName = FailureModeDict.FirstOrDefault(x => x.Key == Risk.FailureModeId).Value;

        // Remove previous failureMode prefix from RiskName
        if (!string.IsNullOrWhiteSpace(previousFailureModeName))
            Risk.Name = Risk.Name.Replace(previousFailureModeName, string.Empty);

        // Prefix RiskName with new FailureMode name, and prevent duplication
        if (!string.IsNullOrWhiteSpace(failureModeName))
        {
            Risk.Name = Risk.Name.Replace(failureModeName, string.Empty);
            Risk.Name = $"{failureModeName.Trim()} {Risk.Name.Trim()}";
        }

        // Cleanup double spaces
        Risk.Name = Regex.Replace(Risk.Name, @"\s+", " ");

        ChangeRiskName(Risk.Name);
    }

    public void UpdateRiskNameDirectly()
    {
        ChangeRiskName();
        UpdateCurrentRisk();
    }

    public void UpdateSortOrderProperty()
    {
        Risk = _riskAnalysisManager.UpdateRisk(Risk);
        RiskTree.SelectedNode.Source.SortOrder = Risk.SortOrder;
        UpdateTreeSortOrder();
    }

    public void ChangeRiskName(string name = null)
    {
        Risk.Name = (name ?? Risk.Name).Trim().LimitLength(100);
        RiskTree.SelectedNode.Name = Risk.Name;
        UpdateTreeSortOrder();
    }

    public void UpdateTreeSortOrder()
    {
        RiskTree.SelectedNode.Parent.Nodes = RiskTree.SelectedNode.Parent.Nodes
            .OrderBy(x => x.Source.SortOrder)
            .ThenBy(x => x.Name).ToList();
    }

    public string GetPageTitle()
    {
        var sb = new StringBuilder();

        sb.Append($"{RiskObject.Scenario.Name} - ");

        if (RiskObject.ParentObjectId != null)
            sb.Append($"{Objects.FirstOrDefault(x => x.Id == RiskObject.ParentObjectId)} - ");

        sb.Append($"{RiskObject.Name}");

        return sb.ToString();
    }

    public void EditRiskMatrixCallBack(RiskModel risk)
    {
        Risk.MtbfAfter = risk.MtbfAfter;
        Risk.MtbfBefore = risk.MtbfBefore;
        Risk.MtbfPmo = risk.MtbfPmo;

        if (IsPmoView) SetChartData();
        ProcessTaskConfigurationEffectOnRisk();

        UpdateCurrentRisk();
    }

    #region Task

    public void OpenPreventiveActionPopup(int id, bool pmo)
    {
        _dialogService.Open<PreventiveActionEdit>(PrevActionDetail,
            new Dictionary<string, object>
            {
                {"PreventiveActionId", id},
                {"RiskId", RiskId},
                {"Callback", EventCallback.Factory.Create<TaskModel>(this, SaveTask)},
                {"Pmo", pmo},
                {"IsSapaTask", IsSapaView}
            },
            new DialogOptions {Width = "840px", /*Height = "525px",*/ Resizable = true, Draggable = true});
    }

    public void DeleteTask(TaskModel task)
    {
        var dbTask = Risk.Tasks.FirstOrDefault(x => x.Id == task.Id);
        if (dbTask == null) return;
        Risk.Tasks.Remove(task);
        Risk.Tasks = new List<TaskModel>(Risk.Tasks);
        _riskAnalysisManager.DeleteTask(task);

        ProcessTaskConfigurationEffectOnRisk();
    }

    public void SaveTask(TaskModel task)
    {
        if (task.ClusterCosts > 0 && task.ClusterCosts == task.Costs && task.ClusterCosts != task.EstCosts)
        {
            _dialogService.Open<AreYouSureDialog<TaskModel>>(_localizer["RaOverrideCostsTitle"],
                new Dictionary<string, object>
                {
                    {nameof(AreYouSureDialog<TaskModel>.Item), task},
                    {
                        nameof(AreYouSureDialog<TaskModel>.YesCallback),
                        EventCallback.Factory.Create<TaskModel>(this, SaveTaskOverrideCosts)
                    },
                    {
                        nameof(AreYouSureDialog<TaskModel>.NoCallback),
                        EventCallback.Factory.Create<TaskModel>(this, SaveTaskInternal)
                    },
                    {nameof(AreYouSureDialog<TaskModel>.Text), _localizer["RaOverrideCostsText"].Value},
                },
                new DialogOptions {CloseDialogOnEsc = false, ShowClose = false, Resizable = false, Draggable = true});

            return;
        }

        SaveTaskOverrideCosts(task);
    }

    private void SaveTaskOverrideCosts(TaskModel task)
    {
        // When saving a Task from the RiskEditor, the Costs need to be copied over from the Estimated Costs
        task.Costs = task.EstCosts;
        SaveTaskInternal(task);
    }

    private void SaveTaskInternal(TaskModel task)
    {
        if (task.PartOf.HasValue)
        {
            if (task.Id == task.PartOf)
            {
                task.PartOf = null;

                // Show popup, not Allowed to set itself as parent
                _dialogService.Open<InformationDialog>("field 'PartOf' Cleared.",
                    new Dictionary<string, object>
                    {
                        {"DialogContent", $"'{task.Name}' can't be part of itself"}
                    });
            }
            else
            {
                var parentTask = Risk.Tasks.Find(x => x.Id == task.PartOf);

                if (parentTask?.PartOf != null)
                {
                    task.PartOf = null;
                    _dialogService.Open<InformationDialog>("field 'PartOf' Cleared.",
                        new Dictionary<string, object>
                        {
                            {
                                "DialogContent",
                                $"'{parentTask.Name}' has a PartOf istelf. Multiple levels of inheritence is not supported"
                            }
                        });
                }
            }
        }

        // Save the Task
        _riskAnalysisManager.UpdateTask(task);

        // Refresh the risk, so the Calculation will be done correctly
        Risk = _riskAnalysisManager.GetRisk(Risk.Id);

        // Process the Costs and save the Risk
        ProcessTaskConfigurationEffectOnRisk();
        Risk = _riskAnalysisManager.UpdateRisk(Risk);

        // Refresh Task Grid
        RiskTabs.Reload();
        ProcessTaskConfigurationEffectOnRisk(); //PMO acties worden op een of andere manier gereset. Op deze wijze dwing ik een correctie visualisatie af, maar is onjuist****
    }

    private void ProcessTaskConfigurationEffectOnRisk()
    {
        UpdateTaskGridDropdowns();

        var defaultModificationPct = _lookupManager.GetLookupSettingByPropertyName("ModificationPct");
        Risk?.CalculateTasksCost(defaultModificationPct?.DecimalValue);

        TotalTabs?.Reload();
    }

    public void PasteTaskCallback(TaskModel model, CopyType copyType)
    {
        switch (copyType)
        {
            case CopyType.Copy:
                var copiedTask = model.CopyAsNew<TaskModel>();
                if (copiedTask.PartOf.HasValue && !Risk.Tasks.Exists(x => x.Id == copiedTask.PartOf))
                {
                    copiedTask.PartOf = null;
                }

                copiedTask.MrbId = Risk.Id;
                copiedTask = _riskAnalysisManager.UpdateTask(copiedTask);
                Risk.Tasks = new List<TaskModel>(Risk.Tasks.Append(copiedTask));
                break;

            case CopyType.Cut:
                if (model.MrbId == Risk.Id) break;
                if (model.PartOf.HasValue && !Risk.Tasks.Exists(x => x.Id == model.PartOf))
                {
                    model.PartOf = null;
                }

                model.MrbId = Risk.Id;
                model = _riskAnalysisManager.UpdateTask(model);
                Risk.Tasks = new List<TaskModel>(Risk.Tasks.Append(model));
                break;
        }

        ProcessTaskConfigurationEffectOnRisk();
    }

    public void ReverseDeriveTask(TaskModel model)
    {
        Risk.Tasks.Add(_riskAnalysisManager.DeriveTask(model, true));
    }

    public void DeriveTask(TaskModel model)
    {
        Risk.Tasks.Add(_riskAnalysisManager.DeriveTask(model, false));
    }

    #endregion

    #region Spare

    public void OpenSparePopup(int id, bool pmo)
    {
        _dialogService.Open<SpareEdit>(@_localizer["RaSparePartDetailsTxt"],
            new Dictionary<string, object>
            {
                {"SpareId", id},
                {"RiskId", RiskId},
                {"Callback", EventCallback.Factory.Create<SpareModel>(this, AddOrEditSpareCallback)},
                {"Pmo", pmo}
            },
            new DialogOptions {Width = "700px", /*Height = "510px",*/ Resizable = false, Draggable = true});
    }

    public void DeleteSpare(SpareModel spare)
    {
        var dbSpare = Risk.Spares.FirstOrDefault(x => x.Id == spare.Id);
        if (dbSpare == null) return;
        Risk.Spares.Remove(spare);
        Risk.Spares = new List<SpareModel>(Risk.Spares);
        Risk = _riskAnalysisManager.DeleteSpare(spare);
    }

    public void UpdateSpare(SpareModel spare)
    {
        spare.CalculateSparePartCost();
        _sparePartManager.UpdateSpare(spare);
        ProcessSpareConfigurationEffectOnRisk();
    }

    private void AddOrEditSpareCallback(SpareModel spare)
    {
        spare.MrbId = Risk.Id;

        var currentSpare = Risk.Spares.FirstOrDefault(x => x.Id == spare.Id);
        if (currentSpare != null)
            Risk.Spares[Risk.Spares.IndexOf(currentSpare)] = spare;
        else
            Risk.Spares.Add(spare);

        UpdateSpare(spare);
        TotalTabs?.Reload();
        CloseModal();
    }

    public void PasteSpareCallback(SpareModel model, CopyType copyType)
    {
        switch (copyType)
        {
            case CopyType.Copy:
                var copiedSpare = model.CopyAsNew<SpareModel>();
                copiedSpare.MrbId = Risk.Id;
                copiedSpare = _sparePartManager.UpdateSpare(copiedSpare);
                Risk.Spares = new List<SpareModel>(Risk.Spares.Append(copiedSpare));
                break;

            case CopyType.Cut:
                if (model.MrbId == Risk.Id) break;
                model.MrbId = Risk.Id;
                model = _sparePartManager.UpdateSpare(model);
                Risk.Spares = new List<SpareModel>(Risk.Spares.Append(model));
                break;
        }

        ProcessSpareConfigurationEffectOnRisk();
    }

    private void ProcessSpareConfigurationEffectOnRisk()
    {
        //Does task and spare parts calculation
        Risk.CalculatePreventiveCosts(DefaultDepreciationPct.DecimalValue, DefaultSpareManagementPct.DecimalValue,
            DefaultModificationPct.DecimalValue, true);

        Risk = _riskAnalysisManager.UpdateRisk(Risk);
        TotalTabs?.Reload();

        ProcessTaskConfigurationEffectOnRisk(); //PMO acties worden op een of andere manier gereset. Op deze wijze dwing ik een correctie visualisatie af, maar is onjuist****
    }

    public void ReverseDeriveSpare(SpareModel model)
    {
        Risk.Spares.Add(_riskAnalysisManager.DeriveSpare(model, true));
    }

    public void DeriveSpare(SpareModel model)
    {
        Risk.Spares.Add(_riskAnalysisManager.DeriveSpare(model, false));
    }

    #endregion

    public string GetRiskOrganizerUrl() =>
        $"/value-risk-organizer?scenario={RiskObject?.ScenarioId}&riskobject={RiskObjectId}&scenarios={RiskObject?.ScenarioId}&categories={RiskObject?.ParentObjectId ?? 0}";

    public string FormatAsSelectedCurrency(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
    }

    public string FormatAsNumber(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("N0", CultureInfo.CreateSpecificCulture(Currency));
    }

    public static string FormatAsPoints(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("N0");
    }

    public static string FormatAsResistanceLevel(decimal? value)
    {
        if (value == null) return string.Empty;

        value = Math.Round((decimal) value);
        if (value > 1) value -= 1; //This is done to define the specific demands of the CSIR Resistance Number
        return value.Value.ToString("N0");
    }

    public bool RiskIsReadOnly()
    {
        if (IsSapaView)
            return Risk?.RiskObject?.Status is (int)Status.Budgeting or (int)Status.Complete;

        if (IsFinancialControl)
            return true;

        if ((UserDepartments == null || UserDepartments.Count == 0) && IsSapaView)
            return true;

        //if (_riskObject?.DepartmentId.HasValue == true &&
        //    UserDepartments.Any(dept => dept.Id == _riskObject.DepartmentId.Value))
        //    return false;

        return false; //For the moment, not to strict...
    }

    public string GetRiskIsReadOnlyText()
    {
        int? statusInt = Risk?.RiskObject?.Status;
        Status? status = (statusInt.HasValue && Enum.IsDefined(typeof(Status), statusInt))
            ? (Status?)statusInt
            : null;

        return status.HasValue
            ? $"READONLY - {status}"
            : "READONLY";
    }

    private async Task SetupUserAccess()
    {
        UserRoles = (await GetCurrentUserRoles()).ToList();
        IsAdminUser = UserRoles?.Contains(RoleConstants.Administrators) == true ||
                      UserRoles?.Contains(RoleConstants.PortfolioAdministrators) == true;

        if (!IsAdminUser)
        {
            var user = await GetCurrentUserId();
            UserDepartments = _riskAnalysisSetupManager.GetUserDepartments(user).ToList();
        }
    }
}