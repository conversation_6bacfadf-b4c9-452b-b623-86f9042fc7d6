@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<DeleteRiskObjectWidgetViewModel>
@inject IStringLocalizer<NewRiskObjectWidget> _localizer

<div>
    <div class="form-group my-3">
        <label>@_localizer["DRoDeleteQuestion1Txt"] <b>@RiskObject.Name</b> @_localizer["DRoDeleteQuestion2Txt"]</label>?
    </div>
    
    @if(Risks > 0)
    {
        <div class="alert alert-warning" role="alert"> 
            <label>@_localizer["DRoDeleteWarning1Txt"] <b>@Risks</b> @_localizer["DRoDeleteWarning2Txt"]</label>
        </div>
    }

    <div class="form-group my-3">
        <h5>@_localizer["DRoCautionWarningTxt"]</h5>
    </div>

    <div class="form-group my-3">
        <label>@_localizer["DRoDeleteInstr1Txt"] <span class="select-on-click"><b>@RiskObject.Name</b></span> @_localizer["DRoDeleteInstr2Txt"]</label>
        <RadzenTextBox @bind-Value=@BindingContext.ConfirmText Name="Name" class="form-control" />
    </div>

    @if (!string.IsNullOrWhiteSpace(BindingContext.ErrorMessage))
    {
        <div class="alert alert-danger" role="alert">
            @BindingContext.ErrorMessage
        </div>
    }

    <RadzenButton ButtonStyle=ButtonStyle.Danger Icon="delete" Text=@_localizer["DRoDeleteBtn"] Click=@BindingContext.ConfirmDelete />
</div>

@code
{
    [Parameter] public RiskObjectModel RiskObject { get; set; }

    [Parameter] public int Risks { get; set; }
}
