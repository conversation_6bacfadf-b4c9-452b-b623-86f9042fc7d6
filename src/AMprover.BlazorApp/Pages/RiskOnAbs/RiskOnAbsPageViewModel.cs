using AMprover.BusinessLogic;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON><PERSON>;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models.Tree;
using AMprover.BusinessLogic.Models.ABS;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Models.RiskOnAbs;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Enums;
using System;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using AMprover.BlazorApp.Components;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Helpers;
using Radzen.Blazor;
using AMprover.BusinessLogic.Models.Criticality;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Models;
using System.Reflection;

namespace AMprover.BlazorApp.Pages.RiskOnAbs;

public class RiskOnAbsPageViewModel : BaseViewModel
{
    private readonly IAssetBreakdownManager _assetBreakdownManager;
    private readonly IRiskAnalysisSetupManager _riskAnalysisSetupManager;
    private readonly NavigationManager _navigationManager;
    private readonly IPageNavigationManager _pageNavigationManager;
    private readonly ICriticalityRankingManager _criticalityManager;

    public TreeGeneric<RiskOnAbsTreeModel> RiskOnAbsTree { get; } = new();
    public List<AssetModel> Assets { get; set; } = new();
    public RiskOnAbsTreeModel SelectedNode { get; set; }
    public RadzenTabs Tabs { get; set; }

    public List<MatrixTypes> MatrixTypeList { get; } = new() { MatrixTypes.Before, MatrixTypes.Pmo, MatrixTypes.After };
    private MatrixTypes MatrixType { get; set; } = MatrixTypes.Before;

    private bool _showOnlyAssetsWithData { get; set; }

    public bool ShowOnlyAssetsWithData
    {
        get => _showOnlyAssetsWithData;
        set
        {
            _showOnlyAssetsWithData = value;
            FilterTreeIfNoData(RiskOnAbsTree.Node);
        }
    }

    public List<RiskMatrixTemplateModel> RiskMatrixTemplates { get; set; }
    public RiskMatrixTemplateModel RiskMatrixTemplate { get; set; }
    public MatrixSelectionAggregation MatrixSelectionAggregation { get; set; }
    public bool IsDescending { get; set; }

    public RiskOnAbsQueryString Query { get; set; }

    public CriticalityRankingModelFlat Criticality { get; set; }

    public List<KeyValuePair<string, string>> CriticalityDetails { get; set; } = new();
    private List<GridColumnModel> Columns { get; set; }
    private PropertyInfo[] CriticalityProperties { get; set; }

    public RiskOnAbsPageViewModel(
        ILoggerFactory loggerFactory,
        ILookupManager lookupManager,
        DialogService dialogService,
        IAssetBreakdownManager assetBreakdownManager,
        IRiskAnalysisSetupManager riskAnalysisSetupManager,
        NavigationManager navigationManager,
        IPageNavigationManager pageNavigationManager,
        ICriticalityRankingManager criticalityManager) : base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _assetBreakdownManager = assetBreakdownManager;
        _riskAnalysisSetupManager = riskAnalysisSetupManager;
        _navigationManager = navigationManager;
        _pageNavigationManager = pageNavigationManager;
        _criticalityManager = criticalityManager;
    }

    public override void OnInitialized()
    {
        RiskMatrixTemplates = _riskAnalysisSetupManager.GetAllTemplates();
        RiskMatrixTemplate = RiskMatrixTemplates.FirstOrDefault();
        GetOrientation();

        Query = new RiskOnAbsQueryString(_navigationManager.ToAbsoluteUri(_navigationManager.Uri));

        Columns = _lookupManager
            .GetColumns(GridNames.Criticality.Rankings, true)
            .Where(x => x.Visible == true)
            .ToList();

        CriticalityProperties = typeof(CriticalityRankingModelFlat).GetProperties();

        ProcessQueryString();
        InitializeAssetTree();
    }

    private void GetOrientation()
    {
        var firstEffectColumn = RiskMatrixTemplate.MainGrid.TableColumns.FindIndex(x => x.IsEffectColumn);
        var templateValues = RiskMatrixTemplate.MainGrid.TableContent.Select(x =>
            x.Cells[firstEffectColumn].CustomValue?.ToDecimal(Language).IsNullOrZero() == true
                ? x.Cells[firstEffectColumn].Value.ToDecimal(Language)
                : x.Cells[firstEffectColumn].CustomValue?.ToDecimal(Language)).ToList();
        IsDescending = templateValues.IsDescending();
    }

    private void ProcessQueryString()
    {
        if (string.IsNullOrWhiteSpace(Query.Matrix))
            return;

        var matrix = RiskMatrixTemplates.FirstOrDefault(x => x.Name.Equals(Query.Matrix, StringComparison.OrdinalIgnoreCase));
        if (matrix == null) 
            return;

        RiskMatrixTemplate = matrix;
        GetOrientation();
    }

    public void ChangeMatrix()
    {
        SetUrl(Query.SetMatrix(RiskMatrixTemplate.Name));
        InitializeAssetTree();
    }

    private void SetUrl(string url)
    {
        _pageNavigationManager.SavePageQueryString(url.Split('?').FirstOrDefault(),
            url.Split('?').Skip(1).FirstOrDefault());
        _navigationManager.NavigateTo(url);
    }

    private void InitializeAssetTree()
    {
        Assets = _assetBreakdownManager.GetAllAssetsWithRisksFromSP(RiskMatrixTemplate.Id);
        RiskOnAbsTree.Initialize(_assetBreakdownManager.GetRiskOnAbsTree(Assets));

        if (ShowOnlyAssetsWithData)
            FilterTreeIfNoData(RiskOnAbsTree.Node);

        TreeNodeGeneric<RiskOnAbsTreeModel> firstNode = null;
        if (Query.AssetId != null)
            firstNode = RiskOnAbsTree.GetFlattenedNodes().FirstOrDefault(x => x.Source.Asset?.Id == Query.AssetId);
        firstNode ??= RiskOnAbsTree.Node.Nodes.FirstOrDefault();

        if (firstNode == null)
            return;

        RiskOnAbsTree.SelectNode(firstNode);
        SelectNode(firstNode);
    }

    private void FilterTreeIfNoData(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        if (node.Parent != null && HasUnderLyingRisk(node) == false)
            node.Hidden = ShowOnlyAssetsWithData;

        foreach (var child in node.Nodes)
            FilterTreeIfNoData(child);
    }

    private static bool HasUnderLyingRisk(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        return node.Source.IsRiskNode || node.Nodes.Any(HasUnderLyingRisk);
    }

    public void ChangeTab(int index)
    {
        // Matrix Logic, the last tab does not contain a Matrix
        if(index <= 2)
        {
            MatrixType = MatrixTypeList[index];
            SelectNode(RiskOnAbsTree.SelectedNode);
        }

        SetUrl(Query.SetTabId(index));
    }

    public void ClickNode(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        var assetId = node.Source.Asset?.Id ?? node.Parent.Source.Asset.Id;
        SetUrl(Query.SetAssetId(assetId));

        SelectNode(node);
    }

    private void SelectNode(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        SelectedNode = node.Source;
        MatrixSelectionAggregation = new MatrixSelectionAggregation();

        var risks = SelectedNode.IsRiskNode
            ? new List<(AssetModel, RiskModel)> {new(node.Parent.Source.Asset, SelectedNode.Risk)}
            : GetRisksFromAsset(node);

        foreach (var risk in risks)
            AppendRiskSelectionsToMatrix(risk.Item1, risk.Item2);

        LoadCriticalityDetails(node.Source.Asset?.Code);
    }

    private void LoadCriticalityDetails(string AssetCode)
    {
        Criticality = _criticalityManager.GetCriticality(AssetCode);
        CriticalityDetails = new();

        if (Criticality == null)
            return;

        foreach (var col in Columns)
        {
            var prop = CriticalityProperties.FirstOrDefault(x => x.Name == col.FieldName);
            if (prop == null)
                continue;

            var value = prop.GetValue(Criticality);
            if (value != null)
            {
                CriticalityDetails.Add(new KeyValuePair<string, string>(col.ColumnHeader, value.ToString()));
            }
        }
    }

    public void OpenRiskFromNode(RiskOnAbsTreeModel node)
    {
        if (node.IsRiskNode)
            _navigationManager.NavigateTo($"/value-risk-analysis/{node.Risk.RiskObjectId}/risks/{node.Risk.Id}");
        else
            _dialogService.Open<InformationDialog>("Not a Value Risk", new Dictionary<string, object>
            {
                {"DialogContent", "You can only open Value Risk Nodes from here, not Asset Nodes."}
            });
    }

    private static List<(AssetModel, RiskModel)> GetRisksFromAsset(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        var result = new List<(AssetModel, RiskModel)>();

        foreach (var child in node.Nodes)
            result.AddRange(GetRisksFromAsset(child));

        if (node.Source.IsRiskNode)
            result.Add(new ValueTuple<AssetModel, RiskModel>(node.Parent.Source.Asset, node.Source.Risk));

        return result;
    }

    private void AppendRiskSelectionsToMatrix(AssetModel asset, RiskModel risk)
    {
        var values = GetRiskMatrixValues(risk);
        var selectionX = GetSelectionX(risk);
        var selectionY = IsDescending ? values.Min() : values.Max();

        while (values.Count < selectionX)
            values.Add(null);

        values.Add(selectionY);

        for (var i = 0; i < values.Count; i++)
        {
            var column = MatrixSelectionAggregation.Columns.Skip(i).FirstOrDefault();

            if (column == null)
            {
                column = new MatrixSelectionAggregationColumn();
                MatrixSelectionAggregation.Columns.Add(column);
            }

            var selection = column.Cells.Find(x => x.Index == values[i]);

            if (selection == null)
            {
                selection = new MatrixSelectionAggregationCell {Index = values[i]};
                column.Cells.Add(selection);
            }

            if (!selection.Risks.Exists(x => x.Id == risk.Id))
                selection.Risks.Add(risk);

            if (asset != null)
                selection.Assets.Add(asset);

            selection.Count++;
        }
    }

    private int? GetSelectionX(RiskModel risk)
    {
        return MatrixType switch
        {
            MatrixTypes.Before => risk.FmecaSelections?.Before?.MtbfSelected,
            MatrixTypes.After => risk.FmecaSelections?.After?.MtbfSelected,
            MatrixTypes.Pmo => risk.FmecaSelections?.Pmo?.MtbfSelected,
            _ => throw new ArgumentException($"{MatrixType} is not supported")
        };
    }

    private List<int?> GetRiskMatrixValues(RiskModel risk)
    {
        return MatrixType switch
        {
            MatrixTypes.Before => new List<int?>(risk.FmecaSelections?.Before?.Selected ?? new List<int?>()),
            MatrixTypes.After => new List<int?>(risk.FmecaSelections?.After?.Selected ?? new List<int?>()),
            MatrixTypes.Pmo => new List<int?>(risk.FmecaSelections?.Pmo?.Selected ?? new List<int?>()),
            _ => throw new ArgumentException($"{MatrixType} is not supported")
        };
    }

    public void NavLinkAbs()
    {
        _navigationManager.NavigateTo("/value-risks-on-abs/assign-assets");
    }
}