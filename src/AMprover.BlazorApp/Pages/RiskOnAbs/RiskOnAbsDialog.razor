@inject DialogService DialogService;
@inject NavigationManager NavigationManager;
@inject IStringLocalizer<RiskOnAbsPage> _localizer

<div class="row">
    <div class="col-12">
        @Body
    </div>
</div>

<div class="row">

    <div class="col-6">
        <div class="rabs-info-scrollable-container">
            <label class="bold">@_localizer["RabsValueRisks"]:</label>
            @if (MatrixAggregationCell.Risks.Any())
            {
                <ol type="1">
                    @foreach (var risk in MatrixAggregationCell.Risks)
                    {
                        <li class="text-link-item" style="cursor: pointer;" @onclick=@(() => ClickRisk(risk))>
                            @risk.Name
                        </li>
                    }
                </ol>
            }
        </div>
    </div>

    <div class="col-6">
        <div class="rabs-info-scrollable-container">
            <label class="bold">Assets:</label>
            @if (MatrixAggregationCell.Assets.Any())
            {
                <ol type="1">
                    @foreach (var asset in MatrixAggregationCell.Assets)
                    {
                        <li>
                            @asset.Name
                        </li>
                    }
                </ol>
            }
        </div>
    </div>

</div>

@code
{
    [Parameter] public MarkupString Body { get; set; }

    [Parameter] public MatrixSelectionAggregationCell MatrixAggregationCell { get; set; }

    void ClickRisk(RiskModel risk)
    {
        NavigationManager.NavigateTo($"/value-risk-analysis/{risk.RiskObjectId}/risks/{risk.Id}");
        DialogService.Close();
    }
}
