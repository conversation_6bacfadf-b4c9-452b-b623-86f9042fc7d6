using Microsoft.AspNetCore.WebUtilities;
using System;
using System.Collections.Generic;

namespace AMprover.BlazorApp.Pages.RiskOnAbs
{
    public class RiskOnAbsQueryString
    {
        private Uri Uri { get; set; }

        public string Matrix { get; set; }

        public int? AssetId { get; set; }

        public int TabId { get; set; }

        public RiskOnAbsQueryString (Uri uri)
        {
            Uri = uri;
            ParseUrl(uri);
        }

        public void ParseUrl(Uri uri)
        {
            var queryStrings = QueryHelpers.ParseQuery(uri.Query);

            if (queryStrings.TryGetValue("matrix", out var matrix))
                Matrix = matrix;

            if (queryStrings.TryGetValue("assetId", out var assetIdStr) && int.TryParse(assetIdStr, out var assetId))
                AssetId = assetId;

            if (queryStrings.TryGetValue("tabId", out var tabIdStr) && int.TryParse(tabIdStr, out var tabId))
                TabId = tabId;
        }

        public string SetMatrix(string matrix)
        {
            Matrix = matrix;
            return GetUrl();
        }

        public string SetAssetId(int assetId)
        {
            AssetId = assetId;
            return GetUrl();
        }

        public string SetTabId(int tabId)
        {
            TabId = tabId;
            return GetUrl();
        }

        public string GetUrl()
        {
            var queryParams = new Dictionary<string, string>();

            if (!string.IsNullOrWhiteSpace(Matrix))
                queryParams.Add("matrix", Matrix);

            if (AssetId != null)
                queryParams.Add("assetId", AssetId.ToString());

            queryParams.Add("tabId", TabId.ToString());

            return QueryHelpers.AddQueryString(Uri.AbsolutePath.TrimEnd('/'), queryParams).ToLower();
        }
    }
}
