@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<RiskOnAbsPageViewModel>
@page "/value-risks-on-abs"
@inject IStringLocalizer<RiskOnAbsPage> _localizer
@inject IGlobalDataService GlobalDataService;

<h2>@_localizer["RabsTitle"]</h2>

<div class="row header-navigation">
    <div class="col-4">
        <Breadcrumbs Category="Value Risks on ABS" />
    </div>
    <div class="col-4 text-right">
    </div>
    <div class="col-4 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@_localizer["RabsMenuTitle"] DialogContent=@_localizer["RabsMenuTxt"] />
        <RadzenButton Disabled=!GlobalDataService.CanEdit Icon="launch" Text=@(_localizer["RabsLinkAbsBtn"]) class="float-right my-2" Click=@BindingContext.NavLinkAbs />
    </div>
</div>

<div class="row">
    <div class="col-3 mt-3">

        <div class="row">
            <div class="col-12">
                <div class="float-left mr-3">@_localizer["RabsFmecaMatrix"]:</div>
            </div>
        </div>

        <div class="row">
            <div class="col-10">
                <AMDropdown Data=@BindingContext.RiskMatrixTemplates.ToDictionary(x => x, x => x.Name)
                            @bind-Value=@BindingContext.RiskMatrixTemplate
                            Change=@BindingContext.ChangeMatrix />
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="float-left mr-3">@_localizer["RabsShowOnlyFilled"]:</div>
                <div class="float-left">
                    <AMproverCheckbox @bind-Value=@BindingContext.ShowOnlyAssetsWithData />
                </div>
            </div>
        </div>

        @if (BindingContext.Assets.Any())
        {
            <TreeComponent 
                TItem=RiskOnAbsTreeModel
                Treeview=BindingContext.RiskOnAbsTree
                CssClass="abs-tree"
                NodeClickCallback=@BindingContext.ClickNode
                OpenCallback=@BindingContext.OpenRiskFromNode
                ExpandNodeWhenSelected=false />
        }

    </div>

    <div class="col-9">

        <RadzenTabs Change=@BindingContext.ChangeTab SelectedIndex=@BindingContext.Query.TabId >
            <Tabs>
                @foreach(var matrix in BindingContext.MatrixTypeList)
                {
                    <RadzenTabsItem Text=@GetTabTitle(matrix) >

                        @if (BindingContext.RiskMatrixTemplate != null)
                        {
                            <RiskOnAbsMatrixComponent 
                                RiskMatrixTemplate=@BindingContext.RiskMatrixTemplate
                                MatrixSelectionAggregation=@BindingContext.MatrixSelectionAggregation />
                        }

                    </RadzenTabsItem>
                }

                <RadzenTabsItem Text=@_localizer["RabsCritTabHeader"]>

                    @if (BindingContext.SelectedNode.IsRiskNode)
                    {
                        @_localizer["RabsCritRiskSelected"]
                    }
                    else
                    {
                        @if(BindingContext.Criticality == null)
                        {
                            <p>
                                @_localizer["RabsCritNotAvailable"]
                            </p>
                        }
                        else
                        {
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th scope="col">@_localizer["RabsCritKey"]</th>
                                        <th scope="col">@_localizer["RabsCritValue"]</th>
                                    </tr>
                                </thead>
                                @foreach (var row in BindingContext.CriticalityDetails)
                                {
                                    <tr>
                                        <td>@row.Key</td>
                                        <td>@row.Value</td>
                                    </tr>
                                }
                            </table>
                        }
                    }
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>

    </div>
</div>

@code {
    private string GetTabTitle(MatrixTypes matrixType) => @_localizer[$"RabsMatrix{matrixType}"];
}
