@using AMprover.BusinessLogic.Models.PortfolioSetup;
@inject IStringLocalizer<RiskOnAbsPage> _localizer
@inject IGlobalDataService GlobalDataService;
@inject DialogService DialogService;

<table class="table table-bordered icon-hover" style="table-layout: fixed">
    <tbody>
        @for (var index = 0; index < RiskMatrixTemplate.MainGrid.TableContent.Count; index++)
        {
            var tempIndex = index;
            var row = RiskMatrixTemplate.MainGrid.TableContent[index];
            <tr style="height:50px">
                @for (var cellIndex = 0; cellIndex < row.Cells.Count; cellIndex++)
                {
                    var cellAggregation = GetCell(cellIndex, index);
                    var tempCellIndex = cellIndex;
                    var cell = row.Cells[cellIndex];
                    var column = RiskMatrixTemplate.MainGrid.TableColumns[cellIndex];
                    <td id="@cell.UniqueId(_tab)" style="@cell.BackGround();" class="rabs-cell-container">

                        <div class="rabs-cell-top">
                            @(index == 0 ? cell.GetHeader(_tab) : column.HasSubColumns ?
                                cell.GetConcatenatedSubGridValue(RiskMatrixTemplate, tempIndex, tempCellIndex, _tab, GlobalDataService.Language) :
                                cell.GetValue(RiskMatrixTemplate, _tab, tempCellIndex))
                        </div>


                        @if(index > 0)
                        {
                            <div class="rabs-cell-bottom">
                                <div style="cursor: pointer; display: inline-block;" @onclick=@(() => ShowRisks(cellAggregation))>
                                    @GetCellSelectedContent(cellAggregation)
                                </div>
                            </div>
                        }

                        @if (tempIndex == 0)
                        {
                            var chanceCount = GetChanceCount(cellIndex);
                            if (chanceCount != null)
                            {
                                <div>
                                    @GetChanceContent((int) chanceCount)
                                </div>
                            }
                        }

                        @if (tempIndex == 0 && column.HasSubColumns)
                        {
                            if (activatedSubgrids.Contains(tempCellIndex))
                            {
                                <a style="position:absolute;left:10px;bottom: 1px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => RemoveActiveSubgrid(tempCellIndex))">
                                    <i class="fa-solid fa-arrow-circle-left"></i>
                                </a>
                            }
                            else
                            {
                                <a style="position:absolute;left:10px;bottom: 1px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => AddActiveSubgrid(tempCellIndex))">
                                    <i class="fa-solid fa-arrow-circle-right"></i>
                                </a>
                            }

                            <a style="position:absolute;left:30%;top: -12px;cursor:pointer;z-index:100" class="icon">
                                <i class="fas fa-plus-circle"></i>
                            </a>
                        }
                    </td>

                    @if (column.HasSubColumns && activatedSubgrids.Contains(tempCellIndex))
                    {
                        var subgrid = RiskMatrixTemplate.SubGrids.FirstOrDefault(x => x.ColumnId == tempCellIndex);

                        if (subgrid == null) continue;

                        for (var subgridColumnIndex = 0; subgridColumnIndex < subgrid.TableColumns.Count; subgridColumnIndex++)
                        {
                            var tempSubgridColumIndex = subgridColumnIndex;
                            var subGridCell = subgrid.TableContent[tempIndex].Cells[tempSubgridColumIndex];

                            if (tempIndex == 0)
                            {
                                <td class="risk-matrix-subgrid-cell" id="@subGridCell.UniqueId(_tab, true)" style="@subGridCell.BackGround();position: relative; width: 50px;">
                                    @subGridCell.GetHeader(_tab)
                                </td>
                            }
                            else
                            {
                                <td class="risk-matrix-subgrid-cell" id="@subGridCell.UniqueId(_tab, true)" style="@subGridCell.BackGround();position: relative; width: 50px;">
                                    @subGridCell.GetValue(RiskMatrixTemplate, _tab, tempCellIndex)
                                </td>
                            }
                        }
                    }
                }
            </tr>
        }
    </tbody>
</table>

@code {
    [Parameter] public RiskMatrixTemplateModel RiskMatrixTemplate { get; set; }

    [Parameter] public MatrixSelectionAggregation MatrixSelectionAggregation { get; set; }

    private string _tab = "Description";

    private List<int> activatedSubgrids { get; set; } = new();

    private void AddActiveSubgrid(int column)
    {
        if (!activatedSubgrids.Contains(column))
            activatedSubgrids.Add(column);
    }

    private void RemoveActiveSubgrid(int column)
    {
        if (activatedSubgrids.Contains(column))
            activatedSubgrids.Remove(column);
    }

    private MarkupString GetCellSelectedContent(MatrixSelectionAggregationCell cell)
    {
        if (cell == null)
            return (MarkupString)"";

        var size = Math.Min(25 + cell.Count, 60);
        var fontsize = cell.Count <= 999
        ? size / 2
        : size / 2.5;

        var styles = new List<string>
        {
            $"width:{size}px",
            $"height:{size}px",
            $"font-size:{fontsize}px",
        };

        return (MarkupString)$"<div class=\"rabs-count-circle\" style=\"{string.Join(';', styles)}\">{cell.Count}</div>";
    }

    private MarkupString GetChanceContent(int cellCount)
    {
        if (cellCount == 0)
            return (MarkupString)string.Empty;

        var size = Math.Min(25 + cellCount, 60);
        var padding = size * .1;
        var fontsize = cellCount <= 999
        ? size / 2
        : size / 2.5;

        var styles = new List<string>
        {
            $"width:{size}px",
            $"height:{size}px",
            $"font-size:{fontsize}px",
            $"padding:{padding}px 0px",
        };

        return (MarkupString)$"<div class=\"rabs-chance-count\" style=\"{string.Join(';', styles)}\">{cellCount}</div>";
    }

    private void ShowRisks(MatrixSelectionAggregationCell cell)
    {
        DialogService.Open<RiskOnAbsDialog>(
            _localizer["RabsAggregationDialogTitle"],
            new Dictionary<string, object> 
            {
                {nameof(RiskOnAbsDialog.Body), (MarkupString)_localizer["RabsAggregationDialogBody"].Value },
                {nameof(RiskOnAbsDialog.MatrixAggregationCell), cell }
            }, new DialogOptions
            {
                Draggable = true
            });
    }

    MatrixSelectionAggregationCell GetCell(int column, int row)
    {
        var col = MatrixSelectionAggregation?.Columns.Skip(column).FirstOrDefault();
        var cell = col?.Cells.FirstOrDefault(x => x.Index == row);
        return cell;
    }

    private int? GetChanceCount(int column)
    {
        var gridColumn = RiskMatrixTemplate.MainGrid.TableColumns[column];
        if (gridColumn.IsEffectColumn)
            return null;
        
        var summaryColumn = MatrixSelectionAggregation?.Columns.Skip(column).FirstOrDefault();
        var count = summaryColumn?.Cells.Where(x => x.Index != null).Sum(x => x.Count);
        return count > 0 ? count : null;
    }
}