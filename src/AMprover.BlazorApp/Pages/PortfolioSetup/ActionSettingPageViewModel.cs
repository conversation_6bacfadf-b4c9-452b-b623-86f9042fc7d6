using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using PolicyModel = AMprover.BusinessLogic.Models.RiskAnalysis.PolicyModel;
using AMprover.BusinessLogic.Models.Sapa;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public class ActionSettingPageViewModel : BaseViewModel
{
    private readonly ILookupManager _lookupManager;
    private readonly IIntervalUnitManager _intervalUnitManager;
    private readonly IAttachmentManager _attachmentManager;
    private readonly IDropdownManager _dropdownManager;

    public ActionSettingPageViewModel(
        ILoggerFactory loggerFactory, 
        ILookupManager lookupManager, 
        IIntervalUnitManager intervalUnitManager,
        IAttachmentManager attachmentManager,
        IDropdownManager dropdownManager,
        DialogService dialogService) : base(loggerFactory, lookupManager)
    {
        _lookupManager = lookupManager;
        _intervalUnitManager = intervalUnitManager;
        _attachmentManager = attachmentManager;
        _dropdownManager = dropdownManager;
        _dialogService = dialogService;
    }

    public List<LookupSettingModel> LookupSettings { get; set; } 

    public List<InitiatorModel> Initiators { get; set; }
    public UtilityGrid<InitiatorModel> InitiatorCrudGrid { get; set; }
    
    public List<AdditionalDataModel> AdditionalData { get; set; }
    public UtilityGrid<AdditionalDataModel> AdditionalDataGrid { get; set; }

    public List<ExecutorModel> Executors { get; set; }
    public UtilityGrid<ExecutorModel> ExecutorCrudGrid { get; set; }

    public List<WorkPackageModel> WorkPackages { get; set; }
    public UtilityGrid<WorkPackageModel> WorkPackageCrudGrid { get; set; }

    public List<SapaWorkpackageModel> SapaWorkpackages { get; set; }
    public UtilityGrid<SapaWorkpackageModel> SapaWorkpackageCrudGrid { get; set; }

    public List<IntervalUnitModel> IntervalUnits { get; set; }
    public UtilityGrid<IntervalUnitModel> IntervalCrudGrid { get; set; }

    public List<PolicyModel> Policies { get; set; }
    public UtilityGrid<PolicyModel> PolicyCrudGrid { get; set; }

    public List<AttachmentCategoryModel> AttachmentCategories { get; set; }
    public UtilityGrid<AttachmentCategoryModel> AttachmentCategoryCrudGrid { get; set; }

    public List<AttachmentModel> Attachments { get; set; }
    public UtilityGrid<AttachmentModel> AttachmentCrudGrid { get; set; }
    public Dictionary<string, Dictionary<int, string>> AttachmentDropdownOverrides { get; set; }

    public override void OnInitialized()
    {
        LookupSettings = _lookupManager.GetLookupSettings();
        Initiators = _lookupManager.GetInitiators();
        AdditionalData = _lookupManager.GetAdditionalData();
        Executors = _lookupManager.GetExecutors();
        IntervalUnits = _intervalUnitManager.GetIntervalUnits();
        WorkPackages = _lookupManager.GetWorkPackages();
        SapaWorkpackages = _lookupManager.GetSapaWorkPackages();
        Policies = _lookupManager.GetPolicies();
        AttachmentCategories = _attachmentManager.GetAttachmentCategories();
        Attachments = _attachmentManager.GetAttachments();
        AttachmentDropdownOverrides = _dropdownManager.GetAttachmentDropdownOverrides();

        base.OnInitialized();
    }

    public void UpdateSettings(LookupSettingModel setting)
    {
        _lookupManager.SaveLookupSettings(setting);
    }

    // Initiator
    public void SaveInitiator(InitiatorModel model)
    {
        _lookupManager.SaveInitiator(model, out var result);

        if (model.Id != null)
        {
            if (result.Success)
            {
                Initiators[Initiators.IndexOf(model)] = result.Item;
                InitiatorCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                InitiatorCrudGrid.BindingContext.EditItem(model);
            }
        }
        else
        {
            Initiators.Add(result.Item);
            InitiatorCrudGrid.Reload();
        }
    }

    public void DeleteInitiator(InitiatorModel model)
    {
        if (model.Id == null) return;
        if (_lookupManager.DeleteInitiator(model.Id.Value, out var result))
        {
            Initiators.Remove(model);
            InitiatorCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            InitiatorCrudGrid.BindingContext.EditItem(model);
        }
    }

    #region Policy

    public void SavePolicy(PolicyModel model)
    {
        _lookupManager.SavePolicy(model, out var result);

        if (model.Id != 0)
        {
            if (result.Success)
            {
                Policies[Policies.IndexOf(model)] = result.Item;
                PolicyCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                PolicyCrudGrid.BindingContext.EditItem(model);
            }
        }
        else
        {
            Policies.Add(result.Item);
            PolicyCrudGrid.Reload();
        }
    }

    public void DeletePolicy(PolicyModel model)
    {
        if (model.Id == 0) return;
        if (_lookupManager.DeletePolicy(model.Id, out var result))
        {
            Policies.Remove(model);
            PolicyCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            PolicyCrudGrid.BindingContext.EditItem(model);
        }
    }

    #endregion
    
    #region Additional Data
    
    public void SaveAdditionalData(AdditionalDataModel model)
    {
        _lookupManager.SaveAdditionalData(model, out var result);

        if (model.Id != null)
        {
            if (result.Success)
            {
                AdditionalData[AdditionalData.IndexOf(model)] = result.Item;
                AdditionalDataGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                AdditionalDataGrid.BindingContext.EditItem(model);
            }
        }
        else
        {
            AdditionalData.Add(result.Item);
            AdditionalDataGrid.Reload();
        }
    }
    
    public void DeleteAdditionalData(AdditionalDataModel model)
    {
        if (model.Id == null) return;
        if (_lookupManager.DeleteAdditionalData(model.Id.Value, out var result))
        {
            AdditionalData.Remove(model);
            AdditionalDataGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            AdditionalDataGrid.BindingContext.EditItem(model);
        }
    }
    
    #endregion

    // Executor
    public void SaveExecutor(ExecutorModel model)
    {
        _lookupManager.SaveExecutor(model, out var result);

        if (model.Id != null && Executors.IndexOf(model) >= 0)
        {
            if (result.Success)
            {
                Executors[Executors.IndexOf(model)] = result.Item;
                ExecutorCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                ExecutorCrudGrid.BindingContext.EditItem(model);
            }
        }
        else
        {
            Executors.Add(result.Item);
            ExecutorCrudGrid.Reload();
        }
    }

    public void DeleteExecutor(ExecutorModel model)
    {
        if (model.Id == null) return;
        if (_lookupManager.DeleteExecutor(model.Id.Value, out var result))
        {
            Executors.Remove(model);
            ExecutorCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            ExecutorCrudGrid.BindingContext.EditItem(model);
        }
    }

    // WorkPackage
    public void SaveWorkPackage(WorkPackageModel model)
    {
        _lookupManager.SaveWorkPackage(model, out var result);

        if (model.Id != null)
        {
            if (result.Success)
            {
                WorkPackages[WorkPackages.IndexOf(model)] = result.Item;
                WorkPackageCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                WorkPackageCrudGrid.BindingContext.EditItem(model);
            }
        }
        else
        {
            WorkPackages.Add(result.Item);
            WorkPackageCrudGrid.Reload();
        }
    }

    public void DeleteWorkPackage(WorkPackageModel model)
    {
        if (model.Id == null) return;
        if (_lookupManager.DeleteWorkPackage(model.Id.Value, out var result))
        {
            WorkPackages.Remove(model);
            WorkPackageCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            WorkPackageCrudGrid.BindingContext.EditItem(model);
        }
    }

    // Sapa WorkPackage
    public void SaveSapaWorkPackage(SapaWorkpackageModel model)
    {
        _lookupManager.SaveSapaWorkPackage(model, out var _);
        SapaWorkpackages = _lookupManager.GetSapaWorkPackages();
    }

    public void DeleteSapaWorkPackage(SapaWorkpackageModel model)
    {
        if (model.Id == 0) return;
        if (_lookupManager.DeleteSapaWorkPackage(model.Id, out var result))
        {
            SapaWorkpackages = _lookupManager.GetSapaWorkPackages();
        }
        else
        {
            ShowDbErrorMessage(result);
        }
    }

    // Interval Unit
    public void SaveIntervalUnit(IntervalUnitModel model)
    {
        model.UnitsPerYear ??= 0;
        _intervalUnitManager.SaveIntervalUnit(model, out var result);

        if (model.Id != null)
        {
            if (result.Success)
            {
                IntervalUnits[IntervalUnits.IndexOf(model)] = result.Item;
                IntervalCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                IntervalCrudGrid.BindingContext.EditItem(model);
            }
        }
        else
        {
            IntervalUnits.Add(result.Item);
            IntervalCrudGrid.Reload();
        }
    }

    public void DeleteIntervalUnit(IntervalUnitModel model)
    {
        if (model.Id == null) return;
        if (_intervalUnitManager.DeleteIntervalUnit(model.Id.Value, out var result))
        {
            IntervalUnits.Remove(model);
            IntervalCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            IntervalCrudGrid.BindingContext.EditItem(model);
        }
    }

    // Attachment Categories
    public void SaveAttachmentCategory(AttachmentCategoryModel model)
    {
        _attachmentManager.SaveAttachmentCategory(model);
        AttachmentCategories = _attachmentManager.GetAttachmentCategories();
        AttachmentDropdownOverrides = _dropdownManager.GetAttachmentDropdownOverrides();
    }

    public async Task DeleteAttachmentCategory(AttachmentCategoryModel model)
    {
        if (model.Id == 0) return;
        if (_attachmentManager.DeleteAttachmentCategory(model.Id, out var result))
        {
            AttachmentCategories = _attachmentManager.GetAttachmentCategories();
            AttachmentDropdownOverrides = _dropdownManager.GetAttachmentDropdownOverrides();
        }
        else
        {
            _dialogService.Close();
            await Task.Delay(1);
            ShowErrorMessage(result);
        }
    }

    // Attachments
    public void SaveAttachment(AttachmentModel model)
    {
        var index = Attachments.IndexOf(model);
        model = _attachmentManager.SaveAttachment(model);

        if (index == -1)
        {
            Attachments.Add(model);
        }
        else
        {
            Attachments[index] = model;
        }

        AttachmentCrudGrid.Reload();
    }

    public void DeleteAttachment(AttachmentModel model)
    {
        if (model.Id == 0) return;
        if (_attachmentManager.DeleteAttachment(model.Id, out var result))
        {
            AttachmentCategories = _attachmentManager.GetAttachmentCategories();
            Attachments.Remove(model);
            AttachmentCrudGrid.Reload();
        }
        else
        {
            ShowErrorMessage(result);
        }
    }

    // Dropdown overrides
    public Dictionary<string, Dictionary<int, string>> GetWorkPackageDropDowns()
    {
        var result = new Dictionary<string, Dictionary<int, string>>
        {
            { nameof(WorkPackageModel.IntervalUnit), IntervalUnits.ToDictionary(x => x.Id.Value, y => y.Name) },
            { nameof(WorkPackageModel.Executor), Executors.ToDictionary(x => x.Id.Value, y => y.Name) }
        };
        return result;
    }
}