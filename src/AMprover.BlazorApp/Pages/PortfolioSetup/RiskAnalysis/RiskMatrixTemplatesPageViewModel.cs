using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using System.Collections.Generic;
using System.Linq;
using Radzen;
using Microsoft.Extensions.Logging;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BlazorApp.Components;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.RiskAnalysis;

public class RiskMatrixTemplatesPageViewModel : BaseViewModel
{
    private readonly IRiskAnalysisSetupManager _riskAnalysisSetupManager;
    private readonly NavigationManager _navigationManager;
    private IStringLocalizer<RiskMatrixTemplatePage> _localizer { get; }

    public RiskMatrixTemplatesPageViewModel(
        ILoggerFactory loggerFactory,
        IRiskAnalysisSetupManager riskMatrixManager,
        DialogService dialogService,
        NavigationManager navigationManager,
        IStringLocalizer<RiskMatrixTemplatePage> localizer) : base(loggerFactory)
    {
        _riskAnalysisSetupManager = riskMatrixManager;
        _navigationManager = navigationManager;
        _dialogService = dialogService;
        _localizer = localizer;
    }

    public List<RiskMatrixTemplateModel> RiskMatrixTemplates { get; set; }

    public UtilityGrid<RiskMatrixTemplateModel> RiskMatrixGrid { get; set; }

    public override void OnInitialized()
    {
        RiskMatrixTemplates = _riskAnalysisSetupManager.GetAllTemplates();
    }

    public void AddNewRiskMatrix()
    {
        var defaultId = RiskMatrixTemplates.FirstOrDefault(x => x.IsDefault)?.Id;

        if (defaultId == null)
        {
            _dialogService.Open<InformationDialog>(_localizer["RskMtrxNoDefaultTitle"].Value,
                new Dictionary<string, object>
                {
                    {"DialogContent", _localizer["RskMtrxNoDefaultTxt"].Value }
                });
        }
        else
        {
            var newTemplate = _riskAnalysisSetupManager.CopyTemplate(defaultId.Value);
            _navigationManager.NavigateTo($"/value-risk-matrix/{newTemplate}", true);
        }
    }

    public void SaveRiskMatrix(RiskMatrixTemplateModel model)
    {
        var index = RiskMatrixTemplates.IndexOf(model);
        var result = _riskAnalysisSetupManager.UpdateTemplateModel(model);

        if (result.Id > 0)
            RiskMatrixTemplates[index] = result;
        else
            RiskMatrixTemplates.Add(result);

        RiskMatrixTemplates = new List<RiskMatrixTemplateModel>(RiskMatrixTemplates);
    }

    public void EditRiskMatrixInPopup(RiskMatrixTemplateModel model)
    {
        _navigationManager.NavigateTo($"/value-risk-matrix/{model.Id}");
    }

    public void DeleteRiskMatrix(RiskMatrixTemplateModel model)
    {
        if (RiskMatrixTemplates.Contains(model))
            RiskMatrixTemplates.Remove(model);

        if (model.Id > 0)
            _riskAnalysisSetupManager.DeleteTemplate(model.Id);

        RiskMatrixTemplates = new List<RiskMatrixTemplateModel>(RiskMatrixTemplates);
    }

    public void PasteRiskMatrix(RiskMatrixTemplateModel model)
    {
        var copy = model.CopyAsNew<RiskMatrixTemplateModel>();
        RiskMatrixTemplates.Add(_riskAnalysisSetupManager.UpdateTemplateModel(copy));
        RiskMatrixTemplates = new List<RiskMatrixTemplateModel>(RiskMatrixTemplates);
    }
}