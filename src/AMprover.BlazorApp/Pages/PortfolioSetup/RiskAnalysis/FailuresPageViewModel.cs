using System;
using AMprover.BusinessLogic;
using System.Collections.Generic;
using System.Linq;
using Radzen;
using Microsoft.Extensions.Logging;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models.Failures;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.RiskAnalysis;

public class FailuresPageViewModel : BaseViewModel
{
    private readonly IFailureManager _failureManager;
    private readonly IStringLocalizer<FailuresPage> _localizer;

    public FailuresPageViewModel(
        ILoggerFactory loggerFactory, 
        IFailureManager failureManager,
        IStringLocalizer<FailuresPage> localizer,
        DialogService dialogService) : base(loggerFactory, dialogService)
    {
        _failureManager = failureManager;
        _localizer = localizer;
    }

    public override void OnInitialized()
    {
        FailureModes = _failureManager.GetAllFailureModes();
        LoadFailureCategories();
        base.OnInitialized();
    }

    private void LoadFailureCategories()
    {
        var allFailureCategories = _failureManager.GetAllFailureCategories();
        FirstCategories = allFailureCategories.Primary;
        SecondCategories = allFailureCategories.Secondary;
    }

    // Modes
    public UtilityGrid<FailureModeModel> FailureModesGrid { get; set; }
    public List<FailureModeModel> FailureModes { get; set; }

    // Category 1
    public UtilityGrid<FailureCategory> FirstCategoriesGrid { get; set; }
    public List<FailureCategory> FirstCategories { get; set; }

    // Category 2
    public UtilityGrid<FailureCategory> SecondCategoriesGrid { get; set; }
    public List<FailureCategory> SecondCategories { get; set; }

    // FailureMode
    public void SaveFailureMode(FailureModeModel model)
    {
        var savedFailureMode = _failureManager.SaveFailureMode(model);

        if (model.Id != null)
        {
            FailureModes[FailureModes.IndexOf(model)] = savedFailureMode;
            FailureModes = new List<FailureModeModel>(FailureModes);
        }
        else
        {
            FailureModes = new List<FailureModeModel>(FailureModes.Append(savedFailureMode));
        }

        FailureModesGrid.BindingContext.Grid.CancelEditRow(model);
    }

    public bool ValidateCanSave(FailureModeModel model)
    {
        return !FailureModes.Any(x => x.Name.Equals(model.Name, StringComparison.OrdinalIgnoreCase) && x.Id != model.Id);
    }

    public void ValidateCanSaveFailed(FailureModeModel model)
    {
        ShowErrorMessage(_localizer["FaNoDuplicateNamesTxt"]);
    }

    public async Task DeleteFailureMode(FailureModeModel model)
    {
        if (model?.Id.HasValue != true) return;

        if (_failureManager.FailureModeHasLinkedRisks(model.Id.Value))
        {
            _dialogService.Close();

            // The Are You Sure? dialog is still open, we cannot open another dialog until it is closed
            // await Task.Delay() to fix async issue
            await Task.Delay(1);

            _dialogService.Open<InformationDialog>(_localizer["FaCannotBeDeletedTitle"],
                new Dictionary<string, object>
                {
                    { "DialogContent", _localizer["FaCannotBeDeletedTxt"].Value}
                });
        }
        else
        {
            ConfirmedDeleteFailureMode(model);
        }
    }

    private void ConfirmedDeleteFailureMode(FailureModeModel model)
    {
        _failureManager.DeleteFailureMode(model);
        FailureModes.Remove(model);
        FailureModes = new List<FailureModeModel>(FailureModes);
    }

    // FailureCategories
    public void SaveFailureCategoryOne(FailureCategory model)
    {
        if (model.Id != null)
            _failureManager.SaveFailureCategory(model, 1);
        else
        {
            FirstCategories.Add(_failureManager.SaveFailureCategory(model, 1));
            FirstCategories = new List<FailureCategory>(FirstCategories);
            FirstCategoriesGrid.BindingContext.Grid.CancelEditRow(model);
        }
    }

    public void SaveFailureCategoryTwo(FailureCategory model)
    {
        if (model.Id != null)
            _failureManager.SaveFailureCategory(model, 2);
        else
        {
            SecondCategories.Add(_failureManager.SaveFailureCategory(model, 2));
            SecondCategories = new List<FailureCategory>(SecondCategories);
            SecondCategoriesGrid.BindingContext.Grid.CancelEditRow(model);
        }
    }

    public void DeleteFailureCategory(FailureCategory model)
    {
        if (FirstCategories.Contains(model))
        {
            FirstCategories.Remove(model);
            FirstCategories = new List<FailureCategory>(FirstCategories);
        }

        if (SecondCategories.Contains(model))
        {
            SecondCategories.Remove(model);
            SecondCategories = new List<FailureCategory>(SecondCategories);
        }

        _failureManager.DeleteFailureCategory(model);
    }
}