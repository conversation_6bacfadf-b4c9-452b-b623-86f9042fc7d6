using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.RiskAnalysis;

public class RiskMatrixTemplatePageViewModel : BaseViewModel
{
    private readonly IRiskAnalysisSetupManager _riskAnalysisSetupManager;
    private readonly NavigationManager _navigationManager;

    public RiskMatrixTemplatePageViewModel(
        ILoggerFactory loggerFactory,
        IRiskAnalysisSetupManager riskMatrixManager,
        DialogService dialogService,
        IGlobalDataService globalDataService,
        ILookupManager lookupManager,
        NavigationManager navigationManager) : base(loggerFactory, lookupManager)
    {
        _riskAnalysisSetupManager = riskMatrixManager;
        _navigationManager = navigationManager;
        _dialogService = dialogService;

        CanEdit = globalDataService.CanEdit;
    }

    #region Matrix properties

    public List<string> Tabs { get; } = new()
    {
        GridType.Description.ToString(),
        GridType.ImpactValue.ToString(),
        GridType.CustomValue.ToString(),
        GridType.Points.ToString()
    };


    public bool CanEdit { get; set; }

    public bool ShowError { get; set; }

    public string ErrorText { get; set; }

    [Parameter] public int MatrixId { get; set; }

    public RiskMatrixTemplateModel RiskMatrixTemplate { get; set; }

    public List<int> ActivatedSubGrids { get; set; } = new();

    public RadzenTabs TemplateTabs { get; set; }

    #endregion

    public override void OnInitialized()
    {
        if (MatrixId == 0)
        {
            RiskMatrixTemplate = new RiskMatrixTemplateModel
            {
                MainGrid = new RiskMatrixTemplateGrid
                {
                    TableColumns = Enumerable.Range(0, 5).Select(_ => new RiskMatrixTemplateColumn()).ToList(),
                    TableContent = Enumerable.Range(0, 5).Select(_ =>
                        new RiskMatrixTemplateRow(new List<RiskMatrixTemplateCell>(Enumerable.Range(0, 5)
                            .Select(_ => new RiskMatrixTemplateCell())))).ToList()
                }
            };

            var id = _riskAnalysisSetupManager.UpdateTemplate(RiskMatrixTemplate);
            _navigationManager.NavigateTo($"/value-risk-matrix/{id}", true);
        }
        else
        {
            RiskMatrixTemplate = _riskAnalysisSetupManager.GetTemplate(MatrixId);
            TemplateTabs?.Reload();
        }

        base.OnInitialized();
    }

    public void ValidFormSubmitted()
    {
        if (!CanEdit) return;
        _riskAnalysisSetupManager.UpdateTemplate(RiskMatrixTemplate);
        _navigationManager.NavigateTo("/value-risk-matrix");
    }

    public void AddRow(int currentRow)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var newRow = new RiskMatrixTemplateRow(grid.TableColumns
            .Select(_ => new RiskMatrixTemplateCell()).ToList());

        grid.TableContent.Insert(currentRow + 1, newRow);

        // Add corresponding rows to sub grids
        foreach (var subGrid in RiskMatrixTemplate.SubGrids)
        {
            // Ensure subgrid has enough rows
            if (subGrid.TableContent.Count >= grid.TableContent.Count) 
                continue;
            
            var newSubRow = new RiskMatrixTemplateRow(subGrid.TableColumns
                .Select(_ => new RiskMatrixTemplateCell()).ToList());
            subGrid.TableContent.Insert(currentRow + 1, newSubRow);
        }

        SaveTemplate();
    }

    public void RemoveRow(int currentRow)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        grid.TableContent.RemoveAt(currentRow);

        // Remove corresponding rows from sub grids
        foreach (var subGrid in RiskMatrixTemplate.SubGrids.Where(subGrid => subGrid.TableContent.Count > currentRow))
        {
            subGrid.TableContent.RemoveAt(currentRow);
        }

        SaveTemplate();
    }

    public void AddColumn(int currentColumn)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var newColumn = new RiskMatrixTemplateColumn();
        currentColumn += 1;
        grid.TableColumns.Insert(currentColumn, newColumn);

        grid.TableContent.FirstOrDefault()?.Cells
            .Insert(currentColumn, new RiskMatrixTemplateCell {Description = "<placeholder>"});

        foreach (var tableRow in grid.TableContent.Skip(1))
        {
            tableRow.Cells.Insert(currentColumn, new RiskMatrixTemplateCell());
        }

        SaveTemplate();
    }

    public void RemoveColumn(int currentColumn)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var column = grid.TableColumns[currentColumn];
        var hasSubColumns = column.HasSubColumns;
        grid.TableColumns.Remove(column);

        if (hasSubColumns)
        {
            var subGrid = RiskMatrixTemplate.SubGrids.FirstOrDefault(x => x.ColumnId == currentColumn);

            if (subGrid != null)
                if (subGrid.TableContent.Count >= grid.TableContent.Count)
                {
                    foreach (var tableRow in subGrid.TableContent.Where(tableRow => currentColumn >= 0 && currentColumn < tableRow.Cells.Count))
                    {
                        tableRow.Cells.RemoveAt(currentColumn);
                    }
                }
        }

        foreach (var tableRow in grid.TableContent.Where(tableRow => currentColumn >= 0 && currentColumn < tableRow.Cells.Count))
        {
            tableRow.Cells.RemoveAt(currentColumn);
        }

        SaveTemplate();
    }

    public void RemoveSubColumn(int currentColumn, int subColumnToDelete)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var column = grid.TableColumns[currentColumn];
        var hasSubColumns = column.HasSubColumns;

        if (hasSubColumns)
        {
            var subGrid = RiskMatrixTemplate.SubGrids.FirstOrDefault(x => x.ColumnId == currentColumn);

            if (subGrid?.TableColumns.Count <= 1)
            {
                RiskMatrixTemplate.SubGrids.Remove(subGrid);
            }
            else if (subGrid != null)
            {
                //remove column
                subGrid.TableColumns.RemoveAt(subColumnToDelete);

                //remove columns
                if (subGrid.TableContent.Count >= grid.TableContent.Count)
                {
                    foreach (var tableRow in subGrid.TableContent)
                    {
                        tableRow.Cells.RemoveAt(subColumnToDelete);
                    }
                }
            }

            SaveTemplate();
        }
    }

    public void AddSubColumn(int currentColumn, int order = 0)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var columnCell = RiskMatrixTemplate.MainGrid.TableContent[0].Cells[currentColumn];
        var column = grid.TableColumns[currentColumn];
        column.HasSubColumns = true;

        var subGridsForColumn = RiskMatrixTemplate.SubGrids.Where(x => x.ColumnId == currentColumn).ToList();

        //Add column in existing subgrid
        if (subGridsForColumn.Any())
        {
            //If multiple sub grids available for same column remove all but 1
            if (subGridsForColumn.Count > 1)
            {
                RiskMatrixTemplate.SubGrids.Where(x => x.ColumnId == currentColumn).ToList()
                    .RemoveRange(1, subGridsForColumn.Count);
            }

            //Add column
            var subgrid = subGridsForColumn.First();

            subgrid.TableColumns.Add(new RiskMatrixTemplateColumn());

            for (var index = 0; index < subgrid.TableContent.Count; index++)
            {
                var row = subgrid.TableContent[index];
                row.Cells.Add(new RiskMatrixTemplateCell
                {
                    RowId = index,
                    ColumnId = order,
                    Description = index == 0 ? $"{columnCell.Description} {subgrid.TableColumns.Count}" : "",
                    ShortDescription = index == 0 ? $"{columnCell.Description} {subgrid.TableColumns.Count}" : ""
                });
            }
        }
        //Add new subgrid
        else
        {
            RiskMatrixTemplate.SubGrids.Add(new RiskMatrixTemplateGrid
            {
                ColumnId = RiskMatrixTemplate.MainGrid.TableColumns.Select(x => x.Id).ToList().IndexOf(column.Id),
                TableColumns = new List<RiskMatrixTemplateColumn>
                {
                    new()
                },
                TableContent = Enumerable.Range(0, RiskMatrixTemplate.MainGrid.TableContent.Count).Select(x =>
                        new RiskMatrixTemplateRow(new List<RiskMatrixTemplateCell>
                        {
                            new()
                            {
                                Description = x == 0 ? $"{columnCell.Description} 1" : string.Empty,
                                ShortDescription = x == 0 ? $"{columnCell.Description} 1" : string.Empty,
                                RowId = x,
                                ColumnId = 0
                            }
                        }))
                    .ToList()
            });
        }

        SaveTemplate();
    }

    public void AddActiveSubgrid(int column)
    {
        if (!ActivatedSubGrids.Contains(column))
            ActivatedSubGrids.Add(column);
    }

    public void RemoveActiveSubgrid(int column)
    {
        if (ActivatedSubGrids.Contains(column))
            ActivatedSubGrids.Remove(column);
    }

    private void SaveTemplate()
    {
        if (!CanEdit) return;

        try
        {
            _riskAnalysisSetupManager.UpdateTemplate(RiskMatrixTemplate);
            RiskMatrixTemplate = _riskAnalysisSetupManager.GetTemplate(MatrixId);
        }
        catch (Exception)
        {
            //TODO write error to logging
        }
    }

    public void CopyTemplate()
    {
        if (!CanEdit) return;
        var newTemplate = _riskAnalysisSetupManager.CopyTemplate(MatrixId);
        _navigationManager.NavigateTo($"/value-risk-matrix/{newTemplate}", true);
    }
}
