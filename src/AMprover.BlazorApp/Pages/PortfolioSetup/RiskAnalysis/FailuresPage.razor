@page "/failures"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<FailuresPageViewModel>
@inject IStringLocalizer<FailuresPage> _localizer

<h2>@_localizer["FaHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Failures" />
    </div>
</div>

<RadzenTabs>
    <Tabs>
        <RadzenTabsItem Text=@_localizer["FaFailureModesTxt"]>
            <p>
                @((MarkupString)_localizer["FaSubHeaderTxt"].Value)
            </p>
            <div class="row p-2 mb-3">
                <div class="col-12">
                    <UtilityGrid TItem=BusinessLogic.Models.Failures.FailureModeModel
                                  @ref=@BindingContext.FailureModesGrid
                                  Data=@BindingContext.FailureModes
                                  FileName=@GridNames.Portfolio.FailureModes
                                  Interactive=true
                                  AddNewButton=true
                                  AllowFiltering=true
                                  SaveCallback=@BindingContext.SaveFailureMode
                                  DeleteCallback=@BindingContext.DeleteFailureMode
                                  ExternalSaveValidation=@BindingContext.ValidateCanSave 
                                  ExternalSaveValidationFailed=@BindingContext.ValidateCanSaveFailed />
                </div>
            </div>
        </RadzenTabsItem>
        <RadzenTabsItem Text=@_localizer["FaFailureCategoriesTxt"]>
            <p>
                 @((MarkupString)_localizer["FaSubHeaderFCTxt"].Value)
            </p>
            <div class="row mb-3">
                <div class="col-6">
                    <h4>@_localizer["FaCategory1Txt"]</h4>
                    <UtilityGrid TItem=BusinessLogic.Models.Failures.FailureCategory
                              @ref=@BindingContext.FirstCategoriesGrid
                              Data=@BindingContext.FirstCategories
                              FileName=@GridNames.Portfolio.FailureCategory1
                              Interactive=true
                              AddNewButton=true
                              SaveCallback=@BindingContext.SaveFailureCategoryOne
                              DeleteCallback=@BindingContext.DeleteFailureCategory
                              AllowChangeColumns=false />
                </div>
                <div class="col-6">
                    <h4>@_localizer["FaCategory2Txt"]</h4>
                    <UtilityGrid TItem=BusinessLogic.Models.Failures.FailureCategory
                              @ref=@BindingContext.SecondCategoriesGrid
                              Data=@BindingContext.SecondCategories
                              FileName=@GridNames.Portfolio.FailureCategory2
                              Interactive=true
                              AddNewButton=true
                              SaveCallback=@BindingContext.SaveFailureCategoryTwo
                              DeleteCallback=@BindingContext.DeleteFailureCategory
                              AllowChangeColumns=false />
                </div>
            </div>
        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>
