using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.RiskAnalysis;

public class RiskScenariosViewModel : BaseViewModel
{
    private readonly IScenarioManager _scenarioManager;
    private readonly IDropdownManager _dropdownManager;
    private readonly IStringLocalizer<RiskScenarios> _localizer;

    public RiskScenariosViewModel(
        ILoggerFactory loggerFactory,
        DialogService dialogService,
        IScenarioManager scenarioManager,
        IStringLocalizer<RiskScenarios> localizer,
        IDropdownManager dropdownManager) : base(loggerFactory)
    {
        _dialogService = dialogService;
        _scenarioManager = scenarioManager;
        _dropdownManager = dropdownManager;
        _localizer = localizer;
    }

    public List<ScenarioModel> Scenarios { get; set; }

    public UtilityGrid<ScenarioModel> ScenarioCrudGrid { get; set; }

    public Dictionary<int, string> Statusses { get; set; }

    public string ErrorText { get; set; } = string.Empty;
    
    public string WarningText { get; set; } = string.Empty;

    public bool ShowError { get; set; }
    
    public bool ShowWarning { get; set; }

    public override void OnInitialized()
    {
        Scenarios = _scenarioManager.GetAllScenarios();
        Statusses = _dropdownManager.GetStatusDict();

        base.OnInitialized();
    }

    public Dictionary<string, Dictionary<int, string>> GetDropdownOverrides()
    {
        return new Dictionary<string, Dictionary<int, string>> {{nameof(ScenarioModel.Status), Statusses}};
    }

    public void CloneScenario(ScenarioModel scenario)
    {
        var copy = scenario.CopyAsNew<ScenarioModel>();
        copy.Name += " (clone)";
        Scenarios.Add(_scenarioManager.SaveScenario(copy));
        Scenarios = new List<ScenarioModel>(Scenarios);
    }

    public void SaveScenario(ScenarioModel scenario)
    {
        // Check for duplicate short key but still allow save
        var isDuplicate = Scenarios.Any(s => s.ShortKey.Trim() == scenario.ShortKey && s.Id != scenario.Id);
        
        if (isDuplicate)
        {
            // Show warning but continue with save
            ShowWarning = true;
            WarningText = _localizer["ScWarningTxt", scenario.ShortKey];
        }
        else
        {
            // Clear any previous warnings if saving without duplicates
            ShowWarning = false;
        }

        if (scenario.Id > 0)
        {
            var result = _scenarioManager.SaveScenario(scenario);
            Scenarios[Scenarios.IndexOf(scenario)] = result;
            Scenarios = new List<ScenarioModel>(Scenarios);
        }
        else
        {
            Scenarios = new List<ScenarioModel>(Scenarios.Append(_scenarioManager.SaveScenario(scenario)));
        }
    }

    public void DeleteRiskScenario(ScenarioModel riskScenario)
    {
        if (riskScenario.Id <= 0) return;

        if (!_scenarioManager.DeleteScenario(riskScenario)) 
            return;
        
        Scenarios.Remove(riskScenario);
        Scenarios = new List<ScenarioModel>(Scenarios);
    }
}