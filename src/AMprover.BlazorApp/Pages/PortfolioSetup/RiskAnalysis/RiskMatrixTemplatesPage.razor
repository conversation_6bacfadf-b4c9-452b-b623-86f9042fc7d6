@page "/value-risk-matrix"
@using AMprover.BusinessLogic.Models.PortfolioSetup
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<RiskMatrixTemplatesPageViewModel>
@inject IStringLocalizer<RiskMatrixTemplatePage> _localizer

<h2>@_localizer["RmtsHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Value Risk Matrix" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@_localizer["RmtsHeaderTxt"] DialogContent=@_localizer["RmtsMenuTxt"] />
    </div>
</div>

<p>@((MarkupString)_localizer["RmtsSubHeaderTxt"].Value)</p>

<UtilityGrid TItem=RiskMatrixTemplateModel
            @ref=@BindingContext.RiskMatrixGrid
            Data=@BindingContext.RiskMatrixTemplates
            FileName=@GridNames.Portfolio.RiskMatrixTemplates
            Interactive=true
            UseOpenTextInsteadOfEdit=true
            EditCallBackOverride=@(args => BindingContext.EditRiskMatrixInPopup((RiskMatrixTemplateModel) args))
            AddNewOverride=@BindingContext.AddNewRiskMatrix
            DeleteCallback=@BindingContext.DeleteRiskMatrix
            PasteCallBack=@(args => BindingContext.PasteRiskMatrix(args.Item1)) />
