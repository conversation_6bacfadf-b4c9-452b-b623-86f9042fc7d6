using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using AMprover.Data.Services;
using AutoMapper;
using DeepCopy;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using System;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public class UserViewModel : BaseViewModel
{
    private readonly NavigationManager _navigationManager;
    private readonly IMapper _mapper;
    private readonly IRiskAnalysisSetupManager _riskAnalysisSetupManager;
    private readonly IScopedUserService _scopedUserService;

    public UserViewModel(
        ILoggerFactory loggerFactory,
        IRiskAnalysisSetupManager riskAnalysisSetupManager,
        IMapper mapper,
        NavigationManager navigationManager,
        IScopedUserService scopedUserService) : base(loggerFactory)
    {
        _navigationManager = navigationManager;
        _mapper = mapper;
        _riskAnalysisSetupManager = riskAnalysisSetupManager;
        _scopedUserService = scopedUserService;
    }

    [Parameter] public string UserId { get; set; }
    public List<DepartmentModel> Departments { get; private set; }
    public UserAccountModel UserAccount { get; set; }

    /// <summary>
    /// clone of the object we're editing to be able to 'reset to initial values'
    /// </summary>
    private UserAccount BeforeEditingUserAccount { get; set; }

    private string BeforeEditingUserAccountRole { get; set; }

    public bool ShowValidations { get; set; }

    /// Options to display in the roles dropdown when editing a user
    public Dictionary<string, string> AvailableRoles { get; set; } =
        AMproverIdentityConstants.Roles.GetPortfolioRoles();

    public IEnumerable<int> SelectedDepartmentIds { get; set; } = new List<int>();

    public string CurrentRole { get; set; }

    public string ValidationError { get; set; }

    public override async Task OnInitializedAsync()
    {
        await LoadEditor();
    }

    private void NavigateToOverview()
    {
        _navigationManager.NavigateTo("/users");
    }

    private async Task LoadEditor()
    {
        await DetermineUserAndCurrentRole();
        BeforeEditingUserAccount = DeepCopier.Copy(UserAccount);
        BeforeEditingUserAccountRole = CurrentRole;
    }

    private async Task DetermineUserAndCurrentRole()
    {
        var userAccount = await _scopedUserService.FindByIdAsync(UserId);
        UserAccount = _mapper.Map<UserAccountModel>(userAccount);
        Departments = _riskAnalysisSetupManager.GetAllDepartments();
        UserAccount.Departments = _riskAnalysisSetupManager.GetUserDepartments(UserId);

        SelectedDepartmentIds = UserAccount.Departments.Select(x => x.Id);

        var roles = await _scopedUserService.GetRolesAsync(userAccount);
        CurrentRole = roles.FirstOrDefault(x => AvailableRoles.ContainsKey(x));
    }

    public async Task UpdateUserAccountValidFormSubmitted(EditContext editContext)
    {
        if (string.IsNullOrWhiteSpace(CurrentRole))
        {
            ShowValidations = true;
            return;
        }

        ShowValidations = false;
        await HandleDepartmentAssignmentUpdate();
        await HandleRoleAssignmentUpdate();
        NavigateToOverview();
    }

    public void UpdateUserAccountInvalidFormSubmitted(EditContext editContext)
    {
        ValidationError =
            "User is NOT updated - invalid form submitted. Reload the page (browser refresh) and try again.";
    }

    private async Task HandleDepartmentAssignmentUpdate()
    {
        // Get current department assignments for the user
        var currentDepartments = _riskAnalysisSetupManager.GetUserDepartments(UserId);
        var currentDepartmentIds = currentDepartments.Select(d => d.Id).ToHashSet();

        // Determine departments to add and remove
        var departmentsToAdd = SelectedDepartmentIds.Except(currentDepartmentIds).ToList();
        var departmentsToRemove = currentDepartmentIds.Except(SelectedDepartmentIds).ToList();

        // Remove departments that are no longer selected
        foreach (var departmentId in departmentsToRemove)
        {
            _riskAnalysisSetupManager.DeleteUserDepartments(UserId, departmentId);
        }

        // Add newly selected departments
        foreach (var departmentId in departmentsToAdd)
        {
            _riskAnalysisSetupManager.AddUserDepartments(UserId, departmentId);
        }
    }

    private async Task HandleRoleAssignmentUpdate()
    {
        var user = await _scopedUserService.FindByIdAsync(UserId);

        if (user == null)
        {
            return;
        }

        // Get user's current roles
        var userRoles = await _scopedUserService.GetRolesAsync(user);

        // Determine which roles to add and which to remove
        var rolesToAdd = new List<string>();
        var rolesToRemove = new List<string>();

        // Add the selected role if user doesn't have it
        if (CurrentRole != null && !userRoles.Contains(CurrentRole))
        {
            rolesToAdd.Add(CurrentRole);
        }

        // Remove all other roles except the selected one
        rolesToRemove.AddRange(userRoles.Where(role => role != CurrentRole));

        // Apply the changes
        if (rolesToAdd.Any())
        {
            await _scopedUserService.AddToRolesAsync(user, rolesToAdd);
        }

        if (rolesToRemove.Any())
        {
            await _scopedUserService.RemoveFromRolesAsync(user, rolesToRemove);
        }
    }
}