using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Ra<PERSON>zen;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Constants;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.Settings;

public class SettingsViewModel : BaseViewModel
{
    private readonly ILookupManager _lookupManager;
    private readonly NavigationManager _navigationManager;
    private readonly IGlobalDataService _globalDataService;

    public SettingsViewModel(
        ILoggerFactory loggerFactory, 
        ILookupManager lookupManager, 
        DialogService dialogService,
        NavigationManager navigationManager,
        IGlobalDataService globalDataService,
        IJSRuntime jsRuntime, 
        IConfiguration configuration) : base(loggerFactory, configuration, lookupManager, jsRuntime, navigationManager)
    {
        _lookupManager = lookupManager;
        _dialogService = dialogService;
        _navigationManager = navigationManager;
        _globalDataService = globalDataService;
    }

    public List<LookupSettingModel> LookupSettings { get; set; }
    public LookupSettingModel LanguageSetting { get; set; }
    public Dictionary<string, string> Languages => GetLanguages();
    public LookupSettingModel CurrencySetting { get; set; }
    public Dictionary<string, string> Currencies => GetCurrencies();
    public Dictionary<string, LookupSettingModel> CalculationSettings { get; set; } = new();
    
    public LookupSettingModel RamsAllowedDeptSetting { get; set; }
    public LookupSettingModel RamsTotalAllowedBlocksSetting { get; set; }
    public LookupSettingModel EnablePercCalc { get; set; }
    public LookupSettingModel EnableColumnsForTypes { get; set; }
    public LookupSettingModel ShowSubColumns { get; set; }
    public LookupSettingModel ShowRegenerateImages { get; set; }

    public override void OnInitialized()
    {
        LookupSettings = _lookupManager.GetLookupSettings();

        InitLanguageSettings();
        InitCalculationSettings();
        InitConfigurationSettings();
        base.OnInitialized();
    }

    private void InitLanguageSettings()
    {
        LanguageSetting = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.Language, StringComparison.OrdinalIgnoreCase));
        CurrencySetting = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.Currency, StringComparison.OrdinalIgnoreCase));
    }

    private void InitConfigurationSettings()
    {
        RamsAllowedDeptSetting = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.RamsAllowedDept, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.RamsAllowedDept };
        RamsTotalAllowedBlocksSetting = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.RamsTotalAllowedBlocks, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.RamsTotalAllowedBlocks };
        EnableColumnsForTypes = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.EnableColumnsForTypes, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.EnableColumnsForTypes };
        ShowSubColumns = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.ShowSubColumns, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.ShowSubColumns };
        ShowRegenerateImages = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.ShowRegenerateImages, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.ShowRegenerateImages };
    }

    private void InitCalculationSettings()
    {
        CalculationSettings.Clear();
        AddLookupSettingByKeyIfExists("Discount rate", "DiscountRate");
        AddLookupSettingByKeyIfExists("Depreciation percent", "DepreciationPct");
        AddLookupSettingByKeyIfExists("Inflation percent", "InflationPct");
        AddLookupSettingByKeyIfExists("Modification percent", "ModificationPct");
        AddLookupSettingByKeyIfExists("Spareman percent", "SpareManPct");
        AddLookupSettingByKeyIfExists("Priority code inspection", "PriorityCodeInspection");
        AddLookupSettingByKeyIfExists("Weibull factor", "WeibullFactor");
    }

    private void AddLookupSettingByKeyIfExists(string displayName, string key)
    {
        var setting = LookupSettings.FirstOrDefault(x => x.Property.Equals(key, StringComparison.OrdinalIgnoreCase));

        if (setting != null)
            CalculationSettings.Add(displayName, setting);
    }

    public async Task OnCheckboxChange(bool? isChecked, LookupSettingModel model)
    {
        model.IntValue = isChecked == true ? 1 : 0;
        await UpdateSettings(model);
    }

    public async Task UpdateSettings(LookupSettingModel setting)
    {
        _lookupManager.SaveLookupSettings(setting);

        if(setting.Property.Equals(PropertyNames.Language, StringComparison.OrdinalIgnoreCase))
        {
            await AddOrUpdateCookie(_navigationManager.Uri);
            _globalDataService.Language = setting.TextValue;
        }

        if (setting.Property.Equals(PropertyNames.Currency, StringComparison.OrdinalIgnoreCase))
        {
            _globalDataService.Currency = setting.TextValue;
        }
    }
}