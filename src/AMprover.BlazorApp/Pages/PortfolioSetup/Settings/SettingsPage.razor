@page "/settings"
@using AMprover.Data.Constants
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<SettingsViewModel>
@inject IStringLocalizer<SettingsPage> _localizer

<h2>@_localizer["SeHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Settings"/>
    </div>
</div>

<p>@((MarkupString) _localizer["SeSubHeaderTxt"].Value)</p>

<RadzenTabs>
    <Tabs>
        <!-- Localization -->
        <RadzenTabsItem Text=@_localizer["SeLocalizationTabTxt"]>
            <div class="row">
                <div class="col-md-4 col-6">
                    <AMDropdown
                        @bind-Value=@BindingContext.LanguageSetting.TextValue
                        Data=@BindingContext.Languages
                        Change=@(args => BindingContext.UpdateSettings(BindingContext.LanguageSetting))
                        Label=@_localizer["SeLanguageTxt"]/>

                    <AMDropdown
                        @bind-Value=@BindingContext.CurrencySetting.TextValue
                        Data=@BindingContext.Currencies
                        Change=@(args => BindingContext.UpdateSettings(BindingContext.CurrencySetting))
                        Label=@_localizer["SeCurrencyTxt"]/>
                </div>
            </div>
        </RadzenTabsItem>

        <!-- Calculation -->
        <RadzenTabsItem Text=@_localizer["SeCalculationTabTxt"]>
            @foreach (var setting in BindingContext.CalculationSettings)
            {
                <div class="row">
                    <div class="col-md-4 col-6">
                        @switch (setting.Value.SettingType)
                        {
                            case SettingType.Decimal:
                                <AMproverNumberInput @bind-Value=@setting.Value.DecimalValue
                                                     Change=@(args => BindingContext.UpdateSettings(setting.Value))
                                                     TValue="decimal?"
                                                     Format="P2"
                                                     Min="0"
                                                     Label=@setting.Key/>
                                break;
                            case SettingType.Int:
                                <AMproverNumberInput @bind-Value=@setting.Value.IntValue
                                                     Change=@(() => BindingContext.UpdateSettings(setting.Value))
                                                     TValue="int?"
                                                     Format="N2"
                                                     Min="0"
                                                     Label=@setting.Key/>
                                break;
                        }
                    </div>
                </div>
            }
        </RadzenTabsItem>

        <!-- SETTINGS -->
        <AuthorizeView Context="_" Roles="@RoleConstants.Administrators">
            <Authorized>
                <RadzenTabsItem Text=@_localizer["SeConfigurationTabTxt"]>
                    <AMproverNumberInput @bind-Value=@BindingContext.RamsAllowedDeptSetting.IntValue Label=@_localizer["SeRamsAllowedDeptTxt"] Placeholder=@_localizer["SeRamsAllowedDeptTxt"]
                                         TValue="int?"
                                         Change=@(() => BindingContext.UpdateSettings(BindingContext.RamsAllowedDeptSetting))/>

                    <AMproverNumberInput @bind-Value=@BindingContext.RamsTotalAllowedBlocksSetting.IntValue Label=@_localizer["SeRamsTotalAllowedBlocksTxt"] Placeholder=@_localizer["SeRamsTotalAllowedBlocksTxt"]
                                         TValue="int?"
                                         Change=@(() => BindingContext.UpdateSettings(BindingContext.RamsTotalAllowedBlocksSetting))/>

                    
                    <Radzen.Blazor.RadzenCheckBox 
                        Value="@(BindingContext.EnableColumnsForTypes.IntValue == 1)"
                        TValue="bool?"
                        Change="@(args => BindingContext.OnCheckboxChange(args, BindingContext.EnableColumnsForTypes))" />
                    
                    &nbsp @_localizer["SeEnableColumnForTypeTxt"]
                    
                    <br /><br />

                    <Radzen.Blazor.RadzenCheckBox 
                        Value="@(BindingContext.ShowSubColumns.IntValue == 1)"
                        TValue="bool?"
                        Change="@(args => BindingContext.OnCheckboxChange(args, BindingContext.ShowSubColumns))" />
                    
                    &nbsp @_localizer["SeRiskMatrixShowSubColumnsTxt"]

                    <br /><br />

                    <Radzen.Blazor.RadzenCheckBox 
                        Value="@(BindingContext.ShowRegenerateImages.IntValue == 1)"
                        TValue="bool?"
                        Change="@(args => BindingContext.OnCheckboxChange(args, BindingContext.ShowRegenerateImages))" />

                    &nbsp @_localizer["SeShowRegenerateImagesTxt"]
                           
                </RadzenTabsItem>
            </Authorized>
        </AuthorizeView>
    </Tabs>
</RadzenTabs>
<br/>