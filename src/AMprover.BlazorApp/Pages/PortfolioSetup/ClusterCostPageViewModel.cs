using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BusinessLogic.Enums;
using System.Collections.Generic;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public class ClusterCostPageViewModel : BaseViewModel
{
    private readonly IPortfolioSetupManager _portfolioSetupManager;

    public ClusterCostPageViewModel(
        ILoggerFactory loggerFactory,
        IPortfolioSetupManager portfolioSetupManager,
        DialogService dialogService) : base(loggerFactory)
    {
        _portfolioSetupManager = portfolioSetupManager;
        _dialogService = dialogService;
    }

    public Dictionary<CommonCostType, string> CostType { get; private set; } = new();


    public override void OnInitialized()
    {
        CommonCosts = _portfolioSetupManager.GetCommonCosts();
        CostType = _portfolioSetupManager.GetCostLevelTypes();

        base.OnInitialized();
    }

    public UtilityGrid<CommonCostModel> CommonCostsGrid { get; set; }
    public List<CommonCostModel> CommonCosts { get; set; }

    // Common Costs
    public void DeleteCommonCost(CommonCostModel commonCost)
    {
        if (CommonCosts.Contains(commonCost))
        {
            CommonCosts.Remove(commonCost);
            CommonCosts = new List<CommonCostModel>(CommonCosts);
        }

        _portfolioSetupManager.DeleteCommonCost(commonCost);
    }

    public string GetClusterCostType(CommonCostType clustercosttype)
    {
        if (CostType.TryGetValue(clustercosttype, out var type))
            return type;

        _logger.LogError($"{nameof(GetClusterCostType)} was called but {nameof(CostType)} has not been filled yet");
        return clustercosttype.ToString();
    }

    public async Task UpdateCommonCost(CommonCostModel commonCost)
    {
        commonCost = _portfolioSetupManager.UpdateCommonCost(commonCost);
        await AddOrUpdateCommonCostsGrid(commonCost);
    }

    public async Task AddOrUpdateCommonCostsGrid(CommonCostModel model)
    {
        var rowIndex = CommonCosts.FindIndex(x => x.Id == model.Id);
        if (rowIndex >= 0)
        {
            CommonCosts[rowIndex] = model;
        }
        else
        {
            CommonCosts.Add(model);
        }

        await CommonCostsGrid.BindingContext.Grid.Reload();
    }

    public void PasteCommonCostCallback(CommonCostModel model)
    {
        var copiedCost = model.CopyAsNew<CommonCostModel>();
        CommonCosts.Add(_portfolioSetupManager.UpdateCommonCost(copiedCost));
        CommonCosts = new List<CommonCostModel>(CommonCosts);
    }
}