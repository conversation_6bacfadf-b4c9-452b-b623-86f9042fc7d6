using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Collections.Generic;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Radzen.Blazor;
using AMprover.BusinessLogic.Models.Import;
using AMprover.BusinessLogic.Models;
using AMprover.BlazorApp.Pages.RiskOrganize.Import;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public class CommonActionsPageViewModel : BaseViewModel
{
    private readonly IPortfolioSetupManager _portfolioSetupManager;
    private readonly ILookupManager _lookupManager;
    private readonly IDropdownManager _dropdownManager;
    private readonly IStringLocalizer<CommonActionEdit> _localizer;

    public CommonActionsPageViewModel(
        ILoggerFactory loggerFactory,
        IPortfolioSetupManager portfolioSetupManager,
        DialogService dialogService,
        IDropdownManager dropdownManager,
        ILookupManager lookupManager,
        ICommonActionImportManager commonActionImportManager,
        IStringLocalizer<CommonActionEdit> localizer) : base(loggerFactory)
    {
        _portfolioSetupManager = portfolioSetupManager;
        _dialogService = dialogService;
        _dropdownManager = dropdownManager;
        _lookupManager = lookupManager;
        _localizer = localizer;
    }

    public override void OnInitialized()
    {
        ExecutorDict = _dropdownManager.GetExecutorDict();
        PolicyDict = _dropdownManager.GetPolicyDict();
        IntervalDict = _dropdownManager.GetIntervalUnitDict();
        InitiatorDict = _dropdownManager.GetInitiatorDict();
        WorkPackageDict = _dropdownManager.GetWorkpackageDict();
        CommonTasksDict = _dropdownManager.GetCommonTasksDict();
        UnitTypeDict = _dropdownManager.GetLookupUserDefinedByFilterDict("UnitTypes");
        TaskTypeDict = _lookupManager.GetLookupByFilterDict("MeasureType", true);

        CommonActions = _portfolioSetupManager.GetCommonActions();
        CommonActionsImport = _portfolioSetupManager.GetCommonActionImportModels();

        base.OnInitialized();
    }

    public RadzenTabs Tabs { get; set; }
    private Dictionary<int, string> ExecutorDict { get; set; }
    private Dictionary<int, string> PolicyDict { get; set; }
    private Dictionary<int, string> IntervalDict { get; set; }
    private Dictionary<int, string> InitiatorDict { get; set; }
    private Dictionary<int, string> WorkPackageDict { get; set; }
    private Dictionary<int, string> CommonTasksDict { get; set; }
    public Dictionary<int, string> UnitTypeDict { get; set; }
    public Dictionary<string, string> TaskTypeDict { get; set; } = new Dictionary<string, string>();

    public UtilityGrid<CommonTaskModel> CommonActionsGrid { get; set; }
    public List<CommonTaskModel> CommonActions { get; set; }
    public List<CommonActionImportModel> CommonActionsImport { get; set; }

    public RadzenFileInput<string> UploadRef { get; set; }
    public AnalyzeCommonActionImportResult ImportAnalyzeResult { get; set; } = new();
    public ImportResult ImportResult { get; set; }
    public string FileUpload { get; set; }

    public void OpenCommonActionPopUp(int id)
    {
        _dialogService.Open<CommonActionEdit>(_localizer["CaEditPopupHeader"],
            new Dictionary<string, object> {
                { "CommonTaskId", id },
                { "Callback", EventCallback.Factory.Create<CommonTaskModel>(this, AddOrUpdateCommonActionGrid)}
            },
            new DialogOptions { Width = "740px", /*Height = "630px",*/ Resizable = false, Draggable = true });
    }

    // Common Actions
    public async Task DeleteCommonAction(CommonTaskModel commonTask)
    {
        var taskIdsUsingCommonAction = _portfolioSetupManager.GetTaskIdsUsingCommonAction(commonTask.Id);

        if(taskIdsUsingCommonAction.Count > 0)
        {
            _dialogService.Close();

            // Required to Replace exiting Dialog with new one
            await Task.Delay(1);

            // Extra confirm since common action is in use
            _dialogService.Open<AreYouSureDialog<CommonTaskModel>>(_localizer["CaCommonActionInUse"],
                new Dictionary<string, object> 
                {
                    { 
                        nameof(AreYouSureDialog<CommonTaskModel>.Item), 
                        commonTask
                    },
                    { 
                        nameof(AreYouSureDialog<CommonTaskModel>.YesCallback), 
                        EventCallback.Factory.Create<CommonTaskModel>(this, ConfirmDeleteCommonAction) 
                    },
                    { 
                        nameof(AreYouSureDialog<CommonTaskModel>.Text), 
                        string.Format(_localizer["CaCommonActionInUseDetails"], taskIdsUsingCommonAction.Count)
                    }
                },
            new DialogOptions { Width = "600px", Resizable = false, Draggable = true });

            return;
        }

        ConfirmDeleteCommonAction(commonTask);
    }

    public void ConfirmDeleteCommonAction(CommonTaskModel commonTask)
    {
        if (CommonActions.Contains(commonTask))
        {
            CommonActions.Remove(commonTask);
            CommonActions = new List<CommonTaskModel>(CommonActions);
        }

        _portfolioSetupManager.DeleteCommonAction(commonTask);
        CommonActionsImport = _portfolioSetupManager.GetCommonActionImportModels();
        Tabs?.Reload();
    }

    public async Task UpdateCommonAction(CommonTaskModel commonTask)
    {
        commonTask = _portfolioSetupManager.UpdateCommonAction(commonTask);
        CommonActionsImport = _portfolioSetupManager.GetCommonActionImportModels();
        await AddOrUpdateCommonActionGrid(commonTask);
    }

    public async Task AddOrUpdateCommonActionGrid(CommonTaskModel model)
    {
        var rowIndex = CommonActions.FindIndex(x => x.Id == model.Id);
        if (rowIndex >= 0)
        {
            CommonActions[rowIndex] = model;
        }
        else
        {
            CommonActions.Add(model);
        }

        await CommonActionsGrid.BindingContext.Grid.Reload();
    }

    public void PasteCommonTaskCallback(CommonTaskModel model)
    {
        var copiedTask = model.CopyAsNew<CommonTaskModel>();
        CommonActions.Add(_portfolioSetupManager.UpdateCommonAction(copiedTask));
        CommonActions = new List<CommonTaskModel>(CommonActions);
        CommonActionsImport = _portfolioSetupManager.GetCommonActionImportModels();
    }

    // Import
    public void OpenImportWidget()
    {
        _dialogService.Open<ImportCommonActionWidget>
                    ("Import Common Actions",
                    new Dictionary<string, object>
                    {
                        { 
                            nameof(ImportCommonActionWidget.RefreshCommonActions), 
                            EventCallback.Factory.Create(this, RefreshItems) 
                        }
                    },
                    new DialogOptions() { Draggable = true });
    }

    public void RefreshItems()
    {
        CommonActions = _portfolioSetupManager.GetCommonActions();
        CommonActionsImport = _portfolioSetupManager.GetCommonActionImportModels();
        Tabs?.Reload();
    }

    // Dropdown overrides
    public Dictionary<string, Dictionary<int, string>> GetCommonActionDropdownOverrides()
    {
        var result = new Dictionary<string, Dictionary<int, string>>
        {
            { nameof(CommonTaskModel.IntervalUnitId), IntervalDict},
            { nameof(CommonTaskModel.ExecutorId), ExecutorDict },
            { nameof(CommonTaskModel.InitiatorId), InitiatorDict },
            { nameof(CommonTaskModel.WorkPackageId), WorkPackageDict },
            { nameof(CommonTaskModel.MxPolicyId), PolicyDict },
            { nameof(CommonTaskModel.UnitType), UnitTypeDict },
        };
        return result;
    }

    public Dictionary<string, Dictionary<string, string>> GetCommonActionOptionOverrides()
    {
        var result = new Dictionary<string, Dictionary<string, string>>
        {
            { nameof(CommonTaskModel.Type), TaskTypeDict },
        };
        return result;
    }
}