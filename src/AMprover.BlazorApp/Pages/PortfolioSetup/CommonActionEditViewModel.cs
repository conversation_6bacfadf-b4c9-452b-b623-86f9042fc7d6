using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public class CommonActionEditViewModel : BaseViewModel, IEntityEditorViewModel
{
    private readonly IPortfolioSetupManager _portfolioSetupManager;
    private readonly ILookupManager _lookupManager;
    private readonly IDropdownManager _dropdownManager;

    public CommonActionEditViewModel(
        ILoggerFactory loggerFactory,
        ILookupManager lookupManager,
        IPortfolioSetupManager portfolioSetupManager,
        DialogService dialogService,
        IDropdownManager dropdownManager) : base(loggerFactory, lookupManager)
    {
        _portfolioSetupManager = portfolioSetupManager;
        _dialogService = dialogService;
        _lookupManager = lookupManager;
        _dropdownManager = dropdownManager;
    }

    // Dropdowns
    public Dictionary<int, string> ExecutorsDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> InitiatorsDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> WorkPackageDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> IntervalUnitsDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> PolicyDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<int, string> UnitTypeDict { get; set; } = new Dictionary<int, string>();
    public Dictionary<string, string> TaskDict { get; set; } = new Dictionary<string, string>();

    [Parameter] public int CommonTaskId { get; set; }

    [Parameter] public EventCallback<CommonTaskModel> Callback { get; set; }

    public CommonTaskModel CommonTask { get; set; }

    public string ErrorText { get; set; }

    public bool ShowError { get; set; }

    public EntityEditorMode EditorMode
    {
        get
        {
            return CommonTaskId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    public override void OnInitialized()
    {
        ExecutorsDict = _dropdownManager.GetExecutorDict();
        InitiatorsDict = _dropdownManager.GetInitiatorDict();
        WorkPackageDict = _dropdownManager.GetWorkpackageDict();
        IntervalUnitsDict = _dropdownManager.GetIntervalUnitDict();
        PolicyDict = _dropdownManager.GetPolicyDict();
        UnitTypeDict = _dropdownManager.GetLookupUserDefinedByFilterDict("UnitTypes");

        TaskDict = _lookupManager.GetLookupByFilterDict("MeasureType", true);//TODO: change back when custom dropdown or other solution is implemented. For now quick fix

        switch (EditorMode)
        {
            case EntityEditorMode.Create:
                CommonTask = new CommonTaskModel { Id = CommonTaskId };
                break;
            default:
                CommonTask = _portfolioSetupManager.GetCommonAction(CommonTaskId);
                break;
        }
    }

    public async Task ValidTaskSubmitted(CommonTaskModel commonAction)
    {
        await CreateOrUpdateObjectAndNavigate(commonAction).ConfigureAwait(false);
    }

    private async Task CreateOrUpdateObjectAndNavigate(CommonTaskModel commonAction)
    {
        var updatedCommonAction = _portfolioSetupManager.UpdateCommonAction(commonAction);
        await Callback.InvokeAsync(updatedCommonAction).ConfigureAwait(false);
        _dialogService.Close();
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ErrorText = "Update NOT executed, invalid form submitted";
        _logger.LogWarning($"Invalid {EditorMode} form submitted for {nameof(CommonTaskModel)} with Id {CommonTaskId}.");
        _logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }
}