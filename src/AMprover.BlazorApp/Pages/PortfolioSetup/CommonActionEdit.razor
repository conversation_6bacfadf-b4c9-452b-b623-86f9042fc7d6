@page "/commonactions/{CommonTaskId:int}"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<CommonActionEditViewModel>
@inject IStringLocalizer<CommonActionEdit> _localizer
@inject IGlobalDataService GlobalDataService;

@if (BindingContext.CommonTask != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem=CommonTaskModel Data=@BindingContext.CommonTask
                                      Submit=@BindingContext.ValidTaskSubmitted OnInvalidSubmit=@BindingContext.InvalidTaskSubmitted>
        <div class="row">
            <div class="col-sm-2">
                <div class="form-group neg-margin">
                    <label>Id:</label>
                    <label class="form-control">@BindingContext.CommonTask.Id</label>
                </div>
            </div>
            <div class="col-sm-6">
                <AMproverTextBox @bind-Value="BindingContext.CommonTask.Name" Label=@_localizer["CaeNameLbl"] Required=true MaxLength="60" />
            </div>
            <div class="col-sm-4">
                <AMproverTextBox @bind-Value="BindingContext.CommonTask.ReferenceId" Label=@_localizer["CaeReferenceIdLbl"] MaxLength="30" />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverDictionaryDropdown AllowFiltering="true" AllowClear="true" Required=true
                                Data=@BindingContext.PolicyDict.ToNullableDictionary()
                                @bind-Value=@BindingContext.CommonTask.MxPolicyId 
                                Label=@_localizer["CaePolicyLbl"] />
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown AllowClear=true AllowFiltering=true Required=true
                                            Data=@BindingContext.TaskDict
                                            @bind-Value=@BindingContext.CommonTask.Type
                                            Label=@_localizer["CaeActionTypeLbl"] />
            </div>
            <div class="col-sm-2">
                <AMproverNumberInput Label="Priority" Format="0" TValue="int?" @bind-Value="BindingContext.CommonTask.PriorityCode" />
            </div>
            <div class="col-sm-2">
                <AMproverTextBox @bind-Value="BindingContext.CommonTask.FilterRef" Label=@_localizer["CaeFilterRefLbl"] MaxLength="20" />
            </div>
        </div>

        <RadzenTabs>
            <Tabs>
                <RadzenTabsItem Text=@_localizer["CaeDescriptionLbl"]>
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-5">
                                <AMproverTextArea @bind-Value=@BindingContext.CommonTask.Description Cols="30" Rows="3" Label=@_localizer["CaeDescriptionLbl"] />
                                <AMproverTextArea @bind-Value=@BindingContext.CommonTask.Remark Cols="30" Rows="3" Label=@_localizer["CaeRemarksLbl"] />
                            </div>
                            <div class="col-sm-7">
                                <label>@_localizer["CaeModifiableLbl"]:</label>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <Radzen.Blazor.RadzenCheckBox class="form-control mt-5 ml-3" @bind-Value="@BindingContext.CommonTask.CostModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>
                                    <div class="col-sm-4">
                                        <AMproverNumberInput Label=@_localizer["CaeCostLbl"] Format="c0" TValue="decimal?" @bind-Value="BindingContext.CommonTask.Costs" class="form-control" />
                                    </div>
                                    <div class="col-sm-5">
                                        <AMproverDictionaryDropdown Label="UnitType" AllowClear="true" AllowFiltering="true" class="form-control"
                                                        Data="@BindingContext.UnitTypeDict" @bind-Value="@BindingContext.CommonTask.UnitType" Required=true Min=1 />
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <AMproverCheckbox class="form-control mt-5 ml-3" @bind-Value="@BindingContext.CommonTask.InitiatorModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>
                                    <div class="col-sm-9">
                                        <AMproverDictionaryDropdown Label=@_localizer["CaeInitiatorLbl"] AllowClear="true" AllowFiltering="true" class="form-control"
                                                                    Data=@BindingContext.InitiatorsDict.ToNullableDictionary()
                                                                    @bind-Value=@BindingContext.CommonTask.InitiatorId Required=true />
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <Radzen.Blazor.RadzenCheckBox class="form-control mt-5 ml-3" @bind-Value="@BindingContext.CommonTask.ExecutorModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>

                                    <div class="col-sm-9">
                                        <AMproverDictionaryDropdown Label=@_localizer["CaeExecutorLbl"] AllowFiltering="true" AllowClear="true" class="form-control"
                                                                    Data=@BindingContext.ExecutorsDict.ToNullableDictionary()
                                                                    @bind-Value=@BindingContext.CommonTask.ExecutorId Required=true />
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <Radzen.Blazor.RadzenCheckBox class="form-control mt-5 ml-3" @bind-Value="@BindingContext.CommonTask.WorkPackageModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>

                                    <div class="col-sm-9">
                                        <AMproverDictionaryDropdown Label=@_localizer["CaeWorkPackageLbl"] AllowFiltering="true" AllowClear="true" class="form-control"
                                                                    Data=@BindingContext.WorkPackageDict.ToNullableDictionary()
                                                                    @bind-Value=@BindingContext.CommonTask.WorkPackageId Required=true/>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <Radzen.Blazor.RadzenCheckBox class="form-control mt-5 ml-3" @bind-Value="@BindingContext.CommonTask.IntervalModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>
                                    <div class="col-sm-4">
                                        <AMproverNumberInput Label=@_localizer["CaeIntervalLbl"] Format="0" TValue="decimal?" @bind-Value="BindingContext.CommonTask.Interval" class="form-control" />
                                    </div>
                                    <div class="col-sm-5">
                                        <AMproverDictionaryDropdown Label="IntervalUnit" AllowClear="true" AllowFiltering="true" class="form-control"
                                                                    Data=@BindingContext.IntervalUnitsDict.ToNullableDictionary()
                                                                    @bind-Value=@BindingContext.CommonTask.IntervalUnitId Required=true />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>

                <RadzenTabsItem Text=@_localizer["CaeGeneralItemsLbl"]>
                    <div class="row">
                        <div class="col-sm-12">
                            <AMproverTextArea @bind-Value=@BindingContext.CommonTask.GeneralDescription Cols="30" Rows="3" Label="General description" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <AMproverTextArea @bind-Value=@BindingContext.CommonTask.Permit Cols="30" Rows="3" Label="@_localizer["CaePermitLbl"]" />
                        </div>
                        <div class="col-sm-6">
                            <AMproverTextArea @bind-Value=@BindingContext.CommonTask.Responsible Cols="30" Rows="3" Label=@_localizer["CaeResponsibleLbl"] />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3">
                            <AMproverNumberInput  Label=@_localizer["CaeDownTimeLbl"] Format="0" TValue="decimal?" @bind-Value="BindingContext.CommonTask.DownTime" class="form-control" />
                        </div>
                        <div class="col-sm-3">
                            <AMproverNumberInput  Label=@_localizer["CaeDurationLbl"] Format="0" TValue="decimal?" @bind-Value="BindingContext.CommonTask.Duration" class="form-control" />
                        </div>
                        <div class="col-sm-3">
                        </div>
                        <div class="col-sm-3">
                            <AMproverNumberInput Label=@_localizer["CaeSortOrderLbl"] Format="0" TValue="int?" @bind-Value="BindingContext.CommonTask.SortOrder" class="form-control" />
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["CaePropertiesLbl"]>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>@_localizer["CaeInitiatedByLbl"]:</label>
                                <label>@BindingContext.CommonTask.InitiatedBy</label>
                            </div>
                        </div>
                        @if (BindingContext.CommonTask.DateInitiated != null)
                        {
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>@_localizer["CaeOnLbl"]:</label>
                                    <label>@BindingContext.CommonTask.DateInitiated.Value.ToString("dd-MM-yyyy hh:mm")</label>
                                </div>
                            </div>
                        }
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>@_localizer["CaeInitiatedByLbl"]:</label>
                                <label>@BindingContext.CommonTask.ModifiedBy</label>
                            </div>
                        </div>
                        @if (BindingContext.CommonTask.DateModified != null)
                        {
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>@_localizer["CaeOnLbl"]:</label>
                                    <label>@BindingContext.CommonTask.DateModified.Value.ToString("dd-MM-yyyy hh:mm")</label>
                                </div>
                            </div>
                        }
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>

        <RadzenButton Disabled=!GlobalDataService.CanEdit type="submit" class="btn btn-primary" Text="Save" />
    </Radzen.Blazor.RadzenTemplateForm>
}

@code{
    [Parameter] public int CommonTaskId { get; set; }

    [Parameter] public EventCallback<CommonTaskModel> Callback { get; set; }
}