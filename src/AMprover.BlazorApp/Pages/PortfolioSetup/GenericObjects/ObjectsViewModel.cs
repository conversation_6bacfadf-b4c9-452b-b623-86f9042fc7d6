using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models;
using System;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.GenericObjects;

public class ObjectsViewModel : BaseViewModel
{
    private readonly IObjectManager _objectManager;
    private readonly IStringLocalizer<ObjectsPage> _localizer;

    public ObjectsViewModel(
        ILoggerFactory loggerFactory, 
        IObjectManager objectManager,
        IStringLocalizer<ObjectsPage> localizer,
        DialogService dialogService) : base(loggerFactory)
    {
        _objectManager = objectManager;
        _localizer = localizer;
        _dialogService = dialogService;
    }

    public Dictionary<ObjectLevel, List<ObjectModel>> Objects { get; private set; } = new();

    public UtilityGrid<ObjectModel> ObjectGrid { get; set; }

    public List<GenericObjectLevel> Levels { get; set; }

    public UtilityGrid<GenericObjectLevel> LevelsCrudGrid { get; set; }

    public override void OnInitialized()
    {
        Objects = _objectManager.GetAllObjects();
        ObjectLevels = _objectManager.GetObjectLevelNames();
        LoadLevels();
        base.OnInitialized();       
    }

    private void LoadLevels()
    {
        Levels = _objectManager.GetGenericObjectLevels();
    }

    public void SaveRow(GenericObjectLevel level)
    {
        Levels[level.Level] = _objectManager.UpdateGenericObjectLevel(level);
        Levels = new List<GenericObjectLevel>(Levels);
    }

    public void CloneObject(ObjectModel model)
    {
        var copy = model.CopyAsNew();
        copy.Name += " (clone)";
        SaveObject(copy);
    }

    public void SaveObject(ObjectModel model)
    {
        var objects = Objects[model.GetObjectLevel()];
        var result = _objectManager.SaveObject(model);

        if (model.Id is > 0 && objects.IndexOf(model) >= 0)
        {
            objects[objects.IndexOf(model)] = result;
            Objects[model.GetObjectLevel()] = new List<ObjectModel>(objects);
        }
        else
        {
            Objects[model.GetObjectLevel()] = new List<ObjectModel>(objects.Append(result));
        }
    }

    public bool ValidateCanSave(ObjectModel model)
    {
        var objects = Objects[model.GetObjectLevel()];

        // Objects must have unique Name AND Shortkey, check ID to make sure your not comparing the object to itself
        return !objects.Any(x => x.Id != model.Id 
            && (x.Name.Trim().Equals(model.Name.Trim(), StringComparison.OrdinalIgnoreCase) 
             || x.ShortKey.Trim().Equals(model.ShortKey.Trim(), StringComparison.OrdinalIgnoreCase)));
    }

    public void ValidateCanSaveFailed(ObjectModel model)
    {
        ShowErrorMessage(_localizer["ObNoDuplicateNamesOrShortkeyTxt"]);
    }

    public async Task DeleteObject(ObjectModel model)
    {
        if (model == null || model.Id == 0) return;

        if (_objectManager.ObjectHasDependencies(model.Id))
        {
            _dialogService.Close();

            // The Are You Sure? dialog is still open, we cannot open another dialog until it is closed
            // await Task.Delay() to fix async issue
            await Task.Delay(1);

            _dialogService.Open<InformationDialog>(_localizer["ObCannotBeDeletedTitle"],
                new Dictionary<string, object>
                {
                    { "DialogContent", _localizer["ObCannotBeDeletedTxt"].Value}
                });
        }
        else
        {
            _objectManager.DeleteObject(model.Id);

            Objects[model.GetObjectLevel()].Remove(model);
            Objects[model.GetObjectLevel()] = new List<ObjectModel>(Objects[model.GetObjectLevel()]);
        }
    }
}