@page "/objects"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<ObjectsViewModel>
@inject IStringLocalizer<ObjectsPage> _localizer

<h2>@_localizer["ObHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Objects" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@_localizer["ObHeaderTxt"] DialogContent=@_localizer["ObMenuTxt"] />
    </div>
</div>

<p>@((MarkupString)_localizer["ObSubHeaderTxt"].Value)</p>

<RadzenTabs>
    <Tabs>

        @foreach (var type in BindingContext.Objects)
        {
            <RadzenTabsItem Text=@BindingContext.GetObjectLevel(type.Key)>
                <div class="row mb-3">
                    <div class="col-12">

                        <UtilityGrid TItem=ObjectModel
                                  @ref=@BindingContext.ObjectGrid
                                  Data=@BindingContext.Objects[type.Key]
                                  FileName=@GridNames.Portfolio.Objects
                                  Interactive=true
                                  AddNewButton=true
                                  AllowFiltering=true
                                  CloneCallBack=@BindingContext.CloneObject
                                  SaveCallback=@BindingContext.SaveObject
                                  DeleteCallback=@BindingContext.DeleteObject
                                  NewItemTemplate=@(new ObjectModel { Level = (int)type.Key }) 
                                  ExternalSaveValidation=@BindingContext.ValidateCanSave 
                                  ExternalSaveValidationFailed=@BindingContext.ValidateCanSaveFailed 
                                  MaxRows=50 />

                    </div>
                </div>
            </RadzenTabsItem>
        }
        <RadzenTabsItem Text=@_localizer["ObLevelHeaderTxt"].Value>
            <div class="row">
                <div class="col-xl-6 col-lg-8 col-10">

                     <UtilityGrid TItem=GenericObjectLevel
                            @ref=@BindingContext.LevelsCrudGrid
                            Data=@BindingContext.Levels
                            FileName=@GridNames.Portfolio.ObjectLevels
                            Interactive=true
                            SaveCallback=@BindingContext.SaveRow
                            AllowChangeColumns=false />

                </div>
            </div>
        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>