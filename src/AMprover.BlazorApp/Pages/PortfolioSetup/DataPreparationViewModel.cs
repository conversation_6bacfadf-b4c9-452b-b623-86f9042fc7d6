using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public class DataPreparationViewModel : BaseViewModel
{
    private readonly NavigationManager _navigationManager;

    public DataPreparationViewModel(ILoggerFactory loggerFactory, DialogService dialogService, NavigationManager navigationManager) : base(loggerFactory)
    {
        _dialogService = dialogService;
        _navigationManager = navigationManager;
    }
    public ElementReference HierarchyLevel { get; set; }
        
    public void Navigate(string page)
    {
        _navigationManager.NavigateTo(page);
    }
}