using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Helpers;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using AMprover.Data.Infrastructure;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public class UsersViewModel : BaseViewModel
{
    private readonly UserManager<UserAccount> _userManager;
    private readonly IAssetManagementPortfolioResolver _portfolioResolver;
    private readonly IRiskAnalysisSetupManager _riskAnalysisManager;
    private readonly IServiceProvider _serviceProvider;

    public UsersViewModel(
        IAssetManagementPortfolioResolver portfolioResolver,
        ILoggerFactory loggerFactory,
        IRiskAnalysisSetupManager riskAnalysisManager,
        UserManager<UserAccount> userManager,
        IServiceProvider serviceProvider) : base(loggerFactory)
    {
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _portfolioResolver = portfolioResolver ?? throw new ArgumentNullException(nameof(portfolioResolver));
        _riskAnalysisManager = riskAnalysisManager ?? throw new ArgumentNullException(nameof(riskAnalysisManager));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

        // Initialize collections to avoid null checks
        Departments = new List<DepartmentModel>();
        UserRoleCache = new Dictionary<string, HashSet<string>>();
        ColUsers = new ObservableRangeCollection<UserAccount>();
    }

    public RadzenDataGrid<DepartmentModel> DepartmentGrid { get; set; }
    // Properties
    public List<DepartmentModel> Departments { get; private set; }

    // Store all roles for each user, not just admin status
    private Dictionary<string, HashSet<string>> UserRoleCache { get; set; }

    private ObservableRangeCollection<UserAccount> _colUsers;

    public ObservableRangeCollection<UserAccount> ColUsers
    {
        get => _colUsers;
        private set => Set(ref _colUsers, value);
    }

    private string _strError;

    public string StrError
    {
        get => _strError ?? string.Empty;
        set => Set(ref _strError, value);
    }

    private bool _isLoading;

    public bool IsLoading
    {
        get => _isLoading;
        set => Set(ref _isLoading, value);
    }

    // Methods
    public override async Task OnInitializedAsync()
    {
        try
        {
            IsLoading = true;

            var usersTask = LoadUsersAsync();
            var departmentsTask = LoadDepartmentsAsync();

            await Task.WhenAll(usersTask, departmentsTask);

            // Cache user roles
            await CacheUserRolesAsync();
        }
        catch (Exception ex)
        {
            StrError = $"Error loading data: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    public async Task DeleteDepartment(int department)
    {
        _riskAnalysisManager.DeleteDepartment(department);
        Departments.RemoveAll(x => x.Id == department);
        await DepartmentGrid.Reload();
    }

    private async Task LoadUsersAsync()
    {
        try
        {
            var currentPortfolio = _portfolioResolver.GetCurrentPortfolio();
            if (currentPortfolio == null)
            {
                StrError = "No active portfolio selected";
                return;
            }

            // Create a new scope to get a fresh UserManager and DbContext
            using var scope = _serviceProvider.CreateScope();
            var scopedUserManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();

            // Get users assigned to this portfolio
            var portfolioUsers = await scopedUserManager.Users
                .Where(x => x.PortfolioAssignments.Any(y => y.PortfolioId == currentPortfolio.Id))
                .ToListAsync();

            var administratorUsers = new List<UserAccount>();
            foreach (var user in portfolioUsers)
            {
                if (!await scopedUserManager.IsInRoleAsync(user, RoleConstants.Administrators))
                {
                    administratorUsers.Add(user);
                }
            }

            var sortedUsers = administratorUsers
                .OrderBy(ua => GetDomainFromEmail(ua.UserName))
                .ThenBy(ua => ua.Name)
                .ToList();

            ColUsers = new ObservableRangeCollection<UserAccount>(sortedUsers);
        }
        catch (Exception ex)
        {
            StrError = $"Error loading users: {ex.Message}";
            throw;
        }
    }

    private async Task LoadDepartmentsAsync()
    {
        try
        {
            Departments = _riskAnalysisManager.GetAllDepartments();
        }
        catch (Exception ex)
        {
            StrError = $"Error loading departments: {ex.Message}";
            throw;
        }
    }

    private async Task CacheUserRolesAsync()
    {
        try
        {
            // Create a new scope to get a fresh UserManager and DbContext
            using var scope = _serviceProvider.CreateScope();
            var scopedUserManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();

            foreach (var user in ColUsers)
            {
                var roles = await scopedUserManager.GetRolesAsync(user);
                UserRoleCache[user.Id] = new HashSet<string>(roles);
            }
        }
        catch (Exception ex)
        {
            StrError = $"Error loading user permissions: {ex.Message}";
            throw;
        }
    }

    private bool IsInRole(UserAccount user, string roleName)
    {
        if (user == null || string.IsNullOrEmpty(user.Id))
            return false;

        return UserRoleCache.TryGetValue(user.Id, out var roles) && roles.Contains(roleName);
    }

    public bool IsAdministrator(UserAccount user)
    {
        return IsInRole(user, RoleConstants.PortfolioAdministrators);
    }

    private static string GetDomainFromEmail(string email)
    {
        if (string.IsNullOrEmpty(email) || !email.Contains('@'))
            return string.Empty;

        var parts = email.Split('@');
        return parts.Length > 1 ? parts[1] : string.Empty;
    }
}