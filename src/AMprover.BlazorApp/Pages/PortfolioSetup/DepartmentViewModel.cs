using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public class DepartmentViewModel : BaseViewModel
{
    private readonly NavigationManager _navigationManager;
    private readonly IRiskAnalysisSetupManager _riskAnalysisSetupManager;
    private bool _isNewDepartment;

    public DepartmentViewModel(
        ILoggerFactory loggerFactory,
        NavigationManager navigationManager,
        IRiskAnalysisSetupManager riskAnalysisSetupManager) : base(loggerFactory)
    {
        _navigationManager = navigationManager;
        _riskAnalysisSetupManager = riskAnalysisSetupManager;
    }

    [Parameter] public int DepartmentId { get; set; }

    public DepartmentModel Department { get; set; }

    public override void OnInitialized()
    {
        if (DepartmentId != 0)
        {
            Department = _riskAnalysisSetupManager.GetDepartment(DepartmentId);
        }
        else
        {
            Department = new DepartmentModel
            {
                Id = 0,
                Description = string.Empty,
                ShortKey = string.Empty
            };
            _isNewDepartment = true;
        }
    }

    public void HandleValidSubmit()
    {
        // Save department
        _riskAnalysisSetupManager.CreateOrUpdateDepartment(Department);

        // Navigate back to list
        _navigationManager.NavigateTo("/users");
    }

    public void Cancel()
    {
        _navigationManager.NavigateTo("/users");
    }
}