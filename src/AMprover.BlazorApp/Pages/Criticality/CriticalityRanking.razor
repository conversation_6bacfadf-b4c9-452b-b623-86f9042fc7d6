@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<CriticalityRankingViewModel>
@page "/criticality-ranking"
@inject IStringLocalizer<CriticalityRanking> _localizer
@inject IGlobalDataService GlobalDataService;

<h2>@_localizer["CrHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col-6">
        <Breadcrumbs Category="Criticality Ranking" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right btn-centered my-2 mx-2" DialogTitle=@_localizer["CrMenuTitle"] DialogContent=@_localizer["CrMenuTxt"] />

        <RadzenButton Text=@_localizer["CrUpdateAssetsBtn"]
                      ButtonStyle="ButtonStyle.Secondary"
                      Icon="update" class="float-right my-2"
                      Click=@BindingContext.ImportAssets
                      Disabled=@BindingContext.Loading />

        <RadzenButton Text=@_localizer["CrRecalcBtn"] 
                      ButtonStyle="ButtonStyle.Secondary"
                      Icon="refresh" class="float-right my-2 mx-2"
                      Click=@BindingContext.RecalculateAllCriticalities
                      Disabled=@BindingContext.Loading />

        <RadzenButton Text=@_localizer["CrDeleteBtn"] 
                      Icon="delete" class="float-right my-2" 
                      Click=@BindingContext.OpenDeleteWidget
                      Disabled=@BindingContext.Loading />
    </div>
</div>

<div class="row">
    <div class="col-7">
        <div class="criticality-pie-chart">
            <RadzenChart>
                <RadzenPieSeries Data="@BindingContext.CriticalityCategories" Title="Categories"
                                 CategoryProperty="Key" ValueProperty="Value" Fills=@BindingContext.PiechartColors />
            </RadzenChart>
        </div>
    </div>
    
    <div class="col-5">
        <div class="row">
            @* <div class="col-6">
                <AMDropdown AllowFiltering="true"
                    Data=@BindingContext.Templates.ToDictionary(x => (int?)x.Id, x => x.Name)
                    @bind-Value=@BindingContext.RiskMatrix.IntValue
                    Change=@(() => BindingContext.SaveCriticalitySettings(BindingContext.RiskMatrix))
                    Label=@(_localizer["CrValueRiskMatrixLbl"]) />
            </div> *@
            @if (BindingContext.GetCritLevel() <= 3)
            {
                <div class="col-4" /> // Work Around to get the UpperLevel Colums to the right in case of CritLevel == 3
            }
            <div class="col-4" style="margin-top:10px;">
                @if (BindingContext.CritCategoryCalculation.TextValue == "ValueCalculation")
                {
                    <AMproverNumberInput Label=@BindingContext.GetUpperBtxt()
                                     @bind-Value=@BindingContext.UpperBlevel.IntValue
                                     Change=@(() => BindingContext.SaveCriticalitySettings(BindingContext.UpperBlevel))
                                     TValue="int?"
                                     Format="c0" />

                    <AMproverNumberInput Label=@BindingContext.GetUpperCtxt()
                                     @bind-Value=@BindingContext.UpperClevel.IntValue
                                     Change=@(() => BindingContext.SaveCriticalitySettings(BindingContext.UpperClevel))
                                     TValue="int?"
                                     Format="c0" />
                }
                else if (BindingContext.CritCategoryCalculation.TextValue == "CriticalityCalculation" || BindingContext.CritCategoryCalculation.TextValue == "CriticalityHighCalculation")
                {
                    <AMproverNumberInput Label=@BindingContext.GetUpperBtxt()
                                     @bind-Value=@BindingContext.UpperBlevel.IntValue
                                     Change=@(() => BindingContext.SaveCriticalitySettings(BindingContext.UpperBlevel))
                                     TValue="int?" />

                    <AMproverNumberInput Label=@BindingContext.GetUpperCtxt()
                                     @bind-Value=@BindingContext.UpperClevel.IntValue
                                     Change=@(() => BindingContext.SaveCriticalitySettings(BindingContext.UpperClevel))
                                     TValue="int?" />
                }
            </div>
            @if (BindingContext.GetCritLevel() >= 4)
            {
                <div class="col-4" style="margin-top:10px;">

                    @if (BindingContext.CritCategoryCalculation.TextValue == "ValueCalculation")
                    {
                        <AMproverNumberInput Label=@BindingContext.GetUpperDtxt()
                                         @bind-Value=@BindingContext.UpperDlevel.IntValue
                                         Change=@(() => BindingContext.SaveCriticalitySettings(BindingContext.UpperDlevel))
                                         TValue="int?"
                                         Format="c0" />
                        @if (BindingContext.GetCritLevel() >= 5)
                        {
                            <AMproverNumberInput Label=@BindingContext.GetUpperEtxt()
                                         @bind-Value=@BindingContext.UpperElevel.IntValue
                                         Change=@(() => BindingContext.SaveCriticalitySettings(BindingContext.UpperElevel))
                                         TValue="int?"
                                         Format="c0" />
                        }
                    }
                    else if (BindingContext.CritCategoryCalculation.TextValue == "CriticalityCalculation" || BindingContext.CritCategoryCalculation.TextValue == "CriticalityHighCalculation")
                    {
                        <AMproverNumberInput Label=@BindingContext.GetUpperDtxt()
                                        @bind-Value=@BindingContext.UpperDlevel.IntValue
                                        Change=@(() => BindingContext.SaveCriticalitySettings(BindingContext.UpperDlevel))
                                        TValue="int?" />
                        @if (BindingContext.GetCritLevel() >= 5)
                        {
                            <AMproverNumberInput Label=@BindingContext.GetUpperEtxt()
                                        @bind-Value=@BindingContext.UpperElevel.IntValue
                                        Change=@(() => BindingContext.SaveCriticalitySettings(BindingContext.UpperElevel))
                                        TValue="int?" />
                        }
                    }
                </div>
            }
            <div class="col-4" style="margin-top:10px;">
                <div class="form-group" >
                    
                    <AMproverDictionaryDropdown @bind-Value=@BindingContext.CategoryType
                                                Data=@BindingContext.CategoryDropdownOptions
                                                Label=@_localizer["CrCategoryTypeTxt"]
                                                Change=@BindingContext.ChangeCategoryType />
                    <AMproverDictionaryDropdown @bind-Value=@BindingContext.CategoryCalculation
                                                Data=@BindingContext.CategoryCalcDropdownOptions
                                                Label=@_localizer["CrCategoryCalculationTxt"]
                                                Change=@BindingContext.ChangeCategoryCalculation />
                </div>
            </div>
        </div>
    </div>
</div>

<RadzenTabs class="hide-tabs-nav" @ref=@BindingContext.Tabs>
    <Tabs>
        <RadzenTabsItem>

            <UtilityGrid TItem=CriticalityRankingModelFlat
                         @ref=BindingContext.UtilityGrid
                         Data=BindingContext.CriticalityRankings
                         FileUploadCallBack=@BindingContext.ProcessUpload
                         FileName=@GridNames.Criticality.Rankings
                         CssClass="abs-result-preview-container"
                         SaveCallback=@BindingContext.SaveCriticality
                         OptionOverrides=@BindingContext.GetCriticalityOptions()
                         DropDownOverrides=@BindingContext.DropdownOverrides
                         OpenPopup=@(args => BindingContext.EditRow(args))
                         Interactive="true"
                         MaxRows="100"
                         AllowXlsExport=true
                         AllowFiltering=true
                         OnFilter=@BindingContext.OnFilter
                         DynamicWidth=true 
                         OpenPageCallback=@(args => BindingContext.OpenRiskOnAbs(args.Asset.Id))
                         OpenPageTextOverride=@_localizer["CrOpenAssetInRiskOnAbs"] />

        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>

