@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<DeleteCriticalityWidgetViewModel>
@inject IStringLocalizer<CriticalityRanking> _localizer

<div class="row mb-3">
    <div class="col-12">
        <p>@((MarkupString)_localizer["CrDeleteTxt"].Value)</p>
        <p>
            <RadzenButton Text="Delete Criticalities" Icon="delete_forever" ButtonStyle="ButtonStyle.Danger" Click=@BindingContext.DeleteAllCriticalities />
        </p>
    </div>
</div>

@code{
    [Parameter] public EventCallback ConfirmCallback { get; set; }
}