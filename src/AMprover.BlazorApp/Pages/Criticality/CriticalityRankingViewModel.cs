using Radzen.Blazor;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Criticality;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;
using AMprover.BusinessLogic.Enums;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.Criticality;

public class CriticalityRankingViewModel : BaseViewModel
{
    private readonly ICriticalityRankingManager _criticalityRankingManager;
    private readonly IDropdownManager _dropdownManager;
    private readonly NavigationManager _navigationManager;
    private IStringLocalizer<CriticalityRanking> _localizer { get; }

    public RadzenTabs Tabs { get; set; }

    public List<CriticalityRankingModelFlat> CriticalityRankings { get; set; }

    public UtilityGrid<CriticalityRankingModelFlat> UtilityGrid { get; set; }

    public Dictionary<string, int> CriticalityCategories { get; set; }

    public Dictionary<string, Dictionary<int, string>> DropdownOverrides { get; set; }
    
    public List<LookupSettingModel> LookupSettings { get; set; }
    public List<RiskMatrixTemplateModel> Templates { get; set; } = new();
    public List<string> PiechartColors { get; set; } = new();
    public CriticalityCategoryType CategoryType { get; set; }
    public CriticalityCategoryCalculation CategoryCalculation { get; set; }
    
    public Dictionary<CriticalityCategoryType, string> CategoryDropdownOptions { get; set; }
        = Enum.GetValues<CriticalityCategoryType>().ToDictionary(x => x, y => y.GetDescription());
    public Dictionary<CriticalityCategoryCalculation, string> CategoryCalcDropdownOptions { get; set; }
        = Enum.GetValues<CriticalityCategoryCalculation>().ToDictionary(x => x, y => y.GetDescription());

    public void ChangeCategoryType()
    {
        CritCategoryType.TextValue = $"{CategoryType}";
        SaveCriticalitySettings(CritCategoryType);
        _navigationManager.NavigateTo(_navigationManager.Uri, true);
        }
    public void ChangeCategoryCalculation()
    {
        CritCategoryCalculation.TextValue = $"{CategoryCalculation}";
        SaveCriticalitySettings(CritCategoryCalculation);
    }

    public CriticalityRankingViewModel(
        ILoggerFactory loggerFactory, 
        DialogService dialogService, 
        ICriticalityRankingManager criticalityRankingManager, 
        ILookupManager lookupManager, 
        IDropdownManager dropdownManager,
        IStringLocalizer<CriticalityRanking> localizer,
        NavigationManager navigationManager) : base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _criticalityRankingManager = criticalityRankingManager;
        _dropdownManager = dropdownManager;
        _navigationManager = navigationManager;
        _localizer = localizer;

        DropdownOverrides = _dropdownManager.GetCriticalityDropOverrides();
    }

    public LookupSettingModel CritCategoryType { get; set; }
    public LookupSettingModel CritCategoryCalculation { get; set; }
    public LookupSettingModel UpperBlevel { get; set; }
    public LookupSettingModel UpperClevel { get; set; }
    public LookupSettingModel UpperDlevel { get; set; }
    public LookupSettingModel UpperElevel { get; set; }
    //public LookupSettingModel RiskMatrix { get; set; }

    public override void OnInitialized()
    {
        CriticalityRankings = _criticalityRankingManager.GetAllCriticalityRankings();
        LookupSettings = _lookupManager.GetLookupSettings();
        
        InitCriticalitySettings();
        CategoryType = Enum.Parse<CriticalityCategoryType>(CritCategoryType.TextValue);
        CategoryCalculation = Enum.Parse<CriticalityCategoryCalculation>(CritCategoryCalculation.TextValue);

        SetCategoryPieChart();
        base.OnInitialized();
    }
 
    private void InitCriticalitySettings()
    {
        CritCategoryType = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritCategoryType")) ?? new LookupSettingModel { Property = "CritCategoryType", TextValue = $"{CriticalityCategoryType.ABC}" };
        CritCategoryCalculation = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritCategoryCalculation")) ?? new LookupSettingModel { Property = "CritCategoryCalculation", TextValue = $"{CriticalityCategoryCalculation.CriticalityCalculation}" };
        UpperBlevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperBlevel")) ?? new LookupSettingModel { Property = "CritUpperBlevel", IntValue = 0 };
        UpperClevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperClevel")) ?? new LookupSettingModel { Property = "CritUpperClevel", IntValue = 0 };
        UpperDlevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperDlevel")) ?? new LookupSettingModel { Property = "CritUpperDlevel", IntValue = 0 };
        UpperElevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperElevel")) ?? new LookupSettingModel { Property = "CritUpperElevel", IntValue = 0 };
        //RiskMatrix = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritRiskMatrix", StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = "CritRiskMatrix", IntValue = 0 };
    }

    public void SaveCriticality(CriticalityRankingModelFlat model)
    {
        if (CritCategoryCalculation.TextValue != "None") model.Calculate((int)UpperBlevel.IntValue, (int)UpperClevel.IntValue, (int)UpperDlevel.IntValue, (int)UpperElevel.IntValue, CritCategoryCalculation.TextValue, CritCategoryType.TextValue);

        var orginalModel = CriticalityRankings.FirstOrDefault(x => x.Id == model.Id);
        var index = CriticalityRankings.IndexOf(orginalModel);
        var updatedModel = _criticalityRankingManager.SaveCriticalityRanking(model);
        CriticalityRankings[index] = updatedModel;
        SetCategoryPieChart();
    }

    public void SaveCriticalitySettings(LookupSettingModel setting)
    {
        _lookupManager.SaveLookupSettings(setting);
    }

    public void OnFilter(DataGridColumnFilterEventArgs<CriticalityRankingModelFlat> _)
    {
        SetCategoryPieChart();
    }

    public void SetCategoryPieChart()
    {
        // Use Filtered Results from the UtilityGrid, Fallback to original collection for inital load
        CriticalityCategories = (UtilityGrid?.BindingContext.Grid.View.ToList() ?? CriticalityRankings)
            .GroupBy(x => x.Category?.ToUpper().Trim())
            .OrderBy(x => GenCategoryIndex(x.Key))
            .ToDictionary(g => g.Key ?? "None", g => g.Count());

        SetPieChartColors();
    }

    private int GenCategoryIndex(string input)
    {
        var result = GetCategorySequence(CategoryType).IndexOf(input);

        if (result == -1)
            return int.MaxValue;

        return result;
    }

    private List<string> GetCategorySequence(CriticalityCategoryType categoryType)
    {
        return categoryType switch
        {
            CriticalityCategoryType.ABC => new List<string> { "A", "B", "C" },
            CriticalityCategoryType.ABCD => new List<string> { "A", "B", "C", "D" },
            CriticalityCategoryType.ABCDE => new List<string> { "A", "B", "C", "D", "E" },
            CriticalityCategoryType.HML => new List<string> { "H", "M", "L" },
            CriticalityCategoryType.ThreeToOne => new List<string> { "3", "2", "1"},
            CriticalityCategoryType.FourToOne => new List<string> { "4", "3", "2", "1" },
            CriticalityCategoryType.FiveToOne => new List<string> { "5", "4", "3", "2", "1" },
            _ => throw new NotImplementedException(),
        };
    }

    public int GetCritLevel()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABC => 3,
            CriticalityCategoryType.ABCD => 4,
            CriticalityCategoryType.ABCDE => 5,
            CriticalityCategoryType.HML => 3,
            CriticalityCategoryType.ThreeToOne => 3,
            CriticalityCategoryType.FourToOne => 4,
            CriticalityCategoryType.FiveToOne => 5,
            _ => throw new NotImplementedException(),
        };
    }

    public string GetUpperBtxt()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABC => _localizer["CrUpperBlevelTxt"],
            CriticalityCategoryType.ABCD => _localizer["CrUpperBlevelTxt"],
            CriticalityCategoryType.ABCDE => _localizer["CrUpperBlevelTxt"],
            CriticalityCategoryType.HML => _localizer["CrUpperMlevelTxt"],
            CriticalityCategoryType.ThreeToOne => _localizer["CrUpper4levelTxt"],
            CriticalityCategoryType.FourToOne => _localizer["CrUpper4levelTxt"],
            CriticalityCategoryType.FiveToOne => _localizer["CrUpper4levelTxt"],
            _ => throw new NotImplementedException(),
        };
    }
    public string GetUpperCtxt()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABC => _localizer["CrUpperClevelTxt"],
            CriticalityCategoryType.ABCD => _localizer["CrUpperClevelTxt"],
            CriticalityCategoryType.ABCDE => _localizer["CrUpperClevelTxt"],
            CriticalityCategoryType.HML => _localizer["CrUpperLlevelTxt"],
            CriticalityCategoryType.ThreeToOne => _localizer["CrUpper3levelTxt"],
            CriticalityCategoryType.FourToOne => _localizer["CrUpper3levelTxt"],
            CriticalityCategoryType.FiveToOne => _localizer["CrUpper3levelTxt"],
            _ => throw new NotImplementedException(),
        };
    }
    public string GetUpperDtxt()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABCD => _localizer["CrUpperDlevelTxt"],
            CriticalityCategoryType.ABCDE => _localizer["CrUpperDlevelTxt"],
            CriticalityCategoryType.FourToOne => _localizer["CrUpper2levelTxt"],
            CriticalityCategoryType.FiveToOne => _localizer["CrUpper2levelTxt"],
            _ => throw new NotImplementedException(),
        };
    }
    public string GetUpperEtxt()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABCDE => _localizer["CrUpperElevelTxt"],
            CriticalityCategoryType.FiveToOne => _localizer["CrUpper1levelTxt"],
            _ => throw new NotImplementedException(),
        };
    }

    public void SetPieChartColors()
    {
        PiechartColors.Clear();
        var UnfilteredCriticalities = UtilityGrid?.BindingContext.Grid.View.ToList() ?? CriticalityRankings;
        var options = UnfilteredCriticalities.Select(x => x.Category?.Trim()?.ToUpper() ?? "").Distinct().Count();

        var categories = GetCategorySequence(CategoryType);

        if (GetCritLevel() == 3)
        {
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[0], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#ff8080"); // Red
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[1], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#ffd280"); // Orange
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[2], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#80c080"); // Green
        }

        else if (GetCritLevel() == 4)
        {
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[0], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#ff8080"); // Red
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[1], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#ffd280"); // Orange
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[2], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#FFFF00"); // Yellow
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[3], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#80c080"); // Green
        }
        else if (GetCritLevel() == 5)
        {
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[0], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#ff8080"); // Red
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[1], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#ffd280"); // Orange
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[2], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#FFFF00"); // Yellow
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[3], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#80c080"); // Green
            if (UnfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[4], StringComparison.OrdinalIgnoreCase)))
                PiechartColors.Add("#009900"); // Dark Green
        }

        // Gray
        for (int i = 0; i < options; i++)
        {
            PiechartColors.Add("#dddddd");
        }
    }

    public void OpenDeleteWidget()
    {
        _dialogService.Open<DeleteCriticalityWidget>
                    (_localizer["CrDeleteHeaderTxt"],new Dictionary<string, object>
                    {
                        {
                            nameof(DeleteCriticalityWidget.ConfirmCallback),
                            EventCallback.Factory.Create(this, WidgetCallBack)
                        }
                    });
    }

    public void WidgetCallBack()
    {
        OnInitialized();
        Tabs.Reload();
    }

    public void OpenRiskOnAbs(int assetId)
    {
        _navigationManager.NavigateTo($"/value-risks-on-abs?assetid={assetId}&tabid=3");
    }

    public void ProcessUpload(FileUpload fileUpload)
    {
        if (string.IsNullOrWhiteSpace(fileUpload.Base64Content))
            return;

        ShowLoadingDialog();

        var uploadedItems = fileUpload.Base64Content
            .GetExcelData<CriticalityRankingModelFlat>(headerIndex: 1)
            .Where(x => !string.IsNullOrWhiteSpace(x.Value.AssetCode))
            .Select(x => x.Value).ToList();
        
        if (_criticalityRankingManager.ImportFileUpload(uploadedItems, out var result))
        {
            _dialogService.Close();
            ShowDialog("Success", true, result.StatusMessage);
        }
        else
        {
            _dialogService.Close();
            ShowDialog("Error!", true, new[] { "Failed to process uploaded file" }.Concat(result.ErrorMessages).ToArray());
        }
    }

    private void ShowLoadingDialog()
    {
        _dialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    public void ImportAssets()
    {
        var newAssets = _criticalityRankingManager.ImportFromAssets();
        ShowDialog("Success!", true, $"Imported {newAssets.Count} new assets");
    }

    public void EditRow(CriticalityRankingModelFlat row)
    {
        _dialogService.Open<CriticalityEdit>("Edit Criticality",
            new Dictionary<string, object>
            {
                { "Model", row },
                { "Callback", EventCallback.Factory.Create<CriticalityRankingModelFlat>(this, EditCallBack) },
            },
            new DialogOptions { Width = "700px", Resizable = false, Draggable = true });
    }

    private async Task EditCallBack(CriticalityRankingModelFlat model)
    {
        var rowIndex = CriticalityRankings.FindIndex(x => x.Id == model.Id);
        if (rowIndex >= 0)
        {
            CriticalityRankings[rowIndex] = model;
            await UtilityGrid.BindingContext.Grid.Reload();
        }
    }

    // Dropdown overrides
    public Dictionary<string, Dictionary<string, string>> GetCriticalityOptions()
    {
        var result = new Dictionary<string, Dictionary<string, string>>
        {
            {
                nameof(CriticalityRankingModelFlat.Category),
                GetCategorySequence(CategoryType).ToDictionary(x => x, y => y)
            }
        };
        return result;
    }

    //Recalculate All Criticalities
    public async Task RecalculateAllCriticalities()
    {
        Loading = true;
        await Task.Delay(1);

        for (int i = 0; i < CriticalityRankings.Count; i++)
        {
            CriticalityRankingModelFlat model;

            model = CriticalityRankings[i];
            model.Calculate((int)UpperBlevel.IntValue, (int)UpperClevel.IntValue, (int)UpperDlevel.IntValue, (int)UpperElevel.IntValue, CritCategoryCalculation.TextValue, CritCategoryType.TextValue);
        }

        _criticalityRankingManager.UpdateCriticalities(CriticalityRankings);
        _navigationManager.NavigateTo(_navigationManager.Uri, true);

        Loading = false;
    }
}