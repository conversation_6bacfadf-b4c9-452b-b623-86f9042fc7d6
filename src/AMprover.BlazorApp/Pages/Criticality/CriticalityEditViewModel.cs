using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Criticality;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Models;
using System.Linq;
using AMprover.BusinessLogic.Enums;

namespace AMprover.BlazorApp.Pages.Criticality;

public class CriticalityEditViewModel : BaseViewModel
{
    private readonly ICriticalityRankingManager _criticalityRankingManager;
    private readonly IDropdownManager _dropdownManager;
    private readonly ILookupManager _lookupManager;
    public List<LookupSettingModel> LookupSettings { get; set; }

    public CriticalityEditViewModel(
        ILoggerFactory loggerFactory,
        DialogService dialogService,
        ICriticalityRankingManager criticalityRankingManager,
        ILookupManager lookupManager,
        IDropdownManager dropdownManager) : base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _criticalityRankingManager = criticalityRankingManager;
        _dropdownManager = dropdownManager;
        _lookupManager = lookupManager;
    }

    [Parameter] public CriticalityRankingModelFlat Model { get; set; }
    [Parameter] public EventCallback<CriticalityRankingModelFlat> CallBack { get; set; }

    public Dictionary<int, string> FailureModes { get; set; }
    public Dictionary<int, string> SiCategories { get; set; }
    public Dictionary<int, string> statusDict { get; set; }

    Dictionary<string, string> CriticalityGridColumnHeaders { get; set; }

    public LookupSettingModel CritCategoryType { get; set; }
    public LookupSettingModel CritCategoryCalculation { get; set; }
    public LookupSettingModel UpperBlevel { get; set; }
    public LookupSettingModel UpperClevel { get; set; }
    public LookupSettingModel UpperDlevel { get; set; }
    public LookupSettingModel UpperElevel { get; set; }
    //public LookupSettingModel RiskMatrix { get; set; }

    public override void OnInitialized()
    {
        LookupSettings = _lookupManager.GetLookupSettings();
        FailureModes = _dropdownManager.GetFailureModesDict();
        SiCategories = _dropdownManager.GetSiCategoriesDict();
        statusDict = _dropdownManager.GetStatusDict();
        CriticalityGridColumnHeaders = _lookupManager.GetColumnHeaders(GridNames.Criticality.Rankings);

        InitCriticalitySettings();
    }

    private void InitCriticalitySettings()
    {
        CritCategoryType = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritCategoryType")) ?? new LookupSettingModel { Property = "CritCategoryType", TextValue = $"{CriticalityCategoryType.ABC}" };
        CritCategoryCalculation = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritCategoryCalculation")) ?? new LookupSettingModel { Property = "CritCategoryCalculation", TextValue = $"{CriticalityCategoryCalculation.CriticalityCalculation}" };
        UpperBlevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperBlevel")) ?? new LookupSettingModel { Property = "CritUpperBlevel", IntValue = 0 };
        UpperClevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperClevel")) ?? new LookupSettingModel { Property = "CritUpperClevel", IntValue = 0 };
        UpperDlevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperDlevel")) ?? new LookupSettingModel { Property = "CritUpperDlevel", IntValue = 0 };
        UpperElevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperElevel")) ?? new LookupSettingModel { Property = "CritUpperElevel", IntValue = 0 };
        //RiskMatrix = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritRiskMatrix", StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = "CritRiskMatrix", IntValue = 0 };
    }

    public string GetHeadertext(string fieldName)
    {
        if (CriticalityGridColumnHeaders.ContainsKey(fieldName))
            return CriticalityGridColumnHeaders[fieldName];

        return fieldName;
    }

    public async Task ValidTaskSubmitted(CriticalityRankingModelFlat model)
    {
        if (CritCategoryCalculation.TextValue != "None") model.Calculate((int)UpperBlevel.IntValue, (int)UpperClevel.IntValue, (int)UpperDlevel.IntValue, (int)UpperElevel.IntValue, CritCategoryCalculation.TextValue, CritCategoryType.TextValue);
        Model = _criticalityRankingManager.SaveCriticalityRanking(model);
        await CallBack.InvokeAsync(Model).ConfigureAwait(false);
        _dialogService.Close();
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        _logger.LogError($"{nameof(CriticalityEditViewModel)}.{nameof(InvalidTaskSubmitted)}() => {string.Join('|', args.Errors)}");
    }
}