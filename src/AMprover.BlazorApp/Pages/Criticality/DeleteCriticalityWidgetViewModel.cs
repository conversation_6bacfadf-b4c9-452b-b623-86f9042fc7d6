using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.Criticality;

public class DeleteCriticalityWidgetViewModel : BaseViewModel
{
    [Parameter] public EventCallback ConfirmCallback { get; set; }

    private readonly ICriticalityRankingManager _criticalityManager;

    public DeleteCriticalityWidgetViewModel(
        ILoggerFactory loggerFactory,
        ICriticalityRankingManager criticalityManager,
        DialogService dialogService) : base(loggerFactory, dialogService)
    {
        _criticalityManager = criticalityManager;
    }

    public async Task DeleteAllCriticalities()
    {
        _criticalityManager.DeleteAllCriticalities();
        _dialogService.Close();
        await ConfirmCallback.InvokeAsync();
    }
}