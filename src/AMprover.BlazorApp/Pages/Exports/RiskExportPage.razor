@page "/value-risk-exports"
@using AMprover.BlazorApp.Pages.Report;
@using AMprover.BusinessLogic.Models.Import;
@using AMprover.BusinessLogic.Models.Reports;
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<RiskExportViewModel>
@inject IStringLocalizer<ReportPage> _localizer
@inject IJSRuntime JSRuntime

<div class="row">
    <div class="col-6">
        <h2>@_localizer["RpExportHeaderTxt"]</h2>
    </div>
</div>

<div class="row header-navigation">
    <div class="col-6">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
                <NavLink class="breadcrumb-item" href="value-risk-organizer/" Match="NavLinkMatch.All">
                    Value Risk Organizer
                </NavLink>
                <NavLink class="breadcrumb-item" aria-current="page" Match="NavLinkMatch.All">
                    Value Risk Exporter
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@_localizer["RpExportMenuTitle"] DialogContent=@_localizer["RpExportMenuTxt"] />
    </div>
</div>

<div class="row">
    <div class="col-12">

        <div class="row">
            <div class="col-3">
                <AMDropdown 
                    Label=@_localizer["RpScenarioLbl"]
                    Data=@BindingContext.Scenarios
                    @bind-Value=@BindingContext.SelectedScenario
                    Change=@BindingContext.UpdateRiskObjectDropdown
                    ContainerClass="form-group mt-3"/>
            </div>
        </div>

        <div class="row">
            <div class="col-3">
                <AMDropdown 
                    Label=@_localizer["RpRiskObjectLbl"]
                    Data=@BindingContext.RiskObjects
                    @bind-Value=@BindingContext.SelectedRiskObject
                    Change=@BindingContext.GetData
                    ContainerClass="form-group mt-3"/>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <RadzenTabs>
                    <Tabs>

                        <!-- Tasks -->
                        <RadzenTabsItem Text=@BindingContext.GetRiskTabHeader() >
                            <div class="row">
                                <div class="col-12">

                                    <UtilityGrid TItem=RiskImportModel
                                        Data=BindingContext.Risks
                                        FileName=@GridNames.Imports.Risks
                                        Interactive=false
                                        AllowXlsExport=true
                                        AllowFiltering=true
                                        MaxRows="20"
                                        DynamicWidth=true />

                                </div>
                            </div>
                        </RadzenTabsItem>

                        <!-- Tasks -->
                        <RadzenTabsItem Text=@BindingContext.GetTaskTabHeader() >
                            <div class="row">
                                <div class="col-12">

                                    <UtilityGrid 
                                        TItem=TaskImportModel
                                        Data=BindingContext.Tasks
                                        FileName=@GridNames.Imports.PreventiveActions
                                        Interactive=false
                                        AllowXlsExport=true
                                        AllowFiltering=true
                                        MaxRows="20"
                                        DynamicWidth=true />

                                </div>
                            </div>
                        </RadzenTabsItem>

                        <!-- Spares -->
                        <RadzenTabsItem Text=@BindingContext.GetSpareTabHeader() >
                            <div class="row">
                                <div class="col-12">

                                    <UtilityGrid 
                                        TItem=SpareImportModel
                                        Data=BindingContext.Spares
                                        FileName=@GridNames.Imports.Spares
                                        Interactive=false
                                        AllowXlsExport=true
                                        AllowFiltering=true
                                        MaxRows="20"
                                        DynamicWidth=true />

                                </div>
                            </div>
                        </RadzenTabsItem>
                        <!-- TaskPlan -->
                        <RadzenTabsItem Text=@BindingContext.GetTaskPlanTabHeader()>
                            <div class="row">
                                <div class="col-12">

                                    <UtilityGrid TItem=TaskPlanReportItemModel
                                                 Data=BindingContext.TaskPlan
                                                 FileName=@GridNames.Imports.TaskPlan
                                                 Interactive=false
                                                 AllowXlsExport=true
                                                 AllowFiltering=true
                                                 MaxRows="20"
                                                 DynamicWidth=true />

                                </div>
                            </div>
                        </RadzenTabsItem>

                    </Tabs>
                </RadzenTabs>
            </div>
        </div>

    </div>
</div>

@code
{
    [Parameter]
    public string SelectedReportType { get; set; }
}