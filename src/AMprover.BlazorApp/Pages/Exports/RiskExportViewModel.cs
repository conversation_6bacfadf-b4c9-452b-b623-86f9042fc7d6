using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BlazorApp.Pages.Report;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Import;
using AMprover.BusinessLogic.Models.Reports;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;

namespace AMprover.BlazorApp.Pages.Exports;

public class RiskExportViewModel : BaseViewModel
{
    private IDropdownManager _dropdownManager { get; }
    private IObjectManager _objectManager { get; }
    private IRiskExportManager _riskExportManager { get; }
    private IStringLocalizer<ReportPage> _localizer { get; }

    public ReportViewQueryParams QueryParams { get; set; }

    public List<ObjectModel> Objects { get; set; } = new List<ObjectModel>();

    public Dictionary<int, string> Scenarios { get; set; } = new Dictionary<int, string>();

    public Dictionary<int, string> RiskObjects { get; set; } = new Dictionary<int, string>();

    public int SelectedRiskObject { get; set; }

    public Dictionary<int, string> SiCategories { get; set; } = new Dictionary<int, string>();

    public List<RiskImportModel> Risks { get; set; }
    public List<SpareImportModel> Spares { get; set; }
    public List<TaskImportModel> Tasks { get; set; }
    public List<TaskPlanReportItemModel> TaskPlan { get; set; }

    public RiskExportViewModel(
        ILoggerFactory loggerFactory, 
        DialogService dialogService, 
        ILookupManager lookupManager,
        IObjectManager objectManager,
        IStringLocalizer<ReportPage> localizer,
        IDropdownManager dropdownManager,
        ReportViewQueryParams queryParams, 
        IRiskExportManager riskExportManager) : base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        QueryParams = queryParams;
        _dropdownManager = dropdownManager;
        _objectManager = objectManager;
        _riskExportManager = riskExportManager;
        _localizer = localizer;
    }
    public string TabTaskPlanTitle { get; set; }
    public string TabSparesTitle { get; set; }
    public string TabTasksTitle { get; set; }
    public string TabRiskTitle { get; set; }

    public override void OnInitialized()
    {
        Scenarios = _dropdownManager.GetScenarioDict();
        Scenarios = (new Dictionary<int, string> { { 0, "Please Select" } }).Concat(Scenarios).ToDictionary(k => k.Key, v => v.Value);

        RiskObjects = _dropdownManager.GetRiskObjectDict();
        RiskObjects = (new Dictionary<int, string> { { 0, "Please Select" } }).Concat(RiskObjects).ToDictionary(k => k.Key, v => v.Value);

        SiCategories = _dropdownManager.GetSiCategoriesDict();
        ObjectLevels = _objectManager.GetObjectLevelNames();

        SelectedRiskObject = RiskObjects.Keys.FirstOrDefault();

        SetTitlesTabs();
    }

    public void UpdateRiskObjectDropdown()
    {
        if(SelectedScenario == 0)
        {
            RiskObjects = _dropdownManager.GetRiskObjectDict();
            RiskObjects = (new Dictionary<int, string> { { 0, "Please Select" } }).Concat(RiskObjects).ToDictionary(k => k.Key, v => v.Value);
        }
        else
        {
            RiskObjects = _dropdownManager.GetRiskObjectDictByScenarioId(SelectedScenario);
            RiskObjects = (new Dictionary<int, string> { { 0, "Please Select" } }).Concat(RiskObjects).ToDictionary(k => k.Key, v => v.Value);
        }
    }

    public void GetData()
    {
        Risks = _riskExportManager.GetRisksForExport(SelectedRiskObject);
        Tasks = _riskExportManager.GetTasksByRiskObject(SelectedRiskObject);
        Spares = _riskExportManager.GetSparesByRiskObject(SelectedRiskObject);
        TaskPlan = _riskExportManager.GetTaskPlanByRiskObject(SelectedRiskObject);
    }

    public string GetRiskTabHeader() => $"{TabRiskTitle} ({Risks?.Count ?? 0})";

    public string GetTaskTabHeader() => $"{TabTasksTitle} ({Tasks?.Count ?? 0})";

    public string GetSpareTabHeader() => $"{TabSparesTitle} ({Spares?.Count ?? 0})";

    public string GetTaskPlanTabHeader() => $"{TabTaskPlanTitle} ({TaskPlan?.Count ?? 0})";

    private void SetTitlesTabs()
    {
        TabTaskPlanTitle = _localizer["RpTaskPLanTabTxt"];
        TabSparesTitle = _localizer["RpSparesTabTxt"];
        TabTasksTitle = _localizer["RpActionsTabTxt"];
        TabRiskTitle = _localizer["RpRiskTabTxt"];
    }
}