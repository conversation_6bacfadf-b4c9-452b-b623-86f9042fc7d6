using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.LCC;

public class NewLCCWidgetViewModel : BaseViewModel
{
    private IRiskAnalysisManager _riskAnalysisManager { get; }
    private ILCCManager _lccManager { get; }
    private IDropdownManager _dropdownManager { get; }

    public NewLCCWidgetViewModel(
        ILoggerFactory loggerFactory,
        IRiskAnalysisManager riskAnalysisManager,
        ILookupManager lookupManager, 
        ILCCManager lccManager,
        IDropdownManager dropdownManager,
        DialogService dialogService) : base(loggerFactory,
        lookupManager, dialogService)
    {
        _riskAnalysisManager = riskAnalysisManager;
        _lccManager = lccManager;
        _dropdownManager = dropdownManager;
    }

    [Parameter] public int ScenarioId { get; set; }

    [Parameter] public EventCallback<int> CallBack { get; set; }

    public Dictionary<int, string> RiskObjects { get; set; }

    public int RiskObjectId { get; set; }

    public int? LccRiskObjectId { get; set; }

    public NewLccModel NewLcc { get; set; } = new NewLccModel();
    
    public bool IsBusy { get; set; }

    public override void OnInitialized()
    {
        NewLcc.ScenarioId = ScenarioId;
        RiskObjects = _dropdownManager.GetRiskObjectDictByScenarioId(ScenarioId);
    }

    public async Task Confirm()
    {
        IsBusy = true;

        var tree = _riskAnalysisManager.GetRisksTreeForLcc(RiskObjectId);

        _lccManager.DeleteLccByRiskObject(RiskObjectId);
        CreateLccCollection(tree, true);

        IsBusy = false;

        if (CallBack.HasDelegate)
            await CallBack.InvokeAsync(LccRiskObjectId ?? 0).ConfigureAwait(false);

        LccRiskObjectId = null;
        _dialogService.Close();
    }

    private void CreateLccCollection(TreeNodeGeneric<RiskTreeObject> node, bool setReplacementValue)
    {
        var newLcc = _lccManager.CreateLcc(new NewLccModel { SelectedNode = node, ScenarioId = ScenarioId }, setReplacementValue);
        
        if(node.Parent != null)
            LccRiskObjectId ??= newLcc;

        foreach (var item in node.Nodes)
        {
            item.Parent.Source.LccId = newLcc;
            CreateLccCollection(item, false);
        } 
    }
}