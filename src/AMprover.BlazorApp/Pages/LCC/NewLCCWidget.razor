@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<NewLCCWidgetViewModel>
@inject IStringLocalizer<LCCPage> _localizer

<div class="mb-4">
    <AMDropdown Data=@BindingContext.RiskObjects
                @bind-Value=@BindingContext.RiskObjectId
                Required=true
                Label=@_localizer["LccWRiskObjLbl"] />
</div>


<RadzenButton 
    Click=@BindingContext.Confirm 
    IsBusy=@BindingContext.IsBusy 
    Text=@_localizer["LccWCreateBtn"] 
    Disabled=@(BindingContext.RiskObjectId == 0) />


@code
{
    [Parameter] public int ScenarioId { get; set; }
    [Parameter] public EventCallback<int> CallBack { get; set; }
}
