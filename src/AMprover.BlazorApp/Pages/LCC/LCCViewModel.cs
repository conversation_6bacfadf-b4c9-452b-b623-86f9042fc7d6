using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Components.Pagination;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Pages.LCC;

public class LCCViewModel : BaseViewModel
{
    private ILCCManager _lccManager { get; }
    private IScenarioManager _scenarioManager { get; }
    private IPageNavigationManager _pageNavigationManager { get; }
    private NavigationManager _navigationManager { get; }
    private IDropdownManager _dropdownManager { get; }
    private IStringLocalizer<LCCPage> _localizer { get; }
    public bool _Pmo { get; set; } = false;

    public LCCViewModel(ILoggerFactory loggerFactory, IScenarioManager scenarioManager, ILCCManager lccManager,
        DialogService dialogService, ILookupManager lookupManager, NavigationManager navigationManager,
        IDropdownManager dropdownManager, IStringLocalizer<LCCPage> localizer,
        IPageNavigationManager pageNavigationManager) : base(loggerFactory, lookupManager)
    {
        _scenarioManager = scenarioManager;
        _lccManager = lccManager;
        _dialogService = dialogService;
        _pageNavigationManager = pageNavigationManager;
        _navigationManager = navigationManager;
        _dropdownManager = dropdownManager;
        _localizer = localizer;
    }

    [Parameter] public int? LccId { get; set; }

    public List<ScenarioModel> RiskScenarios { get; set; }

    public int SelectedScenario { get; set; }

    public TreeGeneric<LccTreeObject> LccTree { get; set; } = new TreeGeneric<LccTreeObject>();

    public LccTreeObject CurrentLcc { get; set; }

    public LccModel SelectedLcc { get; set; }

    public LccDetailModel SelectedLccDetail { get; set; }

    public string ErrorText { get; set; }

    public bool ShowError { get; set; }
    public bool LccExclude { get; set; }
    public bool ShowPmoCheckbox { get; set; }
    public bool ShowActualDates { get; set; }

    public RadzenTabs LccTabs { get; set; }

    public RadzenTabs TreeTab { get; set; }

    public RadzenTabs LccDetailsTab { get; set; }

    public RadzenChart OptimalLifeTimeGraph { get; set; }
    public RadzenChart RealExpenditureGraph { get; set; }
    public UtilityGrid<LccDetailModel> OptimalCostsGrid { get; set; }
    public UtilityGrid<LccDetailModel> OptimalCostsGridPmo { get; set; }
    public UtilityGrid<LccDetailModel> RealCostsGrid { get; set; }
    public UtilityGrid<LccEffectDetailModel> OptimalCostEffectDetailsGrid { get; set; }
    public UtilityGrid<LccEffectDetailModel> OptimalCostEffectDetailsGridPmo { get; set; }
    public UtilityGrid<LccEffectDetailModel> RealCostEffectDetailsGrid { get; set; }
    public UtilityGrid<TaskModel> TasksGrid { get; set; }
    public UtilityGrid<RiskModel> RisksGrid { get; set; }

    public Paginator Paginator { get; set; }

    public Dictionary<string, Dictionary<int, string>> TaskDropDownOverrides { get; set; }
    public Dictionary<string, Dictionary<int, string>> RiskDropDownOverrides { get; set; }

    public override void OnInitialized()
    {
        RiskScenarios = _scenarioManager.GetAllScenariosSorted();

        TaskDropDownOverrides = _dropdownManager.GetTaskModelDropDownOverrides();
        RiskDropDownOverrides = _dropdownManager.GetRiskModelDropDownOverrides();

        if (LccId != null)
        {
            SelectedLcc = _lccManager.GetLccDetailed(LccId.Value);
            SelectedScenario = SelectedLcc.ScenarioId
                ?? SelectedLcc.RiskObject?.ScenarioId
                ?? RiskScenarios.FirstOrDefault()?.Id
                ?? 0;

            LccTree.Initialize(_lccManager.GetTreeViewForScenario(SelectedScenario));
            _pageNavigationManager.SetSelectedScenario(SelectedScenario);

            var node = LccTree.GetLccTreeNode(LccId.Value);

            if (node != null)
                LccTree.SelectNode(node);
        }
        else
        {
            SelectedScenario = _pageNavigationManager.GetSelectedScenario() ?? RiskScenarios.FirstOrDefault()?.Id ?? 0;
            SelectFirstLcc();
        }

        if (SelectedLcc != null)
        {
            SelectedLccDetail = SelectedLcc.Details.FirstOrDefault();
            ShowPmoCheckbox = SelectedLcc.RiskObject?.AnalysisType == "PMO";
        }

        ShowActualDates = true; //ToDo: link on LccStartDate>1100
    }

    private void ReloadGrids()
    {
        LccTabs?.Reload();
        TasksGrid?.BindingContext?.Grid?.Reload();
        OptimalCostsGrid?.BindingContext?.Grid?.Reload();
        RealCostsGrid?.BindingContext?.Grid?.Reload();
        RealCostEffectDetailsGrid?.BindingContext?.Grid?.Reload();
        OptimalCostEffectDetailsGrid?.BindingContext?.Grid?.Reload();
    }

    public void SelectScenario(object args)
    {
        SelectedScenario = (int)args;
        _pageNavigationManager.SetSelectedScenario(SelectedScenario);
        LccTree.Initialize(_lccManager.GetTreeViewForScenario(SelectedScenario));
    }

    public void ClickTreeNode(TreeNodeGeneric<LccTreeObject> node)
    {
        CurrentLcc = node.Source;

        if (CurrentLcc.Id.HasValue)
        {
            if (SelectedLcc?.Id != CurrentLcc.Id)
            {
                SelectedLcc = _lccManager.GetLccDetailed(CurrentLcc.Id.Value);
                _pageNavigationManager.SavePageQueryString($"/lcc/{CurrentLcc.Id}", string.Empty);
                _navigationManager.NavigateTo($"/lcc/{CurrentLcc.Id}");
            }

            SelectedLccDetail = SelectedLcc?.Details.FirstOrDefault();
            LccTabs?.Reload();
            UpdatePaginator(CurrentLcc.Id);
        }
    }

    public void ValidLCCSubmitted(EditContext editContext)
    {
        var task = (LccModel)editContext.Model;
        CreateOrUpdateObjectAndNavigate(task);
    }

    public void SaveAndReCalculateLcc()
    {
        CreateOrUpdateObjectAndNavigate(SelectedLcc);
    }

    private void CreateOrUpdateObjectAndNavigate(LccModel lcc)
    {
        SelectedLcc = _lccManager.CalculateLcc(lcc);
        ReloadGrids();
        _logger.LogInformation($"Creating a {nameof(lcc)} succeeded. Name = {lcc.Name}, ID = {lcc.Id}.");
    }

    public void InvalidLccSubmitted(EditContext editContext)
    {
        ErrorText = "Update NOT executed, invalid form submitted";
        _logger.LogWarning($"Invalid form submitted for {nameof(SelectedLcc)} with Id {SelectedLcc.Id}.");
        _logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject((RiskObjectModel)editContext.Model));
    }

    public void OpenNewLccWidget()
    {
        _dialogService.Open<NewLCCWidget>
        (_localizer["LccWHeaderTxt"],
            new Dictionary<string, object>
            {
                {nameof(NewLCCWidget.ScenarioId), SelectedScenario},
                {nameof(NewLCCWidget.CallBack), EventCallback.Factory.Create<int>(this, UpdateTree)}
            });
    }

    private void UpdateTree(int lccRiskObjectId)
    {
        var forceReload = SelectedLcc == null;

        SelectFirstLcc(lccRiskObjectId);
        SaveAndReCalculateLcc();

        if (forceReload)
            _navigationManager.NavigateTo(_navigationManager.Uri, true);
    }

    public void DeleteLccFromTree(LccTreeObject lccTreeObject)
    {
        if (!(lccTreeObject.Id > 0))
            return;

        _lccManager.DeleteLcc(lccTreeObject.Id.Value);

        if (lccTreeObject.Id == SelectedLcc.Id)
            SelectFirstLcc();
    }

    public int GetInitialPaginatorValue()
    {
        var node = LccTree.GetLccTreeNode(LccId ?? 0);
        return GetLccTreeNodes().IndexOf(node);
    }

    public void PaginatorCallback(int lccIndex)
    {
        var treeNode = GetLccTreeNodes()?.Skip(lccIndex)?.FirstOrDefault();

        if (treeNode != null)
        {
            LccTree.SelectNode(treeNode);
            ClickTreeNode(treeNode);
        }
    }

    private void UpdatePaginator(int? lccId)
    {
        lccId ??= LccId;

        if (Paginator != null)
        {
            var node = GetLccTreeNodes().FirstOrDefault(x => x.Id == lccId);
            if (node != null)
                Paginator.BindingContext.SetCurrentExternally(GetLccTreeNodes().IndexOf(node));
        }
    }

    public int GetScenarioCount() => GetLccTreeNodes().Count;

    private List<TreeNodeGeneric<LccTreeObject>> GetLccTreeNodes() => LccTree.GetFlattenedNodes();

    public void SetSelectedLcc(LccDetailModel model)
    {
        SelectedLccDetail = model;
    }

    private void SelectFirstLcc(int? lccId = null)
    {
        LccTree.Initialize(_lccManager.GetTreeViewForScenario(SelectedScenario));

        CurrentLcc = LccTree.Node?.Nodes?.FirstOrDefault()?.Source;

        SelectedLcc = CurrentLcc?.Id != null
            ? _lccManager.GetLccDetailed(CurrentLcc.Id.Value)
            : null;

        if(SelectedLcc != null)
            SelectedLccDetail = SelectedLcc.Details.FirstOrDefault();

        var node = LccTree.GetFlattenedNodes().FirstOrDefault(x => x.Id == lccId);
        node ??= LccTree.Node?.Nodes?.FirstOrDefault();

        if (node != null)
            LccTree.SelectNode(node);

        LccTabs?.Reload();
        LccDetailsTab?.Reload();
        TreeTab?.Reload();
    }

    public string FormatAsSelectedCurrency(object value)
    {
        Refresh(); //op oneigenlijke plaats aangeroepen, maar wil graag refreshen voor PMO viewcheckbox
        return value == null ? string.Empty : ((double)value).ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
    }
    public string FormatDecimalAsSelectedCurrency(object value)
    {
        Refresh(); //op oneigenlijke plaats aangeroepen, maar wil graag refreshen voor PMO viewcheckbox
        return value == null ? string.Empty : ((decimal)value).ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
    }
    public string FormatAsNumber(object value)
    {
        Refresh(); //op oneigenlijke plaats aangeroepen, maar wil graag refreshen voor PMO viewcheckbox
        return value == null ? string.Empty : ((int)value).ToString("F0", CultureInfo.CreateSpecificCulture(Currency));
    }

    public void Refresh()
    {
        if (SelectedLcc.RiskObject?.AnalysisType == "PMO")
            ShowPmoCheckbox = true;
        else ShowPmoCheckbox = false;
    }
    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    public void OpenRisksPage(RiskModel model) => OpenRisksPage(model.RiskObjectId, model.Id);

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    public void OpenRisksPage(TaskModel model) => OpenRisksPage((model.Risk?.RiskObjectId) ?? 0, model.MrbId ?? 0);

    private void OpenRisksPage(int riskObjectId, int riskId) =>
        _navigationManager.NavigateTo($"/value-risk-analysis/{riskObjectId}/risks/{riskId}");

    public string GetOptLifeTime()
    {
       return ShowActualDates ? _localizer["LccOptReplacementLbl"] : _localizer["LccOptLifeTimeLbl"];
    }
}