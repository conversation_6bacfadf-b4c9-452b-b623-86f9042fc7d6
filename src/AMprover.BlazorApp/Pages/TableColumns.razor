@page "/TableColumns/{ControlName}"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<TableColumnsViewModel>
@inject IStringLocalizer<TableColumns> _localizer

@if (BindingContext.GridColumns != null && BindingContext.GridColumns.Any())
{
    <EditForm Model="@BindingContext.GridColumns" OnValidSubmit=@BindingContext.ValidTaskSubmitted OnInvalidSubmit=@BindingContext.InvalidTaskSubmitted>
        <div class="row">
            <div class="col-6">
                <div class="form-group">
                    <div class="row neg-margin-small">
                        <div class="col-sm-2 bold">
                            Selection
                        </div>
                        <div class="col-sm-5 bold">
                            AMprover name
                        </div>
                        <div class="col-sm-5 bold">
                            Field name
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="form-group">
                    <div class="row neg-margin-small">
                        <div class="col-sm-3 bold">
                            Position
                        </div>
                        <div class="col-sm-3 bold">
                            Width
                        </div>
                        <div class="col-sm-6 bold">
                            Type
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <DataAnnotationsValidator/>
        @foreach (var column in BindingContext.GridColumns)
        {
            <div class="row">
                <div class="col-6">
                    <div class="form-group">
                        <div class="row neg-margin-small">

                            <div class="col-sm-2">
                                <RadzenCheckBox @bind-Value=@column.Visible TValue="bool" />
                            </div>
                            <div class="col-sm-5">
                                <RadzenLabel Text=@column.ColumnName  />
                            </div>
                            <div class="col-sm-5">
                                <RadzenTextBox @bind-Value=@column.ColumnHeader  />
                            </div>
                            
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <div class="row neg-margin-small">

                            <div class="col-sm-3">
                                <RadzenNumeric TValue="int" @bind-Value=@column.DisplayIndex />
                            </div>
                            <div class="col-sm-3">
                                <RadzenNumeric TValue="int" @bind-Value=@column.ColumnWidth />
                            </div>
                            @if (BindingContext.NumberFieldTypes.Contains(column.FieldType))
                            {
                                <div class="col-sm-6">
                                    <AMDropdown @bind-Value=@column.FieldType ContainerClass=@string.Empty Data=@BindingContext.NumberFieldTypes.ToDictionary(x => x, x => x.ToString()) />
                                </div>
                            }

                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="float-left py-3">
            <input type="submit" class="btn btn-primary" value=@_localizer["TcSaveBtn"] />
        </div>
        @if (ReseedFunction.HasDelegate)
        {
            <div class="float-right py-3">
                <RadzenButton Text=@_localizer["TcResetColumnsBtn"] Click=@(async _ => await ReseedFunction.InvokeAsync()) />
            </div>
        }

    </EditForm>
}

@code{

    [Parameter]
    public string ControlName { get; set; }

    [Parameter]
    public EventCallback<object> ReseedFunction { get; set; }

    [Parameter] 
    public EventCallback<object> SaveCallback { get; set; }

}