using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Components.Rams;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BlazorApp.Pages.Rams;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.Rams.Connectors;
using AMprover.BusinessLogic.Models.Rams.Converters;
using AMprover.BusinessLogic.Models.Rams.Undo;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using DeepCopy;
using Markdig;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NuGet.Packaging;
using Radzen;
using Radzen.Blazor;
using EnumExtensions = AMprover.BusinessLogic.Extensions.EnumExtensions;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BlazorApp.Pages;

public class ReleaseNotesPageViewModel : BaseViewModel
{
    private readonly IStringLocalizer<RamsEditPage> _localizer;

    private readonly IWebHostEnvironment _webHostEnvironment;

    public string MarkDownContent { get; set; }

    public ReleaseNotesPageViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager,
        IStringLocalizer<RamsEditPage> localizer, DialogService dialogService, IWebHostEnvironment webHostEnvironment) : base(
        loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _localizer = localizer;
        _webHostEnvironment = webHostEnvironment;
    }

    public override async Task OnInitializedAsync()
    {
        var path = Path.Combine(_webHostEnvironment.WebRootPath, $"ReleaseNotes.{Language[..2]}.md");

        //Fallback to english
        if (!File.Exists(path))
        {
            path = Path.Combine(_webHostEnvironment.WebRootPath, "ReleaseNotes.en.md");
        }

        var markDown = await File.ReadAllTextAsync(path);
        MarkDownContent = Markdown.ToHtml(markDown);
    }
}