using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.Data.Infrastructure;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using AMprover.BusinessLogic.Helpers;
using AMprover.Data.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;
using AMprover.Data.Extensions;
using AMprover.BusinessLogic.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using AMprover.Data.Entities.Identity;
using Microsoft.Extensions.DependencyInjection;
using AMprover.Data.Repositories;
using AutoMapper;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Constants;
using System.Threading.Tasks;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Pages.Administration;

public class DatabasesViewModel : BaseViewModel
{
    private readonly IConfiguration _config;
    private readonly string _userName;

    private IMapper _mapper { get; set; }

    private IServiceProvider _serviceProvider { get; set; }

    private IPortfolioManager _portfolioManager { get; set; }

    public List<PortfolioModel> Portfolios { get; set; }

    public PortfolioModel SelectedPortfolio { get; set; }

    private UserManager<UserAccount> _userManager { get; set; }

    public bool PerformRiskFmecaMigration { get; set; }

    public List<UserAccount> Users { get; set; }

    public RadzenTabs Tabs { get; set; }

    public List<List<string>> Output { get; set; }

    public DatabasesViewModel(
        AuthenticationStateProvider authenticationStateProvider,
        UserManager<UserAccount> userManager,
        ILoggerFactory loggerFactory,
        IPortfolioManager portfolioManager,
        IConfiguration config,
        IServiceProvider serviceProvider,
        IMapper mapper) : base(loggerFactory)
    {
        _config = config;
        _portfolioManager = portfolioManager;
        _userName = authenticationStateProvider.GetLoggedInUsername().Result;
        _userManager = userManager;
        _serviceProvider = serviceProvider;
        _mapper = mapper;
    }

    public override void OnInitialized()
    {
        Portfolios = _portfolioManager.GetAll();
        SelectedPortfolio = Portfolios.FirstOrDefault();
        Users = new List<UserAccount>(_userManager.Users.ToList());
        Users = SortUsers(Users);
        Output = new List<List<string>>();

        foreach (var user in Users)
        {
            foreach (var portfolio in LoadPortfoliosForUser(user))
            {
                Portfolios.Find(x => x.Id == portfolio.Id)?.Users.Add(user);
            }
        }

        foreach (var portfolio in Portfolios)
            portfolio.Users = SortUsers(portfolio.Users);

        base.OnInitialized();
    }

    public async Task UpdateAllDatabases()
    {
        Loading = true;
        Output = new List<List<string>>();

        await Print("Starting Update all databases", true);

        foreach (var portfolio in Portfolios)
        {
            await Print("##########################################", true);
            await Print("");

            await UpdateDatabase(portfolio);
        }

        Loading = false;

        await Print("##########################################", true);
        await Print("Finished", true);
    }

    private List<PortfolioModel> LoadPortfoliosForUser(UserAccount user)
    {
        // The IPortfolioProvider is used often, and in DI added as Scoped. Might lead to concurrency
        // conflicts. As the risk is high when doing update, we need to ensure - for this administration purpose - we
        // have a fresh set of data. On other UIs it is fine to keep that Scoped instead of going to Transient globally.
        using (var isolatedScope = _serviceProvider.CreateScope())
        {
            var isolatedPortfolioProvider =
                isolatedScope.ServiceProvider.GetRequiredService<IPortfolioRepository>();

            return isolatedPortfolioProvider.GetForUser(user.Id).Select(_mapper.Map<PortfolioModel>).ToList();
        }
    }

    public async Task UpdateSelectedDatabase()
    {
        Loading = true;
        Output = new List<List<string>>();
        await Print("Starting Update Single Databases", true);

        await UpdateDatabase(SelectedPortfolio);

        Loading = false;
    }

    public void AddUserToDatabase(UserAccount user) =>
        AssignUserToDatabase(user, true);

    public void RemoveUserFromDatabase(UserAccount user) =>
        AssignUserToDatabase(user, false);

    private void AssignUserToDatabase(UserAccount user, bool assign)
    {
        _portfolioManager.Assign(user.Id, SelectedPortfolio.Id, assign);

        if (assign)
        {
            SelectedPortfolio.Users.Add(user);
            SelectedPortfolio.Users = SortUsers(SelectedPortfolio.Users);
            SelectedPortfolio.Users = new List<UserAccount>(SelectedPortfolio.Users);
            return;
        }

        SelectedPortfolio.Users.Remove(user);
        SelectedPortfolio.Users = new List<UserAccount>(SelectedPortfolio.Users);
    }

    private async Task UpdateDatabase(PortfolioModel portfolio)
    {
        try
        {
            await Print($"Updating: {portfolio.DatabaseName}");

            var connectionString = GetConnectionString(portfolio.DatabaseName);
            var optionsBuilder = new DbContextOptionsBuilder<AssetManagementDbContext>();
            optionsBuilder.UseSqlServer(connectionString);
            var context = new AssetManagementDbContext(optionsBuilder.Options);
            await EnsureUpdatedWithLatestMigrations(context, portfolio.DatabaseName);

            if (PerformRiskFmecaMigration)
            {
                await ResetRiskImportModelGrid(context);
                await PerformRiskFmecaSelectionsMigration(context);
            }
        }
        catch (Exception ex)
        {
            Output.Add(new List<string> {$"Failed to check Migrations for {portfolio}"});

            while (ex != null)
            {
                Output.Last().Add($"-- {ex.Message}");
                ex = ex.InnerException;
            }
        }
    }

    private async Task EnsureUpdatedWithLatestMigrations(AssetManagementDbContext context, string databaseName)
    {
        await Print($"Checking {databaseName} for migrations");

        var pendingMigrations = context.Database.GetPendingMigrations().ToList();

        foreach (var migration in pendingMigrations)
        {
            await Print($"Will apply: {migration}");
        }

        if (pendingMigrations.Any())
        {
            try
            {
                context.Database.Migrate();
                await Print($"Finished Migrating {databaseName}");
            }
            catch (Exception ex)
            {
                await Print($"Failed to apply migration: {ex.Message}");

                while (ex != null)
                {
                    await Print($"-- {ex.Message}");
                    ex = ex.InnerException;
                }
            }
        }
        else
        {
            await Print($"No Migrations for {databaseName}.");
        }
    }

    private string GetConnectionString(string databaseName)
    {
        var defaultConnString = _config.GetConnectionString(ConnectionstringConstants.DefaultConnectionstringName);

        if (string.IsNullOrWhiteSpace(defaultConnString))
        {
            throw new IndexOutOfRangeException(
                $"Missing connection string in configuration. Name: {ConnectionstringConstants.DefaultConnectionstringName}.");
        }

        var connStringObject = new SqlConnectionStringBuilder(defaultConnString)
        {
            InitialCatalog = databaseName
        };

        return connStringObject.ToString();
    }

    private List<UserAccount> SortUsers(List<UserAccount> users) => 
        users.OrderBy(ua => ua.UserName.Split('@').Skip(1).FirstOrDefault()).ThenBy(ua => ua.UserName).ToList();

    public async Task PerformRiskFmecaSelectionsMigration(AssetManagementDbContext context)
    {
        var mrbs = context.Mrb
            .Include(x => x.RiskObject).ThenInclude(x => x.RiskObjFmeca)
            .ToList();

        var risks = mrbs
            .Select(x => _mapper.Map<RiskModel>(x))
            .ToList();

        var risksWithTooManyEffectColumns = 0;
        foreach (var risk in risks)
        {
            // For some databases there are a lot of corrupt Matrix Selections.
            // These grid will have multiple empty columns in it, we need to remove those.
            if(int.TryParse(risk.RiskObject.Fmeca.Data.FmecaMainGrid.EffectColumns, out int columnCount))
            {
                if(risk.MainDataGrid.TableColumns.Count > columnCount)
                {
                    risk.MainDataGrid.TableColumns.RemoveRange(columnCount, risk.MainDataGrid.TableColumns.Count - columnCount);
                    risksWithTooManyEffectColumns++;
                }
            }

            risk.FmecaSelections = risk.ToFmecaSelections();

            risk.MainDataGrid.CustomMtbfBefore = $"{risk.MtbfBefore:0.####}";
            risk.MainDataGrid.CustomMtbfAfter = $"{risk.MtbfAfter:0.####}";
            risk.MainDataGrid.CustomMtbfPmo = $"{risk.MtbfPmo:0.####}";

            var existingMrb = mrbs.Find(x => x.Mrbid == risk.Id);
            context.Update(_mapper.Map(risk, existingMrb));
        }

        if(risksWithTooManyEffectColumns > 0)
            await Print($"Removed Invalid Empty Fmeca Columns from {risksWithTooManyEffectColumns} Risks");

        await Print($"Updated Fmeca Selections on {risks.Count} Risks");
        await context.SaveChangesAndClearAsync(_userName);
    }

    public async Task ResetRiskImportModelGrid(AssetManagementDbContext context)
    {
        var gridColumns = context.LookupGridColumn.Where(x => x.ControlName == GridNames.Imports.Risks).ToList();
        context.LookupGridColumn.RemoveRange(gridColumns);
        context.SaveChangesAndClear(_userName);

        await Print($"Cleared {gridColumns.Count} invalid gridColumnSettings");
    }

    private async Task Print(string text, bool lineBreak = false)
    {
        if(lineBreak)
        {
            Output.Add(new() { text });
        }
        else
        {
            Output.LastOrDefault()?.Add(text);
        }

        Tabs.Reload();
        await Task.Delay(1);
    }

    #region Si Fixes
    private string MakeAbsCodeUniqueBeforePrimaryKeyChange(AssetManagementDbContext context)
    {
        var assets = context.Si.ToList();
        var duplicates = assets.GroupBy(x => x.SiName).Where(x => x.Count() > 1);
        var assetsUpdated = 0;

        foreach (var duplicate in duplicates)
        {
            for (int i = 1; i < duplicate.Count(); i++)
            {
                assetsUpdated++;
                var assetToRename = duplicate.Skip(i).First();
                assetToRename.SiName = $"{assetToRename.SiName}-{i + 1}";
                context.Update(assetToRename);
            }
        }

        context.SaveChangesAndClear(_userName);
        return $"{assetsUpdated} assets updated with duplicate SiName";
    }

    private string SetParentNamesForAbs(AssetManagementDbContext context)
    {
        var assets = context.Si.ToList();
        int assetsParentNameSet = 0;

        foreach (var child in assets.Where(x => x.SiPartOf != null))
        {
            var parent = assets.FirstOrDefault(x => x.SiId == child.SiPartOf);

            if (parent != null)
            {
                assetsParentNameSet++;
                child.SiParentName = parent.SiName;
                context.Update(child);
            }
        }

        context.SaveChangesAndClear(_userName);
        return $"{assetsParentNameSet} asset Parent Names set";
    }

    private string SetSiNameInCriticalityRanking(AssetManagementDbContext context)
    {
        var assets = context.Si.ToList();
        var criticalities = context.CriticalityRanking.ToList();
        var critsUpdated = 0;

        foreach (var crit in criticalities.Where(x => x.CritSiId > 0))
        {
            var asset = assets.FirstOrDefault(x => x.SiId == crit.CritSiId);

            if (asset == null) continue;
            crit.CritSiName = asset.SiName;
            context.Update(crit);
            critsUpdated++;
        }

        context.SaveChangesAndClear(_userName);
        return $"{critsUpdated} CriticalityRankings Updated";
    }
    #endregion
}