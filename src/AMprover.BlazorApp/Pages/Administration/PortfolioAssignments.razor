@using AMprover.Data.Constants
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<PortfolioAssignmentsViewModel>

<AuthorizeView Roles="@RoleConstants.Administrators">
    <Authorized>
        <h4>Portfolio asssignments: @BindingContext.CounterT</h4>
        @foreach (var portfolio in BindingContext.AllPortfolios)
        {
            _index++;
            var componentName = "Checkbox" + _index;
            var isChecked = BindingContext.IsAssigned(portfolio.Id);
            <RadzenCheckBox TValue="bool" @bind-Value=@isChecked
                            Change=@(args => BindingContext.OnPortfolioAssignmentChanged((bool?)args, portfolio.Id))/>
            <RadzenLabel Text=@portfolio.Name Component="@componentName" Style="margin-left: 5px;"/>
            <br/>
        }
        <small class="text-muted">(un)checking an portfolio will be saved directly.</small>
    </Authorized>
</AuthorizeView>

@code {
    int _index = 0;

    [Parameter]
    public string UserId { get; set; }

}