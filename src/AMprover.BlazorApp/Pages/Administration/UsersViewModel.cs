using AMprover.BlazorApp.Helpers;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Pages.Administration;

public class UsersViewModel : BaseViewModel
{
    private readonly UserManager<UserAccount> _userManager;
    private readonly NavigationManager _navigationManager;

    public UsersViewModel(
        ILoggerFactory loggerFactory, 
        UserManager<UserAccount> userManager, 
        NavigationManager navigationManager) : base(loggerFactory)
    {
        _userManager = userManager;
        _navigationManager = navigationManager;
    }

    ObservableRangeCollection<UserAccount> colUsers;
    public ObservableRangeCollection<UserAccount> ColUsers
    {
        get => colUsers ?? new ObservableRangeCollection<UserAccount>();
        set => Set(ref colUsers, value);
    }

    string strError;
    public string StrError
    {
        get => strError ?? "";
        set => Set(ref strError, value);
    }

    public override void OnInitialized()
    {
        // Get all the users
        GetUsers();
    }


    public void AddNewUser()
    {
        _navigationManager.NavigateTo($"/administration/users/{Guid.Empty}");
    }

    private void GetUsers()
    {
        // clear any error messages
        StrError = "";
        // Collection to hold users
        var users = _userManager.Users.ToList();
        users = users.OrderBy(ua => ua.UserName.Split('@').Skip(1).FirstOrDefault()).ThenBy(ua => ua.Name).ToList();

        ColUsers = new ObservableRangeCollection<UserAccount>(users);
    }
}