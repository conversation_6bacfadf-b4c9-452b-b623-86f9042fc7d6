using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using AMprover.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using DeepCopy;

namespace AMprover.BlazorApp.Pages.Administration;

public class UserViewModel : BaseViewModel, IEntityEditorViewModel
{
    private readonly IJSRuntime _jsRuntime;
    private readonly NavigationManager _navigationManager;
    private readonly IScopedUserService _scopedUserService;

    public UserViewModel(
        ILoggerFactory loggerFactory,
        IJSRuntime jsRuntime,
        NavigationManager navigationManager,
        IScopedUserService scopedUserService) : base(loggerFactory)
    {
        _navigationManager = navigationManager;
        _scopedUserService = scopedUserService;
        _jsRuntime = jsRuntime;
    }

    [Parameter] public string UserId { get; set; }

    public UserAccount UserAccount { get; set; }

    /// <summary>
    /// clone of the object we're editing to be able to 'reset to initial values'
    /// </summary>
    private UserAccount BeforeEditingUserAccount { get; set; }
    private string BeforeEditingUserAccountRole { get; set; }

    /// Options to display in the roles dropdown when editing a user
    public Dictionary<string,string> AvailableRoles { get; set; } = AMproverIdentityConstants.Roles.GetAll();

    public string CurrentRole { get; set; }

    public string ValidationError { get; set; }

    public string ValidationErrorPassword { get; set; }

    public override async Task OnInitializedAsync()
    {
        await LoadEditor();
    }

    private void NavigateToOverview()
    {
        _navigationManager.NavigateTo("/administration/users");
    }

    private void NavigateToUser(string userId)
    {
        _navigationManager.NavigateTo($"/administration/users/{userId}");
    }

    private async Task LoadEditor()
    {
        NewPassword = null;
        switch (EditorMode)
        {
            case EntityEditorMode.Update:
                await DetermineUserAndCurrentRole();
                break;
            default:
                UserAccount = new UserAccount { Id = UserId };
                CurrentRole = RoleConstants.Default;
                break;
        }

        // Is there a more elegant way to handle reset/cancel with Blazor EditForm
        // "The computer says: NO!"  https://github.com/dotnet/aspnetcore/issues/21110
        // Not, order does matter: do this after DetermineUserAndCurrentRole()
        BeforeEditingUserAccount = DeepCopier.Copy(UserAccount);
        BeforeEditingUserAccountRole = CurrentRole;
    }

    /// <summary>
    /// TODO: Refactor this nightmare before more roles are defined
    /// </summary>
    /// <returns></returns>
    private async Task DetermineUserAndCurrentRole()
    {
        UserAccount = await _scopedUserService.FindByIdAsync(UserId);

        var isAdmin = await _scopedUserService.IsInRoleAsync(UserAccount, RoleConstants.Administrators);
        if (isAdmin)
        {
            CurrentRole = RoleConstants.Administrators;
            return;
        }

        var isReadonly = await _scopedUserService.IsInRoleAsync(UserAccount, RoleConstants.ReadOnly);
        if (isReadonly)
        {
            CurrentRole = RoleConstants.ReadOnly;
            return;
        }

        CurrentRole = RoleConstants.Default;
    }

    public void Cancel()
    {
        UserAccount.Name = BeforeEditingUserAccount.Name;
        UserAccount.UserName = BeforeEditingUserAccount.UserName;
        UserAccount.Email = BeforeEditingUserAccount.Email;
        CurrentRole = BeforeEditingUserAccountRole;

        NavigateToOverview();
    }

    public async Task UpdateUserAccountValidFormSubmitted(EditContext editContext)
    {
        ValidationError = null;

        var updatedUserAccountViaUi = (UserAccount)editContext.Model;
        // We enforce the e-mail equals the username, both in lowercase.
        updatedUserAccountViaUi.UserName = updatedUserAccountViaUi.UserName.ToLower();
        updatedUserAccountViaUi.Email = updatedUserAccountViaUi.UserName;

        IdentityResult dbUpdateResult;

        switch (EditorMode)
        {
            case EntityEditorMode.Update:
                dbUpdateResult = await UpdateUserToDatabase(updatedUserAccountViaUi);
                if (CanContinueAfterDatabaseChanges(dbUpdateResult))
                {
                    await HandleRoleAssignmentUpdate();
                    NavigateToOverview(); // Go back to user overview, nothing to do left
                }
                break;
            case EntityEditorMode.Create:
                dbUpdateResult = await CreateUserToDatabase(updatedUserAccountViaUi);
                if (CanContinueAfterDatabaseChanges(dbUpdateResult))
                {
                    UserId = updatedUserAccountViaUi.Id;
                    await HandleRoleAssignmentUpdate();

                    NavigateToUser(updatedUserAccountViaUi.Id); // After creating the account, an ID has been assigned, can't use this.UserId (=0000-000...)
                }
                break;
        }
    }

    public void UpdateUserAccountInvalidFormSubmitted(EditContext editContext)
    {
        ValidationError = "User is NOT updated - invalid form submitted. Reload the page (browser refresh) and try again.";
    }

    private bool CanContinueAfterDatabaseChanges(IdentityResult dbUpdateResult)
    {
        if (dbUpdateResult.Succeeded) 
            return true;
        
        ValidationError = dbUpdateResult.Errors.Any() ? dbUpdateResult.Errors.First().Description : "Error updating user.";
        return false; // abort further processing.

    }

    private async Task<IdentityResult> CreateUserToDatabase(UserAccount updatedUserAccountViaUi)
    {
        // New users should always have a random GUID
        updatedUserAccountViaUi.Id = Guid.NewGuid().ToString();
        updatedUserAccountViaUi.EmailConfirmed = true;

        var createResult = await _scopedUserService.CreateAsync(updatedUserAccountViaUi, updatedUserAccountViaUi.PasswordHash);
        return createResult;
    }

    private async Task<IdentityResult> UpdateUserToDatabase(UserAccount updatedUserAccountViaUi)
    {
        // The UserManager is put into DI with .Scoped. Blazor is working differently, so DB concurrency errors might occur
        // Workaround is to (re)get a fresh record, update the properties you can change via the UI, and update it back.
        var freshUserFromDbToUpdate = await _scopedUserService.FindByIdAsync(UserId);
        freshUserFromDbToUpdate.Name = updatedUserAccountViaUi.Name;
        freshUserFromDbToUpdate.UserName = updatedUserAccountViaUi.UserName;
        freshUserFromDbToUpdate.Email = updatedUserAccountViaUi.Email;

        var updateResult = await _scopedUserService.UpdateAsync(freshUserFromDbToUpdate);

        return updateResult;
    }

    /// <summary>
    /// TODO: Refactor this nightmare before more roles are defined
    /// </summary>
    /// <returns></returns>
    private async Task HandleRoleAssignmentUpdate()
    {
        var freshDbUserAccount = await _scopedUserService.FindByIdAsync(UserId);
        var isAdministrator = await _scopedUserService.IsInRoleAsync(freshDbUserAccount, RoleConstants.Administrators);
        var isReadOnly = await _scopedUserService.IsInRoleAsync(freshDbUserAccount, RoleConstants.ReadOnly);

        switch(CurrentRole)
        {

            // Default user means not part of Admin Role and Not part of ReadOnly Role
            case RoleConstants.Default:

                if(isAdministrator)
                    await _scopedUserService.RemoveFromRoleAsync(freshDbUserAccount, RoleConstants.Administrators);

                if(isReadOnly)
                    await _scopedUserService.RemoveFromRoleAsync(freshDbUserAccount, RoleConstants.ReadOnly);

                break;

            // Administator has Admin Role and does not have ReadOnly Role
            case RoleConstants.Administrators:

                if(!isAdministrator)
                    await _scopedUserService.AddToRoleAsync(freshDbUserAccount, RoleConstants.Administrators);

                if(isReadOnly)
                    await _scopedUserService.RemoveFromRoleAsync(freshDbUserAccount, RoleConstants.ReadOnly);
                break;

            // ReadOnly has ReadOnly Role, and does not have Admin Role
            case RoleConstants.ReadOnly:

                if (isAdministrator)
                    await _scopedUserService.RemoveFromRoleAsync(freshDbUserAccount, RoleConstants.Administrators);

                if (!isReadOnly)
                    await _scopedUserService.AddToRoleAsync(freshDbUserAccount, RoleConstants.ReadOnly);

                break;
        }
    }

    public string NewPassword { get; set; }

    public EntityEditorMode EditorMode
    {
        get
        {
            var userGuid = Guid.Parse(UserId);
            return userGuid == Guid.Empty ? EntityEditorMode.Create : EntityEditorMode.Update;
        }
    }

    public async Task UpdatePassword()
    {
        using (var isolatedScope = _serviceProvider.CreateScope())
        {
            var isolatedUserManager = isolatedScope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();

            if (!string.IsNullOrWhiteSpace(NewPassword))
            {
                var freshUserAccount = await isolatedUserManager.FindByIdAsync(UserId);
                var resetToken = await isolatedUserManager.GeneratePasswordResetTokenAsync(freshUserAccount);
                var userAdjustmentResult = await isolatedUserManager.ResetPasswordAsync(freshUserAccount, resetToken, NewPassword);
                if (userAdjustmentResult.Succeeded == false)
                {
                    ValidationErrorPassword = userAdjustmentResult.Errors.Any() ? string.Join('+', userAdjustmentResult.Errors.Select(err => err.Description)) : "Error updating password";
                    return;
                }
            }
        }

        //reset property to inital value
        NewPassword = null;

        //go back to user overview
        NavigateToOverview();
    }

    public async Task DeleteUser()
    {
        // TODO: Makes this a nice Blazorize UI confirmation dialog -- maybe even a generic one.
        var confirmDelete = await _jsRuntime.InvokeAsync<bool>("confirm", "Do you want to delete it?");
        if (confirmDelete)
        {
            // Get the user
            var targetUser = await _scopedUserService.FindByIdAsync(UserId);
            if (targetUser != null)
            {
                try
                {
                    // Delete the user
                    await _scopedUserService.DeleteAsync(targetUser);
                }
                finally
                {
                    // Refresh Users
                    NavigateToOverview();
                }
            }
        }
    }
}