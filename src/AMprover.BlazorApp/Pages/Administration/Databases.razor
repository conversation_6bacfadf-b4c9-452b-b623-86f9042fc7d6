@page "/administration/databases"
@using AMprover.Data.Constants;
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<DatabasesViewModel>

<AuthorizeView Roles="@RoleConstants.Administrators">
    <Authorized>

        <h2 class="mb-3">Database Management</h2>

        <RadzenTabs @ref=BindingContext.Tabs>
            <Tabs>
                <RadzenTabsItem Text="Assign Members">

                    <div class="row">
                        <div class="col-3">
                            <h3>Databases</h3>
                            <AMDropdown Data=@BindingContext.Portfolios.ToDictionary(x => x, x => x.Name)                                       
                                        @bind-Value=@BindingContext.SelectedPortfolio/>
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="col-12">
                            <div class="row">
                                <div class="col-6">

                                    @if (BindingContext.SelectedPortfolio != null)
                                    {
                                        <h3>Users Assigned</h3>
                                        <UtilityGrid Data=@BindingContext.SelectedPortfolio.Users
                                                     RowSelectCallBack=@(args => BindingContext.RemoveUserFromDatabase((Data.Entities.Identity.UserAccount) args))
                                                     FileName=@GridNames.Admin.Users
                                                     AllowFiltering=true
                                                     AllowSorting=true/>

                                        <hr/>

                                        <h3>Users not assigned</h3>
                                        <UtilityGrid Data=@BindingContext.Users.Except(BindingContext.SelectedPortfolio.Users).ToList()
                                                     RowSelectCallBack=@(args => BindingContext.AddUserToDatabase((Data.Entities.Identity.UserAccount) args))
                                                     FileName=@GridNames.Admin.Users
                                                     AllowFiltering=true
                                                     AllowSorting=true/>
                                    }
                                </div>
                            </div>

                        </div>
                    </div>
                </RadzenTabsItem>

                <RadzenTabsItem Text="Migrations">

                    <div class="row">
                        <div class="col-3">
                            <h3>Databases</h3>
                            <AMDropdown Data=@BindingContext.Portfolios.ToDictionary(x => x, x => x.Name)
                                        @bind-Value=@BindingContext.SelectedPortfolio/>
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="col-12">
                            <div class="row">
                                <div class="col my-2 mx-2">

                                    <h3>Migrations</h3>

                                    <AMproverCheckbox @bind-Value=@BindingContext.PerformRiskFmecaMigration
                                                      Label="Perform Risk Fmeca Migrations"/>

                                    <RadzenButton Text="Update Selected Database"
                                                  Click=@(async () => await BindingContext.UpdateSelectedDatabase())
                                                  Disabled=@(BindingContext.Loading || BindingContext.SelectedPortfolio == null)/>

                                    <RadzenButton Text="Update All Databases"
                                                  Click=@(async () => await BindingContext.UpdateAllDatabases())
                                                  Disabled=@BindingContext.Loading/>

                                    <div>
                                        @if (BindingContext.Output?.Any() == true)
                                        {
                                            <hr/>
                                            <h3>Output</h3>
                                        }

                                        @if (BindingContext.Output != null)
                                        {
                                            foreach (var message in BindingContext.Output)
                                            {
                                                <p>
                                                    @foreach (var text in message)
                                                    {
                                                        @text
                                                        <br/>
                                                    }
                                                </p>
                                            }
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </Authorized>
</AuthorizeView>