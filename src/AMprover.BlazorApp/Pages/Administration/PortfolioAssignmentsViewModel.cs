using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using System;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Repositories;
using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.Administration;

public class PortfolioAssignmentsViewModel : BaseViewModel
{
    private readonly IPortfolioManager _portfolioManager;
    private readonly IServiceProvider _serviceProvider;
    private readonly IMapper _mapper;

    public PortfolioAssignmentsViewModel(
        ILoggerFactory loggerFactory, 
        IMapper mapper, IPortfolioManager portfolioManager,
        IServiceProvider serviceProvider) : base(loggerFactory)
    {
        _mapper = mapper;
        _portfolioManager = portfolioManager;
        _serviceProvider = serviceProvider;
    }

    [Parameter] public string UserId { get; set; }

    public string CounterT { get; set; }

    public List<PortfolioModel> AllPortfolios { get; set; }

    public List<PortfolioModel> UserPortfolios { get; set; }

    public override void OnInitialized()
    {
        AllPortfolios = _portfolioManager.GetAll().OrderBy(x => x.Name).ToList();
        LoadPortfoliosForUser();
    }

    private void LoadPortfoliosForUser()
    {
        // The IPortfolioProvider is used often, and in DI added as Scoped. Might lead to concurrency
        // conflicts. As the risk is high when doing update, we need to ensure - for this administration purpose - we
        // have a fresh set of data. On other UIs it is fine to keep that Scoped instead of going to Transient globally.
        using (var isolatedScope = _serviceProvider.CreateScope())
        {
            var isolatedPortfolioProvider =
                isolatedScope.ServiceProvider.GetRequiredService<IPortfolioRepository>();

            UserPortfolios = isolatedPortfolioProvider.GetForUser(UserId).Select(_mapper.Map<PortfolioModel>).ToList();

            //TODO: remove, just to show working of sfradiobutton
            foreach (var portfolio in AllPortfolios)
            {
                portfolio.Checked = UserPortfolios.FirstOrDefault(x => x.Id == portfolio.Id)?.DatabaseName;
            }

            CounterT = $"{UserPortfolios.Count} / {AllPortfolios.Count}";
        }
    }

    public void OnPortfolioAssignmentChanged(bool? value, int portfolioId)
    {
        _portfolioManager.Assign(UserId, portfolioId, value ?? false);
        LoadPortfoliosForUser();
    }

    public bool IsAssigned(int portfolioId)
    {
        return UserPortfolios.Any(p => p.Id == portfolioId);
    }
}