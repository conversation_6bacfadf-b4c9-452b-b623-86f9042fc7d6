@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<SapaOverviewViewModel>
@page "/sapa-overview"
@inject IStringLocalizer<SapaOverview> _localizer
@inject IGlobalDataService GlobalDataService;
@using AMprover.BusinessLogic.Models.Sapa

<h2>Sapa Overview</h2>

<div class="row header-navigation subheader-height">
    <div class="col-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
                <NavLink class="breadcrumb-item" href="/sapa-overview" Match="NavLinkMatch.All">
                    Sapa Overview
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-3 text-center">
    </div>
    <div class="col-5 text-right">
        <Information class="sapa-float-right float-right btn-centered my-2 ml-1 mr-2" 
                     DialogTitle=@_localizer["SapaInfoTitle"]
                     DialogContent=@_localizer["SapaInfoTxt"] />

        <RadzenButton Icon="print" class="my-2 mx-1"
                      Click=BindingContext.PrintPage
                      Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary />

        @if(BindingContext.SelectedNode?.SapaTreeNodeType is SapaTreeNodeType.RiskObject)
        {
            <RadzenButton Icon="add_circle_outline" class="my-2 mx-1"
            Text=@_localizer["SapaGenerateSapa"]
            Click=@BindingContext.GenerateSapa
            Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary
            Disabled=@(!GlobalDataService.CanEdit || (BindingContext.Sapa?.Status != BusinessLogic.Enums.Status.Budgeting && BindingContext.Sapa != null)) />
        }
        @if (BindingContext.SelectedNode?.SapaTreeNodeType is SapaTreeNodeType.RiskObject or SapaTreeNodeType.SapaCollection)
        {
            <RadzenButton Icon="edit" class="my-2 mx-1"
            Text=@_localizer["SapaUpdateSapa"]
            Click=BindingContext.UpdateSapa
            Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary
            Disabled=@(!GlobalDataService.CanEdit || BindingContext.Sapa?.Status != BusinessLogic.Enums.Status.Budgeting) />

            <RadzenButton Icon="delete" class="my-2 mx-1"
            Text=@_localizer["SapaDeleteSapa"]
            Click=BindingContext.DeleteSapa
            Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary
            Disabled=@(!GlobalDataService.CanEdit || BindingContext.Sapa?.Status != BusinessLogic.Enums.Status.Budgeting) />
        }

    </div>
</div>

<div class="row">
    <div class="col-sm-3 neg-margin">
        <h5>@_localizer["SapaTreeTxt"]</h5>

        <TreeComponent TItem=SapaTreeObject
        Treeview=@BindingContext.SapaTree
        NodeClickCallback=@BindingContext.ClickTreeNode 
        ExpandNodeWhenSelected="true" />

        <div class="task-message">
            <Radzen.Blazor.RadzenCheckBox @bind-Value=BindingContext.ShowTaskInFuture Label= /> @_localizer["SapaShowTasksInFuture"]
        </div>
    </div>
    <div class="col-9">
        <RadzenTabs @bind-SelectedIndex=@BindingContext.QueryParams.TopTabs Change=BindingContext.OnChangeTopTabsComponent @ref=@BindingContext.SapaTabs  >
            <Tabs>
                <RadzenTabsItem Text=@_localizer["SapaTabTxt"]>
                    @if (BindingContext.Sapa == null && BindingContext.SelectedNode != null)
                    {
                        <div class="row">
                            <div class="col-5 m-4">

                                @if (BindingContext.SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.RiskObject && BindingContext.SelectedSapaDict.Keys.Count > 0)
                                {
                                    <AMDropdown AllowFiltering="true"
                                    @bind-Value=@BindingContext.SelectedSapa
                                    Data=@BindingContext.SelectedSapaDict
                                    Label="Selected" />
                                }
                                else
                                {
                                    @_localizer["SapaNoSapaExists"]
                                }

                            </div>
                        </div>
                    }

                    @if (BindingContext.Sapa != null)
                    {
                        <div class="row">
                            <div class="col-8">
                                @if (BindingContext.GetNodeType() == SapaTreeNodeType.SapaCollection)
                                {
                                    <div class="row">
                                        <div class="col-2">
                                            <AMproverTextBox Label=@_localizer["SapaId"]
                                            Value="@BindingContext.SapaCollection.Id.ToString()"
                                            ReadOnly="true" />
                                        </div>
                                        <div class="col-10">
                                            <AMproverTextBox Label=@_localizer["SapaName"]
                                            @bind-Value="@BindingContext.SapaCollection.Name"
                                            Change=@BindingContext.SaveSapaCollection />
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div class="row">
                                        <div class="col-2">
                                            <AMproverTextBox Label=@_localizer["SapaId"]
                                            Value="@BindingContext.Sapa.Id.ToString()"
                                            ReadOnly="true" />
                                        </div>
                                        <div class="col-10">
                                            <AMproverTextBox Label=@_localizer["SapaName"]
                                            @bind-Value="@BindingContext.Sapa.Name"
                                            Change=@BindingContext.SaveSapa
                                            ReadOnly=@BindingContext.GetDisabled() />
                                        </div>
                                    </div>
                                }

                                <div class="row">
                                    <div class="col-6">
                                        <div class="sub-frame">
                                            <RadzenTabs class="sapa-side-tab" @bind-SelectedIndex=@BindingContext.QueryParams.BottomTabs TabPosition=TabPosition.Left @ref=@BindingContext.DetailTabs>
                                                <Tabs>
                                                    @foreach (var year in BindingContext.Sapa?.Years ?? new List<SapaYearModel>())
                                                    {
                                                        <RadzenTabsItem Text="@year.Year.ToString()">

                                                            <div class= "sapa-side-tab-content col-12 ml-1 pr-0">
                                                                <AMproverNumberInput Label=@(_localizer["SapaBudgetYear"] + " " + year.Year.ToString())
                                                                @bind-Value=@year.Budget
                                                                OnBlur=@BindingContext.SaveSapa
                                                                Min=0
                                                                Format="c0"
                                                                ReadOnly=@BindingContext.GetDisabled() />
                                                            </div>

                                                            <div class="sapa-side-tab-content ml-4 pr-2 overview-frame">
                                                                <div class="col-12 px-1">
                                                                    <div>
                                                                        <text class=""> @_localizer["SapaApproved"]:</text>
                                                                        <text class="currency-right">@BindingContext.FormatAsSelectedCurrency(year.BudgetApproved)</text>
                                                                    </div>
                                                                    <div>
                                                                        <text class=""> @_localizer["SapaBudgetRemain"]:</text>
                                                                        <text class="currency-right">@BindingContext.FormatAsSelectedCurrency(year.Budget - year.BudgetApproved)</text>
                                                                    </div>
                                                                    <br>
                                                                    <div>
                                                                        <text class=""> @_localizer["SapaRequested"]:</text>
                                                                        <text class="currency-right">@BindingContext.FormatAsSelectedCurrency(year.BudgetRequest)</text>
                                                                    </div>
                                                                    <div>
                                                                        <text class=""> @_localizer["SapaNotApproved"]:</text>
                                                                        <text class="currency-right">@BindingContext.FormatAsSelectedCurrency(year.BudgetApproved - year.BudgetRequest)</text>
                                                                    </div>
                                                                    <br>
                                                                </div>
                                                            </div>
                                                        </RadzenTabsItem>
                                                    }
                                                </Tabs>

                                            </RadzenTabs>
                                        </div>
                                    </div>
                                    <div class="col-6">

                                        @if (BindingContext.GetNodeType() == SapaTreeNodeType.SapaCollection)
                                        {
                                            <AMproverTextArea Label=@_localizer["SapaDescription"] 
                                            @bind-Value="@BindingContext.SapaCollection.Description" 
                                            Rows="2" 
                                            Change=@BindingContext.SaveSapaCollection 
                                            ReadOnly=@BindingContext.GetCollectionOrWorkPackageDisabled()/>
                                        }
                                        else
                                        {
                                            <AMproverTextArea Label=@_localizer["SapaDescription"] 
                                            @bind-Value="@BindingContext.Sapa.Description" 
                                            Rows="2" 
                                            Change=@BindingContext.SaveSapa 
                                            ReadOnly=@BindingContext.GetCollectionOrWorkPackageDisabled() />
                                        }

                                        <div class="row">
                                            <div class="col-6">
                                                <AMDropdown AllowFiltering="true"
                                                Data=@BindingContext.ItemStatusDict
                                                @bind-Value=@BindingContext.Sapa.Status
                                                Label=@_localizer["SapaStatus"]
                                                Required=false
                                                AllowClear=true
                                                Change=@BindingContext.UpdateSapaStatus
                                                Readonly=@(BindingContext.SelectedNode.SapaTreeNodeType != SapaTreeNodeType.RiskObject) />
                                            </div>

                                            <div class="col-6">
                                                <AMproverTextBox
                                                Label=@_localizer["SapaResponsible"]
                                                @bind-Value="@BindingContext.Sapa.Responsible"
                                                Change=@BindingContext.SaveSapa
                                                ReadOnly=@BindingContext.GetDisabled() />
                                            </div>
                                            <div class="col-12">

                                                @if (BindingContext.GetNodeType() == SapaTreeNodeType.SapaCollection)
                                                {
                                                    <AMproverTextArea Label=@_localizer["SapaRemark"]
                                                    @bind-Value="@BindingContext.SapaCollection.Remark"
                                                    Rows="2"
                                                    Change=@BindingContext.SaveSapaCollection
                                                    ReadOnly=@BindingContext.GetCollectionOrWorkPackageDisabled() />
                                                }
                                                else
                                                {
                                                    <AMproverTextArea Label=@_localizer["SapaRemark"]
                                                    @bind-Value="@BindingContext.Sapa.Remark"
                                                    Rows="2"
                                                    Change=@BindingContext.SaveSapa
                                                    ReadOnly=@BindingContext.GetCollectionOrWorkPackageDisabled() />
                                                }

                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-6">
                                                @if(BindingContext.SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.RiskObject)
                                                {
                                                    <AMDropdown AllowFiltering="true"
                                                    @bind-Value=@BindingContext.SelectedSapa
                                                    Data=@BindingContext.SelectedSapaDict
                                                    Label="Selected"
                                                    ReadOnly=@(!GlobalDataService.CanEdit || BindingContext.Sapa?.Status != BusinessLogic.Enums.Status.Budgeting) />
                                                }
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-4">

                                <div class="overview-frame my-3 mr-3 pt-4 px-4 pb-5">
                                    <div>
                                        <text class="bold">@_localizer["SapaSummaryTxt"] @BindingContext.Sapa.FirstYear.ToString() - @BindingContext.Sapa.LastYear.ToString():</text>
                                    </div>
                                    <br>
                                    <div>
                                        <text class="bold"> @_localizer["SapaBudget"]:</text>
                                        <text class="currency-bold">@BindingContext.FormatAsSelectedCurrency(BindingContext.Sapa.Budget)</text>
                                    </div>
                                    <br>
                                    <div>
                                        <text class="bold"> @_localizer["SapaTotalApproved"]:</text>
                                        <text class="currency-bold">@BindingContext.FormatAsSelectedCurrency(BindingContext.Sapa.BudgetApproved)</text>
                                    </div>
                                    <div>
                                        <text class="bold"> @_localizer["SapaBudgetRemain"]:</text>
                                        <text class="currency-bold">@BindingContext.FormatAsSelectedCurrency(BindingContext.Sapa.Budget - BindingContext.Sapa.BudgetApproved)</text>
                                    </div>
                                    <br>
                                    <div>
                                        <text class="bold"> @_localizer["SapaTotalRequested"]:</text>
                                        <text class="currency-bold">@BindingContext.FormatAsSelectedCurrency(BindingContext.Sapa.BudgetRequest)</text>
                                    </div>
                                    <div>
                                        <text class="bold"> @_localizer["SapaDeclined"]:</text>
                                        <text class="currency-bold">@BindingContext.FormatAsSelectedCurrency(BindingContext.Sapa.BudgetRequest - BindingContext.Sapa.BudgetApproved)</text>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["SapaGraphTxt"] >
                    <div class="row">
                    <div class="col-1">
                        <RadzenToggleButton class="sapa-setting-button" 
                                            Text=@BindingContext.sheqToggleButton
                                            ButtonStyle=ButtonStyle.Base 
                                            Size=ButtonSize.ExtraSmall
                                            Value=@BindingContext.SheqView
                                            Variant="Variant.Outlined" 
                                            Shade="Shade.Default" 
                                            ToggleShade="Shade.Darker" 
                                            Click=@BindingContext.ToggleSheqView />

                        <RadzenToggleButton class="sapa-setting-button"
                                            Text=@BindingContext.yearToggleButton
                                            ButtonStyle=ButtonStyle.Base
                                            Size=ButtonSize.ExtraSmall
                                            Value=@BindingContext.YearView
                                            Variant="Variant.Outlined"
                                            Shade="Shade.Default"
                                            ToggleShade="Shade.Darker"
                                            Click=@BindingContext.ToggleYearView />

                        <RadzenToggleButton class="sapa-setting-button"
                                            Text=@BindingContext.wpToggleButton
                                            Visible=@BindingContext.wpButtonVisible
                                            ButtonStyle=ButtonStyle.Base
                                            Size=ButtonSize.ExtraSmall
                                            Value=@BindingContext.WpView
                                            Variant="Variant.Outlined"
                                            Shade="Shade.Default"
                                            ToggleShade="Shade.Darker"
                                            Click=@BindingContext.ToggleWpView />
                    </div>
                    <div class="col-11">
                        <RadzenRow>
                            <RadzenColumn Size="3">
                                <RadzenStack>
                                    @if (BindingContext.ShowSheqView.TextValue == "true")
                                    {
                                        <RadzenChart @key=@BindingContext.ChartKey class="sapa-graph-layout">
                                            <RadzenLegend Visible="false" />
                                            <RadzenDonutSeries Data="@BindingContext.ChartDataPercOfBudget" Fills=@BindingContext.sheqColorScheme CategoryProperty="BudgetCriteria" ValueProperty="SapaCost"
                                                               InnerRadius="90" Radius="120" Strokes=@(new [] { "#606060", "#909090" }) StrokeWidth="1" TotalAngle="360" StartAngle="180">
                                                <ChildContent>
                                                    <RadzenSeriesDataLabels Visible="false" />
                                                </ChildContent>
                                                <TitleTemplate>
                                                    <div class="rz-donut-content">
                                                        <div>
                                                            <h2>@BindingContext.FormatAsSelectedCurrency(BindingContext.totalBudgetNeed)</h2>
                                                            <strong>Budget Need @BindingContext.budgetYear</strong>
                                                        </div>
                                                        <div>
                                                            <strong>@BindingContext.sheqDonutTitle</strong>
                                                        </div>
                                                    </div>
                                                </TitleTemplate>
                                                <TooltipTemplate Context="data">
                                                    <div>
                                                        <span>@data.BudgetCriteria: </span>@data.CostString
                                                    </div>
                                                </TooltipTemplate>
                                            </RadzenDonutSeries>
                                        </RadzenChart>
                                    }
                                    else
                                    {
                                        <RadzenChart @key=@BindingContext.ChartKey class="sapa-graph-layout">
                                        <RadzenLegend Visible="false" />
                                        @if (BindingContext.budgetSpent <= 1)
                                        {
                                            <RadzenDonutSeries Data="@BindingContext.ChartDataPercOfBudget" Fills=@(new [] { "#62993E", "#A1C490" }) CategoryProperty="BudgetCriteria" ValueProperty="SapaCost"
                                                               InnerRadius="90" Radius="120" Strokes=@(new [] { "#606060", "#909090" }) StrokeWidth="1" TotalAngle="360" StartAngle="180">
                                                <ChildContent>
                                                    <RadzenSeriesDataLabels Visible="false" />
                                                </ChildContent>
                                                <TitleTemplate>
                                                    <div class="rz-donut-content">
                                                        <div>
                                                            <h2>@BindingContext.FormatAsSelectedPercentage(BindingContext.budgetSpent)</h2>
                                                            <strong>Budget Utilization</strong>
                                                        </div>
                                                        <div>
                                                            <strong>@BindingContext.budgetYear</strong>
                                                        </div>
                                                    </div>
                                                </TitleTemplate>
                                                <TooltipTemplate Context="data">
                                                    <div>
                                                        <span>@data.BudgetCriteria: </span><strong>@data.CostString</strong>
                                                    </div>
                                                </TooltipTemplate>
                                            </RadzenDonutSeries>
                                        }
                                        else if (BindingContext.budgetSpent != null)
                                        {
                                            <RadzenDonutSeries Data="@BindingContext.ChartDataPercOfBudget" Fills=@(new [] { "#FF0000", "#62993E" }) CategoryProperty="BudgetCriteria" ValueProperty="SapaCost"
                                                               InnerRadius="90" Radius="120" Strokes=@(new [] { "#606060", "#909090" }) StrokeWidth="1" TotalAngle="360" StartAngle="180">
                                                <ChildContent>
                                                    <RadzenSeriesDataLabels Visible="false" />
                                                </ChildContent>
                                                <TitleTemplate>
                                                    <div class="rz-donut-content">
                                                        <div>
                                                            <h2 style="color:red;">@BindingContext.FormatAsSelectedPercentage(BindingContext.budgetSpent)</h2>
                                                            <strong>Budget Utilization</strong>
                                                        </div>
                                                        <div>
                                                            <strong>@BindingContext.budgetYear</strong>
                                                        </div>
                                                    </div>
                                                </TitleTemplate>
                                                <TooltipTemplate Context="data">
                                                    <div>
                                                        <span>@data.BudgetCriteria: </span><strong>@data.CostString</strong>
                                                    </div>
                                                </TooltipTemplate>
                                            </RadzenDonutSeries>
                                        }
                                    </RadzenChart>
                                    }
                                </RadzenStack>
                            </RadzenColumn>

                            <RadzenColumn Size="5">
                                <div class="sapa-graph-text">
                                    @BindingContext.graphTitle
                                </div>
                                @if (BindingContext.NoDep == 0)
                                {
                                    <div class="sapa-no-graph-text">No budgetdata is available, is the department linked in the RiskOrganizer module?</div>
                                }
                                else
                                {
                                    <RadzenChart @key=@BindingContext.ChartKey class="sapa-graph">
                                        <RadzenSeriesDataLabels Visible = "false"/>
                                        <RadzenColumnSeries Data="@BindingContext.ChartDataAssignedBudget" Fill="#EDDB00" Title="@BindingContext.assignedBudgetYear" CategoryProperty="Department" ValueProperty="SapaCost">
                                            <TooltipTemplate Context="data">
                                                <div>
                                                    <span>@_localizer["SapaBudgetAssigned"]</span> = <strong>@data.CostString</strong>
                                                </div>
                                            </TooltipTemplate>
                                        </RadzenColumnSeries>
                                        <RadzenColumnSeries Data="@BindingContext.ChartDataApprovedCost" Fill="#F79831" Title="@BindingContext.totalApprovedYear" CategoryProperty="Department" ValueProperty="SapaCost">
                                            <TooltipTemplate Context="data">
                                                <div>
                                                    <span>@_localizer["SapaApproved"]</span> = <strong>@data.CostString</strong>
                                                </div>
                                            </TooltipTemplate>
                                        </RadzenColumnSeries>
                                        <RadzenColumnSeries Data="@BindingContext.ChartDataDeclinedCost" Fill="#8F9296" Title="@BindingContext.totalDeclinedYear" CategoryProperty="Department" ValueProperty="SapaCost">
                                            <TooltipTemplate Context="data">
                                                <div>
                                                    <span>@_localizer["SapaDeclined"]</span> = <strong>@data.CostString</strong>
                                                </div>
                                            </TooltipTemplate>
                                        </RadzenColumnSeries>
                                        <RadzenColumnOptions Radius="5" />
                                        <RadzenValueAxis Formatter="@SapaOverviewViewModel.FormatAsEur">
                                            <RadzenValueAxis Min="0" />
                                            <RadzenGridLines Visible="true" />
                                        </RadzenValueAxis>
                                        <RadzenLegend Visible="true" Position="LegendPosition.Bottom" />
                                    </RadzenChart>
                                }
                            </RadzenColumn>

                            <RadzenColumn Size="3">
                                <RadzenStack>
                                    <RadzenChart @key=@BindingContext.ChartKey class="sapa-graph-layout">
                                        <RadzenLegend Visible="false" />
                                        <RadzenDonutSeries Data="@BindingContext.ChartDataSapaIndex" Fills=@(new [] { "#e9ecef", "#A1C490", "#e9ecef", "#62993E", "#e9ecef", "#FF0000" }) CategoryProperty="BudgetCriteria" ValueProperty="SapaPercentage"
                                                           InnerRadius="90" Radius="120" Strokes=@(new [] {"#909090", "#909090", "#606060", "#909090","#909090","#909090" }) StrokeWidth="1" TotalAngle="360" StartAngle="180">
                                                <ChildContent>
                                                    <RadzenSeriesDataLabels Visible="false" />
                                                </ChildContent>
                                                <TitleTemplate>
                                                    <div class="rz-donut-content">
                                                        <div>
                                                            <h2>@BindingContext.FormatAsSelectedNumber(BindingContext.AverageSapaIndex)</h2>
                                                            <strong>Average Sapa Index</strong>
                                                        </div>
                                                    <div>
                                                        <strong>@BindingContext.budgetYear</strong>
                                                    </div>
                                                    </div>
                                                </TitleTemplate>
                                                <TooltipTemplate Context="data">
                                                    <div>
                                                        <span>@data.BudgetCriteria: </span><strong>@data.CostString</strong>
                                                    </div>
                                                </TooltipTemplate>
                                            </RadzenDonutSeries>
                                    </RadzenChart>
                                </RadzenStack>
                            </RadzenColumn>

                        </RadzenRow>
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@BindingContext.GetFilterTabText() >
                    <div class="row">
                        <div class="col-12 col-md-6 col-lg-4">

                            <RadzenListBox class="riskorganizer-scenario-listbox"
                                Data=@BindingContext.SapaWorkPackages
                                @bind-Value=BindingContext.TempSelectedWorkPackages 
                                FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                AllowFiltering="true" Multiple="true" />

                            <RadzenButton Text="Apply"
                                Disabled=@BindingContext.ApplySelectedWorkPackagesBtnEnabled()
                                Click=@BindingContext.UpdateSelectedWorkPackages />

                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["SapaSettingTabTxt"]>
                    @if (BindingContext.Sapa != null)
                    {
                        <div class="row">
                            <div class="col-4">
                                <AMproverNumberInput Label=@_localizer["SapaCbiScore"]
                                                     @bind-Value="@BindingContext.Sapa.CbiScore"
                                                     OnBlur=@BindingContext.SaveSapa
                                                     ReadOnly=@BindingContext.GetDisabled() />
                            </div>
                            <div class="col-4">
                                <AMproverTextBox Label=@_localizer["SapaCbiItem"]
                                                 @bind-Value="@BindingContext.Sapa.CbiItem"
                                                 Change=@BindingContext.SaveSapa
                                                 ReadOnly=@BindingContext.GetDisabled() />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-4">
                                <AMDropdown AllowFiltering="true"
                                            Data=@BindingContext.ExecutorDict
                                            @bind-Value="@BindingContext.Sapa.ExecutorId"
                                            Label=@_localizer["SapaExecutor"]
                                            Required=false
                                            Change=@BindingContext.SaveSapa
                                            Readonly=@BindingContext.GetDisabled() />
                            </div>
                            <div class="col-4">
                                <AMDropdown AllowFiltering="true"
                                            Data=@BindingContext.InitiatorDict
                                            @bind-Value="@BindingContext.Sapa.InitiatorId"
                                            Label="@_localizer["SapaInitiator"]"
                                            Required=false
                                            Change=@BindingContext.SaveSapa
                                            ReadOnly=@BindingContext.GetDisabled() />
                            </div>
                        </div>
                        <br>

                        <br>
                        <div>
                            <Radzen.Blazor.RadzenCheckBox @bind-Value=BindingContext.ApproveForAllYears
                                                          Disabled=@BindingContext.GetDisabled() /> &nbsp @_localizer["SapaApproveForAllYears"]
                        </div>
                    }
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
</div>
<div class="row">
    <div class="col-12">

        @if (BindingContext.Sapa != null)
        {
            <RadzenTabs @bind-SelectedIndex=@BindingContext.QueryParams.BottomTabs @ref=@BindingContext.BottomTabs Change=BindingContext.OnChangeBottomTabsComponent>
                <Tabs >
                    @foreach (var year in BindingContext.Sapa?.Years ?? new List<SapaYearModel>())
                    {
                        <RadzenTabsItem Text="@year.Year.ToString()">

                            <div class="row">
                                <div class="col-12">

                                    @if (BindingContext.GetDisabled())
                                    {
                                        <UtilityGrid TItem=SapaDetailModel
                                                     @ref=BindingContext.SapaGrid
                                                     Data=@(BindingContext.ShowTaskInFuture ? year.Details.ToList() : year.Details.Where(x => x.CostYear1 > 0).ToList())
                                                     FileName=@GridNames.Sapa.Details
                                                     Interactive=true
                                                     DisableEditButton=true
                                                     AllowXlsExport=true
                                                     AllowFiltering=true
                                                     OpenPageCallback=@(args => BindingContext.OpenRisksPage((SapaDetailModel) args)) />
                                    }
                                    else if (BindingContext.SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.WorkPackage 
                                          && BindingContext.Sapa.Status == BusinessLogic.Enums.Status.Budgeting)
                                    {
                                        <UtilityGrid TItem=SapaDetailModel
                                                     @ref=BindingContext.SapaGrid
                                                     Data=@(BindingContext.ShowTaskInFuture ? year.Details.ToList() : year.Details.Where(x => x.CostYear1 > 0).ToList())
                                                     FileName=@GridNames.Sapa.Details
                                                     Interactive=true
                                                     DisableEditButton=true
                                                     AllowXlsExport=true
                                                     AllowFiltering=true
                                                     AcceptCallback=@(BindingContext.AcceptSapaDetail)
                                                     DeclineCallback=@BindingContext.DeclineSapaDetail
                                                     OpenPageCallback=@(args => BindingContext.OpenSapaDetailsPopup((SapaDetailModel) args))
                                                     OrderedListCallback=@BindingContext.AcceptAllCallBack
                                                     OrderedListCallbackBtnIcon="check"
                                                     OrderedListCallbackBtnTxt=@_localizer["SapaApproveAllBtn"]
                                                     OrderedListCallbackExplanation=@_localizer["SapaApproveAllText"] />
                                    }
                                    else 
                                    {
                                        <UtilityGrid TItem=SapaDetailModel
                                                     @ref=BindingContext.SapaGrid
                                                     Data=@(BindingContext.ShowTaskInFuture ? year.Details.ToList() : year.Details.Where(x => x.CostYear1 > 0).ToList())
                                                     FileName=@GridNames.Sapa.Details
                                                     Interactive=true
                                                     DisableEditButton=true
                                                     AllowXlsExport=true
                                                     AllowFiltering=true
                                                     OpenPageCallback=@(args => BindingContext.OpenSapaDetailsPopup((SapaDetailModel) args)) />
                                    }

                                </div>
                            </div>

                        </RadzenTabsItem>
                    }
                </Tabs>
            </RadzenTabs>
        }
    </div>
</div>
