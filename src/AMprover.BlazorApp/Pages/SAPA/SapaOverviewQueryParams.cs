using AMprover.BusinessLogic.Models.Sapa;
using AMprover.BusinessLogic.Navigation;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;

namespace AMprover.BlazorApp.Pages.SAPA
{
    public class SapaOverviewQueryParams : QueryParamsBase
    {
        public SapaOverviewQueryParams(
            ILoggerFactory loggerFactory, 
            NavigationManager navManager)
        : base(loggerFactory, navManager)
        {
        }

        SapaTreeNodeType? _nodeType;
        public SapaTreeNodeType? NodeType
        {
            get => _nodeType;
            set { _nodeType = value; UpdateUrl(); }
        }

        int? _nodeId;
        public int? NodeId
        {
            get => _nodeId;
            set { _nodeId = value; UpdateUrl(); }
        }

        int _topTabs;
        public int TopTabs
        {
            get => _topTabs;
            set { _topTabs = value; UpdateUrl(); }
        }

        int _bottomTabs;
        public int BottomTabs
        {
            get => _bottomTabs;
            set { _bottomTabs = value; UpdateUrl(); }
        }

        List<string> _workPackages;
        public List<string> WorkPackages
        {
            get => _workPackages;
            set { _workPackages = value; UpdateUrl(); }
        }
    }
}
