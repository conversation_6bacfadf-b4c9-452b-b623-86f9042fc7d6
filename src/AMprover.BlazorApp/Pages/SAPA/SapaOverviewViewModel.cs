using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Components.Sapa;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Sapa;
using AMprover.BusinessLogic.Models.Tree;
using AMprover.BusinessLogic.Constants;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Microsoft.JSInterop;
using Task = System.Threading.Tasks.Task;
using Microsoft.EntityFrameworkCore;
using AMprover.BlazorApp.Components;

namespace AMprover.BlazorApp.Pages.SAPA;

public class SapaOverviewViewModel : BaseViewModel
{
    private readonly ILookupManager _lookupManager;
    private readonly IStringLocalizer _localizer;
    private readonly ISapaOverviewManager _sapaOverviewManager;
    private IRiskOrganizerManager _riskOrganizerManager { get; }
    private NavigationManager _navigationManager { get; }
    private readonly IDropdownManager _dropdownManager;
    private readonly IPageNavigationManager _pageNavigationManager;

    public List<string> SelectedSapaWorkPackages { get; set; }
    public List<string> SapaWorkPackages { get; set; }
    public SapaModel Sapa { get; set; }
    public SapaCollectionModel SapaCollection { get; set; }
    public UtilityGrid<SapaDetailModel> SapaGrid { get; set; }
    public SapaOverviewQueryParams QueryParams { get; set; }
    public DepartmentModel Department { get; set; }

    public RadzenTabs SapaTabs { get; set; }
    public RadzenTabs DetailTabs { get; set; }
    public RadzenTabs BottomTabs { get; set; }

    public TreeGeneric<SapaTreeObject> SapaTree { get; set; } = new();
    public SapaTreeObject SelectedNode { get; set; }

    public bool ShowTaskInFuture { get; set; }

    public bool ApproveForAllYears
    {
        get => Sapa.ApproveForAllYears;
        set
        {
            Sapa.ApproveForAllYears = value;
            SaveSapa();
        }
    }

    public int ScenarioId => SelectedNode?.ScenarioId ?? 0;
    public int RiskObjectId => SelectedNode?.RiskObjectId ?? 0;
    public int CollectionId => SelectedNode?.SapaCollectionId ?? 0;
    public int SapaId => SelectedNode?.SapaId ?? 0;
    public int? WorkPackageId => SelectedNode?.WorkPackageId;
    public SapaTreeNodeType SapaType => SelectedNode?.SapaTreeNodeType ?? SapaTreeNodeType.Root;

    public Dictionary<int, string> RiskObjectDict { get; set; }
    public Dictionary<int, string> ScenarioDict { get; set; }
    public Dictionary<int?, string> InitiatorDict { get; set; }
    public Dictionary<int?, string> ExecutorDict { get; set; }
    public Dictionary<int?, string> StatusDict { get; set; }
    public Dictionary<int?, string> DepartmentDict { get; set; }
    public Dictionary<int?, string> sheqDict { get; set; }
    public Dictionary<int?, string> SelectedSapaDict { get; set; }
    public Dictionary<Status?, string> ItemStatusDict { get; set; }

    public int? SelectedSapa
    {
        get => _sapaOverviewManager.GetSelectedSapaCollection(RiskObjectId);
        set
        {
            _sapaOverviewManager.SetSelectedSapaCollection(RiskObjectId, value);
            SapaTree.Initialize(GetSapaTree());

            var existingNode = SapaTree.GetFlattenedNodes()
                .Find(x => x.Source.SapaTreeNodeType == SapaTreeNodeType.RiskObject
                           && x.Source.RiskObjectId == RiskObjectId);

            ClickTreeNode(existingNode);
        }
    }

    #region Chart Data

    public class DataItem
    {
        public string Department { get; set; }
        public int NoSapa { get; set; }
        public double SapaCost { get; set; }
        public string CostString { get; set; }

        public string BudgetCriteria { get; set; }
        public double SapaPercentage { get; set; }
    }

    public DataItem[] ChartDataAssignedBudget { get; set; }
    public DataItem[] ChartDataApprovedCost { get; set; }
    public DataItem[] ChartDataDeclinedCost { get; set; }
    public DataItem[] ChartDataPercOfBudget { get; set; }
    public DataItem[] ChartDataSapaIndex { get; set; }

    public string graphTitle { get; set; }
    public string budgetYear { get; set; }
    public string assignedBudgetYear { get; set; }
    public string totalApprovedYear { get; set; }
    public string totalDeclinedYear { get; set; }

    public decimal? budgetSpent { get; set; }
    public decimal? AverageSapaIndex { get; set; }
    public decimal? MinSapaIndex { get; set; }
    public decimal? MaxSapaIndex { get; set; }
    public decimal? MinBorderSapaIndex { get; set; }
    public decimal? MaxBorderSapaIndex { get; set; }
    decimal? budget { get; set; }
    decimal? budgetApproved { get; set; }
    decimal? budgetDeclined { get; set; }

    public string sheqDonutTitle { get; set; } = "SHEQ";
    public string sheqToggleButton { get; set; }
    public string wpToggleButton { get; set; }
    public string yearToggleButton { get; set; }
    public bool wpButtonVisible { get; set; } = true;
    public decimal totalBudgetNeed { get; set; }
    public string[] sheqItem { get; set; } = new string[8];
    public decimal[] sumSheqItemApproved { get; set; } = new decimal[8];
    public decimal[] sumSheqItemDeclined { get; set; } = new decimal[8];
    public int[] sumNoSheqItemApproved { get; set; } = new int[8];
    public int[] sumNoSheqItemDeclined { get; set; } = new int[8];
    public int NoSheqItems { get; set; } = 0;

    public decimal[,] DepBudgetAssigned { get; set; } = new decimal[6,6];
    public decimal[,] DepBudgetApproved { get; set; } = new decimal[6,6];
    public decimal[,] DepBudgetDeclined { get; set; } = new decimal[6,6];
    public string[,] BudgetDepartment { get; set; } = new string[6,6];
    public int NoDep { get; set; } = 0;

    public List<LookupSettingModel> LookupSettings { get; set; }
    public LookupSettingModel ShowSheqView { get; set; }
    public bool SheqView { get; set; }
    public LookupSettingModel ShowWpView { get; set; }
    public bool WpView { get; set; }
    public LookupSettingModel ShowYearView { get; set; }
    public bool YearView { get; set; }
    public List<string> TempSelectedWorkPackages { get; set; }

    #endregion

    public IEnumerable<string> UserRoles { get; set; }
    public bool IsAdminUser { get; set; }
    public bool IsMainEngineeringUser => UserRoles?.Contains(RoleConstants.MaintenanceEngineering) == true;
    public bool IsFinancialControlUser => UserRoles?.Contains(RoleConstants.FinancialControl) == true;

    // Fix for Duuplicate Key issue. This will not be needed when Radzen gets upgraded
    public string ChartKey { get; set; } = Guid.NewGuid().ToString();

    public SapaOverviewViewModel(
        ILoggerFactory loggerFactory,
        ILookupManager lookupManager,
        ISapaOverviewManager sapaOverviewManager,
        IRiskOrganizerManager riskOrganizerManager,
        NavigationManager navigationManager,
        IDropdownManager dropdownManager,
        AuthenticationStateProvider authenticationStateProvider,
        DialogService dialogService,
        LocalizationHelper localizationHelper,
        UserManager<UserAccount> userManager,
        IJSRuntime jsRuntime,
        IPageNavigationManager pageNavigationManager,
        SapaOverviewQueryParams queryParams) : base(loggerFactory, lookupManager, authenticationStateProvider,
        userManager, jsRuntime)
    {
        _dialogService = dialogService;
        _lookupManager = lookupManager;
        _sapaOverviewManager = sapaOverviewManager;
        _riskOrganizerManager = riskOrganizerManager;
        _navigationManager = navigationManager;
        _dropdownManager = dropdownManager;
        _pageNavigationManager = pageNavigationManager;

        QueryParams = queryParams;
        RiskObjectDict = _dropdownManager.GetSapaRiskObjectDict();
        ScenarioDict = _dropdownManager.GetSapaScenarioDict();
        InitiatorDict = _dropdownManager.GetInitiatorDict().ToNullableDictionary();
        ExecutorDict = _dropdownManager.GetExecutorDict().ToNullableDictionary();
        StatusDict = _dropdownManager.GetStatusDict().ToNullableDictionary();
        DepartmentDict = _dropdownManager.GetDepartmentDict().ToNullableDictionary();
        sheqDict = _dropdownManager.GetDepartmentDict().ToNullableDictionary();

        ItemStatusDict = new Dictionary<Status?, string>
        {
            {Status.Budgeting, "Budgeting"},
            {Status.Need_review, "Need Review"},
            {Status.Complete, "Complete"},
        };

        _localizer = localizationHelper.GetLocalizer<SapaOverview>();
    }

    public override void OnInitialized()
    {
        SapaWorkPackages = _sapaOverviewManager.GetSapaWorkPackages();
        SapaTree.Initialize(GetSapaTree());
        TempSelectedWorkPackages = new List<string>(QueryParams.WorkPackages ?? new List<string>());

        TreeNodeGeneric<SapaTreeObject> nodeToSelect = null;
        if (QueryParams.NodeType != null)
        {
            nodeToSelect = SapaTree.GetFlattenedNodes()
                .Find(x => x.Source.SapaTreeNodeType == QueryParams.NodeType
                           && x.Source.GetNodeId() == QueryParams.NodeId);
        }

        nodeToSelect ??= SapaTree.Node.Nodes.FirstOrDefault();

        if (nodeToSelect != null)
        {
            SapaTree.SelectNode(nodeToSelect);
            ClickTreeNode(nodeToSelect);
            SapaTree.ExpandTreeTillNode(nodeToSelect.Id);
        }

        LookupSettings = _lookupManager.GetLookupSettings();
        ShowSheqView =
            LookupSettings.FirstOrDefault(x =>
                x.Property.Equals(PropertyNames.ShowSheqView, StringComparison.OrdinalIgnoreCase)) ??
            new LookupSettingModel {Property = PropertyNames.ShowSheqView};
        SheqView = ShowSheqView.TextValue == "true";
        ShowYearView =
            LookupSettings.FirstOrDefault(x =>
                x.Property.Equals(PropertyNames.ShowYearView, StringComparison.OrdinalIgnoreCase)) ??
            new LookupSettingModel {Property = PropertyNames.ShowYearView};
        YearView = ShowYearView.TextValue == "true";
        ShowWpView =
            LookupSettings.FirstOrDefault(x =>
                x.Property.Equals(PropertyNames.ShowWpView, StringComparison.OrdinalIgnoreCase)) ??
            new LookupSettingModel { Property = PropertyNames.ShowWpView };
        WpView = ShowWpView.TextValue == "true";

        if (QueryParams.TopTabs == 1) OnChangeTopTabsComponent(1);
    }

    private void SavePageNavigation()
    {
        var uri = new Uri(_navigationManager.Uri);
        _pageNavigationManager.SavePageQueryString(uri.AbsolutePath, QueryParams.ToQueryString());
    }

    public override async Task OnInitializedAsync()
    {
        await SetupUserAccess();
    }

    public bool ApplySelectedWorkPackagesBtnEnabled()
    {
        return TempSelectedWorkPackages?.SequenceEqual(QueryParams.WorkPackages ?? new List<string>()) == true;
    }

    public async Task UpdateSelectedWorkPackages()
    {
        QueryParams.WorkPackages = new List<string>(TempSelectedWorkPackages);
        SapaTree.Initialize(GetSapaTree());

        var nodeToSelect = SapaTree.GetFlattenedNodes()
                .Find(x => x.Source.SapaTreeNodeType == QueryParams.NodeType
                        && x.Source.GetNodeId() == QueryParams.NodeId);

        nodeToSelect ??= SapaTree.Node.Nodes.FirstOrDefault();
        if (nodeToSelect != null)
        {
            SapaTree.SelectNode(nodeToSelect);
            ClickTreeNode(nodeToSelect);
            
        }

        // Force Refresh for bottomTabs Components
        await Task.Delay(1);
        OnChangeBottomTabsComponent(QueryParams.BottomTabs);
        SavePageNavigation();
    }

    private async Task SetupUserAccess()
    {
        UserRoles = (await GetCurrentUserRoles()).ToList();
        IsAdminUser = UserRoles?.Contains(RoleConstants.Administrators) == true ||
                      UserRoles?.Contains(RoleConstants.PortfolioAdministrators) == true;
    }

    public SapaTreeNodeType? GetNodeType()
    {
        return SelectedNode?.SapaTreeNodeType;
    }

    public void OnChangeTopTabsComponent(int index)
    {
        CalculateSapa();

        QueryParams.TopTabs = index;
        if (index == 1) 
        {
            SetChartData();
            if (SheqView)
                SetSheqData();
            else
                SetBudgetData();

            SetToggleBtn();
        }

        SavePageNavigation();
    }

    public void OnChangeBottomTabsComponent(int index)
    {
        CalculateSapa();

        QueryParams.BottomTabs = index;
        if (QueryParams.TopTabs == 1)
        {
            SetChartData();
            if (SheqView)
                SetSheqData();
            else
                SetBudgetData();
        }

        SavePageNavigation();
    }

    public bool GetDisabled()
    {
        if (IsFinancialControlUser || IsAdminUser) return false;

        if (Sapa?.Status != Status.Budgeting)
            return true;

        return SelectedNode?.SapaTreeNodeType != SapaTreeNodeType.WorkPackage;
    }

    public bool GetCollectionOrWorkPackageDisabled()
    {
        if (Sapa?.Status != Status.Budgeting)
            return true;

        return SelectedNode?.SapaTreeNodeType != SapaTreeNodeType.WorkPackage
               && SelectedNode?.SapaTreeNodeType != SapaTreeNodeType.SapaCollection;
    }

    public void ClickTreeNode(TreeNodeGeneric<SapaTreeObject> node)
    {
        SelectedNode = node.Source;
        SapaTree.SelectNode(node);
        QueryParams.NodeType = node.Source.SapaTreeNodeType;
        QueryParams.NodeId = node.Source.GetNodeId();

        if (GetNodeType() == SapaTreeNodeType.WorkPackage)
            GetSapa();
        else
        {
            GetCombinedSapa();

            if (GetNodeType() == SapaTreeNodeType.SapaCollection)
                SapaCollection = _sapaOverviewManager.GetSapaCollection(node.Source.SapaCollectionId ?? 0);
            else if (GetNodeType() == SapaTreeNodeType.RiskObject)
                SelectedSapaDict = _sapaOverviewManager.GetSelectedSapaDict(RiskObjectId);
        }

        SavePageNavigation();
    }

    public void GetCombinedSapa()
    {
        if (RiskObjectId > 0)
            Sapa = SelectedNode.SapaCollectionId > 0
                ? _sapaOverviewManager.GetCombinedSapaModelByCollection(CollectionId, QueryParams.WorkPackages)
                : _sapaOverviewManager.GetCombinedSapaModelByRiskObject(RiskObjectId, QueryParams.WorkPackages);
        else if (ScenarioId > 0)
            Sapa = _sapaOverviewManager.GetCombinedSapaModelByScenario(ScenarioId, QueryParams.WorkPackages);

        if (Sapa?.Years != null && QueryParams.BottomTabs > Sapa.Years.Count() - 1)
            QueryParams.BottomTabs = Sapa.Years.Count() - 1;

        OrderDetails();
        RefreshPage();
    }

    public void GetSapa()
    {
        Sapa = _sapaOverviewManager.GetSapaModelBySapaId(SapaId);

        if (QueryParams.BottomTabs > Sapa.Years.Count() - 1)
            QueryParams.BottomTabs = Sapa.Years.Count() - 1;

        OrderDetails();
        RefreshPage();
    }

    public void GenerateSapa()
    {
        if (RiskObjectId > 0)
        {
            if (!RiskObjectCanBeGenerated(RiskObjectId))
                return;

            _sapaOverviewManager.GenerateSapaByRiskObject(RiskObjectId, _localizer["SapaNoWorkpackage"]);
            SelectedSapaDict = _sapaOverviewManager.GetSelectedSapaDict(RiskObjectId);
        }
        else
        {
            throw new ArgumentException($"RiskObjId or ScenarioId has to be set");
        }

        SapaTree.Initialize(GetSapaTree());

        var existingNode = SapaTree.GetFlattenedNodes()
            .Find(x => x.Source.SapaTreeNodeType == SapaTreeNodeType.RiskObject
                       && x.Source.RiskObjectId == RiskObjectId);

        ClickTreeNode(existingNode);
    }

    private bool RiskObjectCanBeGenerated(int riskObjectId)
    {
        var validation = _sapaOverviewManager.ValidateGenerateSapa(riskObjectId);
        if (validation.LastYear - validation.FirstYear > validation.MaxYears)
        {
            _dialogService.Open<InformationDialog>("Validation Failed",
                new Dictionary<string, object>
                {
                        { "DialogContent", string.Format(_localizer["SapaMaxYearsError"], validation.FirstYear, validation.LastYear, validation.MaxYears) }
                },
                new DialogOptions { Width = "700px", Resizable = true, Draggable = true });

            return false;
        }
        return true;
    }

    private void OrderDetails()
    {
        foreach (var year in Sapa?.Years ?? new List<SapaYearModel>())
        {
            year.Details = year.Details.OrderByDescending(x => x.Approved).ThenByDescending(x => x.RiskSapaIndex);
        }
    }

    public void UpdateSapa()
    {
        switch (SelectedNode.SapaTreeNodeType)
        {
            case SapaTreeNodeType.SapaCollection:
                if (!RiskObjectCanBeGenerated(RiskObjectId))
                    return;

                _sapaOverviewManager.UpdateSapaCollection(SapaCollection.Id, _localizer["SapaNoWorkpackage"]);
                SapaCollection = _sapaOverviewManager.GetSapaCollection(CollectionId);
                SapaTree.Initialize(GetSapaTree());
                var existingNode = SapaTree.GetFlattenedNodes()
                    .Find(x => x.Source.SapaTreeNodeType == SapaTreeNodeType.SapaCollection
                               && x.Source.SapaCollectionId == CollectionId);
                ClickTreeNode(existingNode);
                break;

            case SapaTreeNodeType.RiskObject:
                if (!RiskObjectCanBeGenerated(RiskObjectId))
                    return;

                _sapaOverviewManager.UpdateSapaByRiskObject(RiskObjectId, _localizer["SapaNoWorkpackage"]);
                Sapa = _sapaOverviewManager.GetCombinedSapaModelByRiskObject(RiskObjectId, QueryParams.WorkPackages);
                SapaTree.Initialize(GetSapaTree());
                var existingNodeRo = SapaTree.GetFlattenedNodes()
                    .Find(x => x.Source.SapaTreeNodeType == SapaTreeNodeType.RiskObject
                               && x.Source.RiskObjectId == RiskObjectId);
                ClickTreeNode(existingNodeRo);
                break;

            default:
                throw new NotImplementedException($"{SelectedNode.SapaTreeNodeType} has not been implemented");
        }

        RefreshPage();
    }

    public void DeleteSapa()
    {
        TreeNodeGeneric<SapaTreeObject> nodeToSelect;

        switch (GetNodeType())
        {
            case SapaTreeNodeType.SapaCollection:
                nodeToSelect = SapaTree.SelectedNode.Parent;
                nodeToSelect.Nodes.Remove(SapaTree.SelectedNode);
                if (nodeToSelect.Nodes.Count > 0)
                    nodeToSelect = nodeToSelect.Nodes.FirstOrDefault();

                _sapaOverviewManager.DeleteBySapaCollection(CollectionId);
                break;

            case SapaTreeNodeType.RiskObject:
                nodeToSelect = SapaTree.SelectedNode;
                nodeToSelect.Nodes.Clear();
                _sapaOverviewManager.DeleteSapaByRiskObject(RiskObjectId);
                nodeToSelect.Nodes = nodeToSelect.Nodes
                    .Where(x => x.Source.SapaTreeNodeType != SapaTreeNodeType.WorkPackage).ToList();
                break;

            default:
                throw new ArgumentException($"{GetNodeType()} was not implemented");
        }

        Sapa = null;
        QueryParams.BottomTabs = 0;

        ClickTreeNode(nodeToSelect);
        RefreshPage();
    }

    public void AcceptAllCallBack(List<SapaDetailModel> sapaOrderedDetails)
    {
        var year = Sapa.Years.Skip(QueryParams.BottomTabs).FirstOrDefault();
        if (year == null) return;

        var budgetApproved = 0m;
        var detailsThisYear = sapaOrderedDetails.Where(x => x.CostYear1 > 0).ToList();
        detailsThisYear.ForEach(x => x.Approved = false);
        foreach (var detail in detailsThisYear)
        {
            if (budgetApproved + detail.CostYear1 > year.Budget)
                continue;

            budgetApproved += detail.CostYear1;
            SetSapaDetailAccepted(detail, true);
        }

        SaveSapa();
    }

    public void SaveSapaCollection()
    {
        SapaCollection = _sapaOverviewManager.SaveSapaCollection(SapaCollection);
        SapaTree.SelectedNode.Name = SapaCollection.Name;
    }

    public void SaveSapa()
    {
        if (GetDisabled())
            return;

        SapaTree.SelectedNode.Name = Sapa.Name;
        Sapa = _sapaOverviewManager.SaveSapa(Sapa);
        OrderDetails();
        RefreshPage();
    }

    public void UpdateSapaStatus()
    {
        if (SelectedNode?.RiskObjectId == null)
            return;

        if (Sapa.Status == Status.Need_review)
            _sapaOverviewManager.NeedReviewSapa(SelectedNode.RiskObjectId.Value);
        else
            _sapaOverviewManager.SetSapaStatus(SelectedNode.RiskObjectId.Value, Sapa.Status);
    }

    public void RefreshPage()
    {
        DetailTabs?.Reload();
        BottomTabs?.Reload();
        CalculateSapa();

        if (QueryParams.TopTabs == 1)
        {
            ChartKey = Guid.NewGuid().ToString();
            SetChartData();
            if (SheqView)
                SetSheqData();
            else
                SetBudgetData();
        }
    }

    public void ToggleSheqView()
    {
        ChartKey = Guid.NewGuid().ToString();
        ShowSheqView.TextValue = ShowSheqView.TextValue == "true" ? "false" : "true";
        _lookupManager.SaveLookupSettings(ShowSheqView);
        SheqView = ShowSheqView.TextValue == "true";
        SetToggleBtn();
        CalculateSapa();

        if (SheqView)
            SetSheqData();
        else
            SetBudgetData();
    }

    public void ToggleWpView()
    {
        ChartKey = Guid.NewGuid().ToString();
        ShowWpView.TextValue = ShowWpView.TextValue == "true" ? "false" : "true";
        _lookupManager.SaveLookupSettings(ShowWpView);
        WpView = ShowWpView.TextValue == "true";
        SetToggleBtn();
        CalculateSapa();
        SetChartData();
    }

    public void ToggleYearView()
    {
        ChartKey = Guid.NewGuid().ToString();
        ShowYearView.TextValue = ShowYearView.TextValue == "true" ? "false" : "true";
        _lookupManager.SaveLookupSettings(ShowYearView);
        YearView = ShowYearView.TextValue == "true";
        SetToggleBtn();
        CalculateSapa();
        SetChartData();
    }

    public void SetToggleBtn()
    {
        ChartKey = Guid.NewGuid().ToString();
        sheqToggleButton = SheqView ? sheqDonutTitle + " view" : "Budget view";
        wpToggleButton = WpView ? "Budget type view" : "General view";
        yearToggleButton = YearView ? "Year overview" : "Current year";

        if (YearView || GetNodeType() == SapaTreeNodeType.WorkPackage || GetNodeType() == SapaTreeNodeType.SapaCollection)
            wpButtonVisible = false;
        else
            wpButtonVisible = true;
    }

    public void AcceptSapaDetail(SapaDetailModel sapaDetail)
    {
        SetSapaDetailAccepted(sapaDetail, true);
        SaveSapa();
    }

    public void DeclineSapaDetail(SapaDetailModel sapaDetail)
    {
        SetSapaDetailAccepted(sapaDetail, false);
        SaveSapa();
    }

    private void SetSapaDetailAccepted(SapaDetailModel sapaDetail, bool value)
    {
        if (sapaDetail == null)
            return;

        sapaDetail.Approved = value;
        if (Sapa.ApproveForAllYears)
        {
            var tasksInOtherYears = Sapa.Years.SelectMany(x => x.Details).Where(x => x.TaskId == sapaDetail.TaskId);
            foreach (var t in tasksInOtherYears)
            {
                t.Approved = value;
            }
        }
    }

    public void CalculateSapa()
    {
        if (Sapa == null) return;
        
        //reset eventual graph data
        budget = 0;
        budgetSpent = 1;

        AverageSapaIndex = 0;
        MinSapaIndex = 0;
        MaxSapaIndex = 0;

        NoDep = 0;
        
        foreach (var year in Sapa.Years)
        {
            year.BudgetRequest = year.Details.Sum(x => x.CostYear1);
            year.BudgetApproved = year.Details.Where(x => x.Approved).Sum(x => x.CostYear1);
            //Left and Right Donut
            if (GetCurrentYear().Year == year.Year)
            {
                NoDep = 0; //Reset data (from previous selections)
                budgetYear = year.Year.ToString(CultureInfo.InvariantCulture);

                //Budget Utilization -> Left Donut (%)
                budget = year.Budget;
                budgetApproved = year.BudgetApproved;
                budgetDeclined = year.BudgetRequest - year.BudgetApproved;

                budgetSpent = budget != 0 && budgetApproved != 0 ? budgetApproved / (budget ?? (budgetApproved ?? 1)) : 1; // when no budget is given, budget spent = 100%

                //Sheq Data -> Left Donut (%)
                if (SheqView)
                {
                    totalBudgetNeed = (decimal)budgetApproved + (decimal)budgetDeclined;
                    int RiskObjId;
                    if (SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.Scenario)
                        RiskObjId = (int)_sapaOverviewManager.GetRiskObjId((int)SapaTree.SelectedNode?.Source.ScenarioId);
                    else
                        RiskObjId = SapaTree.SelectedNode?.Source.RiskObjectId ?? 0;

                    var sheqDict = _riskOrganizerManager.GetSheqItems(RiskObjId);

                    NoSheqItems = 0;
                    if (sheqDict != null)
                    {
                        foreach (var Item in sheqDict)
                        {
                            if (Item.Key == 0) // That's the title of the SHEQ dict
                            {
                                sheqDonutTitle = Item.Value;
                            }
                            else if (NoSheqItems < 8)
                            {
                                sheqItem[NoSheqItems] = Item.Value;
                                if (totalBudgetNeed != 0)
                                {
                                    sumSheqItemApproved[NoSheqItems] = year.Details.Where(x => x.Approved).Where(x => x.RiskSafetyBefore == Item.Value).Sum(x => x.CostYear1) / totalBudgetNeed;
                                    sumSheqItemDeclined[NoSheqItems] = year.Details.Where(x => !x.Approved).Where(x => x.RiskSafetyBefore == Item.Value).Sum(x => x.CostYear1) / totalBudgetNeed;
                                }
                                sumNoSheqItemApproved[NoSheqItems] = year.Details.Where(x => x.Approved).Count(x => x.RiskSafetyBefore == Item.Value);
                                sumNoSheqItemDeclined[NoSheqItems] = year.Details.Where(x => !x.Approved).Count(x => x.RiskSafetyBefore == Item.Value);
                                NoSheqItems++;
                            }
                        }
                    }
                }

                //Average Sapa Index -> Right Donut
                AverageSapaIndex = year.BudgetApproved != 0 ? year.Details.Where(x => x.Approved).Sum(x => x.CostYear1 * x.RiskSapaIndex) / year.BudgetApproved : 0;

                MinSapaIndex = (year.Details.Where(x => x.Approved).Min(x => x.RiskSapaIndex) ?? 0);
                MaxSapaIndex = (year.Details.Where(x => x.Approved).Max(x => x.RiskSapaIndex) ?? 0);

                MinBorderSapaIndex = MinSapaIndex < 0 ? Math.Round((MinSapaIndex - 10 ?? 0) / 10) * 10 : 0;
                MaxBorderSapaIndex = MaxSapaIndex < 0 ? 0 : Math.Round((MaxSapaIndex + 149 ?? 0) / 100) * 100; //Make sure there is some grey in the donut to see the values
            }
            //Budget Overview -> Main Graph
            if (GetCurrentYear().Year == year.Year || YearView)
            {
                var n = 0;
                graphTitle = "Overview " + SapaTree.SelectedNode?.Source.Name;
                int yr = year.Year - GetCurrentYear().Year;

                if (YearView)
                { //Show Years
                    if (yr >= 0 && yr < 6) //ivm array grootte, six years from chosen year
                    {
                        DepBudgetAssigned[0, yr] = 0; //reset eventual data
                        var SapaList = new List<SapaModel>();
                        if (SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.Scenario)
                            SapaList = _sapaOverviewManager.GetSapasOnScenario((int)SapaTree.SelectedNode?.Source.ScenarioId);
                        else
                            SapaList = _sapaOverviewManager.GetSapas(SapaTree.SelectedNode?.Source.RiskObjectId ?? 0);

                        BudgetDepartment[0, yr] = year.Year.ToString();
                        foreach (var item in SapaList)
                        {
                            DepBudgetAssigned[0, yr] += _sapaOverviewManager.GetAssignedBudget(item.Id, year.Year);
                            n++;
                            NoDep = n;
                            if (n >= 6) break; //To Do; set up array for number of years to look ahead
                        }
                        DepBudgetApproved[0, yr] = year.Details.Where(x => x.Approved).Sum(x => x.CostYear1);
                        DepBudgetDeclined[0, yr] = year.Details.Sum(x => x.CostYear1) - DepBudgetApproved[0, yr];
                    }
                }
                else if ((WpView && SelectedNode?.SapaTreeNodeType != SapaTreeNodeType.WorkPackage) ||
                    (!WpView && SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.SapaCollection))
                { //Show WorkPackages / Budget types
                    var SapaList = new List<SapaModel>();
                    if (SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.Scenario)
                        SapaList = _sapaOverviewManager.GetSapasOnScenario((int)SapaTree.SelectedNode?.Source.ScenarioId);
                    else
                        SapaList = _sapaOverviewManager.GetSapas((int)SapaTree.SelectedNode?.Source.RiskObjectId);
                    n = 0;
                    foreach (var item in SapaList)
                    {
                        BudgetDepartment[n, 0] = item.Name;
                        DepBudgetAssigned[n, 0] = _sapaOverviewManager.GetAssignedBudget(item.Id, year.Year);
                        DepBudgetApproved[n, 0] = year.Details.Where(x => x.Approved)
                            .Where(x => x.SapaWorkpackageId == item.SapaWorkpackageId).Sum(x => x.CostYear1);
                        DepBudgetDeclined[n, 0] =
                            year.Details.Where(x => x.SapaWorkpackageId == item.SapaWorkpackageId)
                                .Sum(x => x.CostYear1) - DepBudgetApproved[n, 0];

                        if (DepBudgetApproved[n, 0] != 0 || DepBudgetDeclined[n, 0] != 0) // skip empty value
                        {
                            n++;
                            NoDep = n;
                            if (n >= 6) break; //To Do; set up array for number of used departments
                        }
                    }
                }
                else
                { //Show Departments
                    n = 0;
                    if (DepartmentDict == null) return;

                    foreach (var department in DepartmentDict.ToList())
                    {
                        BudgetDepartment[n, 0] = department.Value;
                        DepBudgetAssigned[n, 0] = (decimal)budget;
                        DepBudgetApproved[n, 0] = year.Details.Where(x => x.Approved)
                            .Where(x => x.DepDescription == department.Value).Sum(x => x.CostYear1);
                        DepBudgetDeclined[n, 0] =
                            year.Details.Where(x => x.DepDescription == department.Value).Sum(x => x.CostYear1) -
                            DepBudgetApproved[n, 0];

                        if (DepBudgetApproved[n, 0] != 0 || DepBudgetDeclined[n, 0] != 0) // skip empty value
                        {
                            n++;
                            NoDep = n;
                            if (n > 6) break; //To Do; set up array for number of used departments
                        }
                    }
                }
            }

            if (!YearView)
            {
                assignedBudgetYear = _localizer["SapaBudgetAssigned"] + ": " + FormatAsSelectedCurrency(budget);
                totalApprovedYear = _localizer["SapaApproved"] + ": " + FormatAsSelectedCurrency(budgetApproved);
                totalDeclinedYear = _localizer["SapaDeclined"] + ": " + FormatAsSelectedCurrency(budgetDeclined);
            }
            else
            {
                assignedBudgetYear = _localizer["SapaBudgetAssigned"];
                totalApprovedYear = _localizer["SapaApproved"];
                totalDeclinedYear = _localizer["SapaDeclined"];
            }
        }

        Sapa.Budget = Sapa.Years.Sum(x => x.Budget);
        Sapa.BudgetRequest = Sapa.Years.Sum(x => x.BudgetRequest);
        Sapa.BudgetApproved = Sapa.Years.Sum(x => x.BudgetApproved);
    }

    private TreeNodeGeneric<SapaTreeObject> GetSapaTree() =>
        _sapaOverviewManager.GetSapaTree(QueryParams.WorkPackages);

    public SapaYearModel GetCurrentYear()
    {
        return Sapa?.Years.Skip(QueryParams.BottomTabs).FirstOrDefault();
    }

    public string GetFilterTabText()
    {
        return TempSelectedWorkPackages.Count == 0 || TempSelectedWorkPackages.Count == SapaWorkPackages.Count
            ? _localizer["SapaFilterTxt"]
            : $"{_localizer["SapaFilterTxt"]} ({TempSelectedWorkPackages.Count})";
    }

    public string FormatAsSelectedCurrency(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
    }

    public string FormatAsSelectedPercentage(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("P2", CultureInfo.CreateSpecificCulture(Currency));
    }

    public string FormatAsSelectedNumber(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("N0", CultureInfo.CreateSpecificCulture(Currency));
    }

    public static string FormatAsEur(object value) =>
        ((double) value).ToString("C0", CultureInfo.CreateSpecificCulture("nl-NL"));

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    public void OpenRisksPage(SapaDetailModel model) => OpenRisksPage(model.RiskObjId, model.RiskId);

    public void OpenRisksPage(int riskObjectId, int riskId) =>
        _navigationManager.NavigateTo($"/value-risk-analysis/{riskObjectId}/risks/{riskId}");

    /// <summary>
    /// Open SapaDetail Page
    /// </summary>
    public void OpenSapaDetailsPopup(SapaDetailModel model)
    {
        _dialogService.Open<SapaDetailPrint>(_localizer["SapaOverviewTxt"],
            new Dictionary<string, object>
            {
                {"SapaDetailId", model.Id},
                {"Callback", EventCallback.Factory.Create<SapaDetailModel>(this, SaveTask)}
            },
            new DialogOptions { Width = "1400px", Resizable = true, Draggable = true });
    }

    public void SaveTask(SapaDetailModel task)
    {
        _dialogService.Open<AreYouSureDialog<SapaDetailModel>>(_localizer["RaOverrideCostsTitle"],
            new Dictionary<string, object>
            {
                {nameof(AreYouSureDialog<SapaDetailModel>.Item), task}
            },
            new DialogOptions {CloseDialogOnEsc = false, ShowClose = false, Resizable = false, Draggable = true});
    }

    public List<string> sheqColorScheme { get; set; }
       = new()
       { // meant to change for different numbers of Rows in a Risk Matrix
            "#008000", "#009900", "#00B000", "#33CC33", "#6FDB6F", "#66FF33", "#FFFF99", "#CCFF66", "#FFFF66", "#FFFF00", "#F69200", "#FF0000", "#CC0000", "#990033"
       };
  
    private void SetChartData()
    {
        if (Sapa == null) return;
        // Create data items for each budget type using loops
        var assignedBudgetItems = new List<DataItem>();
        var approvedCostItems = new List<DataItem>();
        var declinedCostItems = new List<DataItem>();
        
        if (!YearView)
        {
            for (int i = 0; i < NoDep; i++)
            // Create data items for each department (0 to NoDep-1)
            {
                assignedBudgetItems.Add(new DataItem
                {
                    Department = BudgetDepartment[i, 0],
                    SapaCost = (double)DepBudgetAssigned[i, 0],
                    CostString = FormatAsSelectedCurrency(DepBudgetAssigned[i, 0])
                });

                approvedCostItems.Add(new DataItem
                {
                    Department = BudgetDepartment[i, 0],
                    SapaCost = (double)DepBudgetApproved[i, 0],
                    CostString = FormatAsSelectedCurrency(DepBudgetApproved[i, 0])
                });

                declinedCostItems.Add(new DataItem
                {
                    Department = BudgetDepartment[i, 0],
                    SapaCost = (double)DepBudgetDeclined[i, 0],
                    CostString = FormatAsSelectedCurrency(DepBudgetDeclined[i, 0])
                });
            }
        }
        else
        {
            int? maxYrs = Sapa.Years.Count() >= 5 ? 5 : Sapa.Years.Count();

            for (int yr = 0; yr < maxYrs; yr++)
            {
                assignedBudgetItems.Add(new DataItem
                {
                    Department = BudgetDepartment[0, yr],
                    SapaCost = (double)DepBudgetAssigned[0, yr],
                    CostString = FormatAsSelectedCurrency(DepBudgetAssigned[0, yr])
                });

                approvedCostItems.Add(new DataItem
                {
                    Department = BudgetDepartment[0, yr],
                    SapaCost = (double)DepBudgetApproved[0, yr],
                    CostString = FormatAsSelectedCurrency(DepBudgetApproved[0, yr])
                });

                declinedCostItems.Add(new DataItem
                {
                    Department = BudgetDepartment[0, yr],
                    SapaCost = (double)DepBudgetDeclined[0, yr],
                    CostString = FormatAsSelectedCurrency(DepBudgetDeclined[0, yr])
                });
            }
        }

        // Assign the created lists to their respective chart data properties
        ChartDataAssignedBudget = assignedBudgetItems.ToArray();
        ChartDataApprovedCost = approvedCostItems.ToArray();
        ChartDataDeclinedCost = declinedCostItems.ToArray();

        ChartDataSapaIndex = new[]
        {
            new DataItem
            {
                BudgetCriteria = "Minimum Sapa Index",
                SapaPercentage = (double) (MinSapaIndex ?? 0),
                CostString = FormatAsSelectedNumber(MinSapaIndex ?? 0)
            },
            new DataItem
            {
                BudgetCriteria = "Under Average",
                SapaPercentage = (double) ((AverageSapaIndex ?? 0) - (MinSapaIndex ?? 0)),
                CostString = FormatAsSelectedNumber(AverageSapaIndex ?? 0)
            },
            new DataItem //NB. Needs to be taken into account twice, so a line for the average is drawn
            {
                BudgetCriteria = "Average Sapa Index",
                SapaPercentage = 1,
                CostString = FormatAsSelectedNumber(AverageSapaIndex ?? 0)
            },
            new DataItem
            {
                BudgetCriteria = "Over Average",
                SapaPercentage = (double) ((MaxSapaIndex ?? 0) - (AverageSapaIndex ?? 0)),
                CostString = FormatAsSelectedNumber(MaxSapaIndex ?? 0)
            },
            new DataItem
            {
                BudgetCriteria = "Maximum Value",
                SapaPercentage = (double) ((MaxBorderSapaIndex ?? 0) - (MinBorderSapaIndex ?? 0) - (MaxSapaIndex ?? 0)),
                CostString = FormatAsSelectedNumber((MaxBorderSapaIndex ?? 0) - (MinBorderSapaIndex ?? 0))
            },
            new DataItem
            {
                BudgetCriteria = "Negative Sapa Index",
                SapaPercentage = (double) (MinBorderSapaIndex ?? 0),
                CostString = FormatAsSelectedNumber(MinBorderSapaIndex ?? 0)
            }
        };

        // Handle the view-specific data
        if (SheqView) // Left Donut - SHEQ view
            SetSheqData();
        else
            SetBudgetData();
    }

    private void SetBudgetData()
    {
        if (budgetSpent <= 1)
        {
            ChartDataPercOfBudget = new[]
            {
                new DataItem //Approved Budget
                {
                    BudgetCriteria = "Approved",
                    SapaCost = (double) (budgetSpent ?? 0),
                    CostString = FormatAsSelectedPercentage(budgetSpent ?? 0)
                },
                new DataItem //Rest Budget
                {
                    BudgetCriteria = "Rest Budget",
                    SapaCost = (double) (1 - (budgetSpent ?? 0)),
                    CostString = FormatAsSelectedPercentage(1 - (budgetSpent ?? 0))
                }
            };
        }
        else if (budgetSpent > 1)
        {
            ChartDataPercOfBudget = new[]
            {
                new DataItem //Overrun
                {
                    BudgetCriteria = "Budget Overrrun",
                    SapaCost = (double) ((budgetSpent ?? 0) - 1),
                    CostString = FormatAsSelectedPercentage((budgetSpent ?? 0) - 1)
                },
                new DataItem //Approved Budget
                {
                    BudgetCriteria = "Approved",
                    SapaCost = (double) (2 - (budgetSpent ?? 0)),
                    CostString = FormatAsSelectedPercentage(1)
                }
            };
        }
    }

    private void SetSheqData()
    {
        var dataItems = new List<DataItem>();

        // Determine the highest index based on NoSheqItems
        var highestIndex = NoSheqItems;

        // Add approved items in reverse order (highest to lowest index)
        for (var i = highestIndex; i >= 0; i--)
        {
            dataItems.Add(new DataItem
            {
                BudgetCriteria = sheqItem[i],
                SapaCost = (double) sumSheqItemApproved[i],
                CostString = FormatAsSelectedPercentage(sumSheqItemApproved[i]) + ", " + sumNoSheqItemApproved[i] + " items approved"
            });
        }

        // Add declined items in reverse order (highest to lowest index)
        for (var i = highestIndex; i >= 0; i--)
        {
            dataItems.Add(new DataItem
            {
                BudgetCriteria = sheqItem[i],
                SapaCost = (double) sumSheqItemDeclined[i],
                CostString = FormatAsSelectedPercentage(sumSheqItemDeclined[i]) + ", " + sumNoSheqItemDeclined[i] + " items declined"
            });
        }

        ChartDataPercOfBudget = dataItems.ToArray();
    }
}