using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.ABS;

public class DeleteAbsWidgetViewModel : BaseViewModel
{
    [Parameter] public EventCallback ConfirmCallback { get; set; }

    private readonly IAssetBreakdownManager _assetBreakdownManager;

    public DeleteAbsWidgetViewModel(
        ILoggerFactory loggerFactory,
        IAssetBreakdownManager assetBreakdownManager,
        DialogService dialogService) : base(loggerFactory, dialogService)
    {
        _assetBreakdownManager = assetBreakdownManager;
    }

    public async Task DeleteAllAssets()
    {
        _assetBreakdownManager.DeleteAllAssets();
        _dialogService.Close();
        await ConfirmCallback.InvokeAsync();
    }
}