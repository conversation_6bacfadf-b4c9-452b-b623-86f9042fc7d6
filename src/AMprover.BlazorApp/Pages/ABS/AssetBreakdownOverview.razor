@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<AssetBreakdownOverviewViewModel>
@page "/asset-breakdown-structure"
@inject IStringLocalizer<AssetBreakdownOverview> _localizer
@inject IGlobalDataService GlobalDataService;

<h2>@_localizer["AbsHeaderTxt"]</h2>
<div class="row header-navigation">
    <div class="col-6">

        <Breadcrumbs Category="Asset Breakdown Structure" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right btn-centered my-2 mx-2" DialogTitle=@_localizer["AbsMenuTitle"] DialogContent=@_localizer["AbsMenuTxt"]/>
        <RadzenButton Disabled=!GlobalDataService.CanEdit ButtonStyle="ButtonStyle.Secondary" Icon="input" Text=@(_localizer["AbsImportTabTxt"]) class="float-right my-2 mx-0" Click=@BindingContext.OpenImportABSWidget />
        <RadzenButton Text="Delete Assets" Icon="delete" class="float-right my-2 mx-2" Click=@BindingContext.OpenDeleteWidget />
    </div>
</div>

<p>@((MarkupString)_localizer["AbsSubHeaderTxt"].Value)</p>
    
<br/>

<RadzenTabs @ref=@BindingContext.Tabs Change=@BindingContext.TabsChange>
    <Tabs>

        <RadzenTabsItem Text=@_localizer["AbsAssetTabTxt"]>
                <UtilityGrid TItem=AssetModel
                     @ref=BindingContext.AssetGrid
                     Data=BindingContext.Assets
                     FileName=@GridNames.Portfolio.AssetBreakdownStructure
                     Interactive=true
                     AllowXlsExport=true
                     AllowSorting=true
                     AllowFiltering=true
                     AddNewButton=false
                     MaxRows=25
                     SaveCallback=@BindingContext.SaveAsset 
                     DeleteCallback=@BindingContext.DeleteAsset
                     DropDownOverrides=@BindingContext.GetDropdownOverride() />
        </RadzenTabsItem>

        <RadzenTabsItem Text=@_localizer["AbsTreeViewTabTxt"]>
            <div class="row">
                <div class="col-6">
                    <TreeComponent TItem=AssetModel 
                            Treeview=BindingContext.AssetTree 
                            CssClass="abs-tree" 
                            NodeClickCallback=@BindingContext.SelectAsset
                            PasteCallback=@BindingContext.PasteNode 
                            HandleCutPasteInternally=true />
                </div>

                <div class="col-6">
                    <RadzenDataGrid AllowFiltering="true" AllowPaging="true" AllowSorting="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                PageSize="12" Data="@BindingContext.AssetDetails" TItem="KeyValuePair<string,string>">
                        <Columns>
                            <RadzenDataGridColumn Width="30%" TItem="KeyValuePair<string,string>" Property="@nameof(KeyValuePair<string, string>.Key)" Title="@nameof(KeyValuePair<string, string>.Key)"/>
                            <RadzenDataGridColumn Width="70%" TItem="KeyValuePair<string,string>" Property="@nameof(KeyValuePair<string, string>.Value)" Title="@nameof(KeyValuePair<string, string>.Value)"/>
                        </Columns>
                    </RadzenDataGrid>
                </div>
            </div>
        </RadzenTabsItem>

        @if (GlobalDataService.CanEdit)
        {
            <!-- Si / Asset Category -->
            <RadzenTabsItem Text=@_localizer["AbsCategorySettingTabTxt"]>
                <div class="row mb-3">
                    <div class="col-12">
                        <UtilityGrid TItem=LookupUserDefinedModel
                                  @ref=@BindingContext.AssetCategoryGrid
                                  Data=@BindingContext.AssetCategories
                                  FileName=@GridNames.Portfolio.AssetCategories
                                  Interactive=true
                                  AddNewButton=true
                                  AllowFiltering=true 
                                  SaveCallback=@BindingContext.UpdateAssetCategory
                                  DeleteCallback=@BindingContext.DeleteAssetCategory
                                  NewItemTemplate=@(new LookupUserDefinedModel{ Filter = "SICategory"}) 
                                  ExternalSaveValidation=@BindingContext.ValidateCanSaveAssetCategory 
                                  ExternalSaveValidationFailed=@BindingContext.ValidateCanSaveAssetCategoryFailed />
                    </div>
                </div>
            </RadzenTabsItem>
        }
    </Tabs>
</RadzenTabs>
