@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<ImportAbsWidgetViewModel>
@inject IStringLocalizer<AssetBreakdownOverview> _localizer

<div class="row">
    <div class="col-12">

        <div class="row mb-3">
            <div class="col-12">

                <div class="row">
                    <div class="col-12">
                        @((MarkupString)_localizer["AbsUploadTemplateTxt"].Value)
                    </div>
                </div>

                @if (HasAssetsInDatabase)
                {
                    <div class="row my-3">
                        <div class="col-12" style="color:darkorange;">
                            @_localizer["AbsCreateTreeBasedOnAssetCatNotAllowed"]
                        </div>
                    </div>
                }
                else
                {
                    <div class="row my-3">
                        <div class="col-12">
                            <RadzenCheckBox @bind-Value=@BindingContext.CreateTreeBasedOnAssetCategory Disabled=@HasAssetsInDatabase /> 
                            @_localizer["AbsCreateTreeBasedOnAssetCat"]
                        </div>
                    </div>
                }

                <div class="row mt-4">
                    <div class="col-12">
                        <RadzenFileInput @ref=@BindingContext.UploadRef 
                                         @bind-Value=@BindingContext.FileUpload 
                                         Accept=".xlsx,.xls" TValue="string" 
                                         ChooseText=@_localizer["AbsImportTxt"]
                                         Change=@(args => @BindingContext.OnChange(args)) 
                                         Error=@(args => @BindingContext.OnError(args)) />
                    </div>
                </div>

                @if (!string.IsNullOrWhiteSpace(BindingContext.UploadError))
                {
                    <div class="row mt-4">
                        <div class="col-12" style="color:red;">
                            @BindingContext.UploadError
                        </div>
                    </div>
                }
            </div>
        </div>

    </div>
</div>

@code {
    [Parameter] public EventCallback ConfirmCallback { get; set; }
    [Parameter] public bool HasAssetsInDatabase { get; set; }
}