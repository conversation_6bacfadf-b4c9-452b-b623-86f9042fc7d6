@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<DeleteAbsWidgetViewModel>
@inject IStringLocalizer<AssetBreakdownOverview> _localizer

<div class="row mb-3">
    <div class="col-12">
        <p>@((MarkupString)_localizer["AbsDeleteTxt"].Value)</p>
        <p>
            <RadzenButton Text="Delete Assets" Icon="delete_forever" ButtonStyle="ButtonStyle.Danger" Click=@BindingContext.DeleteAllAssets />
        </p>
    </div>
</div>

@code{
    [Parameter] public EventCallback ConfirmCallback { get; set; }
}