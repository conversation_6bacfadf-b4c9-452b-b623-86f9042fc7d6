using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.ABS;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Ra<PERSON>zen;
using Radzen.Blazor;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models;
using Microsoft.Extensions.Localization;
using AMprover.BusinessLogic.Helpers;
using Microsoft.AspNetCore.Components;

namespace AMprover.BlazorApp.Pages.ABS;

public class AssetBreakdownOverviewViewModel : BaseViewModel
{
    private readonly ILookupManager _lookupManager;
    private readonly IAssetBreakdownManager _assetBreakdownManager;
    private readonly IJSRuntime _jsRuntime;
    private readonly IStringLocalizer _localizer;

    public AssetBreakdownOverviewViewModel(
        ILoggerFactory loggerFactory,
        ILookupManager lookupManager,
        DialogService dialogService,
        IAssetBreakdownManager assetBreakdownManager,
        IJSRuntime jsRuntime,
        LocalizationHelper localizationHelper) : base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _assetBreakdownManager = assetBreakdownManager;
        _jsRuntime = jsRuntime;
        _lookupManager = lookupManager;

        _localizer = localizationHelper.GetLocalizer<AssetBreakdownOverview>();
    }

    public RadzenTabs Tabs { get; set; }

    public List<AssetModel> Assets { get; set; }
    public UtilityGrid<AssetModel> AssetGrid { get; set; }

    public TreeGeneric<AssetModel> AssetTree { get; } = new TreeGeneric<AssetModel>();

    private AssetModel _asset { get; set; }
    public AssetModel Asset
    {
        get => _asset;
        set
        {
            _asset = value;
            AssetDetails = GetAssetDetails(value);
        }
    }

    public List<LookupUserDefinedModel> AssetCategories { get; set; }
    public UtilityGrid<LookupUserDefinedModel> AssetCategoryGrid { get; set; }


    public List<KeyValuePair<string, string>> AssetDetails { get; set; } = new List<KeyValuePair<string, string>>();

    public override void OnInitialized()
    {
        Assets = _assetBreakdownManager.GetAllAssets();
        Asset = Assets.FirstOrDefault();
        AssetCategories = _lookupManager.GetAssetCategories();
    }

    public bool ValidateCanSaveAssetCategory(LookupUserDefinedModel model)
    {
        return AssetCategories.Where(x => x.Value == model.Value).Count() <= 1;
    }

    public void ValidateCanSaveAssetCategoryFailed(LookupUserDefinedModel model)
    {
        ShowErrorMessage(_localizer["AbsCatValMustBeUniqueTxt"]);
    }

    public void UpdateAssetCategory(LookupUserDefinedModel model)
    {
        var original = AssetCategories.FirstOrDefault(x => x.Id == model.Id);
        var result = _lookupManager.SaveAssetCategory(model);

        if (original != null)
            AssetCategories[AssetCategories.IndexOf(original)] = result;
        else
            AssetCategories.Add(result);

        AssetCategoryGrid.Reload();
    }

    public void DeleteAssetCategory(LookupUserDefinedModel model)
    {
        if (model?.Id == null) return;
        if (_lookupManager.DeleteAssetCategory(model.Id, out var result))
        {
            AssetCategories.Remove(model);
            AssetCategoryGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            AssetCategoryGrid.BindingContext.EditItem(model);
        }
    }

    public void TabsChange(int index)
    {
        if (index == 1)
        {
            AssetTree.Initialize(_assetBreakdownManager.GetAssetTree(Assets));
        }
    }

    public void SelectAsset(TreeNodeGeneric<AssetModel> node)
    {
        Asset = node.Source;
    }

    public void SaveAsset(AssetModel asset)
    {
        var updatedAsset = _assetBreakdownManager.SaveAsset(asset);
        if (updatedAsset.ParentId != null && !Assets.Any(x => x.Id == updatedAsset.ParentId))
            updatedAsset.ParentId = null;

        if (!Assets.Contains(asset))
            Assets.Add(updatedAsset);
        else
            Assets[Assets.IndexOf(asset)] = updatedAsset;

        AssetGrid.Reload();
    }

    public void DeleteAsset(AssetModel asset)
    {
        _assetBreakdownManager.DeleteAsset(asset.Id);
        Assets.Remove(asset);
        AssetGrid.Reload();
    }

    public Dictionary<string, Dictionary<int, string>> GetDropdownOverride()
    {
        return new Dictionary<string, Dictionary<int, string>>
        {
            //{ nameof(AssetModel.CategoryId), _dropdownManager.GetSiCategoriesDict() },
        };
    }

    public List<KeyValuePair<string, string>> GetAssetDetails(AssetModel asset)
    {
        var dict = new List<KeyValuePair<string, string>>();

        if (asset == null)
            return dict;

        var fields = asset.GetType().GetFields(BindingFlags.NonPublic | BindingFlags.Instance);

        foreach (var field in fields)
        {
            var name = field.Name;
            var value = field.GetValue(asset);

            if (value != null)
                dict.Add(new KeyValuePair<string, string>(name.Split('<')[1].Split('>')[0], value.ToString()));
        }

        return dict;
    }

    public void PasteNode((List<AssetModel> source, AssetModel destination) args)
    {
        if (args.source?.Any() == true || args.destination == null)
        {
            args.source.First().ParentId = args.destination.Id;
            args.source.First().ParentCode = args.destination.Code;
            _assetBreakdownManager.SaveAsset(args.source.First());
        }
    }

    public void OpenDeleteWidget()
    {
        _dialogService.Open<DeleteAbsWidget>
                    (_localizer["AbsDeleteTabTxt"],
                    new Dictionary<string, object>
                    {
                        {
                            nameof(DeleteAbsWidget.ConfirmCallback),
                            EventCallback.Factory.Create(this, WidgetCallBack)
                        }
                    });
    }

    public void OpenImportABSWidget()
    {
        _dialogService.Open<ImportAbsWidget>
                    (_localizer["AbsImportTabTxt"],
                    new Dictionary<string, object>
                    {
                        {
                            nameof(ImportAbsWidget.ConfirmCallback),
                            EventCallback.Factory.Create(this, WidgetCallBack)
                        },
                        {
                            nameof(ImportAbsWidget.HasAssetsInDatabase),
                            Assets.Any()
                        }
                    });
    }

    public void WidgetCallBack()
    {
        OnInitialized();
        Tabs.Reload();
    }
}