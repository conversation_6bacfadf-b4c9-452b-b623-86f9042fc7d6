using AMprover.BusinessLogic;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;
using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Models;
using Radzen.Blazor;
using AMprover.BlazorApp.Components;
using Microsoft.JSInterop;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.ABS;
using Microsoft.AspNetCore.Components;

namespace AMprover.BlazorApp.Pages.ABS;

public class ImportAbsWidgetViewModel : BaseViewModel
{
    [Parameter] public EventCallback ConfirmCallback { get; set; }

    private readonly ILookupManager _lookupManager;
    private readonly IAssetBreakdownManager _assetBreakdownManager;
    private IJSRuntime _jsRuntime { get; }

    public ImportAbsWidgetViewModel(
        ILoggerFactory loggerFactory,
        ILookupManager lookupManager,
        IAssetBreakdownManager assetBreakdownManager,
        DialogService dialogService,
        IJSRuntime jsRuntime) : base(loggerFactory, dialogService)
    {
        _assetBreakdownManager = assetBreakdownManager;
        _dialogService = dialogService;
        _jsRuntime = jsRuntime;
        _lookupManager = lookupManager;
    }

    public override void OnInitialized()
    {
        Assets = _assetBreakdownManager.GetAllAssets();
        Asset = Assets.FirstOrDefault();
        AssetCategories = _lookupManager.GetAssetCategories();
        UploadError = FileUpload = null;
        CreateTreeBasedOnAssetCategory = false;
    }
    public List<AssetModel> Assets { get; set; }
    private AssetModel _asset { get; set; }
    public AssetModel Asset
    {
        get => _asset;
        set
        {
            _asset = value;
            //AssetDetails = GetAssetDetails(value);
        }
    }
    public List<LookupUserDefinedModel> AssetCategories { get; set; }
    
    public string FileUpload { get; set; }
    public RadzenFileInput<string> UploadRef { get; set; }
    public string UploadError { get; set; }
    public bool CreateTreeBasedOnAssetCategory { get; set; }
    
    public async Task OnChange(string value)
    {
        // clear ref, the component wants to display an image preview which does not apply
        UploadError = FileUpload = null;
        await UploadAssetBreakdownStructure(value?.Split(',').LastOrDefault()?.Trim()).ConfigureAwait(false);
        await _jsRuntime.InvokeAsync<bool>("ClearFileInputValue", UploadRef.Element).ConfigureAwait(false);
    }

    public void OnError(UploadErrorEventArgs args)
    {
        UploadError = args.Message;
    }

    public async Task UploadAssetBreakdownStructure(string base64Input)
    {
        if (string.IsNullOrWhiteSpace(base64Input))
            return;

        ShowLoadingDialog();
        var result = new ImportResult();

        var uploadedItems = base64Input.GetExcelData<AssetModel>(1)
            .Select(x => x.Value).Where(x => !string.IsNullOrWhiteSpace(x.Code)).ToList();

        if(Assets.Count == 0)
        {
            uploadedItems.ForEach(x => { x.Id = 0; x.ParentId = null; });
        }

        if(_assetBreakdownManager.ImportFileHasIssues(uploadedItems, out var errorMesage))
        {
            result.SetFailed();
            result.ErrorMessage = errorMesage;
        }
        else
        {
            result = await _assetBreakdownManager.ProcessImportedAssets(uploadedItems);

            if (result.Success)
            {
                Assets = CreateTreeBasedOnAssetCategory
                    ? _assetBreakdownManager.SetParentIdsBasedOnSequenceAndAssetCategory()
                    : _assetBreakdownManager.GetAllAssets();

                Asset = Assets.FirstOrDefault();
            }
        }

        // Close loading widget
        _dialogService.Close();

        // Close current widget
        _dialogService.Close();

        // Show result Widget
        ShowImportResultDialog(result);

        await ConfirmCallback.InvokeAsync();
    }

    private void ShowLoadingDialog()
    {
        _dialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    private void ShowImportResultDialog(ImportResult importResult)
    {
        var style = importResult.Success
            ? "min-height: 180px;"
            : "min-height: 280px;";

        _dialogService.Open<ImportResultDialog>(importResult.Success ? "Success" : "Error importing assets",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), importResult }
            }, new DialogOptions { Draggable=true, Style = style });
    }
}