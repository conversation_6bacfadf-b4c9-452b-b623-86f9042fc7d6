using AMprover.BlazorApp.Components.ABS;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.ABS;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using FieldInfo = AMprover.BusinessLogic.Models.ABS.FieldInfo;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Components.SplitButton;

namespace AMprover.BlazorApp.Pages.AssignABS;

public class AssignAssetsViewModel : BaseViewModel
{
    private NavigationManager _navigationManager { get; }
    private IAssetBreakdownManager _assetBreakdownManager { get; }
    private IRiskAnalysisManager _riskAnalysisManager { get; }
    private IAssignAssetManager _assignAssetManager { get; }
    private IScenarioManager _scenarioManager { get; }
    private ILookupManager _lookupManager { get; }
    private IExportManager _exportManager { get; }
    private IGlobalDataService _globalDataService { get; }
    private IPageNavigationManager _pageNavigationManager { get; }

    private IObjectManager _objectManager { get; set; }

    public AssignAssetsViewModel(ILoggerFactory loggerFactory,
        DialogService dialogService,
        NavigationManager navigationManager,
        ILogger<AssignAssetsViewModel> logger,
        IAssetBreakdownManager assetBreakdownManager,
        IRiskAnalysisManager riskAnalysisManager,
        IAssignAssetManager assignAssetManager,
        IScenarioManager scenarioManager,
        ILookupManager lookupManager,
        IExportManager exportManager,
        IGlobalDataService globalDataService,
        IPageNavigationManager pageNavigationManager,
        IObjectManager objectManager
    ) : base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _navigationManager = navigationManager;
        _logger = logger;
        _assetBreakdownManager = assetBreakdownManager;
        _riskAnalysisManager = riskAnalysisManager;
        _assignAssetManager = assignAssetManager;
        _scenarioManager = scenarioManager;
        _lookupManager = lookupManager;
        _exportManager = exportManager;
        _globalDataService = globalDataService;
        _pageNavigationManager = pageNavigationManager;
        _objectManager = objectManager;

        var uri = _navigationManager.ToAbsoluteUri(_navigationManager.Uri);
        if (QueryHelpers.ParseQuery(uri.Query).TryGetValue("fixfilters", out _))
            FixFilters();
    }

    private string _previewText = string.Empty;

    public bool UserCanEdit => _globalDataService.CanEdit;

    public string PreviewText
    {
        get => _previewText;
        set
        {
            _previewText = value;
            PreviewFilter(_previewText);
        }
    }

    public bool OnlyUsePreviewFilter { get; set; }

    private List<AssetModel> AllAssets = new List<AssetModel>();

    public List<ScenarioModel> RiskScenarios { get; set; }

    public UtilityGrid<AssetModel> AssetGrid { get; set; }

    public List<GridColumnModel> AssetTableColumns { get; set; } = new List<GridColumnModel>();

    public TreeGeneric<RiskTreeObject> RiskTree { get; set; } = new TreeGeneric<RiskTreeObject>();

    public List<List<FilterQueryRow>> InheritedSelectionFiltersGroups { get; set; } = new List<List<FilterQueryRow>>();

    public List<FilterQueryRow> AllSelectionFilters { get; set; } = new List<FilterQueryRow>();

    public RadzenDataGrid<FilterQueryRow> FilterQueryRowGrid { get; set; }

    private readonly List<DatabaseSelectionCriterium> _allCriteriumOptions =
        Enum.GetValues(typeof(DatabaseSelectionCriterium)).Cast<DatabaseSelectionCriterium>().ToList();

    public List<DatabaseSelectionCriterium> CriteriumOptions { get; set; } = new List<DatabaseSelectionCriterium>();

    public List<string> AndOrOptions { get; set; } = new List<string> {"And", "Or"};

    public RadzenDropDown<string> FieldNameDropDown { get; set; }

    public Dictionary<string, FieldInfo> FieldNames { get; set; } = new Dictionary<string, FieldInfo>();

    public List<AssetModel> FilteredAssets { get; set; } = new List<AssetModel>();

    public List<string> AssetColumns { get; set; } = new List<string>();

    public FilterQueryRow NewFilterQueryRow { get; set; } = new FilterQueryRow
    {
        FieldName = "Code",
        Criterium = DatabaseSelectionCriterium.Contains,
        FieldType = DatabaseFieldType.String
    };

    public override void OnInitialized()
    {
        ObjectLevels = _objectManager.GetObjectLevelNames();
        AssetTableColumns = ForceGetAssetColumns();
        FieldNames = GetFieldNamesDropdown(AssetTableColumns);
        RiskScenarios = _scenarioManager.GetAllScenarios().OrderBy(x => x.Name).ToList();
        SelectedScenario = _pageNavigationManager.GetSelectedScenario() ?? RiskScenarios.FirstOrDefault()?.Id ?? 0;
        AllAssets = _assetBreakdownManager.GetAllAssets();
        CriteriumOptions = new List<DatabaseSelectionCriterium>(_allCriteriumOptions);
        SelectScenario(SelectedScenario);

        base.OnInitialized();
    }

    private Dictionary<string, FieldInfo> GetFieldNamesDropdown(List<GridColumnModel> grid)
    {
        var result = new Dictionary<string, FieldInfo>();
        foreach (var column in grid)
        {
            var type = column.FieldType == FieldType.Text
                ? DatabaseFieldType.String
                : DatabaseFieldType.Int;

            result.Add(column.FieldName, new FieldInfo {Name = column.FieldName, Type = type});
        }

        return result;
    }

    public void ResetDropdown()
    {
        AssetTableColumns = _lookupManager.SeedGridColumns<AssetModel>("assignAbsPreviewControl");
        FieldNames = GetFieldNamesDropdown(AssetTableColumns);
    }

    private List<GridColumnModel> ForceGetAssetColumns()
    {
        var result = _lookupManager.GetColumns("assignAbsPreviewControl", true).ToList();
        return result.Any() ? result : _lookupManager.SeedGridColumns<AssetModel>("assignAbsPreviewControl");
    }

    private void EditRow(FilterQueryRow queryRow)
    {
        FilterQueryRowGrid.EditRow(queryRow);
    }

    public void CancelEditRow(FilterQueryRow row)
    {
        // Undo Changes
        RiskTree.SelectedNode.Source.Filters =
            _assignAssetManager.GetFilterSelectionListFromTreeNode(RiskTree.SelectedNode.Source);
        UpdateFilterGridAndResultView();

        // Stop Edit
        FilterQueryRowGrid.CancelEditRow(row);
    }

    public void SaveRowChanges(FilterQueryRow row)
    {
        // Set fieldType based on FieldName
        row.FieldType = FieldNames[row.FieldName].Type;

        // Prevent incompatible Criteria with fieldType
        if (row.FieldType == DatabaseFieldType.Int &&
            row.Criterium is DatabaseSelectionCriterium.Contains
                or DatabaseSelectionCriterium.StartsWith
                or DatabaseSelectionCriterium.EndsWith)
            row.Criterium = DatabaseSelectionCriterium.Equals;

        // Push changes to DB
        _assignAssetManager.UpdateFilter(row);

        // Stop Edit
        FilterQueryRowGrid.CancelEditRow(row);

        // Update result
        UpdateFilterGridAndResultView();
    }

    private void SetColumns(List<AssetModel> assets)
    {
        AssetColumns.Clear();
        foreach (var field in typeof(AssetModel).GetFields(BindingFlags.NonPublic | BindingFlags.Instance))
        {
            foreach (var asset in assets)
            {
                if (field.GetValue(asset) == null) continue;
                var assetName = field.Name.Split('<')[1].Split('>')[0];
                if (assetName.ToLower() == "id" || assetName.ToLower() == "parentid" ||
                    assetName.ToLower() == "pickassets")
                    continue;

                AssetColumns.Add(assetName);
                break;
            }
        }
    }

    public void SelectScenario(object args)
    {
        SelectedScenario = (int) args;
        RiskTree.Initialize(_riskAnalysisManager.GetRisksTreeByScenario(SelectedScenario));

        var filters = _assignAssetManager.GetFiltersForScenario(SelectedScenario);
        //Filter out the filters that have a none existing field
        filters = filters.Where(x => x.FilterRows.Any(y => FieldNames.ContainsKey(y.FieldName))).ToList();

        AttachFiltersToNodes(filters, RiskTree.Node);

        InheritedSelectionFiltersGroups.Clear();
        UpdateFilterGridAndResultView();

        if (RiskTree.Node?.Nodes?.Any() == true)
        {
            RiskTree.SelectNode(RiskTree.Node.Nodes.First());
            PreviewText = ""; // Force a re-render
        }

        _pageNavigationManager.SetSelectedScenario(SelectedScenario);
    }

    private void AttachFiltersToNodes(List<FilterGroup> filterGroups, TreeNodeGeneric<RiskTreeObject> tree)
    {
        if (filterGroups == null) throw new ArgumentNullException(nameof(filterGroups));
        var flattenedTree = tree.Nodes.Flatten(n => n.Nodes).ToList();

        foreach (var node in flattenedTree)
        {
            var filter = filterGroups.FirstOrDefault(
                x => x.ScenarioId == SelectedScenario
                  && x.RiskObjectId == node.Source.RiskObjectId
                  && x.CollectionId == node.Source.CollectionId
                  && x.InstallationId == node.Source.InstallationId
                  && x.SystemId == node.Source.SystemId
                  && x.AssemblyId == node.Source.AssemblyId
                  && x.ComponentId == node.Source.ComponentId
                  && x.RiskId == node.Source.RiskId);

            if (filter == null) continue;
            filterGroups.Remove(filter);
            node.Source.Filters = filter;
            node.FilteredState = FilteredState.Self;
            SetFilteredRecursive(node.Nodes);

            if (!filterGroups.Any())
                break;
        }
    }

    public void ClickTreeNode(TreeNodeGeneric<RiskTreeObject> node)
    {
        InheritedSelectionFiltersGroups.Clear();
        var ancestors = node.GetAncestors();
        ancestors.Reverse();

        foreach (var parent in ancestors)
        {
            if (parent.FilteredState == FilteredState.None)
                break;

            if (parent.FilteredState == FilteredState.Self)
                InheritedSelectionFiltersGroups.Add(parent.Source.Filters.FilterRows);
        }

        InheritedSelectionFiltersGroups.Reverse();
        UpdateFilterGridAndResultView();
    }

    public void AddFilter()
    {
        if (string.IsNullOrWhiteSpace(_previewText))
            return;

        NewFilterQueryRow.Selection = _previewText;

        RiskTree.SelectedNode.Source.Filters = _assignAssetManager
            .AddFilter(RiskTree.SelectedNode.Source, NewFilterQueryRow, SelectedScenario);

        SetFiltered(RiskTree.SelectedNode);

        PreviewText = string.Empty;

        UpdateFilterGridAndResultView();
    }

    public void ChangeFieldName()
    {
        if (FieldNames[NewFilterQueryRow.FieldName].Type == DatabaseFieldType.String)
        {
            NewFilterQueryRow.Criterium = DatabaseSelectionCriterium.Contains;
            CriteriumOptions = new List<DatabaseSelectionCriterium>(_allCriteriumOptions);
        }
        else
        {
            CriteriumOptions = new List<DatabaseSelectionCriterium>
            {
                DatabaseSelectionCriterium.Equals,
                DatabaseSelectionCriterium.DoesNotEqual
            };
        }

        PreviewFilter(PreviewText);
    }

    public string GetFieldTypeForInputField()
    {
        if (NewFilterQueryRow?.FieldName == null)
            return "text";

        return FieldNames[NewFilterQueryRow.FieldName].Type == DatabaseFieldType.String
            ? "text"
            : "number";
    }

    public void RefreshPreviewFilter() => PreviewFilter(PreviewText);

    private void PreviewFilter(string text)
    {
        // Set fieldType based on FieldName but check if collection contains specific field
        if (!FieldNames.ContainsKey(NewFilterQueryRow.FieldName)) return;

        NewFilterQueryRow.FieldType = FieldNames[NewFilterQueryRow.FieldName].Type;
        NewFilterQueryRow.Selection = text;

        // Prevent incompatible Criterium with fieldType
        if (NewFilterQueryRow.FieldType == DatabaseFieldType.Int)
        {
            if (NewFilterQueryRow.Criterium is DatabaseSelectionCriterium.Contains
                or DatabaseSelectionCriterium.StartsWith or DatabaseSelectionCriterium.EndsWith)
                NewFilterQueryRow.Criterium = DatabaseSelectionCriterium.Equals;

            if (!double.TryParse(text, out _))
            {
                NewFilterQueryRow.Selection = "";
            }
        }

        UpdateFilterGridAndResultView(NewFilterQueryRow);
    }

    private void SetFiltered(TreeNodeGeneric<RiskTreeObject> node)
    {
        node.FilteredState = FilteredState.Self;
        SetFilteredRecursive(node.Nodes);
    }

    private static void SetFilteredRecursive(List<TreeNodeGeneric<RiskTreeObject>> risks)
    {
        foreach (var node in risks)
        {
            if (node.FilteredState == FilteredState.None)
                node.FilteredState = FilteredState.Inherited;

            SetFilteredRecursive(node.Nodes);
        }
    }

    private void DeleteRow(FilterQueryRow row)
    {
        // Remove Filter and refresh Grid
        RiskTree.SelectedNode.Source.Filters = _assignAssetManager.DeleteFilter(row);
        if (!RiskTree.SelectedNode.Source.HasFilters())
            SetNotFiltered(RiskTree.SelectedNode);

        UpdateFilterGridAndResultView();
    }

    public void SplitButtonItemClicked(AMproverSplitButtonItem args, FilterQueryRow item)
    {
        if (args?.Value == "Delete")
        {
            DeleteRow(item);
        }
        else
        {
            EditRow(item);
        }
    }

    private void SetNotFiltered(TreeNodeGeneric<RiskTreeObject> node)
    {
        if (node.Parent != null && node.Parent.FilteredState != FilteredState.None)
        {
            node.FilteredState = FilteredState.Inherited;
        }
        else
        {
            node.FilteredState = FilteredState.None;
            SetNotFilteredRecursive(node.Nodes);
        }
    }

    private void SetNotFilteredRecursive(List<TreeNodeGeneric<RiskTreeObject>> risks)
    {
        foreach (var node in risks)
        {
            if (node.Source.HasFilters()) continue;
            node.FilteredState = FilteredState.None;
            SetNotFilteredRecursive(node.Nodes);
        }
    }

    private void UpdateFilterGridAndResultView(FilterQueryRow previewFilter = null)
    {
        // All Filters Combined
        AllSelectionFilters.Clear();
        InheritedSelectionFiltersGroups.ForEach(objLevel => objLevel.ForEach(filter => filter.Inherited = true));
        AllSelectionFilters.AddRange(InheritedSelectionFiltersGroups.SelectMany(x => x));

        RiskTree.SelectedNode?.Source.GetFilterRows()?.ForEach(x => x.Inherited = false);
        AllSelectionFilters.AddRange(RiskTree.SelectedNode?.Source.GetFilterRows() ?? new List<FilterQueryRow>());

        // include previewFilter
        var allFiltersPlusPreviewFilter = new List<FilterQueryRow>(AllSelectionFilters);
        if (!string.IsNullOrWhiteSpace(previewFilter?.Selection) && FieldNames.ContainsKey(previewFilter?.FieldName))
            allFiltersPlusPreviewFilter.Add(previewFilter);

        // Radzen Grid doesn't automatically update
        FilterQueryRowGrid?.Reload();

        // Get Filtered Assets
        if (OnlyUsePreviewFilter)
        {
            FilteredAssets = previewFilter != null
                ? _assetBreakdownManager.GetFilteredAssets(AllAssets, new() {previewFilter})
                : _assetBreakdownManager.GetFilteredAssets(AllAssets, new());
        }
        else
        {
            FilteredAssets = _assetBreakdownManager.GetFilteredAssets(AllAssets, allFiltersPlusPreviewFilter);
        }

        // Update Result grid, which columns to show
        SetColumns(FilteredAssets);
    }

    public string GetSelectedNodeObjectName()
    {
        var nodeType = RiskTree.SelectedNode?.Source.NodeType;

        if (Enum.TryParse<ObjectLevel>(nodeType?.ToString(), true, out var objectLevel))
            return GetObjectLevel(objectLevel);

        return nodeType?.ToString() ?? Strings.NotAvailable;
    }

    /// <summary>
    /// Rebind all assets to all Risks based on the filters that were created
    /// </summary>
    public async Task RebindAssets()
    {
        ShowLoadingDialog();

        // Force loading dialog to pop up
        await Task.Delay(1);

        _assignAssetManager.LinkAssets(RiskScenarios.Select(x => x.Id).ToList(), AllAssets);
        _dialogService.Close();
        ShowCompleteDialog("Successfully linked assets to Risks");
    }

    /// <summary>
    /// Rebind all assets to all Risks based on the filters that were created
    /// </summary>
    public async Task ClearBindings()
    {
        ShowLoadingDialog();

        // Force loading dialog to pop up
        await Task.Delay(1);

        _assignAssetManager.ClearBindings();
        _dialogService.Close();
        ShowCompleteDialog("Successfully cleared bindings");
    }

    /// <summary>
    /// Rebind all assets to all Risks based on the filters that were created
    /// </summary>
    private void FixFilters()
    {
        ShowLoadingDialog();
        _assignAssetManager.FixBrokenFilters();
        _navigationManager.NavigateTo("/value-risks-on-abs/assign-assets", true);
    }

    private void ShowLoadingDialog()
    {
        _dialogService.Open<RebindAssetResultDialog>("Loading",
            new Dictionary<string, object>
            {
                {nameof(RebindAssetResultDialog.Loading), true},
                {nameof(RebindAssetResultDialog.Text), "Please wait"}
            });
    }

    private void ShowCompleteDialog(string text)
    {
        _dialogService.Open<RebindAssetResultDialog>("Completed",
            new Dictionary<string, object>
            {
                {nameof(RebindAssetResultDialog.Loading), false},
                {nameof(RebindAssetResultDialog.Text), text}
            });
    }
}