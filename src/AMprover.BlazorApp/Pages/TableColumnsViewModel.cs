using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Logging;
using Radzen;

namespace AMprover.BlazorApp.Pages;

public class TableColumnsViewModel : BaseViewModel, IEntityEditorViewModel
{
    private readonly ILookupManager _lookupManager;

    public TableColumnsViewModel(
        ILoggerFactory loggerFactory,
        ILookupManager lookupManager, 
        DialogService dialogService) : base(loggerFactory)
    {
        _lookupManager = lookupManager;
        _dialogService = dialogService;
    }

    [Parameter] public string ControlName { get; set; }

    [Parameter] public EventCallback<object> SaveCallback { get; set; }

    public List<GridColumnModel> GridColumns { get; set; }

    public List<FieldType> NumberFieldTypes { get; private set; } = new() { FieldType.Integer, FieldType.IntegerNoStyling, FieldType.Currency, FieldType.DetailCurrency, FieldType.Number, FieldType.Percentage };

    public EntityEditorMode EditorMode { get; }
        
    public string ErrorText { get; set; }

    public bool ShowError { get; set; }

    public override void OnInitialized()
    {
        GridColumns = _lookupManager.GetColumns(ControlName, false);
    }

    public async void ValidTaskSubmitted(EditContext editContext)
    {
        var gridColumns = (List<GridColumnModel>)editContext.Model;
        CreateOrUpdateObjectAndNavigate(gridColumns);

        if (SaveCallback.HasDelegate)
            await SaveCallback.InvokeAsync();
    }

    private void CreateOrUpdateObjectAndNavigate(IReadOnlyCollection<GridColumnModel> columns)
    {
        _lookupManager.UpdateColumns(columns);
        _logger.LogInformation($"Updating columns {columns.FirstOrDefault()?.ControlName} succeeded.");
        _dialogService.Close(true);
    }
        
    public void InvalidTaskSubmitted(EditContext editContext)
    {
        ErrorText = "Update NOT executed, invalid form submitted";
        _logger.LogWarning($"Invalid form submitted for gridcolumns update.");
        _logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject((List<GridColumnModel>) editContext.Model));
    }
}