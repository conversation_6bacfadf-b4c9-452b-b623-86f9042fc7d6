@page "/reports/{SelectedReportType}"
@using AMprover.BlazorApp.Helpers
@using AMprover.BusinessLogic.Enums.Reports
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<ViewReportViewModel>
@inject IStringLocalizer<ReportPage> _localizer
@inject IJSRuntime JSRuntime

<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
            Home
        </NavLink>
        <NavLink class="breadcrumb-item" href="/reports" Match="NavLinkMatch.All">
            Reports
        </NavLink>
        <NavLink class="breadcrumb-item" aria-current="page" Match="NavLinkMatch.All">
            @BindingContext.ReportName
        </NavLink>
    </ol>
</nav>

<div class="row">
    <div class="col-md-12">

        <h2>@BindingContext.ReportName</h2>

        <RadzenTabs @bind-SelectedIndex=@BindingContext.TabIndex>
            <Tabs>
                <RadzenTabsItem Text="Filters">

                    @if (BindingContext.SelectedReport != ReportType.CommonActions)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@_localizer["RpFilterLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                    @bind-Value=@BindingContext.SelectedFilter 
                                    Data=@BindingContext.Filters.ToDictionary(x => x, x => x.Name)
                                    Change=@(args => BindingContext.ApplyFilter(args))/>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-1">
                                <label>@_localizer["RpFilterNameLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMproverTextBox @bind-Value=@BindingContext.SelectedFilterName/>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-1">
                                <div class="form-group">
                                    <RadzenButton Text="Save filter" Click=@BindingContext.SaveFilter Disabled=@(BindingContext.Busy || string.IsNullOrWhiteSpace(BindingContext.SelectedFilterName)) />
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <RadzenButton Text="Delete filter" Click=@BindingContext.DeleteFilter Disabled=!BindingContext.FilterSelected />
                                </div>
                            </div>
                        </div>

                        @if (BindingContext.ShowFilterNameError)
                        {
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="alert alert-danger">@_localizer["RpFilterSaveErrorLbl"]</div>
                                </div>
                            </div>
                        }

                        <hr/>
                    }

                    <h3>@(BindingContext.SelectedReport is ReportType.CommonActions ? " " : _localizer["RpFilterTextLbl"])</h3>

                    @if (BindingContext.ScenarioList.Any() && BindingContext.SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis or ReportType.PmoRiskAnalysis
                    or ReportType.TaskPlanExtended or ReportType.PmoPreventiveActions or ReportType.PmoRisk or ReportType.Cluster or ReportType.Spares or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@_localizer["RpScenarioLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                                Data=@BindingContext.ScenarioList.ToDictionary(x => (int?)x.Id, y => y.Name)
                                                @bind-Value=@BindingContext.QueryParams.ScenarioFilter
                                                Change=BindingContext.ChangeSelection />
                            </div>
                        </div>
                    }
                    
                    @if (BindingContext.CollectionList.Count > 0 && BindingContext.SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis
                    or ReportType.PmoRiskAnalysis or ReportType.TaskPlanExtended or ReportType.PmoPreventiveActions or ReportType.PmoRisk or ReportType.Cluster or ReportType.Spares or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@BindingContext.GetObjectLevel(ObjectLevel.Collection):</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@BindingContext.CollectionList.ToDictionary(x => (int?)x.Id, x => x.Name)
                                            @bind-Value=@BindingContext.QueryParams.CollectionFilter
                                            Change=BindingContext.ChangeSelection />
                            </div>
                        </div>
                    }
                    
                    @if (BindingContext.InstallationList.Count > 0 && BindingContext.SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis
                    or ReportType.PmoRiskAnalysis or ReportType.TaskPlanExtended or ReportType.PmoRisk or ReportType.PmoPreventiveActions or ReportType.Cluster or ReportType.Spares or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@BindingContext.GetObjectLevel(ObjectLevel.Installation):</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@BindingContext.InstallationList.ToDictionary(x => (int?)x.Id, x => x.Name)
                                            @bind-Value=@BindingContext.QueryParams.InstallationFilter
                                            Change=BindingContext.ChangeSelection />
                            </div>
                        </div>
                    }
                    
                    @if (BindingContext.RiskObjectList.Any() && BindingContext.SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis or ReportType.Cluster or ReportType.PmoRiskAnalysis or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@_localizer["RpRiskObjectLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@BindingContext.RiskObjectList.ToDictionary(x => (int?)x.Id, x => x.Name)
                                            @bind-Value=@BindingContext.QueryParams.RiskObjectFilter
                                            Change=BindingContext.ChangeSelection />
                            </div>
                        </div>
                    }
                    
                    @if (BindingContext.SystemList.Count > 0 && BindingContext.SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis
                    or ReportType.PmoRiskAnalysis or ReportType.TaskPlanExtended or ReportType.PmoRisk or ReportType.PmoPreventiveActions or ReportType.Spares)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>@BindingContext.GetObjectLevel(ObjectLevel.System):</label>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@BindingContext.SystemList.ToDictionary(x => (int?)x.Id, x => x.Name) 
                                            @bind-Value=@BindingContext.QueryParams.SystemFilter
                                            Change=BindingContext.ClearReport />
                            </div>
                        </div>
                    }
                    
                    @if (BindingContext.SiCategories.Any() && BindingContext.SelectedReport is ReportType.SignificantItems)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@_localizer["RpSiCategoriesLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@BindingContext.SiCategories.ToNullableDictionary()
                                            @bind-Value=@BindingContext.QueryParams.SiCategoryFilter 
                                            Change=BindingContext.ClearReport />
                            </div>
                        </div>
                    }

                    @if (BindingContext.SelectedReport is ReportType.Cluster or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@_localizer["RpStatusLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@BindingContext.StatusList.ToNullableDictionary()
                                            @bind-Value=@BindingContext.QueryParams.StatusFilter />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-1">
                                <label>@_localizer["RpClusterNameLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMproverTextBox AllowClear="true" AllowFiltering="true" @bind-Value=@BindingContext.QueryParams.ClusterNameFilter />
                            </div>
                        </div>
                    }

                    @if (BindingContext.SiCategories.Any() && BindingContext.SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis or ReportType.PmoRiskAnalysis or ReportType.Cluster or ReportType.FunctionalTree)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <div class="form-group">
                                    <RadzenButton Text="Generate" Click=@BindingContext.LoadReport BusyText="Generating..." IsBusy=BindingContext.Busy />
                                </div>
                            </div>
                        </div>
                    }

                    @if (BindingContext.ScenarioList.Any() && 
                         BindingContext.SelectedReport is ReportType.TaskPlanExtended or 
                             ReportType.CommonActions or 
                             ReportType.Cluster or 
                             ReportType.PmoRisk or 
                             ReportType.PmoPreventiveActions or 
                             ReportType.Spares or
                             ReportType.RETTaskExport)
                    {
                        <hr/>

                        <div class="row">
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>FileType:</label>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown Data=@BindingContext.RenderTypes
                                            @bind-Value=@BindingContext.SelectedRenderType/>
                            </div>
                        </div>
                    
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <RadzenButton Text="Download" Click=@BindingContext.DownloadReport ButtonStyle=ButtonStyle.Secondary Disabled=BindingContext.Busy BusyText="Downloading..." IsBusy=BindingContext.Busy />
                                </div>
                            </div>
                        </div>
                    }

                </RadzenTabsItem>

                <RadzenTabsItem Text="Result" Disabled=@string.IsNullOrWhiteSpace(BindingContext.Report)>
                    <iframe style="max-width:100%;max-height:75vh" src=@BindingContext.Report></iframe>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
</div>

@code
{
    [Parameter]
    public string SelectedReportType { get; set; }
}