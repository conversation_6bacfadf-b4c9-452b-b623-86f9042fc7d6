using AMprover.BlazorApp.Helpers;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Enums.Reports;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Models.Reports;
using BlazorDownloadFile;
using Microsoft.JSInterop;
using static System.Int32;

namespace AMprover.BlazorApp.Pages.Report;

public class ViewReportViewModel : BaseViewModel
{
    private readonly IWebHostEnvironment _webHostEnvironment;
    private readonly IReportManager _reportManager;
    private readonly IRiskAnalysisManager _riskAnalysisManager;
    private readonly IDropdownManager _dropdownManager;
    private readonly IBlazorDownloadFileService _downloadFileService;
    private readonly IReportFilterManager _reportFilterManager;
    private readonly IObjectManager _objectManager;
    private readonly IJSRuntime _jsRuntime;

    [Parameter] public string SelectedReportType { get; set; }

    public bool ShowFilterNameError { get; set; }

    public ReportType SelectedReport { get; set; }

    public string SelectedRenderType { get; set; } = "Excel";

    public string Report { get; set; }

    public string ReportName { get; set; }

    public ReportViewQueryParams QueryParams { get; set; }

    public List<ObjectModel> Objects { get; set; } = new();

    public List<ReportFilterModel> Filters { get; private set; } = new();

    public Dictionary<int, string> SiCategories { get; private set; } = new();

    public List<ScenarioModel> ScenarioList { get; set; }

    public Dictionary<int, string> ClusterList { get; set; } = new();

    public Dictionary<int, string> StatusList { get; set; } = new();

    public List<RiskObjectModel> RiskObjectList { get; set; }

    public List<ObjectModel> CollectionList { get; set; }

    public List<ObjectModel> InstallationList { get; set; }

    public List<ObjectModel> SystemList { get; set; }

    public int TabIndex { get; set; }

    public bool Busy { get; set; }

    public ReportFilterModel SelectedFilter { get; set; }

    public string SelectedFilterName { get; set; }

    public bool FilterSelected => SelectedFilter != null;

    public Dictionary<string, string> RenderTypes { get; set; }

    public ViewReportViewModel(ILoggerFactory loggerFactory, DialogService dialogService, ILookupManager lookupManager,
        IObjectManager objectManager, IWebHostEnvironment webHostEnvironment, IReportManager reportManager,
        IDropdownManager dropdownManager,
        ReportViewQueryParams queryParams, IRiskAnalysisManager riskAnalysisManager,
        IBlazorDownloadFileService downloadFileService,
        IReportFilterManager reportFilterManager, IJSRuntime jsRuntime) : base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _webHostEnvironment = webHostEnvironment;
        _reportManager = reportManager;
        _riskAnalysisManager = riskAnalysisManager;
        QueryParams = queryParams;
        _dropdownManager = dropdownManager;
        _downloadFileService = downloadFileService;
        _reportFilterManager = reportFilterManager;
        _objectManager = objectManager;
        _jsRuntime = jsRuntime;
    }

    public override void OnInitialized()
    {
        ScenarioList = _reportFilterManager.GetScenariosForReports();
        ClusterList = _dropdownManager.GetClusterDict();
        RiskObjectList = ScenarioList.SelectMany(x => x.RiskObject).ToList();
        CollectionList = GetCollections(RiskObjectList);
        InstallationList = GetInstallations(RiskObjectList);
        StatusList = _dropdownManager.GetStatusDict();

        Objects = _riskAnalysisManager.GetAllObjects();
        SiCategories = _dropdownManager.GetSiCategoriesDict();
        ObjectLevels = _objectManager.GetObjectLevelNames();

        SystemList = Objects.Where(x => x.Level == 2).ToList();

        if (SelectedReportType == ReportType.RETTaskExport.ToString())
            RenderTypes = EnumHelper.GetEnumSelectList<RenderType>().Where(x => x.Value == "Xml")
                .ToDictionary(x => x.Value, x => x.Value);
        else
            RenderTypes = EnumHelper.GetEnumSelectList<RenderType>().ToDictionary(x => x.Value, x => x.Value);

        if (!Enum.TryParse<ReportType>(SelectedReportType, out var reportType)) return;

        SelectedReport = reportType;
        ReportName = SelectedReport.GetReportName(false);

        Filters = _reportFilterManager.GetFiltersByReport(SelectedReport.ToString());

        TabIndex = 0;
    }

    #region Report methods

    public async Task LoadReport()
    {
        Busy = true;
        var report = await GetReportAsBase64(SelectedReport == ReportType.RETTaskExport ? RenderType.Xml : RenderType.Pdf).ConfigureAwait(false);
        Report = await _jsRuntime.InvokeAsync<string>("CreateUrl", report).ConfigureAwait(false);
        TabIndex = 1;
        Busy = false;
    }

    public void ChangeSelection()
    {
        ChangeSelectionInternal();
        ClearReport();
        ChangeSelectionInternal();
    }

    private void ChangeSelectionInternal()
    {
        RiskObjectList = GetScenarios().SelectMany(x => x.RiskObject).ToList();
        CollectionList = GetCollections(RiskObjectList);

        if (QueryParams.CollectionFilter != null)
            RiskObjectList = RiskObjectList.Where(x => x.ParentObjectId == QueryParams.CollectionFilter).ToList();

        InstallationList = GetInstallations(RiskObjectList);

        if (QueryParams.InstallationFilter != null)
            RiskObjectList = RiskObjectList.Where(x => x.ObjectId == QueryParams.InstallationFilter).ToList();

        SystemList = QueryParams.RiskObjectFilter != null
            ? _objectManager.GetAllSystemsFromRiskObject(QueryParams.RiskObjectFilter.Value)
            : Objects.Where(x => x.Level == 2).ToList();
    }

    private List<ScenarioModel> GetScenarios()
    {
        if (QueryParams.ScenarioFilter == null)
            return ScenarioList;

        var selectedScenario = ScenarioList.FirstOrDefault(x => x.Id == QueryParams.ScenarioFilter);

        return selectedScenario != null
            ? new List<ScenarioModel> {selectedScenario}
            : ScenarioList;
    }

    private List<ObjectModel> GetCollections(List<RiskObjectModel> riskObjects)
    {
        return riskObjects.Where(x => x.ParentObject != null)
            .Select(x => x.ParentObject)
            .DistinctBy(x => x.Id).ToList();
    }

    private List<ObjectModel> GetInstallations(List<RiskObjectModel> riskObjects)
    {
        return riskObjects.Where(x => x.ObjectId != 0)
            .Select(x => x.Object)
            .DistinctBy(x => x.Id).ToList();
    }

    public void ClearReport()
    {
        if (QueryParams.CollectionFilter != null)
        {
            var collection = CollectionList.FirstOrDefault(x => x.Id == QueryParams.CollectionFilter);
            QueryParams.CollectionFilter = collection?.Id;
        }

        if (QueryParams.InstallationFilter != null)
        {
            var installation = InstallationList.FirstOrDefault(x => x.Id == QueryParams.InstallationFilter);
            QueryParams.InstallationFilter = installation?.Id;
        }

        if (QueryParams.RiskObjectFilter != null)
        {
            var riskObject = RiskObjectList.FirstOrDefault(x => x.Id == QueryParams.RiskObjectFilter);
            QueryParams.RiskObjectFilter = riskObject?.Id;
        }

        Report = string.Empty;
    }

    public async Task DownloadReport()
    {
        Busy = true;
        try
        {
            if (SelectedReportType == ReportType.RETTaskExport.ToString())
                SelectedRenderType = RenderType.Xml.ToString();
            
            if (Enum.TryParse(SelectedRenderType, out RenderType selectedRenderType))
                await _downloadFileService.DownloadFile(
                    SelectedReport.GetReportName(true),
                    await GetReportAsBase64(selectedRenderType),
                    selectedRenderType.ToContentType());
        }
        catch(Exception e)
        {
			_logger.LogError(e,"Cannot download report");
        }

        Busy = false;
    }

    private async Task<string> GetReportAsBase64(RenderType renderType)
    {
        return Convert.ToBase64String(
            await _reportManager.GetReport(_webHostEnvironment.WebRootPath, SelectedReport, renderType, Currency,
                QueryParams.CollectionFilter, QueryParams.InstallationFilter, QueryParams.SystemFilter, null, null,
                QueryParams.ScenarioFilter, QueryParams.RiskObjectFilter, QueryParams.SiCategoryFilter,
                GetObjectLevel(ObjectLevel.Collection),
                GetObjectLevel(ObjectLevel.Installation),
                GetObjectLevel(ObjectLevel.System),
                GetObjectLevel(ObjectLevel.Component),
                GetObjectLevel(ObjectLevel.Assembly),
                QueryParams.StatusFilter, 
                QueryParams.ClusterNameFilter)
        );
    }

    #endregion

    #region Filter methods

    public void ApplyFilter(object selectedFilter)
    {
        if (selectedFilter == null) return;
        ReportFilterModel filter = (ReportFilterModel) selectedFilter;

        SelectedFilter = filter;
        SelectedFilterName = filter.Name;

        var selectedFilters = filter.SelectedIds.Split(";");

        QueryParams.ScenarioFilter = GetValue(selectedFilters, 0);
        QueryParams.CollectionFilter = GetValue(selectedFilters, 1);
        QueryParams.InstallationFilter = GetValue(selectedFilters, 2);
        QueryParams.SystemFilter = GetValue(selectedFilters, 3);
        QueryParams.SiCategoryFilter = GetValue(selectedFilters, 4);
        QueryParams.StatusFilter = GetValue(selectedFilters, 5);

        QueryParams.ClusterNameFilter = selectedFilters.Skip(6).FirstOrDefault();
    }

    private int? GetValue(string[] selectedIds, int index)
    {
        return TryParse(selectedIds.Skip(index).FirstOrDefault(), out int result)
            ? result
            : null;
    }

    public void SaveFilter()
    {
        if (Filters.Any(x => x.Name == SelectedFilterName && x.SelectedIds == QueryParams.ToFilterString()))
        {
            ShowFilterNameError = true;
            return;
        }

        ShowFilterNameError = false;

        SelectedFilter ??= new ReportFilterModel
        {
            ReportName = SelectedReport.ToString()
        };

        SelectedFilter.Name = SelectedFilterName;
        SelectedFilter.SelectedIds = QueryParams.ToFilterString();

        var result = _reportFilterManager.SaveFilterForReport(SelectedFilter);
        Filters.Add(result);
    }

    public void DeleteFilter()
    {
        _reportFilterManager.DeleteFilterForReport(SelectedFilter);
        Filters.Remove(SelectedFilter);
        SelectedFilter = null;
        SelectedFilterName = string.Empty;
    }

    #endregion
}