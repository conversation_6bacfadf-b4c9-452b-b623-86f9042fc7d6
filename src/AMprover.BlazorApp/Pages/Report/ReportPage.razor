@page "/reports"
@using AMprover.BusinessLogic.Enums.Reports
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<ReportViewModel>
@inject IStringLocalizer<ReportPage> _localizer

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Reports" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@_localizer["RpHeaderTxt"] DialogContent=@_localizer["RpMenuTxt"] />
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <h2>@_localizer["RpHeaderTxt"]</h2>
    </div>
</div>

<div class="row card-row">
    <div class="col-sm-2"><h5>@_localizer["RpDataPreparationTxt"]</h5></div>
    @*<ReportCard CardTitle=@_localizer["RpFunctTreeRep"] CardText=@_localizer["RpFunctTreeRepTxt"]
                QueryParams=@BindingContext.ReportViewQueryParams
                Category="Masterdata" ReportType=@ReportType.FunctionalTree Icon="fas fa-th"/>

    <ReportCard CardTitle=@_localizer["RpABSTreeRep"] CardText=@_localizer["RpABSTreeRepTxt"]
                Category="Masterdata" ReportType=@ReportType.SignificantItems Icon="fas fa-sitemap"/>*@

    <ReportCard CardTitle=@_localizer["RpCommonActionsRep"] CardText=@_localizer["RpCommonActionsRepTxt"]
                Category="Masterdata" ReportType=@ReportType.CommonActions Icon="fas fa-file-alt"/>
</div>
<div class="row card-row">
    <div class="col-sm-2"><h5>@_localizer["RpAssessmentTxt"]</h5></div>
    <ReportCard CardTitle=@_localizer["RpValueRiskRep"] CardText=@_localizer["RpValueRiskRepTxt"]
                QueryParams=@BindingContext.ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.RiskAnalysis Icon="fas fa-puzzle-piece"/>

    <ReportCard CardTitle=@_localizer["RpSapaRep"] CardText=@_localizer["RpSapaRepTxt"]
                QueryParams=@BindingContext.ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.SapaAnalysis Icon="fas fa-puzzle-piece" />

    <ReportCard CardTitle=@_localizer["RpPmoValueRiskRep"] CardText=@_localizer["RpPmoValueRiskRepTxt"]
                QueryParams=@BindingContext.ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.PmoRiskAnalysis Icon="fas fa-puzzle-piece" />
</div>
<div class="row card-row">
    <div class="col-sm-2"></div>
    <ReportCard CardTitle=@_localizer["RpClusterRep"] CardText=@_localizer["RpClusterRepTxt"]
                QueryParams=@BindingContext.ReportViewQueryParams
                Category="Cluster" ReportType=@ReportType.Cluster Icon="fas fa-tasks" />

    @if (BindingContext.ShowRETTaskExport)
    {
        <ReportCard CardTitle=@_localizer["RpRETTaskExportRep"] CardText=@_localizer["RpRETTaskExportRepTxt"]
                    QueryParams=@BindingContext.ReportViewQueryParams
                    Category="Cluster" ReportType=@ReportType.RETTaskExport Icon="fas fa-th-list"/>
    }
</div>
<div class="row card-row">
    <div class="col-sm-2"><h5>@_localizer["RpTabularTxt"]</h5></div>
    <ReportCard CardTitle=@_localizer["RpValuePmoRiskRep"] CardText=@_localizer["RpValuePmoRiskRepTxt"]
                QueryParams=@BindingContext.ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.PmoRisk Icon="fas fa-th-list"/>
    
    <ReportCard CardTitle=@_localizer["RpValuePmoPrevActRep"] CardText=@_localizer["RpValuePmoPrevActRepTxt"]
                QueryParams=@BindingContext.ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.PmoPreventiveActions Icon="fas fa-th-list"/>

    <ReportCard CardTitle=@_localizer["RpSparesRep"] CardText=@_localizer["RpSparesRepTxt"]
                QueryParams=@BindingContext.ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.Spares Icon="fas fa-th-list"/>
</div>
<div class="row card-row">
    <div class="col-sm-2"></div>
    <ReportCard CardTitle=@_localizer["RpExtTaskPlanRep"] CardText=@_localizer["RpExtTaskPlanRepTxt"]
                QueryParams=@BindingContext.ReportViewQueryParams
                Category="Cluster" ReportType=@ReportType.TaskPlanExtended Icon="fas fa-box"/>

</div>