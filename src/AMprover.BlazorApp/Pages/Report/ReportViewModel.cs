using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.Data.Infrastructure;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.Report;

public class ReportViewModel : BaseViewModel
{
    private readonly IRiskAnalysisManager _riskAnalysisManager;
    private readonly IDropdownManager _dropdownManager;
    private readonly IObjectManager _objectManager;
    
    public ReportViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager, IObjectManager objectManager,
        IDropdownManager dropdownManager,IConfiguration configuration,IRiskAnalysisManager riskAnalysisManager, NavigationManager navManager, IAssetManagementPortfolioResolver assetManagementPortfolioResolver) :
        base(loggerFactory, lookupManager,configuration, assetManagementPortfolioResolver)
    {
        _riskAnalysisManager = riskAnalysisManager;
        _dropdownManager = dropdownManager;
        _objectManager = objectManager;
        ReportViewQueryParams = new ReportViewQueryParams(loggerFactory, navManager);
    }

    public List<ObjectModel> Objects { get; set; } = new List<ObjectModel>();

    public Dictionary<int, string> Scenarios { get; set; } = new Dictionary<int, string>();

    public Dictionary<int, string> SiCategories { get; set; } = new Dictionary<int, string>();

    public ReportViewQueryParams ReportViewQueryParams { get; set; }
    
    public bool ShowRETTaskExport { get; set; }
    
    public override void OnInitialized()
    {
        Objects = _riskAnalysisManager.GetAllObjects();
        Scenarios = _dropdownManager.GetScenarioDict();
        SiCategories = _dropdownManager.GetSiCategoriesDict();
        ObjectLevels = _objectManager.GetObjectLevelNames();

        var reports = GetReports();

        if (reports.Any() && reports.Any(x => x.Report == "RETTaskExport"))
            ShowRETTaskExport = true;
    }
}