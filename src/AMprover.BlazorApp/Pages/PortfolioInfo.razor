@page "/portfolioinfo"
@using Microsoft.AspNetCore.Identity;
@using System.Security.Claims
@using AMprover.Data.Infrastructure
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<Data.Entities.Identity.UserAccount> UserManager
@inject IPortfolioManager PortfolioManager
@inject AssetManagementDbContext assetMgtDbContext
@inject IPortfolioSetupManager PortfolioSetupManager

<h2>Authentication details</h2>

<hr />

<p>@_authMessage</p>
<ul>
    <li>Username = @_authUsername</li>
    <li>Name = @_authFullname</li>
</ul>
@if (_authClaims.Any())
{
    <strong>Claims</strong>
    <ul>
        @foreach (var claim in _authClaims)
        {
            <li>@claim.Type: @claim.Value</li>
        }
    </ul>
}
<hr />

<h3>Portfolios for logged-in user</h3>
<ul>
    @foreach (var portfolio in _userPortfolios)
    {
        <li>
            Name = @portfolio.Name ; Database = @portfolio.DatabaseName ; Selected = @(portfolio.Id == _userPorfolioSelectedId)
        </li>
    }
</ul>

<hr />

<h3>Portfolio database info</h3>
<i>Database: @_dbName</i>
<br />
<i>Connection Ok?: @_dbAvailable</i>

<h3>Fake Exception</h3>
<RadzenButton Icon="error" Text="Fake Error" Size="ButtonSize.Medium" Click="@(args => ThrowUp())" />

@code {
    private string _authUsername;
    private string _authFullname;
    private string _authMessage;
    private IEnumerable<Claim> _authClaims = Enumerable.Empty<Claim>();

    private IEnumerable<PortfolioModel> _userPortfolios = Enumerable.Empty<PortfolioModel>();
    private int? _userPorfolioSelectedId;

    private IEnumerable<Type> _entityList = Enumerable.Empty<Type>();
    private string _userPortfolioSelectedName;
    private string _dbName;
    private bool _dbAvailable;

    private void ThrowUp()
    {
        // Any unhandled exception is fatal to a circuit. For more information, see the preceding section on how a Blazor Server app reacts to unhandled exceptions.
        // https://docs.microsoft.com/en-us/aspnet/core/blazor/fundamentals/handle-errors?view=aspnetcore-5.0&pivots=server#how-a-blazor-server-app-reacts-to-unhandled-exceptions
        throw new Exception("kapoet boem knal");
    }

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity.IsAuthenticated)
        {
            _authMessage = $"{user.Identity.Name} is authenticated.";
            _authClaims = user.Claims;

            var loggedInUser = await UserManager.FindByNameAsync(user.Identity.Name);

            _authUsername = loggedInUser?.UserName;
            _authFullname = loggedInUser?.Name;

            // Portfolio information
            _userPortfolios = PortfolioManager.GetPortfoliosForLoggedInUser().ToList();

            var userPortfolioSelected = PortfolioManager.GetCurrentPortfolio();

            _userPorfolioSelectedId = userPortfolioSelected?.Id;
            _userPortfolioSelectedName = userPortfolioSelected?.Name;
            _dbName = userPortfolioSelected.DatabaseName;
            _dbAvailable = assetMgtDbContext.Database.CanConnect();
        }
        else
        {
            _authMessage = "The user is NOT authenticated.";
        }

    }

}
