@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<NewClusterWidgetViewModel>
@inject IStringLocalizer<ClusterPage> _localizer

<div class="form-group">@_localizer["CwHeaderTxt"]</div>

<div class="form-group">
    <label>@_localizer["CwClustNameLbl"]:</label>
    <RadzenTextBox @bind-Value=@BindingContext.Name Name="Name" class="form-control"/>
    <RadzenRequiredValidator Component="Name"/>
</div>

<div class="form-group">
    <label>@_localizer["CwPartOfClustLbl"]:</label>
    <RadzenDropDown Name="PartOfCluster" AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    TextProperty="Value" ValueProperty="Key" class="form-control"
                    Data="@BindingContext.ClusterDict"
                    @bind-Value="@BindingContext.PartOfCluster"/>
    <RadzenRequiredValidator DefaultValue="0" Component="PartOfCluster"></RadzenRequiredValidator>
</div>


<RadzenButton IsBusy=@BindingContext.IsBusy Text=@_localizer["CwCreateBtn"] Click=@BindingContext.CreateNewCluster></RadzenButton>

@code
{
    [Parameter] public int? PartOfCluster { get; set; }

    [Parameter]
    public Action<ClusterModel> CallBack { get; set; }
}