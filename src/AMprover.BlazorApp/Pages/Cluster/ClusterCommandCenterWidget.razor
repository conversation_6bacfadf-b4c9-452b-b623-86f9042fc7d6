@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<ClusterCommandCenterViewModel>
@inject IStringLocalizer<ClusterPage> _localizer

<div>
    <div class="form-group my-3">
        @ConfirmationText
    </div>

    @if (Options?.Any() == true)
    {
        <AMDropdown 
            @bind-Value=@BindingContext.SelectedId
            Data=Options
            Label="Options"
            Required=true 
            AllowFiltering=true />
    }

    @if (ExtraConfirmation)
    {
        <div class="form-group my-3">
            <label>@_localizer["CCCWidgetTxtEnter"] <span class="select-on-click"><b>@ValidationText</b></span> @_localizer["CCCWidgetTxtWarning"]</label>
            <RadzenTextBox @bind-Value=@BindingContext.ConfirmText Name="Name" class="form-control"/>
        </div>
    }

    @if (!string.IsNullOrWhiteSpace(BindingContext.ErrorMessage))
    {
        <div class="alert alert-danger" role="alert">
            @BindingContext.ErrorMessage
        </div>
    }

    <RadzenButton ButtonStyle=ButtonStyle.Danger Text="@ButtonText" Click=@BindingContext.ConfirmProcessing Disabled=@(Options?.Any() == true && BindingContext.SelectedId == 0) />

</div>

@code{

    [Parameter]
    public EventCallback Callback { get; set; }

    [Parameter]
    public EventCallback<int> CallbackInt { get; set; }

    [Parameter]
    public bool ExtraConfirmation { get; set; }

    [Parameter]
    public string ButtonText { get; set; } = "Start";

    [Parameter]
    public string ValidationText { get; set; } = "I AGREE";
    
    [Parameter]
    public string ConfirmationText { get; set; } = "Are you sure?";

    [Parameter]
    public Dictionary<int, string> Options { get; set; }
}