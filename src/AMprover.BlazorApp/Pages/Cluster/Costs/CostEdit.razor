@page "/cluster/costs/{CostId:int}"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<TaskPlanEditViewModel>
@inject IStringLocalizer<CostEdit> _localizer
@inject IGlobalDataService GlobalDataService;

@if (BindingContext.Cost != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem="ClusterCostModel" Data=@BindingContext.Cost Submit=@BindingContext.ValidTaskSubmitted InvalidSubmit=@BindingContext.InvalidTaskSubmitted>
        <div class="row">
            <div class="col-sm-12">
                <AMDropdown Label=@_localizer["CeWCommonCostLbl"]
                            Data=@BindingContext.CommonCosts
                            @bind-Value=@BindingContext.Cost.CommonCostId
                            Change=@(args => BindingContext.OnCommonCostChange(args))
                            Required=true AllowClear="true" AllowFiltering="true" />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput TValue="decimal?" Format="F2" Label=@_localizer["CeWQuantityLbl"] Change=@BindingContext.RecalculateCost @bind-Value=@BindingContext.Cost.Quantity/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput TValue="decimal" Format="c2" Label=@_localizer["CeWPriceLbl"] Change=@BindingContext.RecalculateCost @bind-Value=@BindingContext.Cost.Price/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput ReadOnly="true" Format="F2" Label=@_localizer["CeWCostsLbl"] @bind-Value=@BindingContext.Cost.Cost/>
            </div>
        </div>
        <div class="row">
            @if (!BindingContext.HideUnits)
            {
                <div class="col-sm-4">
                    <AMproverNumberInput Format="F2" Label=@_localizer["CeWUnitsLbl"] @bind-Value=@BindingContext.Cost.Units/>
                </div>
            }
            <div class="col-sm-4">
                <AMproverTextBox ReadOnly=true Label="Calculation type" class="form-control" @bind-Value=@BindingContext.Cost.CalculationType />
            </div>
            <div class="col-sm-4">
                <label>@_localizer["CeWCcDivisionLbl"]:</label>
                <AMproverCheckbox @bind-Value=@BindingContext.Cost.IsCommonTaskCost TValue=bool?/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <div class="form-group">
                    <label>@_localizer["CeWActionLbl"]:</label>
                    <AMDropdown AllowClear="true" AllowFiltering="true" 
                                Data=@BindingContext.Tasks.ToNullableDictionary()
                                @bind-Value="@BindingContext.Cost.TaskId" 
                                Change=@(args => BindingContext.OnTaskChange(args))/>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label>@_localizer["CeWMrbNameLbl"]:</label>
                    <p class="form-control readonly">@BindingContext.SelectedMrbName</p>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <AMDropdown AllowClear="false" AllowFiltering="true" Required=true
                                Label=@_localizer["CeWClustNameLbl"]
                                Data=@BindingContext.Clusters.ToNullableDictionary()
                                @bind-Value="@BindingContext.Cost.ClusterId"/>
                </div>
            </div>
        </div>
        <br/>
        <RadzenButton type="submit" class="btn btn-primary" Text=@_localizer["CeWSaveBtn"] Disabled=!GlobalDataService.CanEdit/>
    </Radzen.Blazor.RadzenTemplateForm>
}

@code{

    [Parameter]
    public int CostId { get; set; }

    [Parameter]
    public int? TaskId { get; set; }

    [Parameter]
    public int? ClusterId { get; set; }

    [Parameter]
    public EventCallback<ClusterModel> Callback { get; set; }

}