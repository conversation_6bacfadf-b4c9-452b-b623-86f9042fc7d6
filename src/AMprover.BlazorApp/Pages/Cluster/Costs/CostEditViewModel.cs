using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Pages.Cluster.Costs;

public class TaskPlanEditViewModel : BaseViewModel, IEntityEditorViewModel
{
    private readonly IClusterManager _clusterManager;
    private readonly IDropdownManager _dropdownManager;
    private readonly IPortfolioSetupManager _portfolioSetupManager;

    public TaskPlanEditViewModel(ILoggerFactory loggerFactory, DialogService dialogService,
        IClusterManager clusterManager,
        IPortfolioSetupManager portfolioSetupManager, ILookupManager lookupManager, IDropdownManager dropdownManager) :
        base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _clusterManager = clusterManager;
        _dropdownManager = dropdownManager;
        _portfolioSetupManager = portfolioSetupManager;
    }

    [Parameter] public int CostId { get; set; }

    [Parameter] public int? TaskId { get; set; }

    [Parameter] public int? ClusterId { get; set; }

    public ClusterCostModel Cost { get; set; }

    public CommonCostModel SelectedCommonCost { get; set; }

    [Parameter] public EventCallback<ClusterModel> Callback { get; set; }

    public string ErrorText { get; set; }

    public bool ShowError { get; set; }

    public Dictionary<int, string> Clusters { get; set; } = new Dictionary<int, string>();

    public Dictionary<int, string> Tasks { get; set; } = new Dictionary<int, string>();

    public Dictionary<int, string> CommonCosts { get; set; } = new Dictionary<int, string>();

    public string SelectedMrbName { get; set; } = "Please link task first";

    public bool HideUnits { get; set; }

    public EntityEditorMode EditorMode
    {
        get
        {
            return CostId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    public override void OnInitialized()
    {
        Clusters = _dropdownManager.GetClusterDict();
        Tasks = ClusterId.HasValue
            ? _dropdownManager.GetTasksForClusterDict(ClusterId.Value)
            : _dropdownManager.GetTasksDict();
        CommonCosts = _dropdownManager.GetCommonCostDict();

        switch (EditorMode)
        {
            case EntityEditorMode.Create:
                Cost = new ClusterCostModel
                    {Id = CostId, TaskId = TaskId, ClusterId = ClusterId, IsCommonTaskCost = true};
                HideUnits = true;
                if (TaskId.HasValue) OnTaskChange(TaskId);
                break;
            default:
                Cost = _clusterManager.GetClusterCost(CostId);
                HideUnits = Cost.HideUnits();
                if (Cost.TaskId.HasValue) OnTaskChange(Cost.TaskId.Value);
                break;
        }
    }

    public void OnTaskChange(object value)
    {
        if (value is int i)
        {
            SelectedMrbName = _clusterManager.GetRiskNameByTaskId(i);
        }
    }

    public void OnCommonCostChange(object value)
    {
        if (value is not int i) return;
        SelectedCommonCost = _portfolioSetupManager.GetCommonCost(i);

        if (SelectedCommonCost == null) return;
        if (SelectedCommonCost.Price.HasValue)
            Cost.Price = SelectedCommonCost.Price.Value;
        Cost.Quantity = SelectedCommonCost.Number;
        Cost.Units = SelectedCommonCost.Units;
        Cost.CalculationType = SelectedCommonCost.CalculationType;
        Cost.Type = SelectedCommonCost.Type;

        //Hide units field if the common cost has p*q instead of p*q*u
        HideUnits = SelectedCommonCost.HideUnits();

        RecalculateCost();
    }

    public void RecalculateCost()
    {
        Cost.CalculateCosts();
    }

    public void ValidTaskSubmitted(ClusterCostModel cost)
    {
        if (SelectedCommonCost != null)
        {
            cost.Type = SelectedCommonCost.Type;
        }

        CreateOrUpdateObjectAndNavigate(cost);
    }

    private void CreateOrUpdateObjectAndNavigate(ClusterCostModel cost)
    {
        Cost = _clusterManager.UpdateClusterCost(cost);
        var cluster = new ClusterModel();

        if (cost.ClusterId.HasValue)
            cluster = _clusterManager.SyncClusterCosts(cost.ClusterId.Value);

        _logger.LogInformation($"Creating a {nameof(ClusterCostModel)} succeeded. Name = {cost.Task?.Risk?.Name}, ID = {cost.Id}.");
        Callback.InvokeAsync(cluster);
        _dialogService.Close();
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ErrorText = "Update NOT executed, invalid form submitted";

        _logger.LogWarning($"Invalid {EditorMode} form submitted for {nameof(TaskModel)} with Id {CostId}.");
        _logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }
}