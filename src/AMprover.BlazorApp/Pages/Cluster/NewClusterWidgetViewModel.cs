using System;
using System.Collections.Generic;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using AutoMapper;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;

namespace AMprover.BlazorApp.Pages.Cluster;

public class NewClusterWidgetViewModel : BaseViewModel
{
    private readonly IMapper _mapper;
    private readonly IClusterManager _clusterManager;
    private readonly IDropdownManager _dropdownManager;

    public NewClusterWidgetViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager,
        IDropdownManager dropdownManager,
        IMapper mapper, IClusterManager clusterManager, DialogService dialogService) : base(loggerFactory,
        lookupManager, dialogService)
    {
        _mapper = mapper;
        _clusterManager = clusterManager;
        _dropdownManager = dropdownManager;
    }

    [Parameter] public int? PartOfCluster { get; set; }

    public string Name { get; set; }

    [Parameter] public Action<ClusterModel> CallBack { get; set; }

    private ClusterModel NewCluster { get; set; } = new ClusterModel();

    public Dictionary<int, string> ClusterDict { get; set; } = new Dictionary<int, string>();

    public bool IsBusy { get; set; }

    public override void OnInitialized()
    {
        ClusterDict = _dropdownManager.GetClusterDict();
    }

    public async System.Threading.Tasks.Task CreateNewCluster()
    {
        if (string.IsNullOrWhiteSpace(Name)) return;

        //reset if done multiple times in a row
        NewCluster = new ClusterModel();

        if (PartOfCluster.HasValue)
        {
            //map all data
            var parentCluster = _clusterManager.GetCluster(PartOfCluster.Value);
            _mapper.Map(parentCluster, NewCluster);

            NewCluster.PartOf = PartOfCluster;
            NewCluster.Level = parentCluster.Level + 1;
            NewCluster.ScenarioId = parentCluster.ScenarioId;
            NewCluster.RiskObjectId = parentCluster.RiskObjectId;
        }

        //Set parameters to create new cluster.
        NewCluster.Id = 0;
        NewCluster.Name = Name;

        NewCluster = _clusterManager.UpdateCluster(NewCluster);

        CallBack?.Invoke(NewCluster);
        _dialogService.Close();
    }
}