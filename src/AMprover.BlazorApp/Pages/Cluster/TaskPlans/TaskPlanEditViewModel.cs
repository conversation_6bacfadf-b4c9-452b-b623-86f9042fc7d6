using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;

namespace AMprover.BlazorApp.Pages.Cluster.TaskPlans;

public class TaskPlanEditViewModel : BaseViewModel, IEntityEditorViewModel
{
    private readonly IClusterManager _clusterManager;
    private readonly IDropdownManager _dropdownManager;

    public TaskPlanEditViewModel(ILoggerFactory loggerFactory,
        DialogService dialogService, IClusterManager clusterManager, ILookupManager lookupManager, IDropdownManager dropdownManager) : base(loggerFactory, lookupManager)
    {
        _dialogService = dialogService;
        _clusterManager = clusterManager;
        _dropdownManager = dropdownManager;
    }

    [Parameter] public int TaskPlanId { get; set; }
    
    [Parameter] public int? ClusterId { get; set; }

    public ClusterTaskPlanModel TaskPlan { get; set; }

    [Parameter] public EventCallback<ClusterTaskPlanModel> Callback { get; set; }

    public string ErrorText { get; set; }

    public bool ShowError { get; set; }

    public Dictionary<int, string> Clusters { get; set; } = new Dictionary<int, string>();
    
    public Dictionary<int, string> Assets { get; set; } = new Dictionary<int, string>();

    public Dictionary<int, string> Risks { get; set; } = new Dictionary<int, string>();

    public Dictionary<int, string> Tasks { get; set; } = new Dictionary<int, string>();

    public Dictionary<int, string> CommonTasks { get; set; } = new Dictionary<int, string>();

    public Dictionary<int, string> Objects { get; set; } = new Dictionary<int, string>();

    public Dictionary<int, string> IntervalUnits { get; set; } = new Dictionary<int, string>();
    
    public Dictionary<int, string> ExecuteStatus { get; set; } = new Dictionary<int, string>();
    
    public EntityEditorMode EditorMode
    {
        get
        {
            return TaskPlanId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    public override void OnInitialized()
    {
        Clusters = _dropdownManager.GetClusterDict();
        Tasks = _dropdownManager.GetTasksDict();
        Risks = _dropdownManager.GetRisksDict();
        CommonTasks = _dropdownManager.GetCommonTasksDict();
        Objects = _dropdownManager.GetObjectsDict();
        Assets = _dropdownManager.GetAssetDict();
        IntervalUnits = _dropdownManager.GetIntervalUnitDict();
        ExecuteStatus = _dropdownManager.GetExecuteStatusDict();
        
        switch (EditorMode)
        {
            case EntityEditorMode.Create:
                TaskPlan = new ClusterTaskPlanModel { Id = TaskPlanId, ClusterId = ClusterId};
                break;
            default:
                TaskPlan = _clusterManager.GetClusterTaskPlan(TaskPlanId);
                break;
        }
    }

    public async Task ValidTaskSubmitted(ClusterTaskPlanModel taskPlan)
    {
        await CreateOrUpdateObjectAndNavigate(taskPlan);
    }

    private async Task CreateOrUpdateObjectAndNavigate(ClusterTaskPlanModel taskPlan)
    {
        TaskPlan = _clusterManager.UpdateClusterTaskPlan(taskPlan);
        _logger.LogInformation($"Creating a {nameof(ClusterTaskPlanModel)} succeeded. Name = {taskPlan.Risk?.Name}, ID = {taskPlan.Id}.");
        await Callback.InvokeAsync(TaskPlan);
        _dialogService.Close();
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ErrorText = "Update NOT executed, invalid form submitted";
        _logger.LogWarning($"Invalid {EditorMode} form submitted for {nameof(ClusterTaskPlanModel)} with Id {TaskPlanId}.");
        _logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }
}