@page "/cluster/taskplan/{TaskPlanId:int}"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<TaskPlanEditViewModel>
@inject TooltipService tooltipService
@inject IGlobalDataService GlobalDataService;

@if (BindingContext.TaskPlan != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem="ClusterTaskPlanModel" Data=@BindingContext.TaskPlan Submit=@BindingContext.ValidTaskSubmitted OnInvalidSubmit=@BindingContext.InvalidTaskSubmitted>
        <DataAnnotationsValidator/>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput ReadOnly="true" Label="Id" @bind-Value="@BindingContext.TaskPlan.Id"/>
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Cluster" Name="Cluster" AllowFiltering="true" class="form-control"
                                Data=@BindingContext.Clusters.ToNullableDictionary()
                                @bind-Value=@BindingContext.TaskPlan.ClusterId />
                <RadzenRequiredValidator DefaultValue=0 Component="Cluster"/>
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Risk" Name="Risk" AllowFiltering="true" class="form-control"
                                Data=@BindingContext.Risks.ToNullableDictionary()
                                @bind-Value=@BindingContext.TaskPlan.RiskId />
                <RadzenRequiredValidator DefaultValue=0 Component="Risk"/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Task" Name="Task" AllowFiltering="true" class="form-control"
                                Data=@BindingContext.Tasks.ToNullableDictionary()
                                @bind-Value=@BindingContext.TaskPlan.TaskId />
                <RadzenRequiredValidator DefaultValue=0 Component="Task"/>
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Object" Name="Object" AllowFiltering="true" class="form-control"
                                Data=@BindingContext.Objects.ToNullableDictionary()
                                @bind-Value=@BindingContext.TaskPlan.ObjectId />
                <RadzenRequiredValidator DefaultValue=0 Component="Object"/>
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Common task" AllowFiltering="true" class="form-control"
                                Data=@BindingContext.CommonTasks.ToNullableDictionary()
                                @bind-Value=@BindingContext.TaskPlan.CommonTaskId />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F0" Label="Quality score" @bind-Value=@BindingContext.TaskPlan.QualityScore/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="Cluster cost per unit" @bind-Value=@BindingContext.TaskPlan.ClusterCostPerUnit/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="Units" @bind-Value=@BindingContext.TaskPlan.SiUnits/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput  Format="c2" Label="Cluster costs" @bind-Value=@BindingContext.TaskPlan.ClusterCosts/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="DownTime" @bind-Value=@BindingContext.TaskPlan.DownTime/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="Duration" @bind-Value=@BindingContext.TaskPlan.Duration/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput  Format="c2" Label="Tool costs" @bind-Value=@BindingContext.TaskPlan.ToolCosts/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Label="Priority" @bind-Value=@BindingContext.TaskPlan.Priority/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="Sequence" @bind-Value=@BindingContext.TaskPlan.Sequence/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput  Label="Slack" @bind-Value=@BindingContext.TaskPlan.Slack/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Label="Shift start date" @bind-Value=@BindingContext.TaskPlan.ShiftStartDate/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Label="Shift end date" @bind-Value=@BindingContext.TaskPlan.ShiftEndDate/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <label>Asset:</label>
                <AMproverDictionaryDropdown Name="Task" AllowFiltering="true" class="form-control"
                                Data=@BindingContext.Assets.ToNullableDictionary()
                                @bind-Value=@BindingContext.TaskPlan.AssetId />
                <RadzenRequiredValidator DefaultValue=0 Component="Task"/>
            </div>
            <div class="col-sm-4">
                <label>Execute status:</label>
                <AMproverDictionaryDropdown Name="Object" AllowFiltering="true" class="form-control"
                                Data=@BindingContext.ExecuteStatus.ToNullableDictionary()
                                @bind-Value=@BindingContext.TaskPlan.ExecuteStatus />
                <RadzenRequiredValidator DefaultValue=0 Component="Object"/>
            </div>
            <div class="col-sm-4">
                <label>Slack interval type:</label>
                <AMproverDictionaryDropdown Name="Object" AllowFiltering="true" class="form-control"
                                Data=@BindingContext.IntervalUnits.ToNullableDictionary()
                                @bind-Value=@BindingContext.TaskPlan.SlackIntervalType />
                <RadzenRequiredValidator DefaultValue=0 Component="Object"/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <div class="form-group">
                    <label>Date generated:</label>
                    <RadzenDatePicker TValue="DateTime?" ShowTime="true" ShowTimeOkButton="true" Disabled="true" @bind-Value=@BindingContext.TaskPlan.DateGenerated  />
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label>Date executed:</label>
                    <RadzenDatePicker TValue="DateTime?" ShowTime="true" ShowTimeOkButton="true" @bind-Value=@BindingContext.TaskPlan.DateExecuted />
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label>Execution date:</label>
                    <RadzenDatePicker TValue="DateTime?" ShowTime="true" ShowTimeOkButton="true" @bind-Value=@BindingContext.TaskPlan.ExecutionDate  />
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Reference Id:</label>
                    <AMproverTextBox Name="ReferenceId" @bind-Value=BindingContext.TaskPlan.ReferenceId class="form-control"/>
                </div>
            </div>
            <div class="col-sm-5">
                <div class="row">
                    <div class="col-sm-7">
                        <label>Interruptable:</label>
                    </div>
                    <div class="col-sm-5">
                        <AMproverCheckbox class="form-control" @bind-Value="@BindingContext.TaskPlan.Interruptable" CssClass="e-primary" TValue="bool?"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-7">
                        <label>Use last date executed:</label>
                    </div>
                    <div class="col-sm-5">
                        <AMproverCheckbox class="form-control" @bind-Value="@BindingContext.TaskPlan.UseLastDateExecuted" CssClass="e-primary" TValue="bool?"/>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <label>Remarks</label>
                    <AMproverTextArea Name="Remarks" @bind-Value=@BindingContext.TaskPlan.Remarks class="form-control"/>
                </div>
            </div>
        </div>
        <br/>
        <RadzenButton type="submit" class="btn btn-primary" Text="Save" Disabled=!GlobalDataService.CanEdit/>
    </Radzen.Blazor.RadzenTemplateForm>
}

@code{

    [Parameter]
    public int TaskPlanId { get; set; }

    [Parameter]
    public int? ClusterId { get; set; }

    [Parameter]
    public EventCallback<ClusterTaskPlanModel> Callback { get; set; }

}