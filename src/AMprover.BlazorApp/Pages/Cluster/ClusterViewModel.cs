using System;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON><PERSON>;
using Radzen.Blazor;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Components.GridTypes.Utility;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BlazorApp.Pages.Cluster.Costs;
using AMprover.BlazorApp.Pages.Cluster.TaskPlans;
using AMprover.BusinessLogic.Extensions;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using ClusterModel = AMprover.BusinessLogic.Models.Cluster.ClusterModel;
using AMprover.BlazorApp.Components.Pagination;
using AutoMapper;

namespace AMprover.BlazorApp.Pages.Cluster;

public class ClusterViewModel : BaseViewModel
{
    private readonly IClusterManager _clusterManager;
    private readonly IMapper _mapper;
    private readonly IRiskAnalysisManager _riskAnalysisManager;
    private readonly IDropdownManager _dropdownManager;
    private readonly IPageNavigationManager _pageNavigation;
    private readonly NavigationManager _navigationManager;
    private readonly IStringLocalizer<ClusterPage> _localizer;

    public ClusterViewModel(ILoggerFactory loggerFactory, IClusterManager clusterManager, DialogService dialogService,
        NavigationManager navigationManager,
        IMapper mapper,
        IRiskAnalysisManager riskAnalysisManager, IDropdownManager dropdownManager,
        IPageNavigationManager pageNavigationManager, IStringLocalizer<ClusterPage> localizer,
        ILookupManager lookupManager) : base(loggerFactory, lookupManager, dialogService)
    {
        _clusterManager = clusterManager;
        _riskAnalysisManager = riskAnalysisManager;
        _dropdownManager = dropdownManager;
        _pageNavigation = pageNavigationManager;
        _navigationManager = navigationManager;
        _localizer = localizer;
        _mapper = mapper;
    }

    [Parameter] public int? ClusterId { get; set; }

    public ClusterModel SelectedCluster { get; set; }
    public List<ClusterTaskModel> SelectedTasks { get; set; } = new();
    public TaskModel SelectedTask { get; private set; }
    public List<ClusterTaskPlanModelWithExtraTaskProperties> ClusterTaskPlanModels { get; set; }

    private ClusterTreeObject CurrentCluster { get; set; }
    public Dictionary<int, string> StatusDict { get; private set; } = new();
    public List<ClusterModel> ClusterList { get; set; } = new();
    public TreeGeneric<ClusterTreeObject> ClusterTree { get; } = new();

    public RadzenTabs ClusterTabs { get; set; }
    public RadzenTabs ClusterDataTabs { get; set; }
    public RadzenTabs TreeTab { get; set; }
    public RadzenTabs DetailsTab { get; set; }

    public UtilityGrid<ClusterTaskModel> TaskConceptGrid { get; set; }
    public UtilityGrid<ClusterCostModel> ClusterCostGrid { get; set; }
    public UtilityGrid<ClusterCostModel> ClusterCostDetailGrid { get; set; }
    public UtilityGrid<ClusterTaskPlanModelWithExtraTaskProperties> TaskPlanGrid { get; set; }
    public UtilityGrid<ClusterModel> ClusterGrid { get; set; }

    public Dictionary<string, Dictionary<int, string>> ClusterTaskPlanDropdownOverrides { get; private set; }
    public Dictionary<string, Dictionary<int, string>> ClusterCostDropdownOverrides { get; private set; }
    public Dictionary<string, Dictionary<int, string>> TaskDropdownOverrides { get; private set; }

    public Dictionary<int, string> IntervalDict { get; private set; }
    public Dictionary<int, string> InitiatorDict { get; private set; }
    public Dictionary<int, string> ExecutorDict { get; private set; }
    private Dictionary<int, string> RiskObjectDict { get; set; }
    public Dictionary<int, string> ScenarioDict { get; private set; }

    public Paginator Paginator { get; set; }

    public bool IsSapaView { get; set; }

    public override void OnInitialized()
    {
        StatusDict = _dropdownManager.GetStatusDict();

        ClusterTaskPlanDropdownOverrides = _dropdownManager.GetClusterTaskPlanDropdownOverrides();
        ClusterCostDropdownOverrides = _dropdownManager.GetClusterCostDropDownOverrides();
        TaskDropdownOverrides = _dropdownManager.GetClusterTaskModelDropDownOverrides();

        ExecutorDict = _dropdownManager.GetExecutorDict();
        InitiatorDict = _dropdownManager.GetInitiatorDict();
        IntervalDict = _dropdownManager.GetIntervalUnitDict();
        RiskObjectDict = _dropdownManager.GetRiskObjectDict();
        ScenarioDict = _dropdownManager.GetScenarioDict();

        SelectedScenario = _pageNavigation.GetSelectedScenario() ?? ScenarioDict.FirstOrDefault().Key;

        ClusterList = _clusterManager.GetClustersForScenario(SelectedScenario);
        SelectedCluster = _clusterManager.GetCluster(ClusterId ?? ClusterList.FirstOrDefault()?.Id ?? 0);
        SelectedTasks = SelectedCluster.Tasks.Select(x => _mapper.Map<TaskModel, ClusterTaskModel>(x)).ToList();
        ClusterTaskPlanModels = _clusterManager.GetClusterTaskPlanWithExtraProperties(SelectedCluster.Id);

        IsSapaView = SelectedCluster.RiskObject?.AnalysisType == "SAPA";

        ClusterTree.Initialize(_clusterManager.GetTreeViewForClusters(SelectedScenario));
        SelectActiveNodeInTree();
    }

    private void SelectActiveNodeInTree()
    {
        var node = ClusterTree.GetClusterTreeNode(SelectedCluster.Id);

        if (node != null)
        {
            ClusterTree.SelectNode(node);
            UpdatePaginator();
            var firstTask = SelectedCluster.Tasks.FirstOrDefault();

            if (firstTask != null)
                SelectClusterTask(firstTask);
        }
    }

    public void ChangeScenario()
    {
        _pageNavigation.SetSelectedScenario(SelectedScenario);
        ClusterList = _clusterManager.GetClustersForScenario(SelectedScenario);
        ReloadTreeAndTabs();
    }

    public void SaveCluster(ClusterModel cluster)
    {
        _clusterManager.UpdateCluster(cluster);
    }

    public void SaveCluster()
    {
        var parentCluster = ClusterList.FirstOrDefault(x => x.Id == SelectedCluster.PartOf);

        if (SelectedCluster.PartOf == SelectedCluster.Id)
        {
            // Cannot set self as parent
            ErrorDialog(_localizer["CCCNoSelfPartOf"]);
        }
        else if (parentCluster == null)
        {
            // Invalid Parent
            ErrorDialog(_localizer["CCCInvalidParent"]);
        }
        else
        {
            SelectedCluster.PartOf = parentCluster.Id;
            SelectedCluster.PartOfCluster = parentCluster;
            BusyDialog("Saving cluster");
            SelectedCluster = _clusterManager.UpdateCluster(SelectedCluster);
            _logger.LogInformation("Saving a cluster succeeded, Name = {SelectedClusterName}, ID = {SelectedClusterId}", SelectedCluster.Name, SelectedCluster.Id);
            _dialogService.Close();
            ReloadTreeAndTabs();
            SelectActiveNodeInTree();
        }
    }

    #region cluster level

    public Dictionary<int, string> GetClusterLevelDropdown()
    {
        return new Dictionary<int, string>
        {
            {1, "1"},
            {2, "2"},
            {3, "3"}
        };
    }

    public void ChangeClustLevel()
    {
        SelectedCluster.PartOf = null;
        SelectedCluster.PartOfCluster = null;
    }

    public Dictionary<int?, string> GetClusterPartOfDropdown()
    {
        return ClusterList.Where(x => x.Level == SelectedCluster.Level).ToDictionary(x => (int?) x.Id, x => x.Name);
    }

    #endregion

    #region Tree related methods

    public void ClickTreeNode(TreeNodeGeneric<ClusterTreeObject> node)
    {
        if (node == null)
        {
            CurrentCluster = null;
            return;
        }

        CurrentCluster = node.Source;

        if (!CurrentCluster.Id.HasValue) return;
        if (SelectedCluster.Id == CurrentCluster.Id) return;

        SelectedCluster = _clusterManager.GetCluster(CurrentCluster.Id.Value);
        ClusterTaskPlanModels = _clusterManager.GetClusterTaskPlanWithExtraProperties(SelectedCluster.Id);

        var firstTask = SelectedCluster.Tasks.FirstOrDefault();
        if (firstTask != null)
        {
            // Fix broken clusters
            if (SelectedCluster.ScenarioId == null)
            {
                SelectedCluster.ScenarioId = firstTask.Risk.RiskObject.ScenarioId;
                SaveCluster();
            }

            SelectClusterTask(firstTask);
        }

        _pageNavigation.SavePageQueryString($"/cluster/{SelectedCluster.Id}");
        _navigationManager.NavigateTo($"/cluster/{SelectedCluster.Id}");

        UpdatePaginator();
    }

    public void DeleteClusterFromTree(object clusterTreeObject)
    {
        if (clusterTreeObject is ClusterTreeObject cluster)
        {
            if (!(cluster.Id > 0))
                return;

            _clusterManager.DeleteCluster(cluster.Id.Value);

            if (cluster.Id == SelectedCluster.Id)
                SelectNearestNode();
        }
        else
        {
            throw new ArgumentException(
                $"Incorrect configuration, function should only receive object of Type {typeof(ClusterTreeObject)}");
        }
    }

    private void SelectNearestNode()
    {
        var location = ClusterTree.GetNodeLocation(ClusterTree.SelectedNode);
        var parentNode = location.Count > 1
            ? ClusterTree.GetNodeFromLocation(location.Take(location.Count - 1).ToList())
            : null;

        if (parentNode?.Nodes.Any() == true)
        {
            var siblingNode = parentNode.Nodes[Math.Max(0, location.Last() - 1)];
            ClickTreeNode(siblingNode);
        }
        else
        {
            var firstNode = ClusterTree.Node.Nodes.FirstOrDefault();
            ClickTreeNode(firstNode);
        }
    }

    #endregion

    #region Cluster tasks

    public void SelectClusterTask(TaskModel selectedClusterTask)
    {
        SelectedTask = selectedClusterTask;

        ClusterCostDetailGrid?.BindingContext.Grid.Reload();
        ClusterDataTabs?.Reload();
    }

    public void SaveClusterTask(ClusterTaskModel clusterTaskModel)
    {
        var clusterTask = _mapper.Map<ClusterTaskModel, TaskModel>(clusterTaskModel);

        // The Costs of a Task should be overriden by the CLusterCosts when saved from the Cluster Page
        clusterTask.Costs = clusterTask.ClusterCosts > 0
            ? clusterTask.ClusterCosts
            : clusterTask.EstCosts;

        clusterTask = _riskAnalysisManager.UpdateTask(clusterTask);
        TaskConceptGrid?.BindingContext.Grid.CancelEditRow(clusterTaskModel);

        //If clusterId is changed we need to make sure the task isn't show anymore.
        if (clusterTask.ClusterId != CurrentCluster?.Id)
        {
            //SelectedTasks = SelectedTasks.Where(x => x.ClusterId == CurrentCluster.Id).ToList();
            ClusterDataTabs?.Reload();
        }
    }

    #endregion

    #region Cluster costs

    public void SaveClusterCost(ClusterCostModel clusterCost)
    {
        _clusterManager.UpdateClusterCost(clusterCost);
        _clusterManager.SyncClusterCosts(SelectedCluster.Id);
        SelectedCluster = _clusterManager.GetCluster(SelectedCluster.Id);
    }

    public void DeleteClusterCost(ClusterCostModel clusterCost)
    {
        _clusterManager.DeleteClusterCost(clusterCost.Id);
        SelectedCluster = _clusterManager.SyncClusterCosts(SelectedCluster.Id);
        SelectedCluster = _clusterManager.GetCluster(SelectedCluster.Id);
    }

    public void OpenClusterCostPopup(int id)
    {
        _dialogService.Open<CostEdit>(_localizer["CvmClusterCostDetailsTxt"],
            new Dictionary<string, object>
            {
                {"CostId", id},
                {"TaskId", SelectedTask?.Id},
                {"ClusterId", SelectedCluster?.Id},
                {"Callback", EventCallback.Factory.Create<ClusterModel>(this, AddOrEditClusterCostCallBack)}
            },
            new DialogOptions {Width = "800px", Resizable = false, Draggable = true});
    }

    private void AddOrEditClusterCostCallBack(ClusterModel model)
    {
        SelectedCluster = _clusterManager.GetCluster(model.Id);
        ClusterTabs?.Reload();
        ClusterDataTabs?.Reload();
        DetailsTab?.Reload();
    }

    #endregion

    #region Task plans

    public void OpenTaskPlanPopup(int id)
    {
        _dialogService.Open<TaskPlanEdit>(_localizer["CvmTaskPlanDetailsTxt"],
            new Dictionary<string, object>
            {
                {"TaskPlanId", id},
                {"ClusterId", SelectedCluster?.Id},
                {"Callback", EventCallback.Factory.Create<ClusterTaskPlanModel>(this, ReloadTreeAndTabs)}
            },
            new DialogOptions {Width = "750px", Resizable = true, Draggable = true});
    }

    public async Task SaveClusterTaskPlan(ClusterTaskPlanModel clusterTaskPlan)
    {
        _clusterManager.UpdateClusterTaskPlan(clusterTaskPlan);
        SelectedCluster = _clusterManager.GetCluster(SelectedCluster.Id);
    }

    public async Task DeleteClusterTaskPlan(ClusterTaskPlanModel clusterTaskPlan)
    {
        _clusterManager.DeleteClusterTaskPlanCost(clusterTaskPlan.Id);
        SelectedCluster = _clusterManager.GetCluster(SelectedCluster.Id);
    }

    #endregion

    #region command center methods

    public void AddNewCluster()
    {
        _dialogService.Open<NewClusterWidget>
        ("New Cluster",
            new Dictionary<string, object>
            {
                {"PartOfCluster", SelectedCluster?.Id},
                {nameof(NewClusterWidget.CallBack), LoadNewCluster}
            });
    }

    private void LoadNewCluster(ClusterModel newCluster)
    {
        ClusterTree.Initialize(_clusterManager.GetTreeViewForClusters(SelectedScenario));
        TreeTab?.Reload();

        ClusterTree.ExpandTreeTillNode(newCluster.Id);

        SelectedCluster = newCluster;
        SelectedTasks = SelectedCluster.Tasks.Select(x => _mapper.Map<TaskModel, ClusterTaskModel>(x)).ToList();
        ClusterTaskPlanModels = _clusterManager.GetClusterTaskPlanWithExtraProperties(SelectedCluster.Id);

        if (SelectedCluster != null)
        {
            ReloadTreeAndTabs();
        }
    }

    public void OnClusterConceptClick(RadzenSplitButtonItem item)
    {
        if (item == null) return;
        var dialogOptions = new DialogOptions {Width = "500px", Resizable = false, Draggable = true};

        switch (item.Value)
        {
            // Update Cluster by Scenario
            case "1":
                _dialogService.Open<ClusterCommandCenterWidget>(_localizer["ClustUpdateClustConceptByScenarioBtn"],
                    new Dictionary<string, object>
                    {
                        {"CallbackInt", EventCallback.Factory.Create<int>(this, UpdateClusterConceptByScenario)},
                        {"ExtraConfirmation", false},
                        {"ConfirmationText", _localizer["CvmUpdateClustConceptScenTxt"].Value},
                        {"Options", ScenarioDict}
                    },
                    dialogOptions);
                break;

            // Update Cluster by RiskObject
            case "2":
                _dialogService.Open<ClusterCommandCenterWidget>(_localizer["ClustUpdateClustConceptByRiskObjectBtn"],
                    new Dictionary<string, object>
                    {
                        {"CallbackInt", EventCallback.Factory.Create<int>(this, UpdateClusterConceptByRiskObject)},
                        {"ExtraConfirmation", false},
                        {"ConfirmationText", _localizer["CvmUpdateClustConceptTxt"].Value},
                        {"Options", RiskObjectDict}
                    },
                    dialogOptions);
                break;

            // Regenerate Cluster by Scenario
            case "3":
                _dialogService.Open<ClusterCommandCenterWidget>(_localizer["ClustRegenerateClustConceptByScenarioBtn"],
                    new Dictionary<string, object>
                    {
                        {"CallbackInt", EventCallback.Factory.Create<int>(this, RegenerateClusterConceptByScenario)},
                        {"ExtraConfirmation", true},
                        {"ConfirmationText", _localizer["CvmRegenerateClusterTxt"].Value},
                        {"Options", ScenarioDict}
                    },
                    dialogOptions);
                break;

            // Regenerate Cluster by RiskObject
            case "4":
                _dialogService.Open<ClusterCommandCenterWidget>(
                    _localizer["ClustRegenerateClustConceptByRiskObjectBtn"],
                    new Dictionary<string, object>
                    {
                        {"CallbackInt", EventCallback.Factory.Create<int>(this, RegenerateClusterConceptByRiskObject)},
                        {"ExtraConfirmation", true},
                        {"ConfirmationText", _localizer["CvmRegenerateClusterTxt"].Value},
                        {"Options", RiskObjectDict}
                    },
                    dialogOptions);
                break;
        }
    }

    public void OnTaskPlanClick()
    {
        _dialogService.Open<ClusterCommandCenterWidget>(_localizer["ClustRegenerateTaskPlanBtn"],
            new Dictionary<string, object>
            {
                {"Callback", EventCallback.Factory.Create(this, RegenerateTaskPlan)},
                {"ConfirmationText", _localizer["CvmRegenerateTaskPlanTxt"].Value}
            },
            new DialogOptions {Width = "400px", Resizable = false, Draggable = true});

        ReloadTreeAndTabs();
    }

    public void ProcessDownTime()
    {
        _clusterManager.ProcessClusterDownTime(SelectedCluster);
        SaveCluster();
    }

    public void ProcessDownDuration()
    {
        _clusterManager.ProcessClusterDuration(SelectedCluster);
        SaveCluster();
    }

    private async Task UpdateClusterConceptByScenario(int scenarioId)
    {
        _dialogService.Close();
        BusyDialog(_localizer["CvmRefreshingClustConceptUpdateTxt"].Value);
        await Task.Run(() => _clusterManager.UpdateClusterConceptByScenarioId(scenarioId, regenerate: false));
        _dialogService.Close();

        ReloadTreeAndTabs();
    }

    private async Task UpdateClusterConceptByRiskObject(int scenarioId)
    {
        _dialogService.Close();
        BusyDialog(_localizer["CvmRefreshingClustConceptUpdateTxt"].Value);
        await Task.Run(() => _clusterManager.UpdateClusterConceptByRiskObjectId(scenarioId, regenerate: false));
        _dialogService.Close();

        ReloadTreeAndTabs();
    }

    private async Task RegenerateClusterConceptByScenario(int scenarioId)
    {
        _dialogService.Close();
        BusyDialog(_localizer["CvmRefreshingClustConceptTxt"].Value);
        await Task.Run(() => _clusterManager.UpdateClusterConceptByScenarioId(scenarioId, regenerate: true));
        _dialogService.Close();

        ReloadTreeAndTabs();
    }

    private async Task RegenerateClusterConceptByRiskObject(int riskObjectId)
    {
        _dialogService.Close();
        BusyDialog(_localizer["CvmRefreshingClustConceptTxt"].Value);
        await Task.Run(() => _clusterManager.UpdateClusterConceptByRiskObjectId(riskObjectId, regenerate: true));
        _dialogService.Close();

        ReloadTreeAndTabs();
    }

    private async Task RegenerateTaskPlan()
    {
        _dialogService.Close();
        BusyDialog(_localizer["CvmRefreshClustPlanTxt"].Value);
        await Task.Run(() => _clusterManager.RegenerateTaskPlan());
        _dialogService.Close();

        ReloadTreeAndTabs();
    }

    private async Task UpdateExistingTaskPlan()
    {
        _dialogService.Close();
        BusyDialog(_localizer["CvmUpdatingTaskPlansTxt"].Value);
        await Task.Run(() => _clusterManager.UpdateExistingTaskPlan());
        _dialogService.Close();

        ReloadTreeAndTabs();
    }

    private void BusyDialog(string message)
    {
        _dialogService.Open<BusyDialog>(_localizer["CvmLoadingTxt"].Value,
            new Dictionary<string, object> {{"Text", message}},
            new DialogOptions {Width = "400px", Resizable = false, Draggable = true});
    }

    private void ErrorDialog(string message)
    {
        _dialogService.Open<InformationDialog>("Error",
            new Dictionary<string, object>
            {
                {"DialogContent", message}
            });
    }

    private void ReloadTreeAndTabs()
    {
        SelectedCluster = _clusterManager.GetCluster(SelectedCluster.Id);
        SelectedTasks = SelectedCluster.Tasks.Select(x => _mapper.Map<TaskModel, ClusterTaskModel>(x)).ToList();
        ClusterTaskPlanModels = _clusterManager.GetClusterTaskPlanWithExtraProperties(SelectedCluster.Id);
        ClusterList = _clusterManager.GetClustersForScenario(SelectedScenario);

        ClusterTree.Initialize(_clusterManager.GetTreeViewForClusters(SelectedScenario));
        SelectActiveNodeInTree();
        TreeTab?.Reload();

        ClusterTabs?.Reload();
        ClusterDataTabs?.Reload();
        DetailsTab?.Reload();
        UpdatePaginator();
    }

    #endregion

    #region paginator

    public int GetInitialPaginatorValue()
    {
        var node = ClusterTree.GetClusterTreeNode(SelectedCluster?.Id ?? ClusterId ?? 0);
        var nodes = ClusterTree.GetFlattenedNodes();

        return nodes.IndexOf(node);
    }

    public int GetClusterCount() => ClusterTree.GetFlattenedNodes().Count;

    public void PaginatorCallback(int clusterIndex)
    {
        var clusterId = ClusterTree.GetFlattenedNodes().Skip(clusterIndex).FirstOrDefault()?.Source?.Id;

        if (clusterId != null)
        {
            var node = ClusterTree.GetFlattenedNodes().Skip(clusterIndex).FirstOrDefault();

            if (node != null)
                ClickTreeNode(node);

            SelectActiveNodeInTree();
        }
    }

    private void UpdatePaginator()
    {
        if (Paginator != null)
        {
            var value = GetInitialPaginatorValue();
            Paginator.BindingContext.SetCurrentExternally(value);
        }
    }

    #endregion
}