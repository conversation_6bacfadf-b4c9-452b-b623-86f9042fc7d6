using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.Cluster;

public class ClusterCommandCenterViewModel : BaseViewModel
{
    public ClusterCommandCenterViewModel(ILoggerFactory loggerFactory) : base(loggerFactory)
    { }

    [Parameter] public EventCallback Callback { get; set; }

    [Parameter] public EventCallback<int> CallbackInt { get; set; }

    public string ConfirmText { get; set; } = string.Empty;
    
    [Parameter]
    public bool ExtraConfirmation { get; set; }
    
    [Parameter]
    public string ValidationText { get; set; }

    public string ErrorMessage { get; set; }

    public int SelectedId { get; set; }

    public void ConfirmProcessing()
    {
        if (ExtraConfirmation && ConfirmText.Trim() != ValidationText)
            ErrorMessage = "The confirmation is incorrect.";
        else
        {
            if(Callback.HasDelegate)
                Callback.InvokeAsync();

            else if(CallbackInt.HasDelegate)
                CallbackInt.InvokeAsync(SelectedId);

            ConfirmText = string.Empty;
        }
    }
}