using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Radzen;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using Microsoft.Extensions.Configuration;

namespace AMprover.BlazorApp.Pages;

public class IndexViewModel : BaseViewModel
{
    private readonly NavigationManager _navigationManager;
    private readonly IJSRuntime _jsRuntime;
    private readonly IPageNavigationManager _pageNavigationManager;

    public IndexViewModel(ILoggerFactory loggerFactory, IConfiguration configuration, DialogService dialogService,
        TooltipService tooltipService,
        NavigationManager navigationManager,
        IPageNavigationManager pageNavigationManager, ILookupManager lookupManager, IJSRuntime jsRuntime) : base(
        loggerFactory, configuration, tooltipService, lookupManager, jsRuntime, navigationManager)
    {
        _dialogService = dialogService;
        _navigationManager = navigationManager;
        _jsRuntime = jsRuntime;
        _pageNavigationManager = pageNavigationManager;
    }

    public ElementReference DataPreparation { get; set; }
    public ElementReference RiskMatrix { get; set; }
    public ElementReference ValueDriverAnalysis { get; set; }
    public ElementReference CriticalityRanking { get; set; }

    public ElementReference RcmFmecaStudy { get; set; }
    public ElementReference PmoStudy { get; set; }
    public ElementReference HazopStudy { get; set; }
    public ElementReference RamsStudy { get; set; }

    public ElementReference LccStudy { get; set; }
    public ElementReference SparePartStudy { get; set; }
    public ElementReference BuildWorkPackages { get; set; }
    public ElementReference LinkPmToAbs { get; set; }
    public ElementReference RiskOnAbs { get; set; }

    public ElementReference Summary { get; set; }
    public ElementReference LTAP { get; set; }
    public ElementReference ValueForecast { get; set; }
    public ElementReference EamUpload { get; set; }

    public override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (!firstRender) return;
        await AddOrUpdateCookie(_navigationManager.Uri);
    }

    public void Navigate(string page)
    {
        var navigationItem = _pageNavigationManager.GetPageUrl(page);
        _navigationManager.NavigateTo(!string.IsNullOrWhiteSpace(navigationItem) ? navigationItem : page);
    }

    public async Task OpenInNewTab(string url)
    {
        await _jsRuntime.InvokeAsync<object>("open", url, "_blank");
    }
}