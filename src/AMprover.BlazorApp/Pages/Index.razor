@page "/"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<IndexViewModel>
@inject IStringLocalizer<Index> _localizer

<div class="landing-page-container">
    <div class="row margin-top-70">
        <div class="col-sm-2">
        </div>
        <div class="col-sm-2">
            <div class="header-item align-items-center text-center">
                <p>@_localizer["IndexFocusTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.DataPreparation
                 @onclick=@(() => BindingContext.Navigate("/data-preparation"))>
                <div class="bg-data-preparation"></div>
                <p>@_localizer["IndexDataPrepTxt"]</p>
            </div>
        </div>
        @*<div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.ValueDriverAnalysis
                 @onclick="@(async() => await BindingContext.OpenInNewTab("https://www.myvdmxl.com"))"
                 @onmouseover="@(() => BindingContext.ShowTooltip(BindingContext.ValueDriverAnalysis, _localizer["IndexValueDriverAnalysisToolTip"]))"
                 @onmouseleave="@(() => BindingContext.HideTooltip(BindingContext.ValueDriverAnalysis))">
                <div class="bg-valuedriveranalysis"></div>
                <p>@_localizer["IndexVdmTxt"]</p>
            </div>
        </div>*@
        <div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.RiskMatrix
                 @onclick=@(() => BindingContext.Navigate("/value-risk-matrix"))>
                <div class="bg-matrix"></div>
                <p>@_localizer["IndexValueRiskMatrixTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2 empty">
            <div class="link-item" @ref=BindingContext.CriticalityRanking
                 @onclick=@(() => BindingContext.Navigate("/criticality-ranking"))>
                <div class="bg-criticality-ranking"></div>
                <p>@_localizer["IndexCritRankTxt"]</p>
            </div>
        </div>
    </div>
    <div class="row align-items-center">
        <div class="col-sm-2">
        </div>
        <div class="col-sm-2">
            <div class="header-item align-items-center text-center">
                <p>@_localizer["IndexAnalyseTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.RcmFmecaStudy
                 @onclick=@(() => BindingContext.Navigate("/value-risk-organizer"))>
                <div class="bg-rcm-fmeca-study"></div>
                <p>@_localizer["IndexRcmFmecaTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.PmoStudy
                 @onclick=@(() => BindingContext.Navigate("/value-risk-organizer?AnalysisType=pmo"))>
                <div class="bg-prev-maint-optimization"></div>
                <p>@_localizer["IndexPmoTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            @*@if (BindingContext.FeatureTurnedOn("Rams"))
            {*@
                <div class="link-item" @ref=BindingContext.RamsStudy
                    @onclick=@(() => BindingContext.Navigate("/rams"))>
                    <div class="bg-rams-analysis"></div>
                    <p>@_localizer["IndexRAMSTxt"]</p>
                </div>
           @* }
            else
            {
                <div class="link-item" @ref=BindingContext.RamsStudy
                     @onmouseover=@(() => BindingContext.ShowTooltip(BindingContext.RamsStudy, _localizer["IndexRamsToolTip"]))
                         @onmouseleave=@(() => BindingContext.HideTooltip(BindingContext.RamsStudy))>
                    <div class="bg-rams-analysis"></div>
                    <p>@_localizer["IndexRAMSTxt"]</p>
                </div>
            }*@
        </div>
    </div>
    <div class="row align-items-center">
        <div class="col-sm-2">
        </div>
        <div class="col-sm-2">
            <div class="header-item align-items-center text-center">
                <p>@_localizer["IndexOptTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.LccStudy
                 @onclick=@(() => BindingContext.Navigate("/lcc"))>
                <div class="bg-life-cycle-costing"></div>
                <p>@_localizer["IndexLccTxt"]</p>
            </div>
        </div>
        @*<div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.SparePartStudy
                 @onclick=@(() => BindingContext.Navigate("/reports"))
                 @onmouseover=@(() => BindingContext.ShowTooltip(BindingContext.SparePartStudy, _localizer["IndexSparePartToolTip"]))
                 @onmouseleave=@(() => BindingContext.HideTooltip(BindingContext.SparePartStudy))>
                <div class="bg-define-spare-parts"></div>
                <p>@_localizer["IndexSparePartTxt"]</p>
            </div>
        </div>*@
        <div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.BuildWorkPackages
                 @onclick=@(() => BindingContext.Navigate("/cluster"))>
                <div class="bg-build-work-packages"></div>
                <p>@_localizer["IndexWorkPackagesTxt"]</p>
            </div>
        </div>
        @*<div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.LinkPmToAbs
            @onclick=@(() => BindingContext.Navigate("/assign-assets"))>
                <div class="bg-link-risks-to-abs"></div>
                <p>@_localizer["IndexLinkPmAbsTxt"] </p>
            </div>
        </div>*@
        <div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.RiskOnAbs
                 @onclick=@(() => BindingContext.Navigate("/value-risks-on-abs"))>
                <div class="bg-rabs"></div>
                <p>@_localizer["IndexLinkVrOmAbsTxt"]</p>
            </div>
        </div>
    </div>
    <div class="row align-items-center">
        <div class="col-sm-2">
        </div>
        <div class="col-sm-2">
            <div class="header-item align-items-center text-center">
                <p>@_localizer["IndexDeployTxt"] </p>
            </div>
        </div>
        <div class="col-sm-2 empty">
            <div class="link-item" @ref=BindingContext.Summary
                @onclick=@(() => BindingContext.Navigate("/reports"))>
                <div class="bg-reporting"></div>
                <p>@_localizer["IndexSummaryTxt"] </p>
            </div>
        </div>
        @* <div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.LTAP
                 @onmouseover="@(args => BindingContext.ShowTooltip(BindingContext.LTAP, _localizer["IndexLtapToolTip"]))"
                 @onmouseleave=@(() => BindingContext.HideTooltip(BindingContext.LTAP))>
                 <div class="bg-long-term-asset-plan"></div>
                <p>@_localizer["IndexLtapTxt"]</p>
            </div>
        </div>*@
        <div class="col-sm-2">
            <div class="link-item" @ref=BindingContext.ValueForecast
                @onclick=@(() => BindingContext.Navigate("/VDMXL-view"))>
                <div class="bg-value-forecast"></div>
                <p>@_localizer["IndexValForecastTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2 empty">
            <div class="link-item" @ref=BindingContext.EamUpload
                 @onclick=@(() => BindingContext.Navigate("/value-risk-exports"))>
                <div class="bg-eam-upload"></div>
                <p>@_localizer["IndexEamUploadTxt"]</p>
            </div>
        </div>
    </div>
</div>