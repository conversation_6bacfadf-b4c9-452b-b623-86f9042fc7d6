@page "/VDMXL-view/{LccId:int?}"
@using BusinessLogic.Models.LCC
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<VdmXlViewModel>
@inject IStringLocalizer<VdmXlPage> _localizer
@inject IGlobalDataService GlobalDataService;

<div class="row">
    <div class="col-6">
        <p><h2>@((MarkupString)_localizer["VdmXlHeaderTxt"].Value)</h2></p>
    </div>
</div>

<div class="row header-navigation">
    <div class="col-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
                <NavLink class="breadcrumb-item" aria-current="page" Match="NavLinkMatch.All">
                    VDMXL-view
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-4 text-center">
        <Paginator Initial=@BindingContext.GetInitialPaginatorValue()
                   Count=@BindingContext.GetScenarioCount()
                   CallBack=@BindingContext.PaginatorCallback
                   @ref=@BindingContext.Paginator />
    </div>
    <div class="col-4 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@_localizer["VdmXlMenuTitle"] DialogContent=@_localizer["VdmXlMenuTxt"] />
        <RadzenButton Icon="add_circle_outline" Text=@_localizer["VdmXlNewVdmXl"] class="float-right my-2 mx-0" Click=@BindingContext.OpenNewLccWidget ButtonStyle=ButtonStyle.Secondary Disabled=!GlobalDataService.CanEdit />
        <RadzenButton class="float-right my-2 mx-2" Icon="refresh" Text=@_localizer["VdmXlRecalculateBtn"] Click=@BindingContext.SaveAndReCalculateLcc
                      ButtonStyle=ButtonStyle.Secondary Disabled=BindingContext.Busy BusyText="Calculating..." IsBusy=BindingContext.Busy />
    </div>
</div>

<div class="row">
    <div class="col-sm-3">
        <RadzenDropDown AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" Data="@BindingContext.RiskScenarios" @bind-value=BindingContext.SelectedScenario
                        TextProperty="Name" ValueProperty="Id" TValue="int" Change=@(args => BindingContext.SelectScenario(args)) />

        <RadzenTabs @ref=@BindingContext.TreeTab TabPosition=TabPosition.Right class="hide-tabs-nav">
            <Tabs>
                <RadzenTabsItem>
                    <TreeComponent TItem=LccTreeObject
                                   Treeview=@BindingContext.LccTree
                                   NodeClickCallback=@BindingContext.ClickTreeNode 
                                   DeleteCallback=@(args => BindingContext.DeleteLccFromTree(args))/>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
    <div class="col-sm-9">
        <RadzenTabs @ref=@BindingContext.VdmTab TabPosition=TabPosition.Right class="hide-tabs-nav">
            <Tabs>
                <RadzenTabsItem>
                    @if (BindingContext.SelectedLcc != null)
                    {
                        <EditForm Model="@BindingContext.SelectedLcc">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="row">
                                        <div class="col-sm-2">
                                            <div class="form-group vdmxl-neg-margin-small">
                                                <label>Id:</label>
                                                <span class="form-control readonly vdmxl-neg-margin-small">@BindingContext.SelectedLcc.Id</span>
                                            </div>
                                        </div>
                                        <div class="col-sm-10">
                                            <AMproverTextBox @bind-Value=@BindingContext.SelectedLcc.Name MaxLength="50" Label=@_localizer["VdmXlNameLbl"] />
                                        </div>
                                    </div>
                                    <div class="vdmxl-frame-overview vdmxl-bg-image">
                                        <div class="row col-sm-12">
                                            <div class="col-sm-3 mt-2">
                                                <RadzenRow class="float-left vdmxl-view-before">
                                                    <p>@_localizer["VdmXlVRwoActionsTxt"]</p>
                                                </RadzenRow>
                                                @if (!BindingContext.IsSapaView)
                                                {
                                                    <RadzenRow class="float-left vdmxl-view-pmo">
                                                        <p>@_localizer["VdmXlVRPmoTxt"]</p>
                                                    </RadzenRow>
                                                }
                                                <RadzenRow class="float-left vdmxl-view-after">
                                                    <p>@_localizer["VdmXlVRwithActionsTxt"]</p>
                                                </RadzenRow>
                                            </div>
                                            <div class="col-sm-6 center vdmxl-frame-specific ">
                                                <text class="rz-text-align-center bold">@_localizer["VdmXlRisksTxt"]:</text>
                                                <RadzenRow Gap=0>
                                                    <RadzenColumn Size="5" class="rz-text-align-left vdmxl-view-index rz-p-1">
                                                        <p>@((MarkupString)BindingContext.RiskTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before rz-p-1">@*Visible=false Size="0"*@
                                                        <p>@((MarkupString)BindingContext.RiskTxt[1])</p>
                                                    </RadzenColumn>
                                                    @if (!BindingContext.IsSapaView)
                                                    {
                                                        <RadzenColumn class="vdmxl-view-pmo rz-p-1">
                                                            <p>@(
                                                                 (MarkupString)BindingContext.RiskTxt[2]
                                                                 )</p>
                                                        </RadzenColumn>
                                                    }
                                                    <RadzenColumn class="vdmxl-view-after rz-p-1">
                                                        <p>@((MarkupString)BindingContext.RiskTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                           
                                            <div class="col-sm-3 center">
                                                <RadzenRow class="rz-text-align-center" Gap=0>
                                                    <RadzenColumn class="rz-text-align-left vdmxl-view-index mt-2">
                                                        <p>@((MarkupString)BindingContext.NpvTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before mt-2">
                                                        <p>@((MarkupString)BindingContext.NpvTxt[1])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                                @if (!BindingContext.IsSapaView)
                                                {
                                                    <RadzenRow class="rz-text-align-center" Gap=0>
                                                        <RadzenColumn class="vdmxl-neg-margin" />
                                                        <RadzenColumn class="vdmxl-view-pmo vdmxl-neg-margin">
                                                            <p>@((MarkupString)BindingContext.NpvTxt[2])</p>
                                                        </RadzenColumn>
                                                    </RadzenRow>
                                                }
                                                <RadzenRow class="rz-text-align-center" Gap=0>
                                                    <RadzenColumn class="vdmxl-neg-margin" />
                                                    <RadzenColumn class="vdmxl-view-after vdmxl-neg-margin">
                                                        <p>@((MarkupString)BindingContext.NpvTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                                <RadzenRow class="rz-text-align-center" Gap=0>
                                                    <RadzenColumn class="rz-text-align-left vdmxl-view-index vdmxl-neg-margin">
                                                        <p>@((MarkupString)BindingContext.AecTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before vdmxl-neg-margin">
                                                        <p>@((MarkupString)BindingContext.AecTxt[1])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                                @if (!BindingContext.IsSapaView)
                                                {
                                                    <RadzenRow class="rz-text-align-center" Gap=0>
                                                        <RadzenColumn class="vdmxl-neg-margin" />
                                                        <RadzenColumn class="vdmxl-view-pmo vdmxl-neg-margin">
                                                            <p>@(
                                                                 (MarkupString)BindingContext.AecTxt[2]
                                                                 )</p>
                                                        </RadzenColumn>
                                                    </RadzenRow>
                                                }
                                                <RadzenRow class="rz-text-align-center" Gap=0>
                                                    <RadzenColumn class="vdmxl-neg-margin" />
                                                    <RadzenColumn class="vdmxl-view-after vdmxl-neg-margin">
                                                        <p>@((MarkupString)BindingContext.AecTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                        </div>
                                        <br /><br /><br /><br /><br />
                                        <div class="row col-sm-12">
                                            <div class="col-sm-4 float-left vdmxl-frame-specific">
                                                <div>
                                                    <text class="bold">@_localizer["VdmXlUtlizationTxt"]:</text>
                                                </div>
                                                <RadzenRow class="rz-text-align-center rz-border-lightgrey" Gap=0>
                                                    <RadzenColumn Size="5" class="rz-text-align-left vdmxl-view-index rz-p-1">
                                                        <p>@((MarkupString)BindingContext.UtilizationTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before rz-p-1">
                                                        <p>@((MarkupString)BindingContext.UtilizationTxt[1])</p>
                                                    </RadzenColumn>
                                                    @if (!BindingContext.IsSapaView)
                                                    {
                                                        <RadzenColumn class="vdmxl-view-pmo rz-p-1">
                                                            <p>@((MarkupString)BindingContext.UtilizationTxt[2])</p>
                                                        </RadzenColumn>
                                                    }
                                                    <RadzenColumn class="vdmxl-view-after rz-p-1">
                                                        <p>@((MarkupString)BindingContext.UtilizationTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                            <div class="col-sm-4 center"></div>
                                            <div class="col-sm-4 float-right vdmxl-frame-specific">
                                                <div>
                                                    <text class="bold">@_localizer["VdmXlOpexTxt"]:</text>
                                                </div>
                                                <RadzenRow class="rz-text-align-center rz-border-lightgrey" Gap=0>
                                                    <RadzenColumn Size="5" class="rz-text-align-left vdmxl-view-index rz-p-1">
                                                        <p>@((MarkupString)BindingContext.OpexTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before rz-p-1">
                                                        <p>@((MarkupString)BindingContext.OpexTxt[1])</p>
                                                    </RadzenColumn>
                                                    @if (!BindingContext.IsSapaView)
                                                    {
                                                        <RadzenColumn class="vdmxl-view-pmo rz-p-1">
                                                            <p>@((MarkupString)BindingContext.OpexTxt[2])</p>
                                                        </RadzenColumn>
                                                    }
                                                    <RadzenColumn class="vdmxl-view-after rz-p-1">
                                                        <p>@((MarkupString)BindingContext.OpexTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                        </div>
                                        <br /><br /><br /><br /><br />
                                        <div class="row col-sm-12">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6 center vdmxl-frame-specific">
                                                <div >
                                                    <text class="bold">@_localizer["VdmXlCapexTxt"]:</text>
                                                </div>
                                                <RadzenRow class="rz-text-align-center rz-border-lightgrey" Gap=0>
                                                    <RadzenColumn Size="5" class="rz-text-align-left vdmxl-view-index rz-p-1">
                                                        <p>@((MarkupString)BindingContext.CapexTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before rz-p-1">
                                                        <p>@((MarkupString)BindingContext.CapexTxt[1])</p>
                                                    </RadzenColumn>
                                                    @if (!BindingContext.IsSapaView)
                                                    {
                                                        <RadzenColumn class="vdmxl-view-pmo rz-p-1">
                                                            <p>@((MarkupString)BindingContext.CapexTxt[2])</p>
                                                        </RadzenColumn>
                                                    }
                                                    <RadzenColumn class="vdmxl-view-after rz-p-1">
                                                        <p>@((MarkupString)BindingContext.CapexTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <br />
                        </EditForm>
                    }
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
</div>

@code{
    [Parameter]
    public int? LccId { get; set; }
}