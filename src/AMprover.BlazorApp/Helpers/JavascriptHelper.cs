using System.Threading.Tasks;
using Microsoft.JSInterop;

namespace AMprover.BlazorApp.Helpers;

public class JavaScriptHelper
{
    private readonly IJSRuntime _jsRuntime;

    public JavaScriptHelper(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
    }

    public async Task<T> InvokeAsync<T>(string functionName, params object[] args)
    {
        try
        {
            if (typeof(T) != typeof(void)) return await _jsRuntime.InvokeAsync<T>(functionName, args);
            await _jsRuntime.InvokeVoidAsync(functionName, args);
            return default;
        }
        catch (JSException e)
        {
            return typeof(T) == typeof(void) ? default : default(T);
        }
    }
}