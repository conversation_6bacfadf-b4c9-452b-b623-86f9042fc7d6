using AMprover.BusinessLogic.Enums.Reports;
using System;

namespace AMprover.BlazorApp.Helpers;

public static class ContentTypeHelper
{
    public static string ToContentType(this RenderType renderType)
    {
        return renderType switch
        {
            RenderType.Pdf => "application/pdf",
            RenderType.Word => "application/vnd.openxmlformats-officedocument.wordprocessingml.template",
            RenderType.Excel => "application/vnd.ms-excel",
            RenderType.Image => "image/jpeg",
            RenderType.Xml => "application/xml",
            RenderType.Unknown => throw new NotImplementedException(),
            _ => throw new ArgumentOutOfRangeException(nameof(renderType), renderType, null)
        };
    }
}