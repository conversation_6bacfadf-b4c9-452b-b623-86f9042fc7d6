<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="CaeNameLbl" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="CaeReferenceIdLbl" xml:space="preserve">
    <value>Id de référence</value>
  </data>
  <data name="CaePolicyLbl" xml:space="preserve">
    <value>Stratégie</value>
  </data>
  <data name="CaeActionTypeLbl" xml:space="preserve">
    <value>Type d'action</value>
  </data>
  <data name="CaeDescriptionLbl" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="CaeRemarksLbl" xml:space="preserve">
    <value>Remarques</value>
  </data>
  <data name="CaeModifiableLbl" xml:space="preserve">
    <value>Modifiable</value>
  </data>
  <data name="CaeCostLbl" xml:space="preserve">
    <value>Coût</value>
  </data>
  <data name="CaeUnitTypeLbl" xml:space="preserve">
    <value>Type d'unité</value>
  </data>
  <data name="CaeInitiatorLbl" xml:space="preserve">
    <value>Initiateur</value>
  </data>
  <data name="CaeExecutorLbl" xml:space="preserve">
    <value>Exécutant</value>
  </data>
  <data name="CaeWorkPackageLbl" xml:space="preserve">
    <value>Dossier de travail</value>
  </data>
  <data name="CaeIntervalLbl" xml:space="preserve">
    <value>Intervalle</value>
  </data>
  <data name="CaeGeneralItemsLbl" xml:space="preserve">
    <value>Points généraux</value>
  </data>
  <data name="CaePermitLbl" xml:space="preserve">
    <value>Permis</value>
  </data>
  <data name="CaeResponsibleLbl" xml:space="preserve">
    <value>Responsable</value>
  </data>
  <data name="CaeDownTimeLbl" xml:space="preserve">
    <value>Temps d'arrêt</value>
  </data>
  <data name="CaeDurationLbl" xml:space="preserve">
    <value>Durée</value>
  </data>
  <data name="CaePropertiesLbl" xml:space="preserve">
    <value>Propriétés</value>
  </data>
  <data name="CaeInitiatedByLbl" xml:space="preserve">
    <value>Initiée par</value>
  </data>
  <data name="CaeOnLbl" xml:space="preserve">
    <value>On</value>
  </data>
  <data name="CaSubHeaderTxt" xml:space="preserve">
    <value>&lt;b&gt;Actions communes&lt;/b&gt; peuvent être utilisées pour définir des actions standard, qui peuvent être utilisées pour configurer des plans de tâches.</value>
  </data>
  <data name="CaHeaderTxt" xml:space="preserve">
    <value>Actions communes</value>
  </data>
  <data name="CaMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;Objectif : La liste des actions communes sont des actions prédéfinies et des paramètres d’action qui peuvent être utilisés dans l’onglet Action préventive du module &lt;b&gt;Évaluation des valeur/risques&lt;/b&gt;. Dans ce module, vous pouvez sélectionner une action commune et la mettre à jour en fonction de vos résultats d’évaluation spécifiques et des paramètres d’actions préventives requis.
&lt;/br&gt;&lt;ol&gt;
&lt;li&gt;Utilisez &lt;b&gt;New&lt;/b&gt; pour ajouter un enregistrement.&lt;/li&gt;
&lt;li&gt;Utilisez &lt;b&gt;Edit&lt;/b&gt; pour modifier une ligne d’enregistrement.&lt;/li&gt;
&lt;li&gt;Utilisez &lt;b&gt;Open as popup &lt;/b&gt;pour modifier le contenu d’un enregistrement. &lt;/li&gt;
&lt;li&gt;Utilisez &lt;b&gt;Modifier les colonnes&lt;/b&gt; pour modifier la disposition de l’onglet/tableau. &lt;/li&gt;
&lt;/ol&gt;&lt;/p&gt;</value>
  </data>
  <data name="CaeSortOrderLbl" xml:space="preserve">
    <value>Ordre de tri</value>
  </data>
</root>