using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Identity;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Services
{
    /// <summary>
    /// Service that provides scoped UserManager operations to avoid DbContext disposal issues in Blazor Server
    /// </summary>
    public interface IScopedUserService
    {
        Task<UserAccount> FindByIdAsync(string userId);
        Task<bool> IsInRoleAsync(UserAccount user, string role);
        Task<IList<string>> GetRolesAsync(UserAccount user);
        Task<IdentityResult> CreateAsync(UserAccount user, string password);
        Task<IdentityResult> UpdateAsync(UserAccount user);
        Task<IdentityResult> DeleteAsync(UserAccount user);
        Task<IdentityResult> AddToRoleAsync(UserAccount user, string role);
        Task<IdentityResult> RemoveFromRoleAsync(UserAccount user, string role);
        Task<IdentityResult> AddToRolesAsync(UserAccount user, IEnumerable<string> roles);
        Task<IdentityResult> RemoveFromRolesAsync(UserAccount user, IEnumerable<string> roles);
        Task<List<UserAccount>> GetUsersWithPortfolioAssignmentsAsync(int portfolioId);
    }
}
