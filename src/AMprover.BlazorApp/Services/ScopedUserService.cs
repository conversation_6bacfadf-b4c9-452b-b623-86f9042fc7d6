using AMprover.Data.Entities.Identity;
using AMprover.Data.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace AMprover.BlazorApp.Services
{
    /// <summary>
    /// Implementation of IScopedUserService that creates a new scope for each operation
    /// to avoid DbContext disposal issues in Blazor Server applications
    /// </summary>
    public class ScopedUserService : IScopedUserService
    {
        private readonly IServiceProvider _serviceProvider;

        public ScopedUserService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<UserAccount> FindByIdAsync(string userId)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.FindByIdAsync(userId);
        }

        public async Task<bool> IsInRoleAsync(UserAccount user, string role)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.IsInRoleAsync(user, role);
        }

        public async Task<IList<string>> GetRolesAsync(UserAccount user)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.GetRolesAsync(user);
        }

        public async Task<IdentityResult> CreateAsync(UserAccount user, string password)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.CreateAsync(user, password);
        }

        public async Task<IdentityResult> UpdateAsync(UserAccount user)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.UpdateAsync(user);
        }

        public async Task<IdentityResult> DeleteAsync(UserAccount user)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.DeleteAsync(user);
        }

        public async Task<IdentityResult> AddToRoleAsync(UserAccount user, string role)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.AddToRoleAsync(user, role);
        }

        public async Task<IdentityResult> RemoveFromRoleAsync(UserAccount user, string role)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.RemoveFromRoleAsync(user, role);
        }

        public async Task<IdentityResult> AddToRolesAsync(UserAccount user, IEnumerable<string> roles)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.AddToRolesAsync(user, roles);
        }

        public async Task<IdentityResult> RemoveFromRolesAsync(UserAccount user, IEnumerable<string> roles)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();
            return await userManager.RemoveFromRolesAsync(user, roles);
        }

        public async Task<List<UserAccount>> GetUsersWithPortfolioAssignmentsAsync(int portfolioId)
        {
            using var scope = _serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();

            return await userManager.Users
                .Where(x => x.PortfolioAssignments.Any(y => y.PortfolioId == portfolioId))
                .ToListAsync();
        }
    }
}
