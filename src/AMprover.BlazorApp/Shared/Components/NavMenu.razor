@using AMprover.Data.Constants
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<NavMenuViewModel>
@inject IStringLocalizer<NavMenu> _localizer

<div class="top-row pl-4 navbar navbar-dark">
    <a class="navbar-brand" href=""></a>
</div>

<div>
    <ul class="nav flex-column">
        <li class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <img src="/images/HomeWhite.png" height="20px" width="20px"/>
                <span class="sidebar-text px-2">@_localizer["NmHomeLbl"]</span>
            </NavLink>
        </li>
        <li class="nav-item px-3">
            <NavLink class="nav-link" @onclick="() => _expandPortfolioSetup = !_expandPortfolioSetup">
                <img src="/images/DataPrepWhite.png" height="20px" width="20px"/>
                <span class="sidebar-text px-2">@_localizer["NmDataPrepTxt"]</span>
                @if (_expandPortfolioSetup)
                {
                    <span class="fas fa-chevron-up" aria-hidden="true"></span>
                }
                else
                {
                    <span class="fas fa-chevron-down" aria-hidden="true"></span>
                }
            </NavLink>
        </li>
        @if (_expandPortfolioSetup)
        {
            <li class="nav-item px-3 submenu">
                <NavLink class="nav-link" href="/scenarios">
                    <img src="/images/ScenarioGrey.png" height="28px" width="28px"/>
                    <span class="sidebar-text px-0">@_localizer["NmScenariosTxt"]</span>
                </NavLink>
            </li>
            <li class="nav-item px-3 submenu">
                <NavLink class="nav-link" href="/failures">
                    <img src="/images/FailureModeGrey.png" height="24px" width="24px"/>
                    <span class="sidebar-text px-2">@_localizer["NmFailuresTxt"]</span>
                </NavLink>
            </li>
            <li class="nav-item px-3 submenu">
                <NavLink class="nav-link" href="/objects">
                    <img src="/images/GeneralHierarchyGrey.png" height="24px" width="24px"/>
                    <span class="sidebar-text px-2">@_localizer["NmGenHierObjectsTxt"]</span>
                </NavLink>
            </li>
            <li class="nav-item px-3 submenu">
                <NavLink class="nav-link" href="/action-setting">
                    <img src="/images/ActionSettingsGrey.png" height="28px" width="28px"/>
                    <span class="sidebar-text px-1">@_localizer["NmActionSettingsTxt"]</span>
                </NavLink>
            </li>
            <li class="nav-item px-3 submenu">
                <NavLink class="nav-link" href="/common-actions">
                    <img src="/images/CommonActionGrey.png" height="28x" width="28px"/>
                    <span class="sidebar-text px-1">@_localizer["NmCommonActionsTxt"]</span>
                </NavLink>
            </li>
            <li class="nav-item px-3 submenu">
                <NavLink class="nav-link" href="/cluster-cost-settings">
                    <img src="/images/ClusterCostGrey.png" height="28px" width="28px"/>
                    <span class="sidebar-text px-1">@_localizer["NmClusterCostSettings"]</span>
                </NavLink>
            </li>
            <li class="nav-item px-3 submenu">
                <NavLink class="nav-link" href="/settings">
                    <img src="/images/InstellingenGrey.png" height="24px" width="24px"/>
                    <span class="sidebar-text px-2">@_localizer["NmAMproverSettingsTxt"]</span>
                </NavLink>
            </li>
            <li class="nav-item px-3 submenu">
                <NavLink class="nav-link" href="/asset-breakdown-structure">
                    <img src="/images/AbsGrey.png" height="24px" width="24px"/>
                    <span class="sidebar-text px-2">@_localizer["NmABSTxt"]</span>
                </NavLink>
            </li>
            <li class="nav-item px-3 submenu">
                <NavLink class="nav-link" href="/releasenotes">
                    <span class="fas fa-note-sticky" aria-hidden="true"></span>
                    <span class="sidebar-text px-2">@_localizer["NmReleaseNotesTxt"]</span>
                </NavLink>
            </li>
        }
        <li class="nav-item px-3">
            <NavLink class="nav-link" href="/value-risk-matrix">
                <img src="/images/ValueRiskMatrixWhite.png" height="20px" width="20px"/>
                <span class="sidebar-text px-2">@_localizer["NmValueRiskMatrixTxt"]</span>
            </NavLink>
        </li>
        <li class="nav-item px-3">
            <NavLink class="nav-link" href="/criticality-ranking">
                <img src="/images/AbcRankingWhite.png" height="24px" width="24px"/>
                <span class="sidebar-text px-2">@_localizer["NmAbcRankingTxt"]</span>
            </NavLink>
        </li>
        <li class="nav-item px-3">
            <AMproverNavLink class="nav-link" href="/value-risk-organizer?" Match="NavLinkMatch.Prefix" @onclick=@(() => BindingContext.Navigate("/value-risk-organizer"))>
                <img src="/images/ValueRiskOrganizer1White.png" height="28px" width="28px"/>
                <span class="sidebar-text px-0">@_localizer["NmValueRiskOrganizerTxt"]</span>
            </AMproverNavLink>
        </li>
        @if (BindingContext.SapaEnabled)
        {
            <li class="nav-item px-3">
                <AMproverNavLink class="nav-link" href="/sapa-overview?" Match="NavLinkMatch.Prefix" @onclick=@(() => BindingContext.Navigate("/sapa-overview"))>
                    <img src="/images/SapaWhite.png" height="28px" width="28px"/>
                    <span class="sidebar-text px-2">@_localizer["NmSapaOverviewTxt"]</span>
                </AMproverNavLink>
            </li>
        }
        <li class="nav-item px-3">
            <NavLink class="nav-link" href="/lcc" Match="NavLinkMatch.Prefix">
                <img src="/images/LccStudyWhite.png" height="24px" width="24px"/>
                <span class="sidebar-text px-2">@_localizer["NmLccTxt"]</span>
            </NavLink>
        </li>
        <li class="nav-item px-3">
            <AMproverNavLink class="nav-link" href="/cluster" Match="NavLinkMatch.Prefix" @onclick=@(() => BindingContext.Navigate("/cluster"))>
                <img src="/images/ClusterWhite.png" height="24px" width="24px"/>
                <span class="sidebar-text px-2">@_localizer["NmWorkPackagesTxt"]</span>
            </AMproverNavLink>
        </li>
        <li class="nav-item px-3">
            <AMproverNavLink class="nav-link" href="/value-risks-on-abs" Match="NavLinkMatch.Prefix" @onclick=@(() => BindingContext.Navigate("/value-risks-on-abs"))>
                <img src="/images/RiskOnAbsWhite.png" height="24px" width="24px"/>
                <span class="sidebar-text px-2">@_localizer["NmRiskOnAbsTxt"]</span>
            </AMproverNavLink>
        </li>
        @*<li class="nav-item px-3">
            <NavLink class="nav-link" href="/assign-assets" Match="NavLinkMatch.Prefix" @onclick=@(() => BindingContext.Navigate("/assign-assets"))>
                <img src="/images/LinkStructuresWhite.png" height="22px" width="22px" />
                <span class="sidebar-text px-2">@_localizer["NmLinkPmAbsTxt"]</span>
            </NavLink>
        </li>*@
        <li class="nav-item px-3">
            <NavLink class="nav-link" href="/reports">
                <img src="/images/ReportsWhite.png" height="20px" width="20px"/>
                <span class="sidebar-text px-2">@_localizer["NmSummaryTxt"]</span>
            </NavLink>
        </li>

        @code {
            private string AdminRoles => $"{RoleConstants.Administrators},{RoleConstants.PortfolioAdministrators}";
        }

        <AuthorizeView Roles="@AdminRoles">
            <li class="nav-item px-3">
                <NavLink class="nav-link" href="/users">
                    <span class="fas fa-user" aria-hidden="true"></span>
                    <span class="sidebar-text px-2">@_localizer["NmUsersTxt"]</span>
                </NavLink>
            </li>
        </AuthorizeView>
        @if (BindingContext.HasSelectedRiskAssessment)
        {
            <li class="nav-item px-3 submenu">
                <span class="sidebar"></span>@_localizer["NmRecentlyUsedTxt"]
                <AMproverNavLink class="nav-link" href="/value-risk-analysis/" Match="NavLinkMatch.Prefix" @onclick=@(() => BindingContext.Navigate("/value-risk-analysis/0/risks/0"))>
                    <img src="/images/value_risk.png" height="24px" width="24px"/>
                    <span class="sidebar-text px-2">@_localizer["NmLatestValueRiskAssessmentTxt"]</span>
                </AMproverNavLink>
            </li>
        }
    </ul>
</div>

@code
{
    private bool _expandPortfolioSetup = false;
}