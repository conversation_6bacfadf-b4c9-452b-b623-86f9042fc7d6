@using AMprover.BusinessLogic.Helpers
@using Ra<PERSON>zen
@using System.Globalization
@using Accord
@using Microsoft.Extensions.Logging
@typeparam TValue
@inherits FormComponent<TValue>
@inject ILoggerFactory LoggerFactory
@inject IGlobalDataService GlobalDataService

@if (Visible)
{
    @if (ShowOnlyInput)
    {
        <input class=@(GetClass())  @ref="@_input" type="text" inputmode="decimal" name="@Name" disabled="@(Disabled || !CanEdit)" readonly="@ReadOnly"
               tabindex="@TabIndex" lang=@GlobalDataService.Language
               placeholder="@Placeholder" autocomplete="@(AutoComplete ? "on" : "off")" value="@FormattedValue" @onchange="@OnChange" @onblur=@HandleBlur
               onkeypress="Radzen.numericKeyPress(event)"
               onpaste="let e = arguments[0] || window.event; return !(/[^0-9\.]/.test((e.clipboardData || window.clipboardData).getData('Text')));"/>
    }
    else
    {
        <div class=@(UsedInGrid ? "" : "form-group")>

            @if (!string.IsNullOrEmpty(Label))
            {
                <div class="neg-margin-small">
                    <label>@Label:</label>
                </div>
            }
            <div class="neg-margin-small">
                <input class=@(GetClass())  @ref="@_input" type="text" inputmode="decimal" name="@Name" disabled="@(Disabled || !CanEdit)" readonly="@ReadOnly"
                       tabindex="@TabIndex" lang=@GlobalDataService.Language
                       placeholder="@Placeholder" autocomplete="@(AutoComplete ? "on" : "off")" value="@FormattedValue" @onchange="@OnChange" @onblur=@HandleBlur
                       onkeypress="Radzen.numericKeyPress(event)"
                       onpaste="let e = arguments[0] || window.event; return !(/[^0-9\.]/.test((e.clipboardData || window.clipboardData).getData('Text')));"/>

                @if (Required)
                {
                    <RadzenRequiredValidator Component=@Name DefaultValue=@DefaultValue/>
                }
            </div>
        </div>
    }
}

@code {

    #region Parameters

    [Parameter] public bool HideStyling { get; set; }

    [Parameter] public bool ShowOnlyInput { get; set; }

    [Parameter]
    public override TValue Value
    {
        get => _value;
        set
        {
            if (!EqualityComparer<TValue>.Default.Equals(value, _value))
            {
                _value = value;
            }
        }
    }

    [Parameter] public bool ReadOnly { get; set; }

    [Parameter] public bool AutoComplete { get; set; } = true;

    [Parameter] public bool UsedInGrid { get; set; }

    [Parameter] public string Label { get; set; }

    [Parameter] public string Format { get; set; }

    [Parameter] public string Unit { get; set; }

    [Parameter] public decimal? Min { get; set; }

    [Parameter] public decimal? Max { get; set; }

    [Parameter] public double Multiplier { get; set; } = 1.0;

    [Parameter] public string Step { get; set; }

    [Parameter] public bool ForceUpdateHtml { get; set; }

    [Parameter] public EventCallback OnBlur { get; set; }

    [Parameter] public bool Required { get; set; }

    [Parameter] public object DefaultValue { get; set; }

    [Parameter] public bool ForceInvariantCulture { get; set; }

    [Parameter] public bool AllowEmpty { get; set; }

    #endregion

    private bool CanEdit { get; set; }

    protected override async Task OnInitializedAsync()
    {
        CanEdit = GlobalDataService.CanEdit;
        await base.OnInitializedAsync();

        // When the user first opens the webapp on a page that uses this component, the Users role has not been set yet.
        // Hacky way to ensure that the user is not blocked to edit fields even though they should be allowed to.
        if (!GlobalDataService.Initialized)
        {
            await Task.Delay(500);
            CanEdit = GlobalDataService.CanEdit;
        }
    }

    private ElementReference _input;

    private bool IsTypeSupported()
    {
        var type = typeof(TValue).IsGenericType ? typeof(TValue).GetGenericArguments()[0] : typeof(TValue);

        switch (Type.GetTypeCode(type))
        {
            case TypeCode.UInt32:
            case TypeCode.Int32:
            case TypeCode.Decimal:
            case TypeCode.Double:
            case TypeCode.Single:
                return true;
            default:
                return false;
        }
    }

    private string FormattedValue
    {
        get
        {
            try
            {
                if (Value == null) return string.Empty;

                // Apply multiplier to display value
                var valueWithMultiplier = ForceInvariantCulture
                    ? Convert.ToDouble(Value, CultureInfo.InvariantCulture) * Multiplier
                    : Convert.ToDouble(Value) * Multiplier;

                if (Format == null)
                {
                    return valueWithMultiplier.ToString(CultureInfo.InvariantCulture);
                }

                return string.IsNullOrEmpty(Unit) ? valueWithMultiplier.ToNumericDisplayValue(GetNumberFormat(), Format) : $"{valueWithMultiplier.ToNumericDisplayValue(GetNumberFormat(), Format)} {Unit}";
            }
            catch (Exception e)
            {
                var logger = LoggerFactory.CreateLogger(nameof(AMproverNumberInput<TValue>));
                logger.LogError("Unable to format value '{Value}' as '{Type}' - {EMessage}", Value, Value?.GetType(), e.Message);
                return string.Empty;
            }
        }
        set => _ = InternalValueChanged(value); // Allow input handling
    }


    public override bool HasValue => Value != null;

    private async Task OnChange(ChangeEventArgs args)
    {
        await InternalValueChanged(args.Value);
    }

    private async Task HandleBlur(FocusEventArgs e)
    {
        // Invoke the provided OnBlur event callback
        if (OnBlur.HasDelegate)
        {
            await OnBlur.InvokeAsync(e);
        }
    }

    private async Task InternalValueChanged(object value)
    {
        if (value == null)
            return;

        if (AllowEmpty && value.ToString() == string.Empty)
        {
            await JSRuntime.InvokeAsync<string>("Radzen.setInputValue", _input, "");
            await Change.InvokeAsync(default);
            await ValueChanged.InvokeAsync(default);
            return;
        }

        var cultureString = GlobalDataService.Currency ?? "nl-NL";

        var cleanedValue = value.ToString().RemoveNonNumberDigitsAndCharacters();

        if (ForceInvariantCulture)
        {
            cultureString = "en-US";

            cleanedValue = cleanedValue.Replace(",", ".");
            var delimiter = cleanedValue.Split('.').Length - 1;

            if (delimiter > 1)
            {
                var lastIndex = cleanedValue.LastIndexOf('.');
                if (lastIndex > 0)
                {
                    cleanedValue = cleanedValue.Substring(0, lastIndex).Replace(".", "") + cleanedValue.Substring(lastIndex);
                }
            }
        }

        if (string.IsNullOrWhiteSpace(cleanedValue))
        {
            var logger = LoggerFactory.CreateLogger(nameof(AMproverNumberInput<TValue>));
            logger.LogError("Number input doesnt contain any non number digits");
            return;
        }

        if (!decimal.TryParse(cleanedValue, NumberStyles.Any, CultureInfo.CreateSpecificCulture(cultureString), out var newValue))
        {
            var logger = LoggerFactory.CreateLogger(nameof(AMproverNumberInput<TValue>));
            logger.LogError("Number input parsing failed with value {Value}", value);
            return;
        }

        var newValueAsDecimal = newValue == 0 && Min is null or > 0 ? default(decimal?) : (decimal) ConvertType.ChangeType(newValue, typeof(decimal));

        if (Equals(Value, newValue) && !ValueChanged.HasDelegate)
        {
            await JSRuntime.InvokeAsync<string>("Radzen.setInputValue", _input, Value);
            return;
        }

        if (newValueAsDecimal > Max)
        {
            newValueAsDecimal = Max.Value;
        }

        if (newValueAsDecimal < Min)
        {
            newValueAsDecimal = Min.Value;
        }

        newValueAsDecimal /= (decimal)Multiplier;

        Value = (Value is string)
            ? (TValue)(object)newValueAsDecimal.DecimalToString(cultureString)
            : (TValue)ConvertType.ChangeType(newValueAsDecimal, typeof(TValue));        

        if (!ValueChanged.HasDelegate || ForceUpdateHtml)
        {
            await JSRuntime.InvokeAsync<string>("Radzen.setInputValue", _input, Value);
        }

        await ValueChanged.InvokeAsync(Value);

        if (FieldIdentifier.FieldName != null || FieldIdentifier.Model != null)
        {
            EditContext?.NotifyFieldChanged(FieldIdentifier);
        }

        await Change.InvokeAsync(Value);
    }

    protected override string GetComponentCssClass()
    {
        var fieldCssClass = EditContext?.FieldCssClass(FieldIdentifier);

        return $"rz-spinner {(Disabled ? " rz-state-disabled" : "")} {fieldCssClass}";
    }

    async Task UpdateValueWithStep(bool stepUp)
    {
        if (Disabled || ReadOnly)
        {
            return;
        }

        var step = string.IsNullOrEmpty(Step) || Step == "any" ? 1 : double.Parse(Step.Replace(",", "."), CultureInfo.InvariantCulture);

        var valueToUpdate = Value != null ? Convert.ChangeType(Value, typeof(decimal)) : (decimal) Convert.ChangeType(default(decimal), typeof(decimal));

        var newValue = (decimal) Convert.ChangeType(valueToUpdate, typeof(decimal)) + (decimal) Convert.ChangeType(stepUp ? step : -step, typeof(decimal));

        if (newValue > Max || newValue < Min || Equals(Value, newValue))
        {
            return;
        }

        Value = (TValue) ConvertType.ChangeType(newValue, typeof(TValue));

        await ValueChanged.InvokeAsync(Value);
        EditContext?.NotifyFieldChanged(FieldIdentifier);
        await Change.InvokeAsync(Value);

        StateHasChanged();
    }

    private string GetClass()
    {
        var styling = HideStyling ? "borderless-input " : "form-control ";

        if (Attributes?.Any() != true)
            return styling;

        styling += Attributes.TryGetValue("class", out var @class) ? @class.ToString() : string.Empty;
        return styling;
    }

    private string GetNumberFormat()
    {
        if (ForceInvariantCulture)
            return "en-US";

        return GlobalDataService.Currency;
    }
}
