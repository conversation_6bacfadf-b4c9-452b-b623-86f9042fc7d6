@using Task = System.Threading.Tasks.Task
@inject IGlobalDataService GlobalDataService
@inject IAMproverUserManager UserManager
@inject ILookupManager LookupManager

@code {
    protected override void OnInitialized()
    {
        GlobalDataService.Language = LookupManager.GetLanguage();
        GlobalDataService.Currency = LookupManager.GetCurrency();

        var task = Task.Run(async () => await UserManager.GetUserRole());
        GlobalDataService.UserRole = task.Result;
    }
}