@inherits AMproverBaseComponent<TValue>
@typeparam TValue

<div class="form-group">

    @if (!string.IsNullOrEmpty(Label))
    {
        <label>@Label:</label>
    }

    <RadzenCheckBox @bind-Value=@Value
                    Disabled=IsDisabled()
                    TValue=TValue
                    Change=@Change
                    Name=@Name
                    class=@("form-control " + GetClass()) />

</div>

@code {

    [Parameter] public EventCallback<TValue> Change { get; set; }

}
