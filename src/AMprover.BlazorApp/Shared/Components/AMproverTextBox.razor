@inherits AMproverTextComponent

<div class="form-group">
    @if (!string.IsNullOrEmpty(Label))
    {
        <div class="neg-margin-small">
            <label>@Label:</label>
        </div>
    }
    <div class="neg-margin-small">
        <RadzenTextBox @bind-Value=@Value
                       Disabled=IsDisabled()
                       Change=@Change
                       MaxLength=@MaxLength
                       Name=@Name
                       class=@("form-control " + GetClass())
                       ReadOnly=@ReadOnly />

        @if (Required) 
        {
            <RadzenRequiredValidator Component=@Name DefaultValue=@DefaultValue />
        }
    </div>
</div>
