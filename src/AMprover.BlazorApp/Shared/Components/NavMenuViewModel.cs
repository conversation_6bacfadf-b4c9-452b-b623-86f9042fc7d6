using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Entities.AM;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using AMprover.BusinessLogic.Constants;

namespace AMprover.BlazorApp.Shared.Components;

public class NavMenuViewModel : BaseViewModel
{
    private readonly ILookupManager _lookupManager;
    private readonly IPageNavigationManager _pageNavigationManager;
    private readonly NavigationManager _navigationManager;
    private readonly IRiskAnalysisManager _riskAnalysisManager;

    public NavMenuViewModel(ILoggerFactory loggerFactory, IPageNavigationManager pageNavigationManager, ILookupManager lookupManager,
        NavigationManager navigationManager, IRiskAnalysisManager riskAnalysisManager) : base(
        loggerFactory)
    {
        _lookupManager = lookupManager;
        _pageNavigationManager = pageNavigationManager;
        _navigationManager = navigationManager;
        _riskAnalysisManager = riskAnalysisManager;
    }

    public List<LookupSettingModel> LookupSettings { get; set; }
    public LookupSettingModel EnableColumnsForTypes { get; set; }
    public bool HasSelectedRiskAssessment { get; set; }
    public bool SapaEnabled { get; set; }

    public override void OnInitialized()
    {
        LookupSettings = _lookupManager.GetLookupSettings();
        HasSelectedRiskAssessment = _pageNavigationManager.GetPageQueryStrings().Any(x =>
            x.Path.Contains("risks")
            && x.Path.Contains("value-risk-analysis"));

        EnableColumnsForTypes = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.EnableColumnsForTypes, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.EnableColumnsForTypes };
        SapaEnabled = EnableColumnsForTypes.IntValue == 1;
    }

    public void Navigate(string page)
    {
        var navigationItem = _pageNavigationManager.GetPageUrl(page);
        
        //If contains invalid risk analysis link
        if(navigationItem.Contains("/value-risk-analysis/0/risks/0"))
        {
            _navigationManager.NavigateTo("/value-risk-organizer");
        }

        //If contains a removed risk analysis link. Doing this here instead of on initialized because we dont want to
        //try and resolve risk item every page load
        if (navigationItem.Contains("risks") && navigationItem.Contains("value-risk-analysis"))
        {
            //split string to get risk id
            var urlParts = navigationItem.Split("/");
            var riskId = urlParts.LastOrDefault();

            //If risk id is available in string and is not 0 check if exists
            if (riskId != null)
            {
                if (int.TryParse(riskId, out var riskItemId))
                {
                    if (_riskAnalysisManager.FindRiskExists(riskItemId))
                    {
                        _navigationManager.NavigateTo(navigationItem);
                    }
                }
            }
        }

        //If above validations are false just continue with navigation
        _navigationManager.NavigateTo(navigationItem);
    }
}
