@using AMprover.BlazorApp.Helpers
@inject DropdownHelper DropdownHelper;

@if (Open)
{
    <div class="am-dropdown-option-container" style=@GetStyle() @onmouseover=@(() => Hovered = true) @onmouseout=@(() => Hovered = false) @onblur=Blur>

        @* When the dropdown opens we focus on the searchbar. When Filtering is disabled, we focus on this invisble element to enable onBlur *@
        <input type="button" class="am-dropdown-focus" @onblur=Blur @ref=focusElement />

        @if (AllowFiltering)
        {
            <div class="am-dropdown-filter-container">
                <input class="am-dropdown-filter-input" placeholder="search..." type="text" @oninput=UpdateSearch @onblur=Blur @ref=searchElement />
            </div>
        }

        <div class="am-dropdown-scroll-container">
            @foreach (var opt in FilteredData ?? Data)
            {
                <div class=@GetItemClass(opt) @onclick=@(x => ClickOption(opt))>
                    @opt
                </div>
            }
        </div>
    </div>
}

@code {
    public List<string> Data { get; set; }

    public List<string> FilteredData { get; set; }

    public int Value { get; set; }

    public EventCallback<int> ValueChanged { get; set; }

    public bool Open = false;

    public bool AllowFiltering { get; set; } = false;

    public string FilterQuery { get; set; }

    public ElementReference searchElement { get; set; }

    public ElementReference focusElement { get; set; }

    public bool Hovered { get; set; }

    public AMDropdownBase DropdownReference { get; set; }

    public void UpdateSearch(ChangeEventArgs e)
    {
        FilterQuery = e.Value.ToString();
        UpdateFilterQuery();
        StateHasChanged();
    }

    public DOMRect? Rect { get; set; }

    public void Blur()
    {
        if (Hovered == false)
        {
            Open = false;
            DropdownReference?.CloseDropdownCallback();
            Run();
        }
    }

    public void UpdateFilterQuery()
    {
        if (!AllowFiltering || string.IsNullOrWhiteSpace(FilterQuery))
        {
            FilteredData = Data;
        }
        else
        {
            FilteredData = Data.Where(x => x.Contains(FilterQuery, StringComparison.OrdinalIgnoreCase)).ToList();
        }
    }

    protected override void OnInitialized()
    {
        DropdownHelper.DropdownOptions = this;
        base.OnInitialized();
    }

    public async Task ClickOption(string val)
    {
        Open = false;

        // Force Dropdown options to close, even in ValueChanged() might be a long running operation.
        // StateHasChanged() does not work here.
        await Task.Delay(1);

        Value = Data.IndexOf(val);
        await ValueChanged.InvokeAsync(Value);
    }

    public void Run()
    {
        FilterQuery = string.Empty;
        UpdateFilterQuery();
        StateHasChanged();

        if (searchElement.Context != null)
        {
            searchElement.FocusAsync();
        }
        else if (focusElement.Context != null)
        {
            focusElement.FocusAsync();
        }
    }

    public string GetStyle()
    {
        var left = (int)(Rect?.left ?? 0);
        var top = (int)((Rect?.top ?? 0) + (Rect?.height ?? 0) + (Rect?.scrollY ?? 0));
        var width = (int)(Rect?.width ?? 0);

        return $"left:{left}px;top:{top}px;width:{width}px";
    }

    public string GetItemClass(string val)
    {
        if (Data.IndexOf(val) == Value)
            return "am-dropdown-item am-selected";

        return "am-dropdown-item";
    }
}
