@using Microsoft.Extensions.Logging
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject ILoggerFactory LoggerFactory

@code {
    protected override async Task OnInitializedAsync()
    {
        Uri requestUri = new Uri(NavigationManager.Uri, UriKind.RelativeOrAbsolute);
        string redirectUrl = requestUri.PathAndQuery;


        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity.IsAuthenticated)
        {
            //Probably the user is authenticated but not fully authorized to view the requested page. That is worth mentioning.
            var logger = LoggerFactory.CreateLogger(GetType().Name);
            logger.LogWarning($"Authenticated user {authState.User.Identity.Name} is not (enough) authorized to view {requestUri.PathAndQuery}. Redirecting to login page.");
        }

        bool hasReturnUrl = requestUri.AbsolutePath != "/";
        string returnUrlQuery = hasReturnUrl ? $"?returnUrl={Uri.EscapeDataString(redirectUrl)}" : null;
        NavigationManager.NavigateTo($"/Identity/Account/Login{returnUrlQuery}", true);
    }
}