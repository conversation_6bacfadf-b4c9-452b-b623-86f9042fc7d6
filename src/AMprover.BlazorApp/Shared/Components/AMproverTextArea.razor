@inherits AMproverTextComponent

<div class="form-group">

    @if (!string.IsNullOrEmpty(Label))
    {
        <div class="neg-margin-small">
            <label>@Label:</label>
        </div>
    }
    <div class="neg-margin-small">
        <RadzenTextArea @bind-Value=@Value
                        Disabled=IsDisabled()
                        Change=@Change
                        MaxLength=@MaxLength
                        Name=@Name
                        class=@("form-control " + GetClass())
                        Cols=@Cols
                        Rows=@Rows 
                        ReadOnly=@ReadOnly />

        @if (Required)
        {
            <RadzenRequiredValidator Component=@Name DefaultValue=@DefaultValue />
        }
    </div>
</div>

@code {
    [Parameter] public int Rows { get; set; } = 2;

    [Parameter] public int Cols { get; set; } = 30;
}
