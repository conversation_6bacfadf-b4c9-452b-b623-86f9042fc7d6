@implements IDisposable
@inject IJSRuntime _jsRuntime

@foreach (var tooltip in _tooltips)
{
    <div id="@UniqueId" class="rz-tooltip @tooltip.Options.CssClass" style="display: none; top: 0; left: 0; z-index: 1001; position: absolute;">
        <div class="rz-tooltip-content @GetCssClass(tooltip)" style="@tooltip.Options.Style">
            @if (!string.IsNullOrEmpty(tooltip.Options.Text))
            {
                <div>
                    @((MarkupString)tooltip.Options.Text)
                </div>
            }
            else if (tooltip.Options.ChildContent != null)
            {
                @tooltip.Options.ChildContent(Service)
            }
        </div>
    </div>
}

@code {
    string GetCssClass(Tooltip tooltip)
    {
        return $"rz-{Enum.GetName(typeof(TooltipPosition), tooltip.Options.Position)?.ToLower()}-tooltip-content";
    }

    private string UniqueId { get; set; }

    [Inject] private TooltipService Service { get; set; }

    List<Tooltip> _tooltips = new List<Tooltip>();

    private async Task Open(ElementReference element, Type type, TooltipOptions options)
    {
        _tooltips.Clear();
        _tooltips.Add(new Tooltip() { Type = type, Options = options, Element = element });

        await InvokeAsync(StateHasChanged);
    }

    bool IsJsRuntimeAvailable { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        IsJsRuntimeAvailable = true;

        var tooltip = _tooltips.LastOrDefault();

        if (tooltip != null)
        {
            await _jsRuntime.InvokeVoidAsync("Radzen.openTooltip",
                tooltip.Element,
                UniqueId,
                tooltip.Options.Duration,
                Enum.GetName(typeof(TooltipPosition), tooltip.Options.Position)?.ToLower());
        }
    }

    private async Task Close()
    {
        var lastTooltip = _tooltips.LastOrDefault();
        if (lastTooltip != null)
        {
            _tooltips.Remove(lastTooltip);
            await _jsRuntime.InvokeVoidAsync("Radzen.closePopup", UniqueId);
        }

        await InvokeAsync(() => { StateHasChanged(); });
    }

    public void Dispose()
    {
        if (IsJsRuntimeAvailable)
        {
            _jsRuntime.InvokeVoidAsync("Radzen.destroyPopup", UniqueId);
        }

        Service.OnOpen -= OnOpen;
        Service.OnClose -= OnClose;
        Service.OnNavigate -= OnNavigate;
    }

    protected override void OnInitialized()
    {
        UniqueId = Convert.ToBase64String(Guid.NewGuid().ToByteArray()).Replace("/", "-").Replace("+", "-").Substring(0, 10);

        Service.OnOpen += OnOpen;
        Service.OnClose += OnClose;
        Service.OnNavigate += OnNavigate;
    }

    void OnOpen(ElementReference element, Type type, TooltipOptions options)
    {
        Open(element, type, options).ConfigureAwait(false);
    }

    void OnClose()
    {
        Close().ConfigureAwait(false);
    }

    void OnNavigate()
    {
        _jsRuntime.InvokeVoidAsync("Radzen.closePopup", UniqueId);
    }
}