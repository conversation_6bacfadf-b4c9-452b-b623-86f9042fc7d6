@typeparam TValue
@inject IGlobalDataService GlobalDataService;

@code {
    protected override async Task OnInitializedAsync()
    {
        CanEdit = GlobalDataService.CanEdit;

        await base.OnInitializedAsync();
    }

    private TValue _value;

    [Parameter]
    public virtual TValue Value
    {
        get => _value;
        set
        {
            if (EqualityComparer<TValue>.Default.Equals(value, _value))
                return;

            _value = value;
            ValueChanged.InvokeAsync(_value);
            StateHasChanged();
        }
    }

    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public string Label { get; set; }

    public string Name => new((Label ?? string.Empty).Where(char.IsLetter).ToArray());

    [Parameter]
    public bool Required { get; set; }

    [Parameter]
    public object DefaultValue { get; set; }

    [Parameter(CaptureUnmatchedValues = true)]
    public IReadOnlyDictionary<string, object> Attributes { get; set; }

    protected bool CanEdit { get; set; }

    protected string GetClass()
    {
        if (Attributes?.Any() != true)
            return string.Empty;

        return Attributes.TryGetValue("class", out var @class) ? @class.ToString() : string.Empty;
    }

    protected bool IsDisabled() => Disabled || !CanEdit;
}
