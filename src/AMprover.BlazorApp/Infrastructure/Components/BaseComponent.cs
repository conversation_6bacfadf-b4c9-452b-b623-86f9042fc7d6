using System;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading.Tasks;
using AMprover.BlazorApp.Infrastructure.Bindings;
using AMprover.BlazorApp.Infrastructure.Collections;
using AMprover.BlazorApp.Infrastructure.Events;
using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;

namespace AMprover.BlazorApp.Infrastructure.Components;

public abstract class BaseComponent : ComponentBase, IDisposable, IAsyncDisposable
{
    private ConcurrentHashSet<IBinding> _bindings = new();
    private IBindingFactory _bindingFactory = null!;
    private IEventManager _eventManager = null!;
    private IEventManagerFactory _eventManagerFactory = null!;

    protected BaseComponent(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
        InitializeDependencies();
    }

    protected BaseComponent()
    { }

    [Inject] protected IServiceProvider ServiceProvider { get; set; } = null!;

    private void InitializeDependencies()
    {
        _eventManagerFactory ??= ServiceProvider.GetRequiredService<IEventManagerFactory>();
        _bindingFactory ??= ServiceProvider.GetRequiredService<IBindingFactory>();
        _eventManager ??= _eventManagerFactory.Create();
    }

    protected internal TValue Bind<TViewModel, TValue>(TViewModel viewModel,
        Expression<Func<TViewModel, TValue>> property)
        where TViewModel : BaseViewModel
    {
        return AddBinding(viewModel, property);
    }

    public virtual TValue AddBinding<TViewModel, TValue>(TViewModel viewModel,
        Expression<Func<TViewModel, TValue>> propertyExpression) where TViewModel : BaseViewModel
    {
        var propertyInfo = ValidateAndResolveBindingContext(viewModel, propertyExpression);

        var binding = _bindingFactory.Create(viewModel, propertyInfo, _eventManagerFactory.Create());
        if (_bindings.Contains(binding)) return (TValue)binding.GetValue();

        _eventManager.AddEventListener<IBinding, EventArgs>(binding, nameof(Binding.BindingValueChanged),
            BindingOnBindingValueChanged);
        binding.Initialize();

        _bindings.Add(binding);

        return (TValue)binding.GetValue();
    }

    protected override void OnInitialized()
    {
        InitializeDependencies();
    }

    internal virtual void BindingOnBindingValueChanged(object sender, EventArgs e)
    {
        InvokeAsync(StateHasChanged);
    }

    [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S2302:\"nameof\" should be used", Justification = "<Pending>")]
    protected static PropertyInfo ValidateAndResolveBindingContext<TViewModel, TValue>(TViewModel viewModel,
        Expression<Func<TViewModel, TValue>> property)
    {
        if (viewModel is null)
            throw new BindingException("ViewModelType is null");

        if (property is null)
            throw new BindingException("Property expression is null");

        if (property.Body is not MemberExpression m)
            throw new BindingException("Binding member needs to be a property");

        if (m.Member is not PropertyInfo p)
            throw new BindingException("Binding member needs to be a property");

        if (typeof(TViewModel).GetProperty(p.Name) is null)
            throw new BindingException($"Cannot find property {p.Name} in type {viewModel.GetType().FullName}");

        return p;
    }

    #region IDisposable Support

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
            if (_bindings is not null)
            {
                DisposeBindings();

                _bindings = null!;
            }
    }

    public async ValueTask DisposeAsync()
    {
        await DisposeAsyncCore();

        Dispose(false);
        GC.SuppressFinalize(this);
    }

    protected virtual ValueTask DisposeAsyncCore()
    {
        if (_bindings is null) return default;
        DisposeBindings();
        _bindings = null!;

        return default;
    }

    private void DisposeBindings()
    {
        foreach (var binding in _bindings)
        {
            _eventManager.RemoveEventListener(binding);
            binding.Dispose();
        }
    }

    ~BaseComponent()
    {
        Dispose(false);
    }

    #endregion
}