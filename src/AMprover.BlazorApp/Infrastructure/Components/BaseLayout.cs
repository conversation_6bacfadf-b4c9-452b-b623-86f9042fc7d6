using System.Threading.Tasks;
using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;

namespace AMprover.BlazorApp.Infrastructure.Components;

public abstract class BaseLayout : LayoutComponentBase
{
    [Inject] private IGlobalDataService _globalDataService { get; set; }

    [Inject] private IAMproverUserManager _userManager { get; set; }

    public BaseLayout() { }

    protected override async Task OnInitializedAsync()
    {
        // _globalDataService.UserRole = await _userManager.GetUserRole();
    }

    /// <summary>
    /// Hamburger Menu Logic
    /// </summary>
    public string SideMenuClass { get; set; }
    public bool SideMenuActive { get; set; }
    public void ActivateMenu()
    {
        SideMenuActive = !SideMenuActive;
        SideMenuClass = SideMenuActive ? "active" : string.Empty;
    }
}
