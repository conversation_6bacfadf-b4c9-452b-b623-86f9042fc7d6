using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Security.Claims;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Configuration;
using AMprover.BlazorApp.Infrastructure.Bindings;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.Data.Entities.Identity;
using AMprover.Data.Infrastructure;
using AMprover.Data.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Radzen;

namespace AMprover.BlazorApp.Infrastructure.ViewModels.Base;

public abstract class BaseViewModel : INotifyPropertyChanged, IDisposable
{
    private readonly IConfiguration _configuration;
    private readonly TooltipService _tooltipService;
    protected readonly ILookupManager _lookupManager;
    private readonly IJSRuntime _jsRuntime;
    private readonly NavigationManager _navigationManager;
    protected ILogger _logger;
    protected DialogService _dialogService;
    private readonly IAssetManagementPortfolioResolver _assetManagementPortfolioResolver;
    private readonly AuthenticationStateProvider _authenticationStateProvider;
    private readonly UserManager<UserAccount> _userManager;

    private readonly Dictionary<string, List<Func<object, Task>>> _subscriptions = new();

    public event PropertyChangedEventHandler PropertyChanged;

    public bool Loading { get; set; }

    public List<string> Tools { get; } = new() {"Search"};

    protected Dictionary<ObjectLevel, string> ObjectLevels { get; set; } = new();

    public string Language => _lookupManager.GetLanguage();
    public string Currency => _lookupManager.GetCurrency();

    private string ElementWithMouse { get; set; }

    // Keep track which Scenario is selected when changing pages
    public int SelectedScenario { get; set; }

    protected BaseViewModel()
    {
        // TODO Dennis => Remove this constructor when figured out how to pass LoggerFactory through : TreeComponentViewModel<T> : BaseViewModel
    }

    protected BaseViewModel(ILoggerFactory loggerFactory)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _lookupManager = lookupManager;
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager,
        AuthenticationStateProvider authenticationStateProvider, UserManager<UserAccount> userManager)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _lookupManager = lookupManager;
        _authenticationStateProvider = authenticationStateProvider;
        _userManager = userManager;
    }
    
    protected BaseViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager,
        AuthenticationStateProvider authenticationStateProvider, UserManager<UserAccount> userManager, IJSRuntime jsRuntime)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _lookupManager = lookupManager;
        _authenticationStateProvider = authenticationStateProvider;
        _userManager = userManager;
        _jsRuntime = jsRuntime;
    }
    
    protected BaseViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager,
        AuthenticationStateProvider authenticationStateProvider, UserManager<UserAccount> userManager, DialogService dialogService)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _lookupManager = lookupManager;
        _authenticationStateProvider = authenticationStateProvider;
        _userManager = userManager;
        _dialogService = dialogService;
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager, IConfiguration configuration,
        IAssetManagementPortfolioResolver assetManagementPortfolioResolver)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _lookupManager = lookupManager;
        _configuration = configuration;
        _assetManagementPortfolioResolver = assetManagementPortfolioResolver;
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, DialogService dialogService)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _dialogService = dialogService;
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager, DialogService dialogService)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _lookupManager = lookupManager;
        _dialogService = dialogService;
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, TooltipService tooltipService, ILookupManager lookupManager)
    {
        _tooltipService = tooltipService;
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _lookupManager = lookupManager;
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, TooltipService tooltipService, IJSRuntime jsRuntime)
    {
        _tooltipService = tooltipService;
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _jsRuntime = jsRuntime;
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, ILookupManager lookupManager, TooltipService tooltipService,
        IJSRuntime jsRuntime)
    {
        _tooltipService = tooltipService;
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _jsRuntime = jsRuntime;
        _lookupManager = lookupManager;
    }


    protected BaseViewModel(ILoggerFactory loggerFactory, IConfiguration configuration, ILookupManager lookupManager,
        IJSRuntime jsRuntime, NavigationManager navigationManager)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _configuration = configuration;
        _lookupManager = lookupManager;
        _jsRuntime = jsRuntime;
        _navigationManager = navigationManager;
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, TooltipService tooltipService, ILookupManager lookupManager,
        IJSRuntime jsRuntime, NavigationManager navigationManager)
    {
        _tooltipService = tooltipService;
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _lookupManager = lookupManager;
        _jsRuntime = jsRuntime;
        _navigationManager = navigationManager;
    }

    protected BaseViewModel(ILoggerFactory loggerFactory, IConfiguration configuration, TooltipService tooltipService,
        ILookupManager lookupManager,
        IJSRuntime jsRuntime, NavigationManager navigationManager)
    {
        _configuration = configuration;
        _tooltipService = tooltipService;
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _lookupManager = lookupManager;
        _jsRuntime = jsRuntime;
        _navigationManager = navigationManager;
    }

    protected void ShowDbErrorMessage(DbOperationResult dbResult)
    {
        _dialogService.Open<NotificationDialog>("Error",
            new Dictionary<string, object>
            {
                {nameof(NotificationDialog.Texts), dbResult.ErrorMessages}
            });
    }

    protected void ShowErrorMessage(string errorMessage)
    {
        _dialogService.Open<NotificationDialog>("Error",
            new Dictionary<string, object>
            {
                {nameof(NotificationDialog.Texts), new List<string> {errorMessage}}
            });
    }

    protected void ShowDialog(string title, bool reloadOnClose, params string[] texts)
    {
        _dialogService.Open<NotificationDialog>(title,
            new Dictionary<string, object>
            {
                {nameof(NotificationDialog.Texts), texts.ToList()},
                {nameof(NotificationDialog.ReloadPageOnClose), reloadOnClose}
            },
            new DialogOptions
            {
                ShowClose = false,
                Resizable = false,
                Draggable = true
            });
    }

    public void ShowAreYouSurePopup<T>(T item, string title, EventCallback<T> YesCallback)
    {
        _dialogService.Open<AreYouSureDialog<T>>(title,
            new Dictionary<string, object>
            {
                {
                    nameof(AreYouSureDialog<T>.Item),
                    item
                },
                {
                    nameof(AreYouSureDialog<T>.YesCallback),
                    EventCallback.Factory.Create(this, YesCallback)
                }
            });
    }

    public async Task<string> GetCurrentUserId()
    {
        var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
        return _userManager.GetUserId(authState.User);
    }

    public async Task<IEnumerable<string>> GetCurrentUserRoles()
    {
        var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        return user.Claims
            .Where(c => c.Type == ClaimTypes.Role)
            .Select(c => c.Value);
    }
    
    public async Task PrintPage()
    {
        await _jsRuntime.InvokeVoidAsync("window.print");
    }

    public async Task ShowTooltip(ElementReference elementReference, string content,
        TooltipPosition position = TooltipPosition.Top)
    {
        ElementWithMouse = elementReference.Id;
        await Task.Delay(2000);

        if (elementReference.Id == ElementWithMouse)
            _tooltipService.Open(elementReference, content, new TooltipOptions {Position = position, Duration = 6000});
    }

    public async Task ShowTooltipRiskMatrix(ElementReference elementReference, string content,
        TooltipPosition position = TooltipPosition.Top)
    {
        ElementWithMouse = elementReference.Id;

        if (content is {Length: > 35})
        {
            await Task.Delay(2000);

            if (elementReference.Id == ElementWithMouse)
                _tooltipService.Open(elementReference, content,
                    new TooltipOptions {Position = position, Duration = 6000});
        }
    }

    public async Task HideTooltip(ElementReference elementReference)
    {
        if (elementReference.Id == ElementWithMouse)
        {
            ElementWithMouse = string.Empty;
        }

        await Task.Delay(6000);

        if (string.IsNullOrEmpty(elementReference.Id))
        {
            _tooltipService.Close();
        }
    }

    protected async Task AddOrUpdateCookie(string redirectUrl)
    {
        var currentCookieValue =
            await _jsRuntime.InvokeAsync<string>("ReadCookie.ReadCookie",
                CookieRequestCultureProvider.DefaultCookieName);

        var language = Language;

        if (string.IsNullOrWhiteSpace(currentCookieValue) ||
            !currentCookieValue.Contains(language, StringComparison.InvariantCultureIgnoreCase))
        {
            await _jsRuntime.InvokeAsync<object>("WriteCookie.WriteCookie",
                CookieRequestCultureProvider.DefaultCookieName,
                CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(language)), DateTime.Now.AddMonths(1));

            _navigationManager.NavigateTo(redirectUrl, true);
        }
    }

    protected Dictionary<string, string> GetLanguages()
    {
        return _configuration.GetSection("Languages").GetChildren().OrderBy(x => x.Value)
            .ToDictionary(x => x.Key, x => x.Value);
    }

    protected Dictionary<string, string> GetCurrencies()
    {
        return _configuration.GetSection("Currencies").GetChildren().OrderBy(x => x.Value)
            .ToDictionary(x => x.Key, x => x.Value);
    }

    protected List<ReportSetting> GetReports()
    {
        var currentPortfolio = _assetManagementPortfolioResolver.GetCurrentPortfolio();

        return _configuration.GetSection("Reports").Get<List<ReportSetting>>()
            .Where(x => x.Database == currentPortfolio.Name)
            .ToList();
    }

    public bool FeatureTurnedOn(string featureName)
    {
        var feature = _configuration.GetSection("Features").GetChildren().FirstOrDefault(x => x.Key == featureName)?
            .Value;

        return bool.TryParse(feature, out var result) && result;
    }

    /// <summary>
    /// Get Dynamic name for objectLevels, used in many places, and therefore moved to BaseViewModel
    /// </summary>
    /// <param name="objectLevel"></param>
    /// <returns></returns>
    public string GetObjectLevel(ObjectLevel objectLevel)
    {
        if (ObjectLevels.TryGetValue(objectLevel, out var level))
            return level;

        _logger.LogError($"{nameof(GetObjectLevel)} was called but {nameof(ObjectLevels)} has not been filled yet");
        return objectLevel.ToString();
    }

    protected bool Set<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName!);
        if (!_subscriptions.ContainsKey(propertyName!)) return true;
        foreach (var action in _subscriptions[propertyName!]) action(value!);
        return true;
    }

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected void Subscribe<T>(Expression<Func<T>> expression, Action<T> action)
    {
        SubscribeAsync(expression, arg =>
        {
            action(arg);
            return Task.CompletedTask;
        });
    }

    private void SubscribeAsync<T>(Expression<Func<T>> property, Func<T, Task> func)
    {
        if (property is null) throw new BindingException("Property cannot be null");
        if (property.Body is not MemberExpression m)
            throw new BindingException("Subscription member must be a property");

        if (m.Member is not PropertyInfo propertyInfo)
            throw new BindingException("Subscription member must be a property");

        var propertyName = propertyInfo.Name;
        if (!_subscriptions.ContainsKey(propertyName))
            _subscriptions[propertyName] = new List<Func<object, Task>>();

        _subscriptions[propertyName].Add(async value => await func((T) value).ConfigureAwait(false));
    }

    #region IDisposable support

    ~BaseViewModel()
    {
        Dispose(false);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
    }

    #endregion

    #region Lifecycle Methods

    /// <summary>
    ///     Method invoked when the component is ready to start, having received its
    ///     initial parameters from its parent in the render tree.
    /// </summary>
    public virtual void OnInitialized()
    {
    }

    /// <summary>
    ///     Method invoked when the component is ready to start, having received its
    ///     initial parameters from its parent in the render tree.
    ///     Override this method if you will perform an asynchronous operation and
    ///     want the component to refresh when that operation is completed.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing any asynchronous operation.</returns>
    public virtual Task OnInitializedAsync()
    {
        return Task.CompletedTask;
    }

    /// <summary>
    ///     Method invoked when the component has received parameters from its parent in
    ///     the render tree, and the incoming values have been assigned to properties.
    /// </summary>
    public virtual void OnParametersSet()
    {
    }

    /// <summary>
    ///     Method invoked when the component has received parameters from its parent in
    ///     the render tree, and the incoming values have been assigned to properties.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing any asynchronous operation.</returns>
    public virtual Task OnParametersSetAsync()
    {
        return Task.CompletedTask;
    }

    /// <summary>
    ///     Notifies the component that its state has changed. When applicable, this will
    ///     cause the component to be re-rendered.
    /// </summary>
    protected void StateHasChanged()
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(null));
    }

    /// <summary>
    ///     Returns a flag to indicate whether the component should render.
    /// </summary>
    /// <returns></returns>
    public virtual bool ShouldRender()
    {
        return true;
    }

    /// <summary>
    ///     Method invoked after each time the component has been rendered.
    /// </summary>
    /// <param name="firstRender">
    ///     Set to <c>true</c> if this is the first time
    ///     <see cref="ComponentBase.OnAfterRender(bool)" /> has been invoked
    ///     on this component instance; otherwise <c>false</c>.
    /// </param>
    /// <remarks>
    ///     The <see cref="ComponentBase.OnAfterRender(bool)" /> and
    ///     <see cref="ComponentBase.OnAfterRenderAsync(bool)" /> lifecycle methods
    ///     are useful for performing interop, or interacting with values received from <c>@ref</c>.
    ///     Use the <paramref name="firstRender" /> parameter to ensure that initialization work is only performed
    ///     once.
    /// </remarks>
    public virtual void OnAfterRender(bool firstRender)
    {
    }

    /// <summary>
    ///     Method invoked after each time the component has been rendered. Note that the component does
    ///     not automatically re-render after the completion of any returned <see cref="Task" />,
    ///     because
    ///     that would cause an infinite render loop.
    /// </summary>
    /// <param name="firstRender">
    ///     Set to <c>true</c> if this is the first time
    ///     <see cref="ComponentBase.OnAfterRender(bool)" /> has been invoked
    ///     on this component instance; otherwise <c>false</c>.
    /// </param>
    /// <returns>A <see cref="Task" /> representing any asynchronous operation.</returns>
    /// <remarks>
    ///     The <see cref="ComponentBase.OnAfterRender(bool)" /> and
    ///     <see cref="ComponentBase.OnAfterRenderAsync(bool)" /> lifecycle methods
    ///     are useful for performing interop, or interacting with values received from <c>@ref</c>.
    ///     Use the <paramref name="firstRender" /> parameter to ensure that initialization work is only performed
    ///     once.
    /// </remarks>
    public virtual Task OnAfterRenderAsync(bool firstRender)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    ///     Sets parameters supplied by the component's parent in the render tree.
    /// </summary>
    /// <param name="parameters">The parameters.</param>
    /// <returns>
    ///     A <see cref="Task" /> that completes when the component has finished updating and
    ///     rendering itself.
    /// </returns>
    /// <remarks>
    ///     <para>
    ///         The
    ///         <see
    ///             cref="ComponentBase.SetParametersAsync(ParameterView)" />
    ///         method should be passed the entire set of parameter values each
    ///         time
    ///         <see
    ///             cref="ComponentBase.SetParametersAsync(ParameterView)" />
    ///         is called. It not required that the caller supply a parameter
    ///         value for all parameters that are logically understood by the component.
    ///     </para>
    ///     <para>
    ///         The default implementation of
    ///         <see
    ///             cref="ComponentBase.SetParametersAsync(ParameterView)" />
    ///         will set the value of each property
    ///         decorated with <see cref="ParameterAttribute" /> or
    ///         <see cref="CascadingParameterAttribute" /> that has
    ///         a corresponding value in the <see cref="ParameterView" />. Parameters that do
    ///         not have a corresponding value
    ///         will be unchanged.
    ///     </para>
    /// </remarks>
    public virtual Task SetParametersAsync(ParameterView parameters)
    {
        return Task.CompletedTask;
    }

    #endregion
}