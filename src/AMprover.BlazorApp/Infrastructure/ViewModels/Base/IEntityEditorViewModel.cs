namespace AMprover.BlazorApp.Infrastructure.ViewModels.Base;

public interface IEntityEditorViewModel
{
    EntityEditorMode EditorMode { get; }
}

/// <summary>
/// The entity operation the editor is supporting in
/// </summary>
public enum EntityEditorMode
{
    /// <summary>
    /// Editing existing information or update (adding) additional informatie to an existing entity
    /// </summary>
    Update,

    /// <summary>
    /// Create a new entity
    /// </summary>
    Create
}