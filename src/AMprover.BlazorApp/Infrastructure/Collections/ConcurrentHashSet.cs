using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace AMprover.BlazorApp.Infrastructure.Collections;

/// <summary>
/// Represents a thread-safe hash-based set.
/// </summary>
/// <remarks>
/// This class provides a thread-safe implementation of a hash-based set, which is not available
/// in the standard .NET collections. It implements the standard <see cref="ICollection{T}"/> interface
/// for consistency with other collection types.
/// 
/// Internally, it uses a <see cref="ConcurrentDictionary{TKey, TValue}"/> for thread safety,
/// but presents a clean set-like API to consumers.
/// </remarks>
/// <typeparam name="T">The type of elements in the set.</typeparam>
public class ConcurrentHashSet<T> : ICollection<T>
{
    private readonly ConcurrentDictionary<T, byte> _dictionary = new();
    private static readonly byte DummyValue = 0;

    /// <summary>
    /// Gets the number of elements contained in the set.
    /// </summary>
    public int Count => _dictionary.Count;

    /// <summary>
    /// Gets a value indicating whether the set is read-only.
    /// </summary>
    public bool IsReadOnly => false;

    /// <summary>
    /// Adds an element to the set.
    /// </summary>
    /// <param name="item">The element to add to the set.</param>
    /// <returns>true if the element is added to the set; false if the element is already in the set.</returns>
    public bool Add(T item)
    {
        return _dictionary.TryAdd(item, DummyValue);
    }

    /// <summary>
    /// Adds an element to the set.
    /// </summary>
    /// <param name="item">The element to add to the set.</param>
    void ICollection<T>.Add(T item)
    {
        Add(item);
    }

    /// <summary>
    /// Removes all elements from the set.
    /// </summary>
    public void Clear()
    {
        _dictionary.Clear();
    }

    /// <summary>
    /// Determines whether the set contains a specific element.
    /// </summary>
    /// <param name="item">The element to locate in the set.</param>
    /// <returns>true if the set contains the specified element; otherwise, false.</returns>
    public bool Contains(T item)
    {
        return _dictionary.ContainsKey(item);
    }

    /// <summary>
    /// Copies the elements of the set to an array, starting at a particular array index.
    /// </summary>
    /// <param name="array">The one-dimensional array that is the destination of the elements copied from the set.</param>
    /// <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
    public void CopyTo(T[] array, int arrayIndex)
    {
        if (array == null)
            throw new ArgumentNullException(nameof(array));

        if (arrayIndex < 0 || arrayIndex > array.Length)
            throw new ArgumentOutOfRangeException(nameof(arrayIndex));

        if (array.Length - arrayIndex < Count)
            throw new ArgumentException("The number of elements in the source collection is greater than the available space from arrayIndex to the end of the destination array.");

        var i = arrayIndex;
        foreach (var key in _dictionary.Keys)
        {
            array[i++] = key;
        }
    }

    /// <summary>
    /// Returns an enumerator that iterates through the set.
    /// </summary>
    /// <returns>An enumerator that can be used to iterate through the set.</returns>
    public IEnumerator<T> GetEnumerator()
    {
        return _dictionary.Keys.GetEnumerator();
    }

    /// <summary>
    /// Returns an enumerator that iterates through the set.
    /// </summary>
    /// <returns>An enumerator that can be used to iterate through the set.</returns>
    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    /// <summary>
    /// Removes the specified element from the set.
    /// </summary>
    /// <param name="item">The element to remove.</param>
    /// <returns>true if the element is successfully removed; otherwise, false.</returns>
    public bool Remove(T item)
    {
        return _dictionary.TryRemove(item, out _);
    }
}