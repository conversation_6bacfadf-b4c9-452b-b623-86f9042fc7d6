using System;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Reflection;

namespace AMprover.BlazorApp.Infrastructure.Events;

internal interface IEventListener
{
    bool IsAlive { get; }
    object Source { get; }
    Delegate Handler { get; }
    void StopListening();
}

internal abstract class EventListenerBase<T, TArgs> : IEventListener
    where T : class
    where TArgs : EventArgs
{
    private readonly WeakReference<Action<T, TArgs>> _handler;
    private readonly WeakReference<T> _source;

    protected EventListenerBase(T source, Action<T, TArgs> handler)
    {
        _source = new WeakReference<T>(source ?? throw new ArgumentNullException(nameof(source)));
        _handler = new WeakReference<Action<T, TArgs>>(handler ?? throw new ArgumentNullException(nameof(handler)));
    }

    public bool IsAlive => _handler.TryGetTarget(out _) && _source.TryGetTarget(out _);

    public object Source => _source.TryGetTarget(out var source) ? source : null;

    public Delegate Handler => _handler.TryGetTarget(out var handler) ? handler : null;

    public void StopListening()
    {
        if (_source.TryGetTarget(out var source)) StopListening(source);
    }

    protected void HandleEvent(object sender, TArgs e)
    {
        if (_handler.TryGetTarget(out var handler))
            handler((T)sender, e);
        else
            StopListening();
    }

    protected abstract void StopListening(T source);
}

internal class TypedEventListener<T, TArgs> : EventListenerBase<T, TArgs>
    where T : class
    where TArgs : EventArgs
{
    private readonly Action<T, EventHandler<TArgs>> _unregister;

    public TypedEventListener(T source, Action<T, EventHandler<TArgs>> register,
        Action<T, EventHandler<TArgs>> unregister, Action<T, TArgs> handler)
        : base(source, handler)
    {
        if (register == null) throw new ArgumentNullException(nameof(register));
        _unregister = unregister ?? throw new ArgumentNullException(nameof(unregister));
        register(source, HandleEvent!);
    }

    protected override void StopListening(T source)
    {
        _unregister(source, HandleEvent!);
    }
}

internal class PropertyChangedEventListener<T> : EventListenerBase<T, PropertyChangedEventArgs>
    where T : class, INotifyPropertyChanged
{
    public PropertyChangedEventListener(T source, Action<T, PropertyChangedEventArgs> handler)
        : base(source, handler)
    {
        source.PropertyChanged += HandleEvent!;
    }

    protected override void StopListening(T source)
    {
        source.PropertyChanged -= HandleEvent!;
    }
}

internal class CollectionChangedEventListener<T> : EventListenerBase<T, NotifyCollectionChangedEventArgs>
    where T : class, INotifyCollectionChanged
{
    public CollectionChangedEventListener(T source, Action<T, NotifyCollectionChangedEventArgs> handler)
        : base(source, handler)
    {
        source.CollectionChanged += HandleEvent!;
    }

    protected override void StopListening(T source)
    {
        source.CollectionChanged -= HandleEvent!;
    }
}

internal class EventListener<T, TArgs> : EventListenerBase<T, TArgs>
    where T : class
    where TArgs : EventArgs
{
    private readonly EventInfo _eventInfo;

    public EventListener(T source, string eventName, Action<T, TArgs> handler)
        : base(source, handler)
    {
        _eventInfo = source.GetType().GetEvent(eventName) ??
                     throw new ArgumentException("Unknown Event Name", nameof(eventName));
        _eventInfo.AddEventHandler(source,
            _eventInfo.EventHandlerType == typeof(EventHandler<TArgs>)
                ? new EventHandler<TArgs>(HandleEvent!)
                : Delegate.CreateDelegate(_eventInfo.EventHandlerType!, this, nameof(HandleEvent)));
    }

    protected override void StopListening(T source)
    {
        _eventInfo.RemoveEventHandler(source,
            _eventInfo.EventHandlerType == typeof(EventHandler<TArgs>)
                ? new EventHandler<TArgs>(HandleEvent!)
                : Delegate.CreateDelegate(_eventInfo.EventHandlerType!, this, nameof(HandleEvent)));
    }
}