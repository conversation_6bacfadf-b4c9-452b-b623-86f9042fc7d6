using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;

namespace AMprover.BlazorApp.Infrastructure.Events;

public interface IEventManager
{
    /// <summary>
    ///     Registers the given delegate as a handler for the event specified by `eventName` on the given source.
    /// </summary>
    void AddEventListener<T, TArgs>(T source, string eventName, Action<T, TArgs> handler)
        where T : class
        where TArgs : EventArgs;

    /// <summary>
    ///     Registers the given delegate as a handler for the INotifyPropertyChanged.PropertyChanged event
    /// </summary>
    void AddEventListener<T>(T source, Action<T, PropertyChangedEventArgs> handler)
        where T : class, INotifyPropertyChanged;

    /// <summary>
    ///     Registers the given delegate as a handler for the INotifyCollectionChanged.CollectionChanged event
    /// </summary>
    void AddEventListener<T>(T source, Action<T, NotifyCollectionChangedEventArgs> handler)
        where T : class, INotifyCollectionChanged;

    /// <summary>
    ///     Unregisters any previously registered  event handlers on the given source object
    /// </summary>
    void RemoveEventListener<T>(T source)
        where T : class;

    /// <summary>
    ///     Unregisters all  event listeners that have been registered by this  event manager instance
    /// </summary>
    void ClearEventListeners();
}

public class EventManager : IEventManager
{
    private readonly Dictionary<IEventListener, Delegate> _listeners =
        new();

    /// <summary>
    ///     Registers the given delegate as a handler for the event specified by `eventName` on the given source.
    /// </summary>
    public void AddEventListener<T, TArgs>(T source, string eventName, Action<T, TArgs> handler)
        where T : class
        where TArgs : EventArgs
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        _listeners.Add(new EventListener<T, TArgs>(source, eventName, handler), handler);
    }

    /// <summary>
    ///     Registers the given delegate as a handler for the INotifyPropertyChanged.PropertyChanged event
    /// </summary>
    public void AddEventListener<T>(T source, Action<T, PropertyChangedEventArgs> handler)
        where T : class, INotifyPropertyChanged
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        _listeners.Add(new PropertyChangedEventListener<T>(source, handler), handler);
    }

    /// <summary>
    ///     Registers the given delegate as a handler for the INotifyCollectionChanged.CollectionChanged event
    /// </summary>
    public void AddEventListener<T>(T source, Action<T, NotifyCollectionChangedEventArgs> handler)
        where T : class, INotifyCollectionChanged
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        _listeners.Add(new CollectionChangedEventListener<T>(source, handler), handler);
    }

    /// <summary>
    ///     Unregisters any previously registered  event handlers on the given source object
    /// </summary>
    public void RemoveEventListener<T>(T source)
        where T : class
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        var toRemove = new List<IEventListener>();
        foreach (var listener in _listeners.Keys)
            if (!listener.IsAlive)
            {
                toRemove.Add(listener);
            }
            else if (listener.Source == source)
            {
                listener.StopListening();
                toRemove.Add(listener);
            }

        foreach (var item in toRemove) _listeners.Remove(item);
    }

    /// <summary>
    ///     Unregisters all  event listeners that have been registered by this  event manager instance
    /// </summary>
    public void ClearEventListeners()
    {
        foreach (var listener in _listeners.Keys.Where(listener => listener.IsAlive))
            listener.StopListening();

        _listeners.Clear();
    }
}