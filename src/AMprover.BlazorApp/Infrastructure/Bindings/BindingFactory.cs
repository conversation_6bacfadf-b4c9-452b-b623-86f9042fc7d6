using System.ComponentModel;
using System.Reflection;
using AMprover.BlazorApp.Infrastructure.Events;

namespace AMprover.BlazorApp.Infrastructure.Bindings;

public interface IBindingFactory
{
    IBinding Create(INotifyPropertyChanged source, PropertyInfo propertyInfo, IEventManager weakEventManager);
}

internal class BindingFactory : IBindingFactory
{
    public IBinding Create(INotifyPropertyChanged source, PropertyInfo propertyInfo,
        IEventManager weakEventManager)
    {
        return new Binding(source, propertyInfo, weakEventManager);
    }
}