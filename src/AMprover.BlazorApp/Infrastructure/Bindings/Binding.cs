using System;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Reflection;
using AMprover.BlazorApp.Infrastructure.Events;

namespace AMprover.BlazorApp.Infrastructure.Bindings;

public interface IBinding : IDisposable
{
    void Initialize();
    object GetValue();
}

internal class Binding : IBinding
{
    private readonly IEventManager _eventManager;
    private INotifyCollectionChanged _boundCollection;
    private bool _isCollection;

    public Binding(INotifyPropertyChanged source, PropertyInfo propertyInfo, IEventManager weakEventManager)
    {
        _eventManager = weakEventManager ?? throw new ArgumentNullException(nameof(weakEventManager));
        Source = source ?? throw new ArgumentNullException(nameof(source));
        PropertyInfo = propertyInfo ?? throw new ArgumentNullException(nameof(propertyInfo));
    }

    public INotifyPropertyChanged Source { get; }
    public PropertyInfo PropertyInfo { get; }

    public event EventHandler BindingValueChanged;

    public void Initialize()
    {
        _isCollection = typeof(INotifyCollectionChanged).IsAssignableFrom(PropertyInfo.PropertyType);
        _eventManager.AddEventListener(Source, SourceOnPropertyChanged);
        AddCollectionBindings();
    }

    public object GetValue()
    {
        return PropertyInfo.GetValue(Source, null)!;
    }

    private void AddCollectionBindings()
    {
        if (!_isCollection || !(GetValue() is INotifyCollectionChanged collection))
            return;

        _eventManager.AddEventListener(collection, CollectionOnCollectionChanged);
        _boundCollection = collection;
    }

    private void SourceOnPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName is null)
        {
            BindingValueChanged?.Invoke(this, EventArgs.Empty);
            return;
        }

        if (e.PropertyName != PropertyInfo.Name)
            return;

        if (_isCollection)
        {
            if (_boundCollection != null) _eventManager.RemoveEventListener(_boundCollection);

            AddCollectionBindings();
        }


        BindingValueChanged?.Invoke(this, EventArgs.Empty);
    }

    private void CollectionOnCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
    {
        BindingValueChanged?.Invoke(this, EventArgs.Empty);
    }

    #region IDisposable Support

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposing) return;
        if (_boundCollection != null)
            _eventManager.RemoveEventListener(_boundCollection);

        _eventManager.RemoveEventListener(Source);
    }

    #endregion

    #region Base overrides

    public override string ToString()
    {
        return $"{PropertyInfo?.DeclaringType?.Name}.{PropertyInfo?.Name}";
    }

    public override bool Equals(object obj)
    {
        return obj is Binding b && ReferenceEquals(b.Source, Source) && b.PropertyInfo.Name == PropertyInfo.Name;
    }

    public override int GetHashCode()
    {
        var hash = 13;
        hash = hash * 7 + Source.GetHashCode();
        hash = hash * 7 + PropertyInfo.Name.GetHashCode(StringComparison.InvariantCulture);

        return hash;
    }

    #endregion
}