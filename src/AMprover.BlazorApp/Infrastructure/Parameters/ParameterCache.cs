using System;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace AMprover.BlazorApp.Infrastructure.Parameters;

internal interface IParameterCache
{
    ParameterInfo Get(Type type);
    void Set(Type type, ParameterInfo info);
}

internal class ParameterCache : IParameterCache
{
    private ConcurrentDictionary<Type, ParameterInfo> _cache = new();

    public ParameterInfo Get(Type type)
    {
        if (type == null) throw new ArgumentNullException(nameof(type));
        return _cache.TryGetValue(type, out var info) ? info : null;
    }

    public void Set(Type type, ParameterInfo info)
    {
        if (type == null) throw new ArgumentNullException(nameof(type));
        if (info == null) throw new ArgumentNullException(nameof(info));
        _cache.AddOrUpdate(type, info, (_, _) => info);
    }
}