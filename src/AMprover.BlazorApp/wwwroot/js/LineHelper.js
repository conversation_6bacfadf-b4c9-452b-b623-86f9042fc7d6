function getElement(id) {
    return document.getElementById(id);
}

function getElementByClass(className) {
    return document.getElementsByClassName(className);
}

function getOffset(el, parent, scale) {
    const targetRect = el.getBoundingClientRect();
    const containerRect = parent.getBoundingClientRect();

    const left = (targetRect.left - containerRect.left) / scale.scaleX;
    const top = (targetRect.top - containerRect.top) / scale.scaleY;
    const width = (targetRect.width || el.offsetWidth) / scale.scaleX;
    const height = (targetRect.height || el.offsetHeight) / scale.scaleY;

    const right = left + width;
    const bottom = top + height;
    const middle = top + height / 2;
    const center = left + width / 2;

    return {
        left,
        right,
        top,
        bottom,
        middle,
        center
    };
}

function extractScaleValue(transform) {
    const match = transform.match(/matrix\(([^,]+), ([^,]+), ([^,]+), ([^,]+), ([^,]+), ([^)]+)\)/);
    if (match) {
        const scaleX = parseFloat(match[1]);
        const scaleY = parseFloat(match[4]);
        return {scaleX, scaleY};
    } else {
        return {scaleX: 1, scaleY: 1}; // Default scale values
    }
}

function drawLine(props) {
    const parent = document.querySelector(".rams-diagram-container");
    if (!parent) {
        console.log("rams-diagram-container not available");
        return;
    }

    // Check if the parent has any transform applied (such as zoom or scaling)
    const parentTransform = window.getComputedStyle(parent).transform;
    const parentScale = extractScaleValue(parentTransform);

    const {startingElement, endingElement, thickness} = props;
    const {id: startingElementId, x: horizontal1, y: vertical1} = startingElement;
    const {id: endingElementId, x: horizontal2, y: vertical2} = endingElement;

    const firstElement = document.getElementById(startingElementId);
    const secondElement = document.getElementById(endingElementId);

    if (!firstElement || !secondElement) {
        return;
    }

    // Calculate the offset of the elements based on the parent container
    const off1 = getOffset(firstElement, parent, parentScale);
    const off2 = getOffset(secondElement, parent, parentScale);

    const x1 = off1[horizontal1];
    const y1 = off1[vertical1];
    const x2 = off2[horizontal2];
    const y2 = off2[vertical2];

    if (x1 < 0 || x2 < 0 || y1 < 0 || y2 < 0) return;

    // Calculate the length and center of the line
    const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    const cx = (x1 + x2) / 2 - length / 2;
    const cy = (y1 + y2) / 2 - thickness / 2;

    // Calculate the angle between the two points
    const angle = Math.atan2(y1 - y2, x1 - x2) * (180 / Math.PI);

    // Create the line element
    const line = document.createElement("div");
    line.className = "rams-connector";

    // Apply styles to the line element
    line.style = `z-index:100;padding:0;margin:0;height:${thickness}px;line-height:1px;position:absolute;left:${cx}px;top:${cy}px;width:${length}px;transform:rotate(${angle}deg);`;

    parent.appendChild(line);
}

function removeLines() {
    document.querySelectorAll('.rams-connector').forEach(e => e.remove());
}

function resizeTableCells() {
    //console.log("resizing tables from ", origin, " :", new Date());

    // Get all table elements on the page
    const allTables = document.querySelectorAll('table');

    // Convert NodeList to an array for easier manipulation
    const tableArray = Array.from(allTables);

    // Loop through tables from the last (innermost) to the first (outermost)
    for (let i = tableArray.length - 1; i >= 0; i--) {
        const tableElement = tableArray[i];
        // Process the current table
        resizeNestedTableCells(tableElement);
    }
}

function resizeNestedTableCells(table) {
    //console.log("resizeNestedTableCells")

    var maxRamsBlockItemsCount = 0;
    var maxRamsBlockItemsRowIndex = -1;

    for (var i = 0; i < table.rows.length; i++) {
        var ramsBlockItemsCount = Array.from(table.rows[i].cells).filter(cell => cell.classList.contains("rams-block-item")).length;
        if (ramsBlockItemsCount > maxRamsBlockItemsCount) {
            maxRamsBlockItemsCount = ramsBlockItemsCount;
            maxRamsBlockItemsRowIndex = i;
        }
    }

    const filteredRows = table.rows;

    if (filteredRows && filteredRows.length > 2) {
        const columnWidths = Array.from({length: filteredRows[maxRamsBlockItemsRowIndex].cells.length}, () => 0);

        for (let i = 1; i < filteredRows.length - 1; i++) {
            const cells = filteredRows[i].cells;
            for (let j = 0; j < cells.length; j++) {
                const cell = cells[j];
                const computedWidth = getComputedWidth(cell);
                columnWidths[j] = Math.max(columnWidths[j], computedWidth);
            }
        }

        if (filteredRows.length > 2) {
            for (const element of filteredRows) {
                const cells = element.cells;
                for (let j = 0; j < cells.length; j++) {
                    const cell = cells[j];
                    if (!cell.classList.contains("rams-block-item")) {
                        cell.style.width = columnWidths[j] + 'px';
                    }
                }
            }
        }

        // Calculate the total width of columns
        const totalWidth = columnWidths.reduce((acc, width) => acc + width, 0);

        // Set the total width on the parent container
        table.parentNode.style.width = totalWidth + 'px';
    }
}

function getComputedWidth(element) {
    const computedStyle = window.getComputedStyle(element);
    return parseFloat(computedStyle.width);
}

function removeRamsDraggedOverClassExceptID(id) {
    //console.log("removeRamsDraggedOverClassExceptID")
    const elements = document.querySelectorAll('.rams-dd-dragged-over');
    elements.forEach((element) => {
        if (element.id !== id) {
            element.classList.remove('rams-dd-dragged-over');
        }
    });
}

window.stopPropagation = function (event) {
    event.stopPropagation();
};