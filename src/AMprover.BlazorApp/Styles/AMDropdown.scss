// Dropdown
.am-dropdown {
    background-color: #fff;
    cursor: pointer;
    width: 100%;
    position: relative;
    border-radius: 5px;
    border: 1px solid #aaa;
    font-size: 1rem;

    &:hover {
        border: 1px solid #666;
    }

    .am-dropdown-header {
        overflow: hidden;
        padding: 5px 10px 5px 10px;

        label {
            margin: 0px;
        }
    }

    .am-dropdown-chevron {
        float: right;
        position: relative;

        &:after {
            content: "";
            margin-left: -6px;
            margin-top: 6px;
            display: inline-block;
            border-left: 1px solid;
            border-bottom: 1px solid;
            width: 8px;
            height: 8px;
            position: absolute;
            transform: translate(-3px, -3px) rotate(-45deg);
        }
    }

    .am-dropdown-clear {
        float: right;
        position: relative;

        &:after {
            content: "X";
            color: darkred;
            font-size:18px;
            font-weight:800;
            margin-left: -28px;
            margin-top: -5px;
            display: inline-block;
            position: absolute;
        }
    }
}

.am-readonly {
    color: #999;
    border-color: #ddd;
    cursor: default;
    background-color: #e9ecef;

    &:hover {
        border: 1px solid #ddd;
    }
}

.am-disabled {
    color: #999;
    border-color: #ddd;
    cursor: default;

    &:hover {
        border: 1px solid #ddd;
    }
}

// Dropdown Options
.am-dropdown-option-container {
    background-color: #fff;
    position: absolute;
    z-index: 10000;
    border: 1px solid #aaa;
    border-radius: 5px;
    cursor: pointer;
}

.am-dropdown-focus {
    border-width: 0px;
    position: absolute;
    background: rgba(0,0,0,0);
}

.am-dropdown-filter-container {
    width: 100%;
    padding: 3px;
}

.am-dropdown-filter-input {
    width: 100%;
    padding: 5px 10px 5px 10px;
}

.am-dropdown-scroll-container {
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 300px;
}

.am-dropdown-item {
    padding: 5px 10px;
    font-size: 1rem;

    &:hover {
        background-color: #eee !important;
    }
}

.am-selected {
    background-color: #e9e9e9 !important;
}
