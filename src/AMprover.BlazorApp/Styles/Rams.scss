.rams {
  /* seperate generic component for zooming */
  .zoom-controls {
    display: flex;
    justify-content: flex-end; /* Align items to the right */
    gap: 10px;
    z-index: 1000; /* Ensure controls are always on top */
    margin-right: 20px;

    a {
      padding: 10px;
      font-size: 22px;
      cursor: pointer;

      &:hover {
        color: #62bc47;
      }
    }
  }

  .form-control.small {
    width: auto; //take only necessary space for input
    display: inline; //Keep input on same line
  }

  .diagram-container-width-left {
    max-height: 100%;
    overflow: auto;
  }

  .diagram-container-width-full-left {
    max-width: 25vw;

    .rams-diagram .rams-layout {
      max-width: 25vw;
    }
  }

  @media only screen and (min-width: 1200px) {
    .diagram-container-width {
      max-width: 65vw;
    }

    .diagram-container-width-full {
      max-width: 90vw;

      .rams-diagram .rams-layout {
        max-width: 90vw;
        width: 90vw;
      }
    }
  }

  @media only screen and (min-width: 1450px) {
    .diagram-container-width {
      max-width: 67vw;
    }

    .diagram-container-width-full {
      max-width: 91vw;

      .rams-diagram .rams-layout {
        max-width: 91vw;
        width: 91vw;
      }
    }
  }

  @media only screen and (min-width: 1500px) {
    .diagram-container-width {
      max-width: 68vw;
    }

    .diagram-container-width-full {
      max-width: 92vw;

      .rams-diagram .rams-layout {
        max-width: 92vw;
        width: 92vw;
      }
    }
  }

  @media only screen and (min-width: 1850px) {
    .diagram-container-width {
      max-width: 70vw;
    }

    .diagram-container-width-full {
      max-width: 94vw;

      .rams-diagram .rams-layout {
        max-width: 94vw;
        width: 94vw;
      }
    }
  }

  @media only screen and (min-width: 2350px) {
    .diagram-container-width {
      max-width: 71vw;
    }

    .diagram-container-width-full {
      max-width: 95vw;

      .rams-diagram .rams-layout {
        max-width: 95vw;
        width: 95vw;
      }
    }
  }

  @media only screen and (min-width: 3150px) {
    .diagram-container-width {
      max-width: 72vw;
    }

    .diagram-container-width-full {
      max-width: 96vw;
    }

    .rams-diagram .rams-layout {
      max-width: 96vw;
      width: 96vw;
    }
  }

  .row.rams {
    height: 80vh;
  }

  .rams-diagram {
    border: 1px solid black;
    background-color: #FCFCFC;

    .rams-layout {
      width: 70vw;
      max-height: 75vh;
      height: 75vh;
      overflow: scroll !important;
    }

    table {
      tr {
        td:first-child > .divider.left {
          width: 10px;
          left: 0;
        }
      }

      td {
        padding: 15px 0;
      }
    }

    span.fas:hover {
      cursor: pointer;
    }

    h3 {
      margin: 10px;
    }

    .divider {
      background-color: #606060;
      width: 20px;
      height: 2px;
      position: absolute;
      top: 47%;
      left: -10px;
      z-index: 10;

      &.hidden {
        visibility: hidden;
      }

      &.top {
        top: 0;
        left: 50%;
        width: 2px;
        height: 50%;
        z-index: 1;
      }

      &.bottom {
        bottom: 0;
        top: auto;
        left: 50%;
        width: 2px;
        height: 50%;
        z-index: 1;
      }

      &.right {
        left: auto;
        right: 0;
        width: 10px;
      }
    }

    .rams-diagram-container {
      display: flex;
      background-color: white;
      position: relative;
      padding-left: 15px;
      padding-right: 15px;
    }

    .flex-container {
      display: flex;
      align-items: center;
      flex-direction: row;
      flex-basis: auto;
      padding: 0;
      position: relative;
      min-height: 150px;
      z-index: 1;
      background-color: white;

      &.collapsed {
        display: none;
      }

      .flex-container-column {
        margin: 50px 0;
      }

      .flex-item, .flex-container-column {
        flex-basis: 100%;
        min-height: 60px;
        align-items: center;
        justify-content: center;
      }
    }

    .rams-container {
      min-width: 150px;
      min-height: 70px;
      border: 2px dotted #606060;

      h4, h5, h6 {
        margin: 2px;
        border: none;
        padding: 0;
        font-size: 1rem;
      }

      //Handling the hover full text function for diagram blocks and containers
      h4 {
        overflow: hidden;
        margin-bottom: 0;
        text-overflow: ellipsis;
        width: 80%;
      }

      .rams-body {
        padding: 5px;
        margin: 2px 0 -10px 15px;
        text-overflow: ellipsis;
        align-content: space-around;
        overflow: visible;
        white-space: nowrap;
        margin-top: -6px;
      }

      .rams-footer {
        margin: 0 5px 0 5px;

        .rams-footer-content {
          color: #606060;
          max-width: 80%;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }

      &.collapsed {
        width: 150px;
        max-width: 150px;
        height: 88px;
      }

      &.collapsedsmall {
        width: 150px;
        max-width: 150px;
        height: 72px;
      }

      .left-sidebar {
        width: 15px;
        border-right: 2px dotted #606060;
        border-top: 2px dotted #606060;
        background-color: lightgray;
      }

      .txt-margin {
        margin-top: -8px;
      }
    }

    .rams-block {
      width: 150px; //min-width
      min-height: 70px;
      border: 2px solid #606060;
      background-color: #F2F2F2;

      &.riskblock {
        background-color: #D9D9D9;
      }

      h4, h5, h6 {
        margin: 2px 2px 5px 20px;
        border: none;
        font-size: 1rem;
        font-weight: bolder;
        overflow: visible;
        white-space: nowrap;
      }

      //Handling the hover full text function for diagram blocks and containers
      h4 {
        overflow: hidden;
        margin-bottom: 0;
        text-overflow: ellipsis;
        width: 110px;
      }

      .rams-body {
        padding: 5px;
        margin: 2px 0 -10px 15px;
        text-overflow: ellipsis;
        align-content: space-around;
        overflow: visible;
        white-space: nowrap;
      }

      .rams-footer {
        margin: 0 5px 0 5px;

        .rams-footer-content {
          color: #606060;
          max-width: 80%;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }

      .left-link-item {
        background-size: 110%;
        width: 15px;
        height: 15px;
        border-right: 2px solid #606060;
        background-color: #e9ecef;

        &.linkedtorams {
          background-image: url('../images/RamsGrey.png');
          background-size: 110%;
        }

        &.linkedtoriskobject {
          background-image: url('../images/FailureModeGrey.png');
          background-size: 110%;
        }

        &.linkedtoobject {
          background-image: url('../images/AbsGrey.png');
          background-size: 110%;
        }

        &.linkedtorisk {
          background-image: url('../images/ValueRiskSmall.png');
          background-size: 110%;
        }

        &.linkedtodiagram {
          background-image: url('../images/Organogram.png');
          background-size: 110%;
        }
      }

      .left-sidebar {
        width: 15px;
        border-right: 2px solid #606060;
        border-top: 2px solid #606060;
        background-color: #9DD58B;

        &.buffer {
          background-color: #FCCD9A;
        }

        &.coldstandby {
          background-color: #93C9FF;
        }

        &.notinstalled {
          background-color: yellow;
        }

        &.referenceblock {
          background-color: black;
        }

        &.singlenode {
          background-color: purple;
        }
      }

      .txt-margin {
        margin-top: -8px;
      }
    }

    .rams-block:after {
      content: "";
      position: absolute;
      bottom: 0;
      right: 0;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 20px 20px 0 0;
      border-color: transparent transparent transparent transparent;
    }

    .rams-block.DU:after {
      border-color: transparent red transparent transparent;
    }

    .rams-block.DD:after {
      border-color: transparent orange transparent transparent;
    }

    .rams-block.SU:after {
      border-color: transparent lightgreen transparent transparent;
    }

    .rams-block.SD:after {
      border-color: transparent green transparent transparent;
    }

    .selected {
      border: 2px dashed #0066CC;
    }

    .rams-dd-inprogess > * {
      pointer-events: none;
    }

    /*dropzone style style*/
    .rams-dd-dropzone {
      min-height: 30px;
      align-items: center;
    }

    .rams-dd-dropzone-padding-next {
      margin-right: 10px;
    }

    .rams-dd-dropzone-padding-previous {
      margin-left: 10px;
    }

    /*drag drop styles*/
    .rams-dd-spacing {
      min-height: 50px;

      &.no-width {
        width: auto;
      }
    }

    .rams-dd-spacing-dragged-over {
      padding: 5px;
    }

    .rams-dd-dragged-over {
      background-color: midnightblue;
      opacity: 0.6;
      animation: blinker 2s linear infinite;
    }

    .type-container.rams-dd-dragged-over {
      background-color: #f8992f;
    }

    .rams-dd-dragged-over > div {
      background-color: #D7D7D7;
      opacity: 0.6;
      animation: blinker 1s linear infinite;
    }

    .rams-dd-dragged-over-denied {
      background-color: yellow;
      opacity: 0.6;
      animation: blinker 1s linear infinite;
    }

    div:has(div.rams-dd-in-transit) {
      background-color: transparent;
    }

    .rams-dd-in-transit {
      opacity: 0;
      background-color: transparent;
    }

    .rams-dd-in-transit > div {
      opacity: 0;
      background-color: transparent;
    }

    .rams-dd-spacing-full-width-top, .rams-dd-spacing-full-width-center, .rams-dd-spacing-full-width-bottom {
      min-height: 30px;
    }

    .rams-dd-dropzone-parallel {
      background-size: 30px 30px;
      min-height: 30px;

      td {
        padding: 0;
      }
    }

    .rams-dd-spacing-full-width-bottom {
      bottom: 0;
    }

    @keyframes blinker {
      50% {
        opacity: 0;
      }
    }

    .blink_me {
      animation: blinker 1s linear infinite;
    }

    .rams-flex .rams-dd-spacing {
      width: 15px;
      height: auto;
    }

    .rams-flex .rams-dd-dragged-over {
      background-color: #D7D7D7;
      opacity: 0.6;
      animation: blinker 1s linear infinite;
    }

    .rams-flex .rams-dd-dragged-over > div {
      background-color: #D7D7D7;
      opacity: 0.9;
      animation: blinker 1s linear infinite;
    }

    .rams-flex .rams-dd-in-transit {
      background-color: orangered;
    }

    .rams-flex .rams-dd-in-transit > div {
      background-color: orangered;
    }

    .rams-dd-noselect {
      -webkit-touch-callout: none; /* iOS Safari */
      -webkit-user-select: none; /* Safari */
      -khtml-user-select: none; /* Konqueror HTML */
      -moz-user-select: none; /* Old versions of Firefox */
      -ms-user-select: none; /* Internet Explorer/Edge */
      user-select: none; /* Non-prefixed version, currently supported by Chrome, Edge, Opera and Firefox */
    }

    .rams-connector {
      background-color: #0066CC;
    }

    span.xoon {
      font-size: 1rem;
      position: absolute;
      right: 5px;
    }
  }

  /* drag & drop scss */

  .drag-proxy {
    background-color: #D9D9D9;
    border: 2px solid #606060;
    opacity: 0.75;
    z-index: 1000;
    pointer-events: none;
    transform-origin: 0 0;

    .rams-container {
      padding: 5px;

      table {
        tr {
          td:first-child > .divider.left {
            width: 10px;
            left: 0;
          }
        }

        td {
          padding: 15px 0;

          .rams-block {
            background-color: #D9D9D9;
            border: 2px solid #606060;
          }

          h4 {
            span {
              display: none;
            }
          }

          span.xoon, .rams-dd-dropzone-parallel, .spacing-before, .spacing-after {
            display: none;
          }
        }
      }
    }

    .rams-block {
      h4, h5, h6 {
        margin: 2px 2px 5px 20px;
        border: none;
        font-size: 1rem;
        font-weight: bolder;
        overflow: visible;
        white-space: nowrap;
      }

      //Handling the hover full text function for diagram blocks and containers
      h4 {
        overflow: hidden;
        margin-bottom: 0;
        text-overflow: ellipsis;
      }

      .rams-body {
        padding: 5px;
        margin: 2px 0 -10px 15px;
        text-overflow: ellipsis;
        align-content: space-around;
        overflow: visible;
        white-space: nowrap;
      }

      .rams-footer {
        margin: 0 5px 0 5px;

        .rams-footer-content {
          color: #606060;
          max-width: 80%;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }

      .left-link-item {
        width: 15px;
        height: 15px;
        border-right: 2px solid #606060;
        background-color: #e9ecef;
      }

      .left-sidebar {
        width: 15px;
        border-right: 2px solid #606060;
        border-top: 2px solid #606060;
        background-color: #9DD58B;
      }

      .txt-margin {
        margin-top: -8px;
      }
    }
  }

  .btn-mainbar {
    margin-right: 4px;
    margin-top: 5px;
    float: right;
  }

  .btn-bar-left {
    margin-left: -14px;
    margin-bottom: 3px;
    margin-top: -4px;
  }

  .btn-bar-right {
    margin-right: -50px;
    margin-bottom: 3px;
    margin-top: -4px;
    float: right;
  }
}