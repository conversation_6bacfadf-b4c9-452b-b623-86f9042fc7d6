@page "/testing/performance-dropdown"
@inherits AMprover.BlazorApp.Infrastructure.Components.BaseComponent<DropdownTestViewModel>

<h2>Dropdown performance</h2>

<RadzenTabs>
    <Tabs>

        <RadzenTabsItem Text="Empty">
            <p>nothing here</p>
        </RadzenTabsItem>

        <RadzenTabsItem Text="AMprover">
            <div class="row">
                @if (BindingContext.Initialized)
                {
                    @for (int i = 0; i < BindingContext.loops; i++)
                    {
                        int index = i;

                        <div class="col-2">
                            <AMproverDictionaryDropdown
                                Data=@BindingContext.Objects.ToDictionary(x => (int?)x.Id, y => y.Name)
                                @bind-Value=@BindingContext.ResultIdList[index] />
                        </div>
                    
                    }
                }
            </div>
        </RadzenTabsItem>

        <RadzenTabsItem Text="Radzen">
            <div class="row">
                @if (BindingContext.Initialized)
                {
                    @for (int i = 0; i < BindingContext.loops; i++)
                    {
                        int index = i;

                        <div class="col-2">
                            <RadzenDropDown Data=@BindingContext.Objects
                                            TextProperty=@(nameof(ObjectModel.Name))
                                            ValueProperty=@(nameof(ObjectModel.Id))
                                            @bind-Value=@BindingContext.ResultIdList[index] />
                        </div>
                    }
                }
            </div>
        </RadzenTabsItem> 

        <RadzenTabsItem Text="Plain">

            <div class="row">
                @if (BindingContext.Initialized)
                {
                    @for (int i = 0; i < BindingContext.loops; i++)
                    {
                        int index = i;

                        <div class="col-2">
                           <AMDropdown
                               AllowClear=true
                               Data=@BindingContext.Objects.ToDictionary(x => (int?)x.Id, x => x.Name)
                               @bind-Value=@BindingContext.ResultIdList[index] />
                        </div>
                    }
                }
            </div>
        </RadzenTabsItem>

        <RadzenTabsItem Text="Plain with object reference">

            <div class="row">
                @if (BindingContext.Initialized)
                {
                    @for (int i = 0; i < BindingContext.loops; i++)
                    {
                        int index = i;

                        <div class="col-2">
                            <AMDropdown AllowClear=true
                                        Data=@BindingContext.Objects.ToDictionary(x => x, x => x.Name, new ObjectModelComparer())
                                        @bind-Value=@BindingContext.ResultObjectList[index] />
                        </div>
                    }
                }
            </div>
        </RadzenTabsItem>

    </Tabs>
</RadzenTabs>