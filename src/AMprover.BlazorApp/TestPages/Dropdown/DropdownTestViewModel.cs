using AMprover.BlazorApp.Infrastructure.ViewModels.Base;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System;

namespace AMprover.BlazorApp.TestPages.Dropdown
{
    public class DropdownTestViewModel : BaseViewModel
    {
        public DropdownTestViewModel(
            ILoggerFactory loggerFactory,
            DialogService dialogService) : base(loggerFactory)
        {
            _dialogService = dialogService;
        }

        public bool Initialized = false;

        public List<ObjectModel> Objects { get; private set; } = new();

        public List<int?> ResultIdList { get; private set; } = new();
        public List<ObjectModel> ResultObjectList { get; private set; } = new();

        public int loops { get; set; } = 100;

        public int options { get; set; } = 400;

        public override void OnInitialized()
        {
            ResultIdList = new();
            for (int i = 0; i < loops; i++)
            {
                ResultIdList.Add(0);
                ResultObjectList.Add(null);
            }

            for(int i = 1; i <= options; i ++)
            {
                Objects.Add(new ObjectModel
                {
                    Id = i,
                    Name = $"{i:D4} {Guid.NewGuid()}",
                    Description = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean dignissim consequat mauris. Donec finibus, arcu sagittis eleifend porttitor, quam arcu rutrum purus, imperdiet placerat lacus lectus eleifend dui. Fusce commodo diam id nisi malesuada facilisis eu et nunc. Ut lobortis, lectus non congue interdum, mi nisi posuere dui, porttitor congue arcu libero ac nibh. Aenean ut justo ut nibh eleifend viverra eget eget eros. Mauris eleifend tincidunt nulla sit amet mollis. Praesent ac sem ac tellus ultricies cursus. Suspendisse semper convallis est, a eleifend turpis convallis eu.",
                });
            }

            base.OnInitialized();
            Initialized = true;
        }
    }
}
